<idea-plugin>
  <version>1.0.3</version>
  <change-notes><![CDATA[## 1.0.3 - 2025-06-20

### Added

- Updated build configuration with proper dependency management
- Added comprehensive error handling and logging
- Implemented secure API key storage
- Added rate limiting and caching improvements

### Changed

- Migrated from deprecated Codex API to modern OpenAI API
- Improved UI/UX with better error messaging and loading indicators
- Enhanced plugin metadata and descriptions

### Fixed

- Fixed build configuration issues
- Resolved plugin.xml syntax errors
- Fixed memory leaks in UI components
- Added proper null safety checks

### Security

- Implemented secure API key storage
- Added input validation and sanitization
- Added request timeout and retry logic]]></change-notes>
  <id>com.wontlost.zigzag</id>
  <name>Zigzag</name>
  <vendor email="<EMAIL>" url="https://wontlost.com">WontLost Ltd</vendor>
  <description><![CDATA[<h1>Zigzag - AI-Powered Coding Assistant</h1>
        <p>Zigzag integrates OpenAI's powerful language models directly into your IntelliJ IDEA environment, 
        providing intelligent code completion, generation, and refactoring capabilities.</p>
        
        <h2>Features:</h2>
        <ul>
            <li><strong>Smart Code Completion:</strong> Get context-aware code suggestions as you type</li>
            <li><strong>Code Generation:</strong> Generate code from natural language comments</li>
            <li><strong>Code Refactoring:</strong> Improve and optimize your existing code</li>
            <li><strong>Interactive Chat:</strong> Ask questions and get coding help in real-time</li>
        </ul>
        
        <h2>Getting Started:</h2>
        <p>After installation, configure your OpenAI API key in <strong>Settings | Tools | Zigzag</strong> 
        to start using the AI-powered features.</p>]]></description>
  <idea-version since-build="251" until-build="253.*" />
  <depends>com.intellij.modules.platform</depends>
  <depends>com.intellij.modules.java</depends>
  <depends>com.intellij.modules.lang</depends>
  <extensions defaultExtensionNs="com.intellij">
    <notificationGroup id="Zigzag Notifications" displayType="BALLOON" />
    <completion.contributor language="ANY" implementationClass="com.wontlost.zigzag.CodexCompletionContributor" />
    <toolWindow id="Zigzag Assistant" anchor="right" factoryClass="com.wontlost.zigzag.CodexAssistantToolWindowFactory" />
    <applicationConfigurable id="com.wontlost.zigzag.settings" displayName="Zigzag" instance="com.wontlost.zigzag.OpenAISettingsConfigurable" />
    <projectActivity implementation="com.wontlost.zigzag.ZigzagStartupActivity" />
  </extensions>
  <actions>
    <action id="com.wontlost.zigzag.ZigzagAction" class="com.wontlost.zigzag.ZigzagAction" text="Test Zigzag" description="Test Zigzag connection">
      <add-to-group group-id="ToolsMenu" anchor="last" />
    </action>
    <action id="com.wontlost.zigzag.CodexContextAction" class="com.wontlost.zigzag.CodexContextAction" text="AI Assist" description="Generate or refactor with AI">
      <add-to-group group-id="EditorPopupMenu" anchor="last" />
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt A" />
    </action>
  </actions>
</idea-plugin>
