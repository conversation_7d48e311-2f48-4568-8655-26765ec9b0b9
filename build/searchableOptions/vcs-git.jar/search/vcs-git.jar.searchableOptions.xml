<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="abort" path="ActionManager" hit="Abort Cherry-Pick" />
    <option name="cherry-pick" path="ActionManager" hit="Abort Cherry-Pick" />
    <option name="abort" path="ActionManager" hit="Abort Merge" />
    <option name="merge" path="ActionManager" hit="Abort Merge" />
    <option name="abort" path="ActionManager" hit="Abort Rebase" />
    <option name="rebase" path="ActionManager" hit="Abort Rebase" />
    <option name="abort" path="ActionManager" hit="Abort Revert" />
    <option name="revert" path="ActionManager" hit="Abort Revert" />
    <option name="accept" path="ActionManager" hit="Accept Theirs" />
    <option name="theirs" path="ActionManager" hit="Accept Theirs" />
    <option name="accept" path="ActionManager" hit="Accept Yours" />
    <option name="yours" path="ActionManager" hit="Accept Yours" />
    <option name="actions" path="ActionManager" hit="Actions" />
    <option name="add" path="ActionManager" hit="Add all unstaged changes and untracked files to index" />
    <option name="all" path="ActionManager" hit="Add all unstaged changes and untracked files to index" />
    <option name="and" path="ActionManager" hit="Add all unstaged changes and untracked files to index" />
    <option name="changes" path="ActionManager" hit="Add all unstaged changes and untracked files to index" />
    <option name="files" path="ActionManager" hit="Add all unstaged changes and untracked files to index" />
    <option name="index" path="ActionManager" hit="Add all unstaged changes and untracked files to index" />
    <option name="to" path="ActionManager" hit="Add all unstaged changes and untracked files to index" />
    <option name="unstaged" path="ActionManager" hit="Add all unstaged changes and untracked files to index" />
    <option name="untracked" path="ActionManager" hit="Add all unstaged changes and untracked files to index" />
    <option name="add" path="ActionManager" hit="Add all unstaged changes in tracked files to index" />
    <option name="all" path="ActionManager" hit="Add all unstaged changes in tracked files to index" />
    <option name="changes" path="ActionManager" hit="Add all unstaged changes in tracked files to index" />
    <option name="files" path="ActionManager" hit="Add all unstaged changes in tracked files to index" />
    <option name="in" path="ActionManager" hit="Add all unstaged changes in tracked files to index" />
    <option name="index" path="ActionManager" hit="Add all unstaged changes in tracked files to index" />
    <option name="to" path="ActionManager" hit="Add all unstaged changes in tracked files to index" />
    <option name="tracked" path="ActionManager" hit="Add all unstaged changes in tracked files to index" />
    <option name="unstaged" path="ActionManager" hit="Add all unstaged changes in tracked files to index" />
    <option name="add" path="ActionManager" hit="Add selected files to .gitignore" />
    <option name="files" path="ActionManager" hit="Add selected files to .gitignore" />
    <option name="gitignore" path="ActionManager" hit="Add selected files to .gitignore" />
    <option name="selected" path="ActionManager" hit="Add selected files to .gitignore" />
    <option name="to" path="ActionManager" hit="Add selected files to .gitignore" />
    <option name="add" path="ActionManager" hit="Add to .gitignore" />
    <option name="gitignore" path="ActionManager" hit="Add to .gitignore" />
    <option name="to" path="ActionManager" hit="Add to .gitignore" />
    <option name="apply" path="ActionManager" hit="Apply" />
    <option name="apply" path="ActionManager" hit="Apply selected stash" />
    <option name="selected" path="ActionManager" hit="Apply selected stash" />
    <option name="stash" path="ActionManager" hit="Apply selected stash" />
    <option name="branches" path="ActionManager" hit="Branches…" />
    <option name="amend" path="ActionManager" hit="Change commit message via reword option of git rebase or amend" />
    <option name="change" path="ActionManager" hit="Change commit message via reword option of git rebase or amend" />
    <option name="commit" path="ActionManager" hit="Change commit message via reword option of git rebase or amend" />
    <option name="git" path="ActionManager" hit="Change commit message via reword option of git rebase or amend" />
    <option name="message" path="ActionManager" hit="Change commit message via reword option of git rebase or amend" />
    <option name="of" path="ActionManager" hit="Change commit message via reword option of git rebase or amend" />
    <option name="option" path="ActionManager" hit="Change commit message via reword option of git rebase or amend" />
    <option name="or" path="ActionManager" hit="Change commit message via reword option of git rebase or amend" />
    <option name="rebase" path="ActionManager" hit="Change commit message via reword option of git rebase or amend" />
    <option name="reword" path="ActionManager" hit="Change commit message via reword option of git rebase or amend" />
    <option name="via" path="ActionManager" hit="Change commit message via reword option of git rebase or amend" />
    <option name="changelists" path="ActionManager" hit="Changelists" />
    <option name="checkout" path="ActionManager" hit="Checkout" />
    <option name="checkout" path="ActionManager" hit="Checkout Revision" />
    <option name="revision" path="ActionManager" hit="Checkout Revision" />
    <option name="checkout" path="ActionManager" hit="Checkout Selected…" />
    <option name="selected" path="ActionManager" hit="Checkout Selected…" />
    <option name="checkout" path="ActionManager" hit="Checkout Tag or Revision…" />
    <option name="or" path="ActionManager" hit="Checkout Tag or Revision…" />
    <option name="revision" path="ActionManager" hit="Checkout Tag or Revision…" />
    <option name="tag" path="ActionManager" hit="Checkout Tag or Revision…" />
    <option name="and" path="ActionManager" hit="Checkout and Rebase onto Current" />
    <option name="checkout" path="ActionManager" hit="Checkout and Rebase onto Current" />
    <option name="current" path="ActionManager" hit="Checkout and Rebase onto Current" />
    <option name="onto" path="ActionManager" hit="Checkout and Rebase onto Current" />
    <option name="rebase" path="ActionManager" hit="Checkout and Rebase onto Current" />
    <option name="branch" path="ActionManager" hit="Checkout selected branch" />
    <option name="checkout" path="ActionManager" hit="Checkout selected branch" />
    <option name="selected" path="ActionManager" hit="Checkout selected branch" />
    <option name="a" path="ActionManager" hit="Checkout selected revision or a branch on selected commit" />
    <option name="branch" path="ActionManager" hit="Checkout selected revision or a branch on selected commit" />
    <option name="checkout" path="ActionManager" hit="Checkout selected revision or a branch on selected commit" />
    <option name="commit" path="ActionManager" hit="Checkout selected revision or a branch on selected commit" />
    <option name="on" path="ActionManager" hit="Checkout selected revision or a branch on selected commit" />
    <option name="or" path="ActionManager" hit="Checkout selected revision or a branch on selected commit" />
    <option name="revision" path="ActionManager" hit="Checkout selected revision or a branch on selected commit" />
    <option name="selected" path="ActionManager" hit="Checkout selected revision or a branch on selected commit" />
    <option name="clone" path="ActionManager" hit="Clone…" />
    <option name="all" path="ActionManager" hit="Commit All when Nothing Is Staged" />
    <option name="commit" path="ActionManager" hit="Commit All when Nothing Is Staged" />
    <option name="is" path="ActionManager" hit="Commit All when Nothing Is Staged" />
    <option name="nothing" path="ActionManager" hit="Commit All when Nothing Is Staged" />
    <option name="staged" path="ActionManager" hit="Commit All when Nothing Is Staged" />
    <option name="when" path="ActionManager" hit="Commit All when Nothing Is Staged" />
    <option name="all" path="ActionManager" hit="Commit all files when there are no staged files" />
    <option name="are" path="ActionManager" hit="Commit all files when there are no staged files" />
    <option name="commit" path="ActionManager" hit="Commit all files when there are no staged files" />
    <option name="files" path="ActionManager" hit="Commit all files when there are no staged files" />
    <option name="no" path="ActionManager" hit="Commit all files when there are no staged files" />
    <option name="staged" path="ActionManager" hit="Commit all files when there are no staged files" />
    <option name="there" path="ActionManager" hit="Commit all files when there are no staged files" />
    <option name="when" path="ActionManager" hit="Commit all files when there are no staged files" />
    <option name="and" path="ActionManager" hit="Commit and Push…" />
    <option name="commit" path="ActionManager" hit="Commit and Push…" />
    <option name="push" path="ActionManager" hit="Commit and Push…" />
    <option name="commit" path="ActionManager" hit="Commit…" />
    <option name="and" path="ActionManager" hit="Compare HEAD, Staged and Local Versions" />
    <option name="compare" path="ActionManager" hit="Compare HEAD, Staged and Local Versions" />
    <option name="head" path="ActionManager" hit="Compare HEAD, Staged and Local Versions" />
    <option name="local" path="ActionManager" hit="Compare HEAD, Staged and Local Versions" />
    <option name="staged" path="ActionManager" hit="Compare HEAD, Staged and Local Versions" />
    <option name="versions" path="ActionManager" hit="Compare HEAD, Staged and Local Versions" />
    <option name="branch" path="ActionManager" hit="Compare with Branch…" />
    <option name="compare" path="ActionManager" hit="Compare with Branch…" />
    <option name="with" path="ActionManager" hit="Compare with Branch…" />
    <option name="compare" path="ActionManager" hit="Compare with Current" />
    <option name="current" path="ActionManager" hit="Compare with Current" />
    <option name="with" path="ActionManager" hit="Compare with Current" />
    <option name="compare" path="ActionManager" hit="Compare with HEAD Version" />
    <option name="head" path="ActionManager" hit="Compare with HEAD Version" />
    <option name="version" path="ActionManager" hit="Compare with HEAD Version" />
    <option name="with" path="ActionManager" hit="Compare with HEAD Version" />
    <option name="compare" path="ActionManager" hit="Compare with Local Version" />
    <option name="local" path="ActionManager" hit="Compare with Local Version" />
    <option name="version" path="ActionManager" hit="Compare with Local Version" />
    <option name="with" path="ActionManager" hit="Compare with Local Version" />
    <option name="compare" path="ActionManager" hit="Compare with Staged Version" />
    <option name="staged" path="ActionManager" hit="Compare with Staged Version" />
    <option name="version" path="ActionManager" hit="Compare with Staged Version" />
    <option name="with" path="ActionManager" hit="Compare with Staged Version" />
    <option name="compare" path="ActionManager" hit="Compare with Tag…" />
    <option name="tag" path="ActionManager" hit="Compare with Tag…" />
    <option name="with" path="ActionManager" hit="Compare with Tag…" />
    <option name="continue" path="ActionManager" hit="Continue Rebase" />
    <option name="rebase" path="ActionManager" hit="Continue Rebase" />
    <option name="create" path="ActionManager" hit="Create Git Repository…" />
    <option name="git" path="ActionManager" hit="Create Git Repository…" />
    <option name="repository" path="ActionManager" hit="Create Git Repository…" />
    <option name="a" path="ActionManager" hit="Create a fixup commit" />
    <option name="commit" path="ActionManager" hit="Create a fixup commit" />
    <option name="create" path="ActionManager" hit="Create a fixup commit" />
    <option name="fixup" path="ActionManager" hit="Create a fixup commit" />
    <option name="a" path="ActionManager" hit="Create a squash commit" />
    <option name="commit" path="ActionManager" hit="Create a squash commit" />
    <option name="create" path="ActionManager" hit="Create a squash commit" />
    <option name="squash" path="ActionManager" hit="Create a squash commit" />
    <option name="and" path="ActionManager" hit="Create and checkout new branch" />
    <option name="branch" path="ActionManager" hit="Create and checkout new branch" />
    <option name="checkout" path="ActionManager" hit="Create and checkout new branch" />
    <option name="create" path="ActionManager" hit="Create and checkout new branch" />
    <option name="new" path="ActionManager" hit="Create and checkout new branch" />
    <option name="branch" path="ActionManager" hit="Create new branch starting from the selected commit" />
    <option name="commit" path="ActionManager" hit="Create new branch starting from the selected commit" />
    <option name="create" path="ActionManager" hit="Create new branch starting from the selected commit" />
    <option name="from" path="ActionManager" hit="Create new branch starting from the selected commit" />
    <option name="new" path="ActionManager" hit="Create new branch starting from the selected commit" />
    <option name="selected" path="ActionManager" hit="Create new branch starting from the selected commit" />
    <option name="starting" path="ActionManager" hit="Create new branch starting from the selected commit" />
    <option name="the" path="ActionManager" hit="Create new branch starting from the selected commit" />
    <option name="commit" path="ActionManager" hit="Create new tag pointing to this commit" />
    <option name="create" path="ActionManager" hit="Create new tag pointing to this commit" />
    <option name="new" path="ActionManager" hit="Create new tag pointing to this commit" />
    <option name="pointing" path="ActionManager" hit="Create new tag pointing to this commit" />
    <option name="tag" path="ActionManager" hit="Create new tag pointing to this commit" />
    <option name="this" path="ActionManager" hit="Create new tag pointing to this commit" />
    <option name="to" path="ActionManager" hit="Create new tag pointing to this commit" />
    <option name="delete" path="ActionManager" hit="Delete" />
    <option name="directory" path="ActionManager" hit="Directory" />
    <option name="and" path="ActionManager" hit="Disable staging area and switch to changelists" />
    <option name="area" path="ActionManager" hit="Disable staging area and switch to changelists" />
    <option name="changelists" path="ActionManager" hit="Disable staging area and switch to changelists" />
    <option name="disable" path="ActionManager" hit="Disable staging area and switch to changelists" />
    <option name="staging" path="ActionManager" hit="Disable staging area and switch to changelists" />
    <option name="switch" path="ActionManager" hit="Disable staging area and switch to changelists" />
    <option name="to" path="ActionManager" hit="Disable staging area and switch to changelists" />
    <option name="drop" path="ActionManager" hit="Drop" />
    <option name="commits" path="ActionManager" hit="Drop Commits" />
    <option name="drop" path="ActionManager" hit="Drop Commits" />
    <option name="drop" path="ActionManager" hit="Drop selected stash" />
    <option name="selected" path="ActionManager" hit="Drop selected stash" />
    <option name="stash" path="ActionManager" hit="Drop selected stash" />
    <option name="data" path="ActionManager" hit="Dump Git Log Index Data" />
    <option name="dump" path="ActionManager" hit="Dump Git Log Index Data" />
    <option name="git" path="ActionManager" hit="Dump Git Log Index Data" />
    <option name="index" path="ActionManager" hit="Dump Git Log Index Data" />
    <option name="log" path="ActionManager" hit="Dump Git Log Index Data" />
    <option name="commit" path="ActionManager" hit="Edit Commit Message…" />
    <option name="edit" path="ActionManager" hit="Edit Commit Message…" />
    <option name="message" path="ActionManager" hit="Edit Commit Message…" />
    <option name="all" path="ActionManager" hit="Execute Branch Operations on All Roots" />
    <option name="branch" path="ActionManager" hit="Execute Branch Operations on All Roots" />
    <option name="execute" path="ActionManager" hit="Execute Branch Operations on All Roots" />
    <option name="on" path="ActionManager" hit="Execute Branch Operations on All Roots" />
    <option name="operations" path="ActionManager" hit="Execute Branch Operations on All Roots" />
    <option name="roots" path="ActionManager" hit="Execute Branch Operations on All Roots" />
    <option name="fetch" path="ActionManager" hit="Fetch" />
    <option name="fixup" path="ActionManager" hit="Fixup…" />
    <option name="git" path="ActionManager" hit="Git" />
    <option name="by" path="ActionManager" hit="Group By" />
    <option name="group" path="ActionManager" hit="Group By" />
    <option name="branches" path="ActionManager" hit="Hide Git Branches" />
    <option name="git" path="ActionManager" hit="Hide Git Branches" />
    <option name="hide" path="ActionManager" hit="Hide Git Branches" />
    <option name="been" path="ActionManager" hit="Highlights commits which have not been cherry-picked to the current branch" />
    <option name="branch" path="ActionManager" hit="Highlights commits which have not been cherry-picked to the current branch" />
    <option name="cherry-picked" path="ActionManager" hit="Highlights commits which have not been cherry-picked to the current branch" />
    <option name="commits" path="ActionManager" hit="Highlights commits which have not been cherry-picked to the current branch" />
    <option name="current" path="ActionManager" hit="Highlights commits which have not been cherry-picked to the current branch" />
    <option name="have" path="ActionManager" hit="Highlights commits which have not been cherry-picked to the current branch" />
    <option name="highlights" path="ActionManager" hit="Highlights commits which have not been cherry-picked to the current branch" />
    <option name="not" path="ActionManager" hit="Highlights commits which have not been cherry-picked to the current branch" />
    <option name="the" path="ActionManager" hit="Highlights commits which have not been cherry-picked to the current branch" />
    <option name="to" path="ActionManager" hit="Highlights commits which have not been cherry-picked to the current branch" />
    <option name="which" path="ActionManager" hit="Highlights commits which have not been cherry-picked to the current branch" />
    <option name="a" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="able" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="action" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="affect" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="all" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="also" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="be" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="behavior" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="branches" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="check" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="compare" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="create" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="delete" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="dialog" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="if" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="in" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="it" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="new" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="of" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="or" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="out" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="push" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="roots" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="selected" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="single" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="the" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="to" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="vcs" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="will" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="with" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="you" path="ActionManager" hit="If selected, you will be able to check out, compare, delete, or create new branches in all VCS roots with a single action. It will also affect the behavior of the push dialog." />
    <option name="files" path="ActionManager" hit="Ignored Files" />
    <option name="ignored" path="ActionManager" hit="Ignored Files" />
    <option name="from" path="ActionManager" hit="Interactively Rebase from Here…" />
    <option name="here" path="ActionManager" hit="Interactively Rebase from Here…" />
    <option name="interactively" path="ActionManager" hit="Interactively Rebase from Here…" />
    <option name="rebase" path="ActionManager" hit="Interactively Rebase from Here…" />
    <option name="manage" path="ActionManager" hit="Manage Remotes…" />
    <option name="remotes" path="ActionManager" hit="Manage Remotes…" />
    <option name="merge" path="ActionManager" hit="Merge" />
    <option name="current" path="ActionManager" hit="Merge into Current" />
    <option name="into" path="ActionManager" hit="Merge into Current" />
    <option name="merge" path="ActionManager" hit="Merge into Current" />
    <option name="merge" path="ActionManager" hit="Merge…" />
    <option name="branch" path="ActionManager" hit="Navigate Log to Branch Head" />
    <option name="head" path="ActionManager" hit="Navigate Log to Branch Head" />
    <option name="log" path="ActionManager" hit="Navigate Log to Branch Head" />
    <option name="navigate" path="ActionManager" hit="Navigate Log to Branch Head" />
    <option name="to" path="ActionManager" hit="Navigate Log to Branch Head" />
    <option name="branch" path="ActionManager" hit="Navigate Log to Selected Branch Head" />
    <option name="head" path="ActionManager" hit="Navigate Log to Selected Branch Head" />
    <option name="log" path="ActionManager" hit="Navigate Log to Selected Branch Head" />
    <option name="navigate" path="ActionManager" hit="Navigate Log to Selected Branch Head" />
    <option name="selected" path="ActionManager" hit="Navigate Log to Selected Branch Head" />
    <option name="to" path="ActionManager" hit="Navigate Log to Selected Branch Head" />
    <option name="branch" path="ActionManager" hit="New Branch" />
    <option name="new" path="ActionManager" hit="New Branch" />
    <option name="branch" path="ActionManager" hit="New Branch from Current…" />
    <option name="current" path="ActionManager" hit="New Branch from Current…" />
    <option name="from" path="ActionManager" hit="New Branch from Current…" />
    <option name="new" path="ActionManager" hit="New Branch from Current…" />
    <option name="branch" path="ActionManager" hit="New Branch…" />
    <option name="new" path="ActionManager" hit="New Branch…" />
    <option name="new" path="ActionManager" hit="New Tag…" />
    <option name="tag" path="ActionManager" hit="New Tag…" />
    <option name="cherry-picked" path="ActionManager" hit="Not Cherry-Picked Commits" />
    <option name="commits" path="ActionManager" hit="Not Cherry-Picked Commits" />
    <option name="not" path="ActionManager" hit="Not Cherry-Picked Commits" />
    <option name="exclude" path="ActionManager" hit="Open .git/info/exclude" />
    <option name="git" path="ActionManager" hit="Open .git/info/exclude" />
    <option name="info" path="ActionManager" hit="Open .git/info/exclude" />
    <option name="open" path="ActionManager" hit="Open .git/info/exclude" />
    <option name="editor" path="ActionManager" hit="Open .git/info/exclude in editor" />
    <option name="exclude" path="ActionManager" hit="Open .git/info/exclude in editor" />
    <option name="git" path="ActionManager" hit="Open .git/info/exclude in editor" />
    <option name="in" path="ActionManager" hit="Open .git/info/exclude in editor" />
    <option name="info" path="ActionManager" hit="Open .git/info/exclude in editor" />
    <option name="open" path="ActionManager" hit="Open .git/info/exclude in editor" />
    <option name="from" path="ActionManager" hit="Path From Repository Root" />
    <option name="path" path="ActionManager" hit="Path From Repository Root" />
    <option name="repository" path="ActionManager" hit="Path From Repository Root" />
    <option name="root" path="ActionManager" hit="Path From Repository Root" />
    <option name="pop" path="ActionManager" hit="Pop" />
    <option name="apply" path="ActionManager" hit="Pop or apply selected stash as new branch with or without index" />
    <option name="as" path="ActionManager" hit="Pop or apply selected stash as new branch with or without index" />
    <option name="branch" path="ActionManager" hit="Pop or apply selected stash as new branch with or without index" />
    <option name="index" path="ActionManager" hit="Pop or apply selected stash as new branch with or without index" />
    <option name="new" path="ActionManager" hit="Pop or apply selected stash as new branch with or without index" />
    <option name="or" path="ActionManager" hit="Pop or apply selected stash as new branch with or without index" />
    <option name="pop" path="ActionManager" hit="Pop or apply selected stash as new branch with or without index" />
    <option name="selected" path="ActionManager" hit="Pop or apply selected stash as new branch with or without index" />
    <option name="stash" path="ActionManager" hit="Pop or apply selected stash as new branch with or without index" />
    <option name="with" path="ActionManager" hit="Pop or apply selected stash as new branch with or without index" />
    <option name="without" path="ActionManager" hit="Pop or apply selected stash as new branch with or without index" />
    <option name="pop" path="ActionManager" hit="Pop selected stash" />
    <option name="selected" path="ActionManager" hit="Pop selected stash" />
    <option name="stash" path="ActionManager" hit="Pop selected stash" />
    <option name="a" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="changes" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="commit" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="in" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="made" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="new" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="original" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="produce" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="reverts" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="s" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="the" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="which" path="ActionManager" hit="Produce a new commit(s), which reverts the changes made in the original commit(s)" />
    <option name="into" path="ActionManager" hit="Pull Into Selected Using Merge" />
    <option name="merge" path="ActionManager" hit="Pull Into Selected Using Merge" />
    <option name="pull" path="ActionManager" hit="Pull Into Selected Using Merge" />
    <option name="selected" path="ActionManager" hit="Pull Into Selected Using Merge" />
    <option name="using" path="ActionManager" hit="Pull Into Selected Using Merge" />
    <option name="into" path="ActionManager" hit="Pull Into Selected Using Rebase" />
    <option name="pull" path="ActionManager" hit="Pull Into Selected Using Rebase" />
    <option name="rebase" path="ActionManager" hit="Pull Into Selected Using Rebase" />
    <option name="selected" path="ActionManager" hit="Pull Into Selected Using Rebase" />
    <option name="using" path="ActionManager" hit="Pull Into Selected Using Rebase" />
    <option name="pull" path="ActionManager" hit="Pull…" />
    <option name="all" path="ActionManager" hit="Push All up to Here…" />
    <option name="here" path="ActionManager" hit="Push All up to Here…" />
    <option name="push" path="ActionManager" hit="Push All up to Here…" />
    <option name="to" path="ActionManager" hit="Push All up to Here…" />
    <option name="up" path="ActionManager" hit="Push All up to Here…" />
    <option name="all" path="ActionManager" hit="Push all previous commits including the selected one" />
    <option name="commits" path="ActionManager" hit="Push all previous commits including the selected one" />
    <option name="including" path="ActionManager" hit="Push all previous commits including the selected one" />
    <option name="one" path="ActionManager" hit="Push all previous commits including the selected one" />
    <option name="previous" path="ActionManager" hit="Push all previous commits including the selected one" />
    <option name="push" path="ActionManager" hit="Push all previous commits including the selected one" />
    <option name="selected" path="ActionManager" hit="Push all previous commits including the selected one" />
    <option name="the" path="ActionManager" hit="Push all previous commits including the selected one" />
    <option name="push" path="ActionManager" hit="Push..." />
    <option name="rebase" path="ActionManager" hit="Rebase" />
    <option name="current" path="ActionManager" hit="Rebase Current onto Selected" />
    <option name="onto" path="ActionManager" hit="Rebase Current onto Selected" />
    <option name="rebase" path="ActionManager" hit="Rebase Current onto Selected" />
    <option name="selected" path="ActionManager" hit="Rebase Current onto Selected" />
    <option name="rebase" path="ActionManager" hit="Rebase…" />
    <option name="refresh" path="ActionManager" hit="Refresh Stashes" />
    <option name="stashes" path="ActionManager" hit="Refresh Stashes" />
    <option name="list" path="ActionManager" hit="Refresh the List of Stashes" />
    <option name="of" path="ActionManager" hit="Refresh the List of Stashes" />
    <option name="refresh" path="ActionManager" hit="Refresh the List of Stashes" />
    <option name="stashes" path="ActionManager" hit="Refresh the List of Stashes" />
    <option name="the" path="ActionManager" hit="Refresh the List of Stashes" />
    <option name="a" path="ActionManager" hit="Rename a local branch" />
    <option name="branch" path="ActionManager" hit="Rename a local branch" />
    <option name="local" path="ActionManager" hit="Rename a local branch" />
    <option name="rename" path="ActionManager" hit="Rename a local branch" />
    <option name="rename" path="ActionManager" hit="Rename..." />
    <option name="rename" path="ActionManager" hit="Rename…" />
    <option name="repositories" path="ActionManager" hit="Repositories" />
    <option name="repository" path="ActionManager" hit="Repository" />
    <option name="branch" path="ActionManager" hit="Reset Current Branch to Here…" />
    <option name="current" path="ActionManager" hit="Reset Current Branch to Here…" />
    <option name="here" path="ActionManager" hit="Reset Current Branch to Here…" />
    <option name="reset" path="ActionManager" hit="Reset Current Branch to Here…" />
    <option name="to" path="ActionManager" hit="Reset Current Branch to Here…" />
    <option name="head" path="ActionManager" hit="Reset HEAD…" />
    <option name="reset" path="ActionManager" hit="Reset HEAD…" />
    <option name="conflicts" path="ActionManager" hit="Resolve Conflicts…" />
    <option name="resolve" path="ActionManager" hit="Resolve Conflicts…" />
    <option name="popup" path="ActionManager" hit="Restore Popup Size" />
    <option name="restore" path="ActionManager" hit="Restore Popup Size" />
    <option name="size" path="ActionManager" hit="Restore Popup Size" />
    <option name="commit" path="ActionManager" hit="Revert Commit" />
    <option name="revert" path="ActionManager" hit="Revert Commit" />
    <option name="rollback" path="ActionManager" hit="Rollback" />
    <option name="settings" path="ActionManager" hit="Settings" />
    <option name="branches" path="ActionManager" hit="Show Branches" />
    <option name="show" path="ActionManager" hit="Show Branches" />
    <option name="diff" path="ActionManager" hit="Show Diff with Working Tree" />
    <option name="show" path="ActionManager" hit="Show Diff with Working Tree" />
    <option name="tree" path="ActionManager" hit="Show Diff with Working Tree" />
    <option name="with" path="ActionManager" hit="Show Diff with Working Tree" />
    <option name="working" path="ActionManager" hit="Show Diff with Working Tree" />
    <option name="command" path="ActionManager" hit="Show Git Log for Command" />
    <option name="for" path="ActionManager" hit="Show Git Log for Command" />
    <option name="git" path="ActionManager" hit="Show Git Log for Command" />
    <option name="log" path="ActionManager" hit="Show Git Log for Command" />
    <option name="show" path="ActionManager" hit="Show Git Log for Command" />
    <option name="git" path="ActionManager" hit="Show Git Repository Log…" />
    <option name="log" path="ActionManager" hit="Show Git Repository Log…" />
    <option name="repository" path="ActionManager" hit="Show Git Repository Log…" />
    <option name="show" path="ActionManager" hit="Show Git Repository Log…" />
    <option name="git" path="ActionManager" hit="Show Git Stash" />
    <option name="show" path="ActionManager" hit="Show Git Stash" />
    <option name="stash" path="ActionManager" hit="Show Git Stash" />
    <option name="git" path="ActionManager" hit="Show Git Stash Tool Window Tab" />
    <option name="show" path="ActionManager" hit="Show Git Stash Tool Window Tab" />
    <option name="stash" path="ActionManager" hit="Show Git Stash Tool Window Tab" />
    <option name="tab" path="ActionManager" hit="Show Git Stash Tool Window Tab" />
    <option name="tool" path="ActionManager" hit="Show Git Stash Tool Window Tab" />
    <option name="window" path="ActionManager" hit="Show Git Stash Tool Window Tab" />
    <option name="local" path="ActionManager" hit="Show Local Version" />
    <option name="show" path="ActionManager" hit="Show Local Version" />
    <option name="version" path="ActionManager" hit="Show Local Version" />
    <option name="branches" path="ActionManager" hit="Show Recent Branches" />
    <option name="recent" path="ActionManager" hit="Show Recent Branches" />
    <option name="show" path="ActionManager" hit="Show Recent Branches" />
    <option name="at" path="ActionManager" hit="Show Repository at Revision" />
    <option name="repository" path="ActionManager" hit="Show Repository at Revision" />
    <option name="revision" path="ActionManager" hit="Show Repository at Revision" />
    <option name="show" path="ActionManager" hit="Show Repository at Revision" />
    <option name="show" path="ActionManager" hit="Show Staged Version" />
    <option name="staged" path="ActionManager" hit="Show Staged Version" />
    <option name="version" path="ActionManager" hit="Show Staged Version" />
    <option name="area" path="ActionManager" hit="Show Staging Area" />
    <option name="show" path="ActionManager" hit="Show Staging Area" />
    <option name="staging" path="ActionManager" hit="Show Staging Area" />
    <option name="add" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="allows" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="and" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="area" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="changes" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="diff" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="head" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="interactively" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="into" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="local" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="show" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="staged" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="staging" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="that" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="to" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="versions" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="window" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="with" path="ActionManager" hit="Show diff window with HEAD, staged and local versions that allows to interactively add changes into staging area" />
    <option name="content" path="ActionManager" hit="Show editor with local content for the current file" />
    <option name="current" path="ActionManager" hit="Show editor with local content for the current file" />
    <option name="editor" path="ActionManager" hit="Show editor with local content for the current file" />
    <option name="file" path="ActionManager" hit="Show editor with local content for the current file" />
    <option name="for" path="ActionManager" hit="Show editor with local content for the current file" />
    <option name="local" path="ActionManager" hit="Show editor with local content for the current file" />
    <option name="show" path="ActionManager" hit="Show editor with local content for the current file" />
    <option name="the" path="ActionManager" hit="Show editor with local content for the current file" />
    <option name="with" path="ActionManager" hit="Show editor with local content for the current file" />
    <option name="content" path="ActionManager" hit="Show editor with staged content of the current file" />
    <option name="current" path="ActionManager" hit="Show editor with staged content of the current file" />
    <option name="editor" path="ActionManager" hit="Show editor with staged content of the current file" />
    <option name="file" path="ActionManager" hit="Show editor with staged content of the current file" />
    <option name="of" path="ActionManager" hit="Show editor with staged content of the current file" />
    <option name="show" path="ActionManager" hit="Show editor with staged content of the current file" />
    <option name="staged" path="ActionManager" hit="Show editor with staged content of the current file" />
    <option name="the" path="ActionManager" hit="Show editor with staged content of the current file" />
    <option name="with" path="ActionManager" hit="Show editor with staged content of the current file" />
    <option name="files" path="ActionManager" hit="Show ignored files" />
    <option name="ignored" path="ActionManager" hit="Show ignored files" />
    <option name="show" path="ActionManager" hit="Show ignored files" />
    <option name="git" path="ActionManager" hit="Show in Git Log" />
    <option name="in" path="ActionManager" hit="Show in Git Log" />
    <option name="log" path="ActionManager" hit="Show in Git Log" />
    <option name="show" path="ActionManager" hit="Show in Git Log" />
    <option name="allows" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="and" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="commits" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="dialog" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="fixup" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="for" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="interactive" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="rebase" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="remove" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="reorder" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="reword" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="show" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="squash" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="the" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="to" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="which" path="ActionManager" hit="Show the dialog for interactive rebase which allows to squash, fixup, reorder, remove and reword commits" />
    <option name="commit" path="ActionManager" hit="Skip Commit" />
    <option name="skip" path="ActionManager" hit="Skip Commit" />
    <option name="commits" path="ActionManager" hit="Squash Commits…" />
    <option name="squash" path="ActionManager" hit="Squash Commits…" />
    <option name="into" path="ActionManager" hit="Squash Into…" />
    <option name="squash" path="ActionManager" hit="Squash Into…" />
    <option name="stage" path="ActionManager" hit="Stage" />
    <option name="all" path="ActionManager" hit="Stage All" />
    <option name="stage" path="ActionManager" hit="Stage All" />
    <option name="all" path="ActionManager" hit="Stage All Tracked" />
    <option name="stage" path="ActionManager" hit="Stage All Tracked" />
    <option name="tracked" path="ActionManager" hit="Stage All Tracked" />
    <option name="content" path="ActionManager" hit="Stage Without Content" />
    <option name="stage" path="ActionManager" hit="Stage Without Content" />
    <option name="without" path="ActionManager" hit="Stage Without Content" />
    <option name="area" path="ActionManager" hit="Staging Area" />
    <option name="staging" path="ActionManager" hit="Staging Area" />
    <option name="changes" path="ActionManager" hit="Stash Changes…" />
    <option name="stash" path="ActionManager" hit="Stash Changes…" />
    <option name="files" path="ActionManager" hit="Stash Files" />
    <option name="stash" path="ActionManager" hit="Stash Files" />
    <option name="silently" path="ActionManager" hit="Stash Silently" />
    <option name="stash" path="ActionManager" hit="Stash Silently" />
    <option name="dialog" path="ActionManager" hit="Test Git Login Dialog" />
    <option name="git" path="ActionManager" hit="Test Git Login Dialog" />
    <option name="login" path="ActionManager" hit="Test Git Login Dialog" />
    <option name="test" path="ActionManager" hit="Test Git Login Dialog" />
    <option name="commit" path="ActionManager" hit="Undo Commit…" />
    <option name="undo" path="ActionManager" hit="Undo Commit…" />
    <option name="and" path="ActionManager" hit="Undo last commit and put its changes into selected changelist" />
    <option name="changelist" path="ActionManager" hit="Undo last commit and put its changes into selected changelist" />
    <option name="changes" path="ActionManager" hit="Undo last commit and put its changes into selected changelist" />
    <option name="commit" path="ActionManager" hit="Undo last commit and put its changes into selected changelist" />
    <option name="into" path="ActionManager" hit="Undo last commit and put its changes into selected changelist" />
    <option name="its" path="ActionManager" hit="Undo last commit and put its changes into selected changelist" />
    <option name="last" path="ActionManager" hit="Undo last commit and put its changes into selected changelist" />
    <option name="put" path="ActionManager" hit="Undo last commit and put its changes into selected changelist" />
    <option name="selected" path="ActionManager" hit="Undo last commit and put its changes into selected changelist" />
    <option name="undo" path="ActionManager" hit="Undo last commit and put its changes into selected changelist" />
    <option name="unstage" path="ActionManager" hit="Unstage" />
    <option name="changes" path="ActionManager" hit="Unstash Changes…" />
    <option name="unstash" path="ActionManager" hit="Unstash Changes…" />
    <option name="unstash" path="ActionManager" hit="Unstash…" />
    <option name="update" path="ActionManager" hit="Update" />
    <option name="branch" path="ActionManager" hit="Update Branch Filter" />
    <option name="filter" path="ActionManager" hit="Update Branch Filter" />
    <option name="update" path="ActionManager" hit="Update Branch Filter" />
    <option name="branch" path="ActionManager" hit="Update Branch Filter in Log" />
    <option name="filter" path="ActionManager" hit="Update Branch Filter in Log" />
    <option name="in" path="ActionManager" hit="Update Branch Filter in Log" />
    <option name="log" path="ActionManager" hit="Update Branch Filter in Log" />
    <option name="update" path="ActionManager" hit="Update Branch Filter in Log" />
    <option name="branch" path="ActionManager" hit="Update branch filter in log with selected branches" />
    <option name="branches" path="ActionManager" hit="Update branch filter in log with selected branches" />
    <option name="filter" path="ActionManager" hit="Update branch filter in log with selected branches" />
    <option name="in" path="ActionManager" hit="Update branch filter in log with selected branches" />
    <option name="log" path="ActionManager" hit="Update branch filter in log with selected branches" />
    <option name="selected" path="ActionManager" hit="Update branch filter in log with selected branches" />
    <option name="update" path="ActionManager" hit="Update branch filter in log with selected branches" />
    <option name="with" path="ActionManager" hit="Update branch filter in log with selected branches" />
    <option name="data" path="ActionManager" hit="Use Shared Git Log Index Data" />
    <option name="git" path="ActionManager" hit="Use Shared Git Log Index Data" />
    <option name="index" path="ActionManager" hit="Use Shared Git Log Index Data" />
    <option name="log" path="ActionManager" hit="Use Shared Git Log Index Data" />
    <option name="shared" path="ActionManager" hit="Use Shared Git Log Index Data" />
    <option name="use" path="ActionManager" hit="Use Shared Git Log Index Data" />
    <option name="commits" path="ActionManager" hit="Use the native Git concept of staging for making commits" />
    <option name="concept" path="ActionManager" hit="Use the native Git concept of staging for making commits" />
    <option name="for" path="ActionManager" hit="Use the native Git concept of staging for making commits" />
    <option name="git" path="ActionManager" hit="Use the native Git concept of staging for making commits" />
    <option name="making" path="ActionManager" hit="Use the native Git concept of staging for making commits" />
    <option name="native" path="ActionManager" hit="Use the native Git concept of staging for making commits" />
    <option name="of" path="ActionManager" hit="Use the native Git concept of staging for making commits" />
    <option name="staging" path="ActionManager" hit="Use the native Git concept of staging for making commits" />
    <option name="the" path="ActionManager" hit="Use the native Git concept of staging for making commits" />
    <option name="use" path="ActionManager" hit="Use the native Git concept of staging for making commits" />
    <option name="merge" path="ActionManager" hit="VCS Merge/Rebase Widget" />
    <option name="rebase" path="ActionManager" hit="VCS Merge/Rebase Widget" />
    <option name="vcs" path="ActionManager" hit="VCS Merge/Rebase Widget" />
    <option name="widget" path="ActionManager" hit="VCS Merge/Rebase Widget" />
    <option name="operations" path="ActionManager" hit="VCS Operations" />
    <option name="vcs" path="ActionManager" hit="VCS Operations" />
    <option name="vcs" path="ActionManager" hit="VCS Widget" />
    <option name="widget" path="ActionManager" hit="VCS Widget" />
    <option name="control" path="ActionManager" hit="Version Control" />
    <option name="version" path="ActionManager" hit="Version Control" />
    <option name="a" path="ActionManager" hit="When a branch is selected, filter the log by this branch" />
    <option name="branch" path="ActionManager" hit="When a branch is selected, filter the log by this branch" />
    <option name="by" path="ActionManager" hit="When a branch is selected, filter the log by this branch" />
    <option name="filter" path="ActionManager" hit="When a branch is selected, filter the log by this branch" />
    <option name="is" path="ActionManager" hit="When a branch is selected, filter the log by this branch" />
    <option name="log" path="ActionManager" hit="When a branch is selected, filter the log by this branch" />
    <option name="selected" path="ActionManager" hit="When a branch is selected, filter the log by this branch" />
    <option name="the" path="ActionManager" hit="When a branch is selected, filter the log by this branch" />
    <option name="this" path="ActionManager" hit="When a branch is selected, filter the log by this branch" />
    <option name="when" path="ActionManager" hit="When a branch is selected, filter the log by this branch" />
    <option name="a" path="ActionManager" hit="When a branch is selected, navigate the log to this branch head" />
    <option name="branch" path="ActionManager" hit="When a branch is selected, navigate the log to this branch head" />
    <option name="head" path="ActionManager" hit="When a branch is selected, navigate the log to this branch head" />
    <option name="is" path="ActionManager" hit="When a branch is selected, navigate the log to this branch head" />
    <option name="log" path="ActionManager" hit="When a branch is selected, navigate the log to this branch head" />
    <option name="navigate" path="ActionManager" hit="When a branch is selected, navigate the log to this branch head" />
    <option name="selected" path="ActionManager" hit="When a branch is selected, navigate the log to this branch head" />
    <option name="the" path="ActionManager" hit="When a branch is selected, navigate the log to this branch head" />
    <option name="this" path="ActionManager" hit="When a branch is selected, navigate the log to this branch head" />
    <option name="to" path="ActionManager" hit="When a branch is selected, navigate the log to this branch head" />
    <option name="when" path="ActionManager" hit="When a branch is selected, navigate the log to this branch head" />
  </configurable>
  <configurable id="vcs.Git" configurable_name="Git">
    <option name="add" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="branches" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="cherry-picked" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="commits" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="from" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="picking" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="protected" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="pushed" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="suffix" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="the" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="to" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="when" hit="Add the 'cherry-picked from  ' suffix when picking commits pushed to protected branches" />
    <option name="all" hit="All" />
    <option name="always" hit="Always" />
    <option name="auto" hit="Auto" />
    <option name="auto-update" hit="Auto-update if push of the current branch was rejected" />
    <option name="branch" hit="Auto-update if push of the current branch was rejected" />
    <option name="current" hit="Auto-update if push of the current branch was rejected" />
    <option name="if" hit="Auto-update if push of the current branch was rejected" />
    <option name="of" hit="Auto-update if push of the current branch was rejected" />
    <option name="push" hit="Auto-update if push of the current branch was rejected" />
    <option name="rejected" hit="Auto-update if push of the current branch was rejected" />
    <option name="the" hit="Auto-update if push of the current branch was rejected" />
    <option name="was" hit="Auto-update if push of the current branch was rejected" />
    <option name="clean" hit="Clean working tree using:" />
    <option name="tree" hit="Clean working tree using:" />
    <option name="using" hit="Clean working tree using:" />
    <option name="working" hit="Clean working tree using:" />
    <option name="and" hit="Combine stashes and shelves in one tab" />
    <option name="combine" hit="Combine stashes and shelves in one tab" />
    <option name="in" hit="Combine stashes and shelves in one tab" />
    <option name="one" hit="Combine stashes and shelves in one tab" />
    <option name="shelves" hit="Combine stashes and shelves in one tab" />
    <option name="stashes" hit="Combine stashes and shelves in one tab" />
    <option name="tab" hit="Combine stashes and shelves in one tab" />
    <option name="commit" hit="Commit" />
    <option name="configure" hit="Configure GPG Key " />
    <option name="gpg" hit="Configure GPG Key " />
    <option name="key" hit="Configure GPG Key " />
    <option name="area" hit="Enable staging area" />
    <option name="enable" hit="Enable staging area" />
    <option name="staging" hit="Enable staging area" />
    <option name="all" hit="Execute Branch Operations on All Roots" />
    <option name="branch" hit="Execute Branch Operations on All Roots" />
    <option name="execute" hit="Execute Branch Operations on All Roots" />
    <option name="on" hit="Execute Branch Operations on All Roots" />
    <option name="operations" hit="Execute Branch Operations on All Roots" />
    <option name="roots" hit="Execute Branch Operations on All Roots" />
    <option name="check" hit="Explicitly check for incoming commits on remotes:" />
    <option name="commits" hit="Explicitly check for incoming commits on remotes:" />
    <option name="explicitly" hit="Explicitly check for incoming commits on remotes:" />
    <option name="for" hit="Explicitly check for incoming commits on remotes:" />
    <option name="incoming" hit="Explicitly check for incoming commits on remotes:" />
    <option name="on" hit="Explicitly check for incoming commits on remotes:" />
    <option name="remotes" hit="Explicitly check for incoming commits on remotes:" />
    <option name="by" hit="Filter &quot;Update Project&quot; information by paths:" />
    <option name="filter" hit="Filter &quot;Update Project&quot; information by paths:" />
    <option name="information" hit="Filter &quot;Update Project&quot; information by paths:" />
    <option name="paths" hit="Filter &quot;Update Project&quot; information by paths:" />
    <option name="project" hit="Filter &quot;Update Project&quot; information by paths:" />
    <option name="update" hit="Filter &quot;Update Project&quot; information by paths:" />
    <option name="git" hit="Git" />
    <option name="branch" hit="Load branch protection rules from GitHub" />
    <option name="from" hit="Load branch protection rules from GitHub" />
    <option name="github" hit="Load branch protection rules from GitHub" />
    <option name="load" hit="Load branch protection rules from GitHub" />
    <option name="protection" hit="Load branch protection rules from GitHub" />
    <option name="rules" hit="Load branch protection rules from GitHub" />
    <option name="mb" hit="MB" />
    <option name="merge" hit="Merge" />
    <option name="never" hit="Never" />
    <option name="git" hit="No Git roots in the project" />
    <option name="in" hit="No Git roots in the project" />
    <option name="no" hit="No Git roots in the project" />
    <option name="project" hit="No Git roots in the project" />
    <option name="roots" hit="No Git roots in the project" />
    <option name="the" hit="No Git roots in the project" />
    <option name="" hit="Path to Git executable:" />
    <option name="executable" hit="Path to Git executable:" />
    <option name="git" hit="Path to Git executable:" />
    <option name="path" hit="Path to Git executable:" />
    <option name="to" hit="Path to Git executable:" />
    <option name="branches" hit="Protected branches:" />
    <option name="protected" hit="Protected branches:" />
    <option name="push" hit="Push" />
    <option name="rebase" hit="Rebase" />
    <option name="current" hit="Set this path only for the current project" />
    <option name="for" hit="Set this path only for the current project" />
    <option name="only" hit="Set this path only for the current project" />
    <option name="path" hit="Set this path only for the current project" />
    <option name="project" hit="Set this path only for the current project" />
    <option name="set" hit="Set this path only for the current project" />
    <option name="the" hit="Set this path only for the current project" />
    <option name="this" hit="Set this path only for the current project" />
    <option name="shelve" hit="Shelve" />
    <option name="and" hit="Show Push dialog for Commit and Push" />
    <option name="commit" hit="Show Push dialog for Commit and Push" />
    <option name="dialog" hit="Show Push dialog for Commit and Push" />
    <option name="for" hit="Show Push dialog for Commit and Push" />
    <option name="push" hit="Show Push dialog for Commit and Push" />
    <option name="show" hit="Show Push dialog for Commit and Push" />
    <option name="branches" hit="Show Push dialog only when committing to protected branches" />
    <option name="committing" hit="Show Push dialog only when committing to protected branches" />
    <option name="dialog" hit="Show Push dialog only when committing to protected branches" />
    <option name="only" hit="Show Push dialog only when committing to protected branches" />
    <option name="protected" hit="Show Push dialog only when committing to protected branches" />
    <option name="push" hit="Show Push dialog only when committing to protected branches" />
    <option name="show" hit="Show Push dialog only when committing to protected branches" />
    <option name="to" hit="Show Push dialog only when committing to protected branches" />
    <option name="when" hit="Show Push dialog only when committing to protected branches" />
    <option name="stash" hit="Stash" />
    <option name="test" hit="Test" />
    <option name="update" hit="Update" />
    <option name="method" hit="Update method:" />
    <option name="update" hit="Update method:" />
    <option name="credential" hit="Use credential helper" />
    <option name="helper" hit="Use credential helper" />
    <option name="use" hit="Use credential helper" />
    <option name="about" hit="Warn if CRLF line separators are about to be committed" />
    <option name="are" hit="Warn if CRLF line separators are about to be committed" />
    <option name="be" hit="Warn if CRLF line separators are about to be committed" />
    <option name="committed" hit="Warn if CRLF line separators are about to be committed" />
    <option name="crlf" hit="Warn if CRLF line separators are about to be committed" />
    <option name="if" hit="Warn if CRLF line separators are about to be committed" />
    <option name="line" hit="Warn if CRLF line separators are about to be committed" />
    <option name="separators" hit="Warn if CRLF line separators are about to be committed" />
    <option name="to" hit="Warn if CRLF line separators are about to be committed" />
    <option name="warn" hit="Warn if CRLF line separators are about to be committed" />
    <option name="committing" hit="Warn when committing files larger than" />
    <option name="files" hit="Warn when committing files larger than" />
    <option name="larger" hit="Warn when committing files larger than" />
    <option name="than" hit="Warn when committing files larger than" />
    <option name="warn" hit="Warn when committing files larger than" />
    <option name="when" hit="Warn when committing files larger than" />
    <option name="cause" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="committing" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="files" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="issues" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="might" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="names" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="on" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="other" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="systems" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="that" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="warn" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="when" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="with" hit="Warn when committing files with names that might cause issues on other systems" />
    <option name="committing" hit="Warn when committing in detached HEAD or during rebase" />
    <option name="detached" hit="Warn when committing in detached HEAD or during rebase" />
    <option name="during" hit="Warn when committing in detached HEAD or during rebase" />
    <option name="head" hit="Warn when committing in detached HEAD or during rebase" />
    <option name="in" hit="Warn when committing in detached HEAD or during rebase" />
    <option name="or" hit="Warn when committing in detached HEAD or during rebase" />
    <option name="rebase" hit="Warn when committing in detached HEAD or during rebase" />
    <option name="warn" hit="Warn when committing in detached HEAD or during rebase" />
    <option name="when" hit="Warn when committing in detached HEAD or during rebase" />
  </configurable>
</options>