<options>
  <configurable id="fileTemplates" configurable_name="File and Code Templates">
    <option name="javafxapplication" hit="JavaFXApplication" />
    <option name="javafx-helloapplication-groovy" hit="javafx-HelloApplication-groovy" />
    <option name="javafx-helloapplication-java" hit="javafx-HelloApplication-java" />
    <option name="javafx-helloapplication-kotlin" hit="javafx-HelloApplication-kotlin" />
    <option name="javafx-hellocontroller-groovy" hit="javafx-HelloController-groovy" />
    <option name="javafx-hellocontroller-java" hit="javafx-HelloController-java" />
    <option name="javafx-hellocontroller-kotlin" hit="javafx-HelloController-kotlin" />
    <option name="javafx-build" hit="javafx-build" />
    <option name="javafx-gradle-wrapper" hit="javafx-gradle-wrapper" />
    <option name="javafx-hello-view" hit="javafx-hello-view" />
    <option name="javafx-maven-wrapper" hit="javafx-maven-wrapper" />
    <option name="javafx-module-info" hit="javafx-module-info" />
    <option name="javafx-pom" hit="javafx-pom" />
    <option name="javafx-settings" hit="javafx-settings" />
  </configurable>
</options>