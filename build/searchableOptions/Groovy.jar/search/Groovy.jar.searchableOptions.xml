<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="action" path="ActionManager" hit="Action generates equals and hashCode now" />
    <option name="and" path="ActionManager" hit="Action generates equals and hashCode now" />
    <option name="equals" path="ActionManager" hit="Action generates equals and hashCode now" />
    <option name="generates" path="ActionManager" hit="Action generates equals and hashCode now" />
    <option name="hashcode" path="ActionManager" hit="Action generates equals and hashCode now" />
    <option name="now" path="ActionManager" hit="Action generates equals and hashCode now" />
    <option name="action" path="ActionManager" hit="Action generates propertyMissing()" />
    <option name="generates" path="ActionManager" hit="Action generates propertyMissing()" />
    <option name="propertymissing" path="ActionManager" hit="Action generates propertyMissing()" />
    <option name="build" path="ActionManager" hit="Build Resources" />
    <option name="resources" path="ActionManager" hit="Build Resources" />
    <option name="constructor" path="ActionManager" hit="Constructor" />
    <option name="compilestatic" path="ActionManager" hit="Convert Groovy files to @CompileStatic" />
    <option name="convert" path="ActionManager" hit="Convert Groovy files to @CompileStatic" />
    <option name="files" path="ActionManager" hit="Convert Groovy files to @CompileStatic" />
    <option name="groovy" path="ActionManager" hit="Convert Groovy files to @CompileStatic" />
    <option name="to" path="ActionManager" hit="Convert Groovy files to @CompileStatic" />
    <option name="convert" path="ActionManager" hit="Convert Groovy files to Java" />
    <option name="files" path="ActionManager" hit="Convert Groovy files to Java" />
    <option name="groovy" path="ActionManager" hit="Convert Groovy files to Java" />
    <option name="java" path="ActionManager" hit="Convert Groovy files to Java" />
    <option name="to" path="ActionManager" hit="Convert Groovy files to Java" />
    <option name="compilestatic" path="ActionManager" hit="Convert to @CompileStatic" />
    <option name="convert" path="ActionManager" hit="Convert to @CompileStatic" />
    <option name="to" path="ActionManager" hit="Convert to @CompileStatic" />
    <option name="convert" path="ActionManager" hit="Convert to Java" />
    <option name="java" path="ActionManager" hit="Convert to Java" />
    <option name="to" path="ActionManager" hit="Convert to Java" />
    <option name="create" path="ActionManager" hit="Create new Gant script" />
    <option name="gant" path="ActionManager" hit="Create new Gant script" />
    <option name="new" path="ActionManager" hit="Create new Gant script" />
    <option name="script" path="ActionManager" hit="Create new Gant script" />
    <option name="a" path="ActionManager" hit="Creates a new Groovy Class" />
    <option name="class" path="ActionManager" hit="Creates a new Groovy Class" />
    <option name="creates" path="ActionManager" hit="Creates a new Groovy Class" />
    <option name="groovy" path="ActionManager" hit="Creates a new Groovy Class" />
    <option name="new" path="ActionManager" hit="Creates a new Groovy Class" />
    <option name="a" path="ActionManager" hit="Creates a new Groovy Script" />
    <option name="creates" path="ActionManager" hit="Creates a new Groovy Script" />
    <option name="groovy" path="ActionManager" hit="Creates a new Groovy Script" />
    <option name="new" path="ActionManager" hit="Creates a new Groovy Script" />
    <option name="script" path="ActionManager" hit="Creates a new Groovy Script" />
    <option name="compilation" path="ActionManager" hit="Don't generate Java stubs for this Groovy file on compilation" />
    <option name="don" path="ActionManager" hit="Don't generate Java stubs for this Groovy file on compilation" />
    <option name="file" path="ActionManager" hit="Don't generate Java stubs for this Groovy file on compilation" />
    <option name="for" path="ActionManager" hit="Don't generate Java stubs for this Groovy file on compilation" />
    <option name="generate" path="ActionManager" hit="Don't generate Java stubs for this Groovy file on compilation" />
    <option name="groovy" path="ActionManager" hit="Don't generate Java stubs for this Groovy file on compilation" />
    <option name="java" path="ActionManager" hit="Don't generate Java stubs for this Groovy file on compilation" />
    <option name="on" path="ActionManager" hit="Don't generate Java stubs for this Groovy file on compilation" />
    <option name="stubs" path="ActionManager" hit="Don't generate Java stubs for this Groovy file on compilation" />
    <option name="t" path="ActionManager" hit="Don't generate Java stubs for this Groovy file on compilation" />
    <option name="this" path="ActionManager" hit="Don't generate Java stubs for this Groovy file on compilation" />
    <option name="control" path="ActionManager" hit="Dump Groovy Control Flow" />
    <option name="dump" path="ActionManager" hit="Dump Groovy Control Flow" />
    <option name="flow" path="ActionManager" hit="Dump Groovy Control Flow" />
    <option name="groovy" path="ActionManager" hit="Dump Groovy Control Flow" />
    <option name="exclude" path="ActionManager" hit="Exclude from Stub Generation" />
    <option name="from" path="ActionManager" hit="Exclude from Stub Generation" />
    <option name="generation" path="ActionManager" hit="Exclude from Stub Generation" />
    <option name="stub" path="ActionManager" hit="Exclude from Stub Generation" />
    <option name="gant" path="ActionManager" hit="Gant Script" />
    <option name="script" path="ActionManager" hit="Gant Script" />
    <option name="generate" path="ActionManager" hit="Generate toString() method" />
    <option name="method" path="ActionManager" hit="Generate toString() method" />
    <option name="tostring" path="ActionManager" hit="Generate toString() method" />
    <option name="constructor" path="ActionManager" hit="Generates constructor" />
    <option name="generates" path="ActionManager" hit="Generates constructor" />
    <option name="generates" path="ActionManager" hit="Generates getter" />
    <option name="getter" path="ActionManager" hit="Generates getter" />
    <option name="generates" path="ActionManager" hit="Generates setter" />
    <option name="setter" path="ActionManager" hit="Generates setter" />
    <option name="getter" path="ActionManager" hit="Getter" />
    <option name="and" path="ActionManager" hit="Getter and Setter" />
    <option name="getter" path="ActionManager" hit="Getter and Setter" />
    <option name="setter" path="ActionManager" hit="Getter and Setter" />
    <option name="class" path="ActionManager" hit="Groovy Class" />
    <option name="groovy" path="ActionManager" hit="Groovy Class" />
    <option name="console" path="ActionManager" hit="Groovy Console" />
    <option name="groovy" path="ActionManager" hit="Groovy Console" />
    <option name="groovy" path="ActionManager" hit="Groovy Script" />
    <option name="script" path="ActionManager" hit="Groovy Script" />
    <option name="console" path="ActionManager" hit="Launch Groovy console" />
    <option name="groovy" path="ActionManager" hit="Launch Groovy console" />
    <option name="launch" path="ActionManager" hit="Launch Groovy console" />
    <option name="rebuild" path="ActionManager" hit="Rebuild Resources" />
    <option name="resources" path="ActionManager" hit="Rebuild Resources" />
    <option name="all" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="check" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="compilation" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="compiler" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="files" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="for" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="groovy" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="in" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="issues" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="resource" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="roots" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="run" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="the" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="to" path="ActionManager" hit="Run the compiler for all Groovy files in resource roots to check for compilation issues" />
    <option name="check" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="compilation" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="compiler" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="files" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="for" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="groovy" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="in" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="incremental" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="issues" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="resource" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="roots" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="run" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="the" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="to" path="ActionManager" hit="Run the incremental compiler for Groovy files in resource roots to check for compilation issues" />
    <option name="setter" path="ActionManager" hit="Setter" />
    <option name="and" path="ActionManager" hit="equals() and hashCode()" />
    <option name="equals" path="ActionManager" hit="equals() and hashCode()" />
    <option name="hashcode" path="ActionManager" hit="equals() and hashCode()" />
    <option name="methodmissing" path="ActionManager" hit="methodMissing()" />
    <option name="propertymissing" path="ActionManager" hit="propertyMissing()" />
    <option name="tostring" path="ActionManager" hit="toString()" />
  </configurable>
  <configurable id="editor.breadcrumbs" configurable_name="Breadcrumbs">
    <option name="groovy" hit="Groovy" />
  </configurable>
  <configurable id="reference.settingsdialog.IDE.editor.colors.Groovy" configurable_name="Groovy">
    <option name="groovy" hit="Groovy" />
    <option name="annotation" hit="Annotations//Annotation attribute name" />
    <option name="annotations" hit="Annotations//Annotation attribute name" />
    <option name="attribute" hit="Annotations//Annotation attribute name" />
    <option name="name" hit="Annotations//Annotation attribute name" />
    <option name="annotation" hit="Annotations//Annotation name" />
    <option name="annotations" hit="Annotations//Annotation name" />
    <option name="name" hit="Annotations//Annotation name" />
    <option name="bad" hit="Bad character" />
    <option name="character" hit="Bad character" />
    <option name="and" hit="Braces and Operators//Braces" />
    <option name="braces" hit="Braces and Operators//Braces" />
    <option name="operators" hit="Braces and Operators//Braces" />
    <option name="and" hit="Braces and Operators//Brackets" />
    <option name="braces" hit="Braces and Operators//Brackets" />
    <option name="brackets" hit="Braces and Operators//Brackets" />
    <option name="operators" hit="Braces and Operators//Brackets" />
    <option name="and" hit="Braces and Operators//Closure expression braces and arrow" />
    <option name="arrow" hit="Braces and Operators//Closure expression braces and arrow" />
    <option name="braces" hit="Braces and Operators//Closure expression braces and arrow" />
    <option name="closure" hit="Braces and Operators//Closure expression braces and arrow" />
    <option name="expression" hit="Braces and Operators//Closure expression braces and arrow" />
    <option name="operators" hit="Braces and Operators//Closure expression braces and arrow" />
    <option name="and" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="arrow" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="braces" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="expression" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="lambda" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="operators" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="and" hit="Braces and Operators//Operator sign" />
    <option name="braces" hit="Braces and Operators//Operator sign" />
    <option name="operator" hit="Braces and Operators//Operator sign" />
    <option name="operators" hit="Braces and Operators//Operator sign" />
    <option name="sign" hit="Braces and Operators//Operator sign" />
    <option name="and" hit="Braces and Operators//Parentheses" />
    <option name="braces" hit="Braces and Operators//Parentheses" />
    <option name="operators" hit="Braces and Operators//Parentheses" />
    <option name="parentheses" hit="Braces and Operators//Parentheses" />
    <option name="abstract" hit="Classes and Interfaces//Abstract class" />
    <option name="and" hit="Classes and Interfaces//Abstract class" />
    <option name="class" hit="Classes and Interfaces//Abstract class" />
    <option name="classes" hit="Classes and Interfaces//Abstract class" />
    <option name="interfaces" hit="Classes and Interfaces//Abstract class" />
    <option name="and" hit="Classes and Interfaces//Anonymous class" />
    <option name="anonymous" hit="Classes and Interfaces//Anonymous class" />
    <option name="class" hit="Classes and Interfaces//Anonymous class" />
    <option name="classes" hit="Classes and Interfaces//Anonymous class" />
    <option name="interfaces" hit="Classes and Interfaces//Anonymous class" />
    <option name="and" hit="Classes and Interfaces//Class" />
    <option name="class" hit="Classes and Interfaces//Class" />
    <option name="classes" hit="Classes and Interfaces//Class" />
    <option name="interfaces" hit="Classes and Interfaces//Class" />
    <option name="and" hit="Classes and Interfaces//Enum" />
    <option name="classes" hit="Classes and Interfaces//Enum" />
    <option name="enum" hit="Classes and Interfaces//Enum" />
    <option name="interfaces" hit="Classes and Interfaces//Enum" />
    <option name="and" hit="Classes and Interfaces//Interface" />
    <option name="classes" hit="Classes and Interfaces//Interface" />
    <option name="interface" hit="Classes and Interfaces//Interface" />
    <option name="interfaces" hit="Classes and Interfaces//Interface" />
    <option name="and" hit="Classes and Interfaces//Trait" />
    <option name="classes" hit="Classes and Interfaces//Trait" />
    <option name="interfaces" hit="Classes and Interfaces//Trait" />
    <option name="trait" hit="Classes and Interfaces//Trait" />
    <option name="and" hit="Classes and Interfaces//Type parameter" />
    <option name="classes" hit="Classes and Interfaces//Type parameter" />
    <option name="interfaces" hit="Classes and Interfaces//Type parameter" />
    <option name="parameter" hit="Classes and Interfaces//Type parameter" />
    <option name="type" hit="Classes and Interfaces//Type parameter" />
    <option name="block" hit="Comments//Block comment" />
    <option name="comment" hit="Comments//Block comment" />
    <option name="comments" hit="Comments//Block comment" />
    <option name="comments" hit="Comments//Groovydoc//Tag" />
    <option name="groovydoc" hit="Comments//Groovydoc//Tag" />
    <option name="tag" hit="Comments//Groovydoc//Tag" />
    <option name="comments" hit="Comments//Groovydoc//Text" />
    <option name="groovydoc" hit="Comments//Groovydoc//Text" />
    <option name="text" hit="Comments//Groovydoc//Text" />
    <option name="comment" hit="Comments//Line comment" />
    <option name="comments" hit="Comments//Line comment" />
    <option name="line" hit="Comments//Line comment" />
    <option name="field" hit="Fields//Instance field" />
    <option name="fields" hit="Fields//Instance field" />
    <option name="instance" hit="Fields//Instance field" />
    <option name="field" hit="Fields//Static field" />
    <option name="fields" hit="Fields//Static field" />
    <option name="static" hit="Fields//Static field" />
    <option name="keyword" hit="Keyword" />
    <option name="label" hit="Label" />
    <option name="conversion" hit="List/Map to object conversion" />
    <option name="list" hit="List/Map to object conversion" />
    <option name="map" hit="List/Map to object conversion" />
    <option name="object" hit="List/Map to object conversion" />
    <option name="to" hit="List/Map to object conversion" />
    <option name="argument" hit="Map key/Named argument" />
    <option name="key" hit="Map key/Named argument" />
    <option name="map" hit="Map key/Named argument" />
    <option name="named" hit="Map key/Named argument" />
    <option name="call" hit="Methods//Constructor call" />
    <option name="constructor" hit="Methods//Constructor call" />
    <option name="methods" hit="Methods//Constructor call" />
    <option name="constructor" hit="Methods//Constructor declaration" />
    <option name="declaration" hit="Methods//Constructor declaration" />
    <option name="methods" hit="Methods//Constructor declaration" />
    <option name="call" hit="Methods//Instance method call" />
    <option name="instance" hit="Methods//Instance method call" />
    <option name="method" hit="Methods//Instance method call" />
    <option name="methods" hit="Methods//Instance method call" />
    <option name="declaration" hit="Methods//Method declaration" />
    <option name="method" hit="Methods//Method declaration" />
    <option name="methods" hit="Methods//Method declaration" />
    <option name="call" hit="Methods//Static method call" />
    <option name="method" hit="Methods//Static method call" />
    <option name="methods" hit="Methods//Static method call" />
    <option name="static" hit="Methods//Static method call" />
    <option name="number" hit="Number" />
    <option name="instance" hit="References//Instance property reference" />
    <option name="property" hit="References//Instance property reference" />
    <option name="reference" hit="References//Instance property reference" />
    <option name="references" hit="References//Instance property reference" />
    <option name="property" hit="References//Static property reference" />
    <option name="reference" hit="References//Static property reference" />
    <option name="references" hit="References//Static property reference" />
    <option name="static" hit="References//Static property reference" />
    <option name="reference" hit="References//Unresolved reference" />
    <option name="references" hit="References//Unresolved reference" />
    <option name="unresolved" hit="References//Unresolved reference" />
    <option name="gstring" hit="Strings//GString" />
    <option name="strings" hit="Strings//GString" />
    <option name="escape" hit="Strings//Invalid string escape" />
    <option name="invalid" hit="Strings//Invalid string escape" />
    <option name="string" hit="Strings//Invalid string escape" />
    <option name="strings" hit="Strings//Invalid string escape" />
    <option name="string" hit="Strings//String" />
    <option name="strings" hit="Strings//String" />
    <option name="escape" hit="Strings//Valid string escape" />
    <option name="string" hit="Strings//Valid string escape" />
    <option name="strings" hit="Strings//Valid string escape" />
    <option name="valid" hit="Strings//Valid string escape" />
    <option name="and" hit="Variables and Parameters//Local variable" />
    <option name="local" hit="Variables and Parameters//Local variable" />
    <option name="parameters" hit="Variables and Parameters//Local variable" />
    <option name="variable" hit="Variables and Parameters//Local variable" />
    <option name="variables" hit="Variables and Parameters//Local variable" />
    <option name="and" hit="Variables and Parameters//Parameter" />
    <option name="parameter" hit="Variables and Parameters//Parameter" />
    <option name="parameters" hit="Variables and Parameters//Parameter" />
    <option name="variables" hit="Variables and Parameters//Parameter" />
    <option name="and" hit="Variables and Parameters//Reassigned local variable" />
    <option name="local" hit="Variables and Parameters//Reassigned local variable" />
    <option name="parameters" hit="Variables and Parameters//Reassigned local variable" />
    <option name="reassigned" hit="Variables and Parameters//Reassigned local variable" />
    <option name="variable" hit="Variables and Parameters//Reassigned local variable" />
    <option name="variables" hit="Variables and Parameters//Reassigned local variable" />
    <option name="and" hit="Variables and Parameters//Reassigned parameter" />
    <option name="parameter" hit="Variables and Parameters//Reassigned parameter" />
    <option name="parameters" hit="Variables and Parameters//Reassigned parameter" />
    <option name="reassigned" hit="Variables and Parameters//Reassigned parameter" />
    <option name="variables" hit="Variables and Parameters//Reassigned parameter" />
  </configurable>
  <configurable id="debugger.stepping" configurable_name="Stepping">
    <option name="classes" hit="Do not step into specific Groovy classes" />
    <option name="do" hit="Do not step into specific Groovy classes" />
    <option name="groovy" hit="Do not step into specific Groovy classes" />
    <option name="into" hit="Do not step into specific Groovy classes" />
    <option name="not" hit="Do not step into specific Groovy classes" />
    <option name="specific" hit="Do not step into specific Groovy classes" />
    <option name="step" hit="Do not step into specific Groovy classes" />
    <option name="groovy" hit="Groovy" />
  </configurable>
  <configurable id="debugger.hotswap" configurable_name="HotSwap">
    <option name="agent" hit="Enable hot-swap agent for Groovy code" />
    <option name="code" hit="Enable hot-swap agent for Groovy code" />
    <option name="enable" hit="Enable hot-swap agent for Groovy code" />
    <option name="for" hit="Enable hot-swap agent for Groovy code" />
    <option name="groovy" hit="Enable hot-swap agent for Groovy code" />
    <option name="hot-swap" hit="Enable hot-swap agent for Groovy code" />
    <option name="groovy" hit="Groovy" />
  </configurable>
  <configurable id="preferences.sourceCode.Groovy" configurable_name="Groovy">
    <option name="absolute" path="Tabs and Indents" hit="Absolute" />
    <option name="a" path="Code Generation" hit="Add a space at line comment start" />
    <option name="add" path="Code Generation" hit="Add a space at line comment start" />
    <option name="at" path="Code Generation" hit="Add a space at line comment start" />
    <option name="comment" path="Code Generation" hit="Add a space at line comment start" />
    <option name="line" path="Code Generation" hit="Add a space at line comment start" />
    <option name="space" path="Code Generation" hit="Add a space at line comment start" />
    <option name="start" path="Code Generation" hit="Add a space at line comment start" />
    <option name="add" path="Code Generation" hit="Add spaces around block comments" />
    <option name="around" path="Code Generation" hit="Add spaces around block comments" />
    <option name="block" path="Code Generation" hit="Add spaces around block comments" />
    <option name="comments" path="Code Generation" hit="Add spaces around block comments" />
    <option name="spaces" path="Code Generation" hit="Add spaces around block comments" />
    <option name="after" path="Blank Lines" hit="After class header:" />
    <option name="class" path="Blank Lines" hit="After class header:" />
    <option name="header" path="Blank Lines" hit="After class header:" />
    <option name="after" path="Blank Lines" hit="After imports:" />
    <option name="imports" path="Blank Lines" hit="After imports:" />
    <option name="after" path="Blank Lines" hit="After package statement:" />
    <option name="package" path="Blank Lines" hit="After package statement:" />
    <option name="statement" path="Blank Lines" hit="After package statement:" />
    <option name="around" path="Blank Lines" hit="Around class:" />
    <option name="class" path="Blank Lines" hit="Around class:" />
    <option name="around" path="Blank Lines" hit="Around field in interface:" />
    <option name="field" path="Blank Lines" hit="Around field in interface:" />
    <option name="in" path="Blank Lines" hit="Around field in interface:" />
    <option name="interface" path="Blank Lines" hit="Around field in interface:" />
    <option name="around" path="Blank Lines" hit="Around field:" />
    <option name="field" path="Blank Lines" hit="Around field:" />
    <option name="around" path="Blank Lines" hit="Around method in interface:" />
    <option name="in" path="Blank Lines" hit="Around method in interface:" />
    <option name="interface" path="Blank Lines" hit="Around method in interface:" />
    <option name="method" path="Blank Lines" hit="Around method in interface:" />
    <option name="around" path="Blank Lines" hit="Around method:" />
    <option name="method" path="Blank Lines" hit="Around method:" />
    <option name="before" path="Blank Lines" hit="Before '}':" />
    <option name="before" path="Blank Lines" hit="Before imports:" />
    <option name="imports" path="Blank Lines" hit="Before imports:" />
    <option name="before" path="Blank Lines" hit="Before method body:" />
    <option name="body" path="Blank Lines" hit="Before method body:" />
    <option name="method" path="Blank Lines" hit="Before method body:" />
    <option name="before" path="Blank Lines" hit="Before package statement:" />
    <option name="package" path="Blank Lines" hit="Before package statement:" />
    <option name="statement" path="Blank Lines" hit="Before package statement:" />
    <option name="blank" path="Blank Lines" hit="Blank Lines" />
    <option name="lines" path="Blank Lines" hit="Blank Lines" />
    <option name="at" path="Code Generation" hit="Block comment at first column" />
    <option name="block" path="Code Generation" hit="Block comment at first column" />
    <option name="column" path="Code Generation" hit="Block comment at first column" />
    <option name="comment" path="Code Generation" hit="Block comment at first column" />
    <option name="first" path="Code Generation" hit="Block comment at first column" />
    <option name="class" path="Imports" hit="Class count to use import with '*':" />
    <option name="count" path="Imports" hit="Class count to use import with '*':" />
    <option name="import" path="Imports" hit="Class count to use import with '*':" />
    <option name="to" path="Imports" hit="Class count to use import with '*':" />
    <option name="use" path="Imports" hit="Class count to use import with '*':" />
    <option name="with" path="Imports" hit="Class count to use import with '*':" />
    <option name="code" path="Code Generation" hit="Code Generation" />
    <option name="generation" path="Code Generation" hit="Code Generation" />
    <option name="code" path="Code Generation" hit="Comment Code" />
    <option name="comment" path="Code Generation" hit="Comment Code" />
    <option name="continuation" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="indent" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="disable" hit="Disable" />
    <option name="enable" path="GroovyDoc" hit="Enable GroovyDoc formatting" />
    <option name="formatting" path="GroovyDoc" hit="Enable GroovyDoc formatting" />
    <option name="groovydoc" path="GroovyDoc" hit="Enable GroovyDoc formatting" />
    <option name="enforce" path="Code Generation" hit="Enforce on reformat" />
    <option name="on" path="Code Generation" hit="Enforce on reformat" />
    <option name="reformat" path="Code Generation" hit="Enforce on reformat" />
    <option name="general" path="Imports" hit="General" />
    <option name="groovy" hit="Groovy" />
    <option name="groovydoc" path="GroovyDoc" hit="GroovyDoc" />
    <option name="import" path="Imports" hit="Import Layout" />
    <option name="layout" path="Imports" hit="Import Layout" />
    <option name="imports" path="Imports" hit="Imports" />
    <option name="code" path="Blank Lines" hit="In code:" />
    <option name="in" path="Blank Lines" hit="In code:" />
    <option name="declarations" path="Blank Lines" hit="In declarations:" />
    <option name="in" path="Blank Lines" hit="In declarations:" />
    <option name="indent" path="Tabs and Indents" hit="Indent labels" />
    <option name="labels" path="Tabs and Indents" hit="Indent labels" />
    <option name="after" path="Tabs and Indents" hit="Indent statements after label" />
    <option name="indent" path="Tabs and Indents" hit="Indent statements after label" />
    <option name="label" path="Tabs and Indents" hit="Indent statements after label" />
    <option name="statements" path="Tabs and Indents" hit="Indent statements after label" />
    <option name="indent" path="Tabs and Indents" hit="Indent:" />
    <option name="classes" path="Imports" hit="Insert imports for inner classes" />
    <option name="for" path="Imports" hit="Insert imports for inner classes" />
    <option name="imports" path="Imports" hit="Insert imports for inner classes" />
    <option name="inner" path="Imports" hit="Insert imports for inner classes" />
    <option name="insert" path="Imports" hit="Insert imports for inner classes" />
    <option name="empty" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="indents" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="keep" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="lines" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="on" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="blank" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="keep" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="lines" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="maximum" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="indent" path="Tabs and Indents" hit="Label indent style:" />
    <option name="label" path="Tabs and Indents" hit="Label indent style:" />
    <option name="style" path="Tabs and Indents" hit="Label indent style:" />
    <option name="indent" path="Tabs and Indents" hit="Label indent:" />
    <option name="label" path="Tabs and Indents" hit="Label indent:" />
    <option name="imports" path="Imports" hit="Layout static imports separately" />
    <option name="layout" path="Imports" hit="Layout static imports separately" />
    <option name="separately" path="Imports" hit="Layout static imports separately" />
    <option name="static" path="Imports" hit="Layout static imports separately" />
    <option name="at" path="Code Generation" hit="Line comment at first column" />
    <option name="column" path="Code Generation" hit="Line comment at first column" />
    <option name="comment" path="Code Generation" hit="Line comment at first column" />
    <option name="first" path="Code Generation" hit="Line comment at first column" />
    <option name="line" path="Code Generation" hit="Line comment at first column" />
    <option name="loading" hit="Loading..." />
    <option name="blank" path="Blank Lines" hit="Minimum blank lines" />
    <option name="lines" path="Blank Lines" hit="Minimum blank lines" />
    <option name="minimum" path="Blank Lines" hit="Minimum blank lines" />
    <option name="count" path="Imports" hit="Names count to use static import with '*':" />
    <option name="import" path="Imports" hit="Names count to use static import with '*':" />
    <option name="names" path="Imports" hit="Names count to use static import with '*':" />
    <option name="static" path="Imports" hit="Names count to use static import with '*':" />
    <option name="to" path="Imports" hit="Names count to use static import with '*':" />
    <option name="use" path="Imports" hit="Names count to use static import with '*':" />
    <option name="with" path="Imports" hit="Names count to use static import with '*':" />
    <option name="members" path="Code Generation" hit="Order of Members" />
    <option name="of" path="Code Generation" hit="Order of Members" />
    <option name="order" path="Code Generation" hit="Order of Members" />
    <option name="scheme" hit="Scheme:" />
    <option name="from" hit="Set from..." />
    <option name="set" hit="Set from..." />
    <option name="smart" path="Tabs and Indents" hit="Smart tabs" />
    <option name="tabs" path="Tabs and Indents" hit="Smart tabs" />
    <option name="spaces" path="Spaces" hit="Spaces" />
    <option name="size" path="Tabs and Indents" hit="Tab size:" />
    <option name="tab" path="Tabs and Indents" hit="Tab size:" />
    <option name="and" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="indents" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="tabs" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="class" path="Imports" hit="Use fully qualified class names" />
    <option name="fully" path="Imports" hit="Use fully qualified class names" />
    <option name="names" path="Imports" hit="Use fully qualified class names" />
    <option name="qualified" path="Imports" hit="Use fully qualified class names" />
    <option name="use" path="Imports" hit="Use fully qualified class names" />
    <option name="class" path="Imports" hit="Use fully qualified class names in javadoc" />
    <option name="fully" path="Imports" hit="Use fully qualified class names in javadoc" />
    <option name="in" path="Imports" hit="Use fully qualified class names in javadoc" />
    <option name="javadoc" path="Imports" hit="Use fully qualified class names in javadoc" />
    <option name="names" path="Imports" hit="Use fully qualified class names in javadoc" />
    <option name="qualified" path="Imports" hit="Use fully qualified class names in javadoc" />
    <option name="use" path="Imports" hit="Use fully qualified class names in javadoc" />
    <option name="class" path="Imports" hit="Use single class import" />
    <option name="import" path="Imports" hit="Use single class import" />
    <option name="single" path="Imports" hit="Use single class import" />
    <option name="use" path="Imports" hit="Use single class import" />
    <option name="character" path="Tabs and Indents" hit="Use tab character" />
    <option name="tab" path="Tabs and Indents" hit="Use tab character" />
    <option name="use" path="Tabs and Indents" hit="Use tab character" />
    <option name="and" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="braces" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="wrapping" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="line" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="next" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="on" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="signs" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="and" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="line" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="next" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="on" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="signs" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="" path="Spaces" hit="'catch' keyword" />
    <option name="catch" path="Spaces" hit="'catch' keyword" />
    <option name="keyword" path="Spaces" hit="'catch' keyword" />
    <option name="" path="Spaces" hit="'catch' left brace" />
    <option name="brace" path="Spaces" hit="'catch' left brace" />
    <option name="catch" path="Spaces" hit="'catch' left brace" />
    <option name="left" path="Spaces" hit="'catch' left brace" />
    <option name="" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="catch" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="" path="Spaces" hit="'catch' parentheses" />
    <option name="catch" path="Spaces" hit="'catch' parentheses" />
    <option name="parentheses" path="Spaces" hit="'catch' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="do" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="while" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="" path="Spaces" hit="'do' left brace" />
    <option name="brace" path="Spaces" hit="'do' left brace" />
    <option name="do" path="Spaces" hit="'do' left brace" />
    <option name="left" path="Spaces" hit="'do' left brace" />
    <option name="" path="Spaces" hit="'else' keyword" />
    <option name="else" path="Spaces" hit="'else' keyword" />
    <option name="keyword" path="Spaces" hit="'else' keyword" />
    <option name="" path="Spaces" hit="'else' left brace" />
    <option name="brace" path="Spaces" hit="'else' left brace" />
    <option name="else" path="Spaces" hit="'else' left brace" />
    <option name="left" path="Spaces" hit="'else' left brace" />
    <option name="" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="else" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="" path="Spaces" hit="'finally' keyword" />
    <option name="finally" path="Spaces" hit="'finally' keyword" />
    <option name="keyword" path="Spaces" hit="'finally' keyword" />
    <option name="" path="Spaces" hit="'finally' left brace" />
    <option name="brace" path="Spaces" hit="'finally' left brace" />
    <option name="finally" path="Spaces" hit="'finally' left brace" />
    <option name="left" path="Spaces" hit="'finally' left brace" />
    <option name="" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="finally" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="" path="Spaces" hit="'for' left brace" />
    <option name="brace" path="Spaces" hit="'for' left brace" />
    <option name="for" path="Spaces" hit="'for' left brace" />
    <option name="left" path="Spaces" hit="'for' left brace" />
    <option name="" path="Spaces" hit="'for' parentheses" />
    <option name="for" path="Spaces" hit="'for' parentheses" />
    <option name="parentheses" path="Spaces" hit="'for' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="for" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="" path="Spaces" hit="'if' left brace" />
    <option name="brace" path="Spaces" hit="'if' left brace" />
    <option name="if" path="Spaces" hit="'if' left brace" />
    <option name="left" path="Spaces" hit="'if' left brace" />
    <option name="" path="Spaces" hit="'if' parentheses" />
    <option name="if" path="Spaces" hit="'if' parentheses" />
    <option name="parentheses" path="Spaces" hit="'if' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="if" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="" path="Spaces" hit="'switch' left brace" />
    <option name="brace" path="Spaces" hit="'switch' left brace" />
    <option name="left" path="Spaces" hit="'switch' left brace" />
    <option name="switch" path="Spaces" hit="'switch' left brace" />
    <option name="" path="Spaces" hit="'switch' parentheses" />
    <option name="parentheses" path="Spaces" hit="'switch' parentheses" />
    <option name="switch" path="Spaces" hit="'switch' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="switch" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="" path="Spaces" hit="'synchronized' left brace" />
    <option name="brace" path="Spaces" hit="'synchronized' left brace" />
    <option name="left" path="Spaces" hit="'synchronized' left brace" />
    <option name="synchronized" path="Spaces" hit="'synchronized' left brace" />
    <option name="" path="Spaces" hit="'synchronized' parentheses" />
    <option name="parentheses" path="Spaces" hit="'synchronized' parentheses" />
    <option name="synchronized" path="Spaces" hit="'synchronized' parentheses" />
    <option name="" path="Spaces" hit="'try' left brace" />
    <option name="brace" path="Spaces" hit="'try' left brace" />
    <option name="left" path="Spaces" hit="'try' left brace" />
    <option name="try" path="Spaces" hit="'try' left brace" />
    <option name="" path="Spaces" hit="'try' parentheses" />
    <option name="parentheses" path="Spaces" hit="'try' parentheses" />
    <option name="try" path="Spaces" hit="'try' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'try' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'try' statement" />
    <option name="try" path="Wrapping and Braces" hit="'try' statement" />
    <option name="" path="Wrapping and Braces" hit="'try-with-resources'" />
    <option name="try-with-resources" path="Wrapping and Braces" hit="'try-with-resources'" />
    <option name="" path="Spaces" hit="'while' keyword" />
    <option name="keyword" path="Spaces" hit="'while' keyword" />
    <option name="while" path="Spaces" hit="'while' keyword" />
    <option name="" path="Spaces" hit="'while' left brace" />
    <option name="brace" path="Spaces" hit="'while' left brace" />
    <option name="left" path="Spaces" hit="'while' left brace" />
    <option name="while" path="Spaces" hit="'while' left brace" />
    <option name="" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="while" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="" path="Spaces" hit="'while' parentheses" />
    <option name="parentheses" path="Spaces" hit="'while' parentheses" />
    <option name="while" path="Spaces" hit="'while' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="while" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="+" path="Spaces" hit="Additive operators (+, -)" />
    <option name="-" path="Spaces" hit="Additive operators (+, -)" />
    <option name="additive" path="Spaces" hit="Additive operators (+, -)" />
    <option name="operators" path="Spaces" hit="Additive operators (+, -)" />
    <option name="after" path="Spaces" hit="After ':'" />
    <option name="after" path="Spaces" hit="After '?'" />
    <option name="after" path="Spaces" hit="After 'assert' separator" />
    <option name="assert" path="Spaces" hit="After 'assert' separator" />
    <option name="separator" path="Spaces" hit="After 'assert' separator" />
    <option name="after" path="Spaces" hit="After 'for' semicolon" />
    <option name="for" path="Spaces" hit="After 'for' semicolon" />
    <option name="semicolon" path="Spaces" hit="After 'for' semicolon" />
    <option name="after" path="Spaces" hit="After comma" />
    <option name="comma" path="Spaces" hit="After comma" />
    <option name="after" path="Spaces" hit="After type cast" />
    <option name="cast" path="Spaces" hit="After type cast" />
    <option name="type" path="Spaces" hit="After type cast" />
    <option name="align" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="method" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="start" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="throws" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="to" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="align" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="assignments" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="fields" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align multiline named arguments" />
    <option name="arguments" path="Wrapping and Braces" hit="Align multiline named arguments" />
    <option name="multiline" path="Wrapping and Braces" hit="Align multiline named arguments" />
    <option name="named" path="Wrapping and Braces" hit="Align multiline named arguments" />
    <option name="align" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="multiline" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="parenthesised" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="when" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="align" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="methods" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="simple" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="variables" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="multiline" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="when" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="align" path="Wrapping and Braces" hit="Align when multiple" />
    <option name="multiple" path="Wrapping and Braces" hit="Align when multiple" />
    <option name="when" path="Wrapping and Braces" hit="Align when multiple" />
    <option name="annotation" path="Spaces" hit="Annotation parameters" />
    <option name="parameters" path="Spaces" hit="Annotation parameters" />
    <option name="annotation" path="Spaces" hit="Annotation parentheses" />
    <option name="parentheses" path="Spaces" hit="Annotation parentheses" />
    <option name="around" path="Spaces" hit="Around operators" />
    <option name="operators" path="Spaces" hit="Around operators" />
    <option name="array" path="Wrapping and Braces" hit="Array initializer" />
    <option name="initializer" path="Wrapping and Braces" hit="Array initializer" />
    <option name="array" path="Spaces" hit="Array initializer braces" />
    <option name="braces" path="Spaces" hit="Array initializer braces" />
    <option name="initializer" path="Spaces" hit="Array initializer braces" />
    <option name="array" path="Spaces" hit="Array initializer left brace" />
    <option name="brace" path="Spaces" hit="Array initializer left brace" />
    <option name="initializer" path="Spaces" hit="Array initializer left brace" />
    <option name="left" path="Spaces" hit="Array initializer left brace" />
    <option name="assert" path="Wrapping and Braces" hit="Assert statement" />
    <option name="statement" path="Wrapping and Braces" hit="Assert statement" />
    <option name="+" path="Spaces" hit="Assignment operators (=, +=, ...)" />
    <option name="assignment" path="Spaces" hit="Assignment operators (=, +=, ...)" />
    <option name="operators" path="Spaces" hit="Assignment operators (=, +=, ...)" />
    <option name="assignment" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="line" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="next" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="on" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="sign" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="assignment" path="Wrapping and Braces" hit="Assignment statement" />
    <option name="statement" path="Wrapping and Braces" hit="Assignment statement" />
    <option name="before" path="Spaces" hit="Before ':'" />
    <option name="before" path="Spaces" hit="Before '?'" />
    <option name="assert" path="Spaces" hit="Before 'assert' separator" />
    <option name="before" path="Spaces" hit="Before 'assert' separator" />
    <option name="separator" path="Spaces" hit="Before 'assert' separator" />
    <option name="before" path="Spaces" hit="Before 'for' semicolon" />
    <option name="for" path="Spaces" hit="Before 'for' semicolon" />
    <option name="semicolon" path="Spaces" hit="Before 'for' semicolon" />
    <option name="before" path="Spaces" hit="Before comma" />
    <option name="comma" path="Spaces" hit="Before comma" />
    <option name="before" path="Spaces" hit="Before keywords" />
    <option name="keywords" path="Spaces" hit="Before keywords" />
    <option name="before" path="Spaces" hit="Before left brace" />
    <option name="brace" path="Spaces" hit="Before left brace" />
    <option name="left" path="Spaces" hit="Before left brace" />
    <option name="before" path="Spaces" hit="Before parentheses" />
    <option name="parentheses" path="Spaces" hit="Before parentheses" />
    <option name="before" path="Spaces" hit="Before record parameter list" />
    <option name="list" path="Spaces" hit="Before record parameter list" />
    <option name="parameter" path="Spaces" hit="Before record parameter list" />
    <option name="record" path="Spaces" hit="Before record parameter list" />
    <option name="binary" path="Wrapping and Braces" hit="Binary expressions" />
    <option name="expressions" path="Wrapping and Braces" hit="Binary expressions" />
    <option name="bitwise" path="Spaces" hit="Bitwise operators (&amp;, |, ^)" />
    <option name="operators" path="Spaces" hit="Bitwise operators (&amp;, |, ^)" />
    <option name="braces" path="Wrapping and Braces" hit="Braces placement" />
    <option name="placement" path="Wrapping and Braces" hit="Braces placement" />
    <option name="brackets" path="Spaces" hit="Brackets" />
    <option name="builder" path="Wrapping and Braces" hit="Builder methods" />
    <option name="methods" path="Wrapping and Braces" hit="Builder methods" />
    <option name="calls" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="chained" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="method" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="annotations" path="Wrapping and Braces" hit="Class annotations" />
    <option name="class" path="Wrapping and Braces" hit="Class annotations" />
    <option name="brace" path="Spaces" hit="Class left brace" />
    <option name="class" path="Spaces" hit="Class left brace" />
    <option name="left" path="Spaces" hit="Class left brace" />
    <option name="brace" path="Spaces" hit="Closure left brace in method calls" />
    <option name="calls" path="Spaces" hit="Closure left brace in method calls" />
    <option name="closure" path="Spaces" hit="Closure left brace in method calls" />
    <option name="in" path="Spaces" hit="Closure left brace in method calls" />
    <option name="left" path="Spaces" hit="Closure left brace in method calls" />
    <option name="method" path="Spaces" hit="Closure left brace in method calls" />
    <option name="braces" path="Spaces" hit="Code braces" />
    <option name="code" path="Spaces" hit="Code braces" />
    <option name="at" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="column" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="comment" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="first" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="comments" path="Wrapping and Braces" hit="Comments" />
    <option name="control" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="in" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="line" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="one" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="statement" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="a" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="case" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="each" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="line" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="on" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="separate" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="array" path="Spaces" hit="Empty array initializer braces" />
    <option name="braces" path="Spaces" hit="Empty array initializer braces" />
    <option name="empty" path="Spaces" hit="Empty array initializer braces" />
    <option name="initializer" path="Spaces" hit="Empty array initializer braces" />
    <option name="call" path="Spaces" hit="Empty method call parentheses" />
    <option name="empty" path="Spaces" hit="Empty method call parentheses" />
    <option name="method" path="Spaces" hit="Empty method call parentheses" />
    <option name="parentheses" path="Spaces" hit="Empty method call parentheses" />
    <option name="ensure" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="exceeded" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="is" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="margin" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="not" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="right" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="constants" path="Wrapping and Braces" hit="Enum constants" />
    <option name="enum" path="Wrapping and Braces" hit="Enum constants" />
    <option name="equality" path="Spaces" hit="Equality operators (==, !=)" />
    <option name="operators" path="Spaces" hit="Equality operators (==, !=)" />
    <option name="extends" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="implements" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="keyword" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="permits" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="extends" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="implements" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="list" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="permits" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="annotations" path="Wrapping and Braces" hit="Field annotations" />
    <option name="field" path="Wrapping and Braces" hit="Field annotations" />
    <option name="braces" path="Wrapping and Braces" hit="Force braces" />
    <option name="force" path="Wrapping and Braces" hit="Force braces" />
    <option name="clauses" path="Wrapping and Braces" hit="GINQ clauses" />
    <option name="ginq" path="Wrapping and Braces" hit="GINQ clauses" />
    <option name="braces" path="Spaces" hit="GString injection braces" />
    <option name="gstring" path="Spaces" hit="GString injection braces" />
    <option name="injection" path="Spaces" hit="GString injection braces" />
    <option name="declarations" path="Wrapping and Braces" hit="Group declarations" />
    <option name="group" path="Wrapping and Braces" hit="Group declarations" />
    <option name="grouping" path="Spaces" hit="Grouping parentheses" />
    <option name="parentheses" path="Spaces" hit="Grouping parentheses" />
    <option name="at" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="hard" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="wrap" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="annotations" path="Wrapping and Braces" hit="Import annotations" />
    <option name="import" path="Wrapping and Braces" hit="Import annotations" />
    <option name="class" path="Wrapping and Braces" hit="In class declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In class declaration" />
    <option name="in" path="Wrapping and Braces" hit="In class declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="in" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="lambda" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In method declaration" />
    <option name="in" path="Wrapping and Braces" hit="In method declaration" />
    <option name="method" path="Wrapping and Braces" hit="In method declaration" />
    <option name="after" path="Spaces" hit="In named argument after ':'" />
    <option name="argument" path="Spaces" hit="In named argument after ':'" />
    <option name="in" path="Spaces" hit="In named argument after ':'" />
    <option name="named" path="Spaces" hit="In named argument after ':'" />
    <option name="argument" path="Spaces" hit="In named argument before ':'" />
    <option name="before" path="Spaces" hit="In named argument before ':'" />
    <option name="in" path="Spaces" hit="In named argument before ':'" />
    <option name="named" path="Spaces" hit="In named argument before ':'" />
    <option name="in" path="Spaces" hit="In ternary operator (?:)" />
    <option name="operator" path="Spaces" hit="In ternary operator (?:)" />
    <option name="ternary" path="Spaces" hit="In ternary operator (?:)" />
    <option name="break" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="case" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="from" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="branches" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="case" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="clause" path="Wrapping and Braces" hit="Indent 'having' clause" />
    <option name="having" path="Wrapping and Braces" hit="Indent 'having' clause" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'having' clause" />
    <option name="clause" path="Wrapping and Braces" hit="Indent 'on' clause" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'on' clause" />
    <option name="on" path="Wrapping and Braces" hit="Indent 'on' clause" />
    <option name="builder" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="indents" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="keep" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="methods" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="keep" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="reformatting" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="when" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="arrow" path="Spaces" hit="Lambda arrow" />
    <option name="lambda" path="Spaces" hit="Lambda arrow" />
    <option name="breaks" path="Wrapping and Braces" hit="Line breaks" />
    <option name="line" path="Wrapping and Braces" hit="Line breaks" />
    <option name="and" path="Wrapping and Braces" hit="List and map literals" />
    <option name="list" path="Wrapping and Braces" hit="List and map literals" />
    <option name="literals" path="Wrapping and Braces" hit="List and map literals" />
    <option name="map" path="Wrapping and Braces" hit="List and map literals" />
    <option name="and" path="Spaces" hit="List and maps literals" />
    <option name="list" path="Spaces" hit="List and maps literals" />
    <option name="literals" path="Spaces" hit="List and maps literals" />
    <option name="maps" path="Spaces" hit="List and maps literals" />
    <option name="annotations" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="local" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="variable" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="logical" path="Spaces" hit="Logical operators (&amp;&amp;, ||)" />
    <option name="operators" path="Spaces" hit="Logical operators (&amp;&amp;, ||)" />
    <option name="annotations" path="Wrapping and Braces" hit="Method annotations" />
    <option name="method" path="Wrapping and Braces" hit="Method annotations" />
    <option name="arguments" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="call" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="method" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="call" path="Spaces" hit="Method call parentheses" />
    <option name="method" path="Spaces" hit="Method call parentheses" />
    <option name="parentheses" path="Spaces" hit="Method call parentheses" />
    <option name="declaration" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="method" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="parameters" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="declaration" path="Spaces" hit="Method declaration parentheses" />
    <option name="method" path="Spaces" hit="Method declaration parentheses" />
    <option name="parentheses" path="Spaces" hit="Method declaration parentheses" />
    <option name="brace" path="Spaces" hit="Method left brace" />
    <option name="left" path="Spaces" hit="Method left brace" />
    <option name="method" path="Spaces" hit="Method left brace" />
    <option name="method" path="Wrapping and Braces" hit="Method parentheses" />
    <option name="parentheses" path="Wrapping and Braces" hit="Method parentheses" />
    <option name="list" path="Wrapping and Braces" hit="Modifier list" />
    <option name="modifier" path="Wrapping and Braces" hit="Modifier list" />
    <option name="expressions" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="in" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="line" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="multiple" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="one" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="multiplicative" path="Spaces" hit="Multiplicative operators (*, /, %)" />
    <option name="operators" path="Spaces" hit="Multiplicative operators (*, /, %)" />
    <option name="after" path="Wrapping and Braces" hit="New line after '('" />
    <option name="line" path="Wrapping and Braces" hit="New line after '('" />
    <option name="new" path="Wrapping and Braces" hit="New line after '('" />
    <option name="after" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="line" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="new" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="line" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="next" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="on" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="operation" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="sign" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="other" path="Wrapping and Braces" hit="Other" />
    <option name="annotations" path="Wrapping and Braces" hit="Parameter annotations" />
    <option name="parameter" path="Wrapping and Braces" hit="Parameter annotations" />
    <option name="line" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="new" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="on" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="place" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="line" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="new" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="on" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="place" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="after" path="Wrapping and Braces" hit="Put space after keywords" />
    <option name="keywords" path="Wrapping and Braces" hit="Put space after keywords" />
    <option name="put" path="Wrapping and Braces" hit="Put space after keywords" />
    <option name="space" path="Wrapping and Braces" hit="Put space after keywords" />
    <option name="expression" path="Spaces" hit="Regexp expression (==~, =~)" />
    <option name="regexp" path="Spaces" hit="Regexp expression (==~, =~)" />
    <option name="operators" path="Spaces" hit="Relational operators (&lt;, &gt;, &lt;=, &gt;=, &lt;=&gt;)" />
    <option name="relational" path="Spaces" hit="Relational operators (&lt;, &gt;, &lt;=, &gt;=, &lt;=&gt;)" />
    <option name="operators" path="Spaces" hit="Shift operators (&lt;&lt;, &gt;&gt;, &gt;&gt;&gt;)" />
    <option name="shift" path="Spaces" hit="Shift operators (&lt;&lt;, &gt;&gt;, &gt;&gt;&gt;)" />
    <option name="blocks" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="classes" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="lambdas" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="methods" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="else" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="if" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="special" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="treatment" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="call" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="chain" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="over" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="priority" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="take" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="wrapping" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="operation" path="Wrapping and Braces" hit="Ternary operation" />
    <option name="ternary" path="Wrapping and Braces" hit="Ternary operation" />
    <option name="keyword" path="Wrapping and Braces" hit="Throws keyword" />
    <option name="throws" path="Wrapping and Braces" hit="Throws keyword" />
    <option name="list" path="Wrapping and Braces" hit="Throws list" />
    <option name="throws" path="Wrapping and Braces" hit="Throws list" />
    <option name="assignment" path="Spaces" hit="Tuple assignment expression" />
    <option name="expression" path="Spaces" hit="Tuple assignment expression" />
    <option name="tuple" path="Spaces" hit="Tuple assignment expression" />
    <option name="cast" path="Spaces" hit="Type cast parentheses" />
    <option name="parentheses" path="Spaces" hit="Type cast parentheses" />
    <option name="type" path="Spaces" hit="Type cast parentheses" />
    <option name="+" path="Spaces" hit="Unary operators (!, -, +, ++, --, *)" />
    <option name="++" path="Spaces" hit="Unary operators (!, -, +, ++, --, *)" />
    <option name="-" path="Spaces" hit="Unary operators (!, -, +, ++, --, *)" />
    <option name="--" path="Spaces" hit="Unary operators (!, -, +, ++, --, *)" />
    <option name="operators" path="Spaces" hit="Unary operators (!, -, +, ++, --, *)" />
    <option name="unary" path="Spaces" hit="Unary operators (!, -, +, ++, --, *)" />
    <option name="braces" path="Wrapping and Braces" hit="Use flying geese braces" />
    <option name="flying" path="Wrapping and Braces" hit="Use flying geese braces" />
    <option name="geese" path="Wrapping and Braces" hit="Use flying geese braces" />
    <option name="use" path="Wrapping and Braces" hit="Use flying geese braces" />
    <option name="guides" path="Wrapping and Braces" hit="Visual guides" />
    <option name="visual" path="Wrapping and Braces" hit="Visual guides" />
    <option name="within" path="Spaces" hit="Within" />
    <option name="arguments" path="Spaces" hit="Within type arguments" />
    <option name="type" path="Spaces" hit="Within type arguments" />
    <option name="within" path="Spaces" hit="Within type arguments" />
    <option name="clause" path="Wrapping and Braces" hit="Wrap 'having' clause" />
    <option name="having" path="Wrapping and Braces" hit="Wrap 'having' clause" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap 'having' clause" />
    <option name="clause" path="Wrapping and Braces" hit="Wrap 'on' clause" />
    <option name="on" path="Wrapping and Braces" hit="Wrap 'on' clause" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap 'on' clause" />
    <option name="after" path="Wrapping and Braces" hit="Wrap after dot" />
    <option name="dot" path="Wrapping and Braces" hit="Wrap after dot" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap after dot" />
    <option name="after" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="list" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="modifier" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="at" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="margin" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="right" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="call" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="first" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="on" path="Wrapping and Braces" hit="Wrap on typing" />
    <option name="typing" path="Wrapping and Braces" hit="Wrap on typing" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap on typing" />
  </configurable>
  <configurable id="copyright.filetypes.Groovy" configurable_name="Groovy">
    <option name="a" hit="Add blank line after" />
    <option name="after" hit="Add blank line after" />
    <option name="blank" hit="Add blank line after" />
    <option name="dd" hit="Add blank line after" />
    <option name="line" hit="Add blank line after" />
    <option name="add" hit="Add blank line before" />
    <option name="before" hit="Add blank line before" />
    <option name="blank" hit="Add blank line before" />
    <option name="line" hit="Add blank line before" />
    <option name="afte" hit="After other comments" />
    <option name="comments" hit="After other comments" />
    <option name="other" hit="After other comments" />
    <option name="r" hit="After other comments" />
    <option name="bef" hit="Before other comments" />
    <option name="comments" hit="Before other comments" />
    <option name="ore" hit="Before other comments" />
    <option name="other" hit="Before other comments" />
    <option name="borders" hit="Borders" />
    <option name="box" hit="Box" />
    <option name="comment" hit="Comment Type" />
    <option name="type" hit="Comment Type" />
    <option name="groovy" hit="Groovy" />
    <option name="length" hit="Length:" />
    <option name="copyright" hit="No copyright" />
    <option name="no" hit="No copyright" />
    <option name="each" hit="Prefix each line" />
    <option name="line" hit="Prefix each line" />
    <option name="prefix" hit="Prefix each line" />
    <option name="location" hit="Relative Location" />
    <option name="relative" hit="Relative Location" />
    <option name="after" hit="Separator after" />
    <option name="separator" hit="Separator after" />
    <option name="before" hit="Separator before" />
    <option name="separator" hit="Separator before" />
    <option name="separator" hit="Separator:" />
    <option name="block" hit="Use block comment" />
    <option name="comment" hit="Use block comment" />
    <option name="use" hit="Use block comment" />
    <option name="custom" hit="Use custom formatting options" />
    <option name="formatting" hit="Use custom formatting options" />
    <option name="options" hit="Use custom formatting options" />
    <option name="use" hit="Use custom formatting options" />
    <option name="default" hit="Use default settings" />
    <option name="settings" hit="Use default settings" />
    <option name="use" hit="Use default settings" />
    <option name="comment" hit="Use line comment" />
    <option name="line" hit="Use line comment" />
    <option name="use" hit="Use line comment" />
  </configurable>
  <configurable id="reference.projectsettings.compiler.javacompiler" configurable_name="Java Compiler">
    <option name="command" hit="Command line parameters:" />
    <option name="line" hit="Command line parameters:" />
    <option name="parameters" hit="Command line parameters:" />
    <option name="debug" hit="Generate debug info" />
    <option name="generate" hit="Generate debug info" />
    <option name="info" hit="Generate debug info" />
    <option name="groovy-eclipse" hit="Groovy-Eclipse Options" />
    <option name="options" hit="Groovy-Eclipse Options" />
    <option name="groovy-eclipse-batch" hit="Path to groovy-eclipse-batch jar:" />
    <option name="jar" hit="Path to groovy-eclipse-batch jar:" />
    <option name="path" hit="Path to groovy-eclipse-batch jar:" />
    <option name="to" hit="Path to groovy-eclipse-batch jar:" />
    <option name="options" hit="VM options:" />
    <option name="vm" hit="VM options:" />
  </configurable>
  <configurable id="Groovy compiler" configurable_name="Groovy Compiler">
    <option name="exclude" hit="Exclude from stub generation:" />
    <option name="from" hit="Exclude from stub generation:" />
    <option name="generation" hit="Exclude from stub generation:" />
    <option name="stub" hit="Exclude from stub generation:" />
    <option name="compiler" hit="Groovy Compiler" />
    <option name="groovy" hit="Groovy Compiler" />
    <option name="dynamic" hit="Invoke dynamic support" />
    <option name="invoke" hit="Invoke dynamic support" />
    <option name="support" hit="Invoke dynamic support" />
    <option name="configscript" hit="Path to configscript:" />
    <option name="path" hit="Path to configscript:" />
    <option name="to" hit="Path to configscript:" />
  </configurable>
  <configurable id="reference.settingsdialog.project.gant" configurable_name="Gant">
    <option name="gant" hit="Gant" />
    <option name="gant" hit="Gant home:" />
    <option name="home" hit="Gant home:" />
  </configurable>
</options>