<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="actual" path="ActionManager" hit="Actual Size" />
    <option name="size" path="ActionManager" hit="Actual Size" />
    <option name="browse" path="ActionManager" hit="Browse" />
    <option name="browse" path="ActionManager" hit="Browse directory" />
    <option name="directory" path="ActionManager" hit="Browse directory" />
    <option name="background" path="ActionManager" hit="Change Background" />
    <option name="change" path="ActionManager" hit="Change Background" />
    <option name="background" path="ActionManager" hit="Change editor background" />
    <option name="change" path="ActionManager" hit="Change editor background" />
    <option name="editor" path="ActionManager" hit="Change editor background" />
    <option name="close" path="ActionManager" hit="Close Thumbnails" />
    <option name="thumbnails" path="ActionManager" hit="Close Thumbnails" />
    <option name="close" path="ActionManager" hit="Close thumbnails window" />
    <option name="thumbnails" path="ActionManager" hit="Close thumbnails window" />
    <option name="window" path="ActionManager" hit="Close thumbnails window" />
    <option name="convert" path="ActionManager" hit="Convert to PNG" />
    <option name="png" path="ActionManager" hit="Convert to PNG" />
    <option name="to" path="ActionManager" hit="Convert to PNG" />
    <option name="edit" path="ActionManager" hit="Edit Path to External Editor..." />
    <option name="editor" path="ActionManager" hit="Edit Path to External Editor..." />
    <option name="external" path="ActionManager" hit="Edit Path to External Editor..." />
    <option name="path" path="ActionManager" hit="Edit Path to External Editor..." />
    <option name="to" path="ActionManager" hit="Edit Path to External Editor..." />
    <option name="edit" path="ActionManager" hit="Edit path to external editor" />
    <option name="editor" path="ActionManager" hit="Edit path to external editor" />
    <option name="external" path="ActionManager" hit="Edit path to external editor" />
    <option name="path" path="ActionManager" hit="Edit path to external editor" />
    <option name="to" path="ActionManager" hit="Edit path to external editor" />
    <option name="file" path="ActionManager" hit="File Name" />
    <option name="name" path="ActionManager" hit="File Name" />
    <option name="file" path="ActionManager" hit="File Size" />
    <option name="size" path="ActionManager" hit="File Size" />
    <option name="by" path="ActionManager" hit="Filter by Tag" />
    <option name="filter" path="ActionManager" hit="Filter by Tag" />
    <option name="tag" path="ActionManager" hit="Filter by Tag" />
    <option name="by" path="ActionManager" hit="Filter by Theme" />
    <option name="filter" path="ActionManager" hit="Filter by Theme" />
    <option name="theme" path="ActionManager" hit="Filter by Theme" />
    <option name="by" path="ActionManager" hit="Filter images by tag" />
    <option name="filter" path="ActionManager" hit="Filter images by tag" />
    <option name="images" path="ActionManager" hit="Filter images by tag" />
    <option name="tag" path="ActionManager" hit="Filter images by tag" />
    <option name="by" path="ActionManager" hit="Filter images by theme" />
    <option name="filter" path="ActionManager" hit="Filter images by theme" />
    <option name="images" path="ActionManager" hit="Filter images by theme" />
    <option name="theme" path="ActionManager" hit="Filter images by theme" />
    <option name="fit" path="ActionManager" hit="Fit Zoom to Window" />
    <option name="to" path="ActionManager" hit="Fit Zoom to Window" />
    <option name="window" path="ActionManager" hit="Fit Zoom to Window" />
    <option name="zoom" path="ActionManager" hit="Fit Zoom to Window" />
    <option name="grid" path="ActionManager" hit="Grid" />
    <option name="image" path="ActionManager" hit="Increase image view" />
    <option name="increase" path="ActionManager" hit="Increase image view" />
    <option name="view" path="ActionManager" hit="Increase image view" />
    <option name="editor" path="ActionManager" hit="Jump to External Editor" />
    <option name="external" path="ActionManager" hit="Jump to External Editor" />
    <option name="jump" path="ActionManager" hit="Jump to External Editor" />
    <option name="to" path="ActionManager" hit="Jump to External Editor" />
    <option name="level" path="ActionManager" hit="Level up" />
    <option name="up" path="ActionManager" hit="Level up" />
    <option name="editor" path="ActionManager" hit="Open image in external editor" />
    <option name="external" path="ActionManager" hit="Open image in external editor" />
    <option name="image" path="ActionManager" hit="Open image in external editor" />
    <option name="in" path="ActionManager" hit="Open image in external editor" />
    <option name="open" path="ActionManager" hit="Open image in external editor" />
    <option name="preview" path="ActionManager" hit="Preview Tags" />
    <option name="tags" path="ActionManager" hit="Preview Tags" />
    <option name="recursive" path="ActionManager" hit="Recursive" />
    <option name="image" path="ActionManager" hit="Reduce image view" />
    <option name="reduce" path="ActionManager" hit="Reduce image view" />
    <option name="view" path="ActionManager" hit="Reduce image view" />
    <option name="actual" path="ActionManager" hit="Resize image to actual size" />
    <option name="image" path="ActionManager" hit="Resize image to actual size" />
    <option name="resize" path="ActionManager" hit="Resize image to actual size" />
    <option name="size" path="ActionManager" hit="Resize image to actual size" />
    <option name="to" path="ActionManager" hit="Resize image to actual size" />
    <option name="background" path="ActionManager" hit="Set Background Image" />
    <option name="image" path="ActionManager" hit="Set Background Image" />
    <option name="set" path="ActionManager" hit="Set Background Image" />
    <option name="border" path="ActionManager" hit="Show Border" />
    <option name="show" path="ActionManager" hit="Show Border" />
    <option name="image" path="ActionManager" hit="Show Image Thumbnails" />
    <option name="show" path="ActionManager" hit="Show Image Thumbnails" />
    <option name="thumbnails" path="ActionManager" hit="Show Image Thumbnails" />
    <option name="around" path="ActionManager" hit="Show border around the image" />
    <option name="border" path="ActionManager" hit="Show border around the image" />
    <option name="image" path="ActionManager" hit="Show border around the image" />
    <option name="show" path="ActionManager" hit="Show border around the image" />
    <option name="the" path="ActionManager" hit="Show border around the image" />
    <option name="current" path="ActionManager" hit="Show thumbnails view for current directory" />
    <option name="directory" path="ActionManager" hit="Show thumbnails view for current directory" />
    <option name="for" path="ActionManager" hit="Show thumbnails view for current directory" />
    <option name="show" path="ActionManager" hit="Show thumbnails view for current directory" />
    <option name="thumbnails" path="ActionManager" hit="Show thumbnails view for current directory" />
    <option name="view" path="ActionManager" hit="Show thumbnails view for current directory" />
    <option name="grid" path="ActionManager" hit="Toggle grid lines over image" />
    <option name="image" path="ActionManager" hit="Toggle grid lines over image" />
    <option name="lines" path="ActionManager" hit="Toggle grid lines over image" />
    <option name="over" path="ActionManager" hit="Toggle grid lines over image" />
    <option name="toggle" path="ActionManager" hit="Toggle grid lines over image" />
    <option name="browsing" path="ActionManager" hit="Toggle recursive browsing" />
    <option name="recursive" path="ActionManager" hit="Toggle recursive browsing" />
    <option name="toggle" path="ActionManager" hit="Toggle recursive browsing" />
    <option name="chessboard" path="ActionManager" hit="Toggle transparency chessboard under image" />
    <option name="image" path="ActionManager" hit="Toggle transparency chessboard under image" />
    <option name="toggle" path="ActionManager" hit="Toggle transparency chessboard under image" />
    <option name="transparency" path="ActionManager" hit="Toggle transparency chessboard under image" />
    <option name="under" path="ActionManager" hit="Toggle transparency chessboard under image" />
    <option name="image" path="ActionManager" hit="Toggle whether to show the image name" />
    <option name="name" path="ActionManager" hit="Toggle whether to show the image name" />
    <option name="show" path="ActionManager" hit="Toggle whether to show the image name" />
    <option name="the" path="ActionManager" hit="Toggle whether to show the image name" />
    <option name="to" path="ActionManager" hit="Toggle whether to show the image name" />
    <option name="toggle" path="ActionManager" hit="Toggle whether to show the image name" />
    <option name="whether" path="ActionManager" hit="Toggle whether to show the image name" />
    <option name="image" path="ActionManager" hit="Toggle whether to show the image size information" />
    <option name="information" path="ActionManager" hit="Toggle whether to show the image size information" />
    <option name="show" path="ActionManager" hit="Toggle whether to show the image size information" />
    <option name="size" path="ActionManager" hit="Toggle whether to show the image size information" />
    <option name="the" path="ActionManager" hit="Toggle whether to show the image size information" />
    <option name="to" path="ActionManager" hit="Toggle whether to show the image size information" />
    <option name="toggle" path="ActionManager" hit="Toggle whether to show the image size information" />
    <option name="whether" path="ActionManager" hit="Toggle whether to show the image size information" />
    <option name="management" path="ActionManager" hit="Toggle whether to show the tags management panel" />
    <option name="panel" path="ActionManager" hit="Toggle whether to show the tags management panel" />
    <option name="show" path="ActionManager" hit="Toggle whether to show the tags management panel" />
    <option name="tags" path="ActionManager" hit="Toggle whether to show the tags management panel" />
    <option name="the" path="ActionManager" hit="Toggle whether to show the tags management panel" />
    <option name="to" path="ActionManager" hit="Toggle whether to show the tags management panel" />
    <option name="toggle" path="ActionManager" hit="Toggle whether to show the tags management panel" />
    <option name="whether" path="ActionManager" hit="Toggle whether to show the tags management panel" />
    <option name="chessboard" path="ActionManager" hit="Transparency Chessboard" />
    <option name="transparency" path="ActionManager" hit="Transparency Chessboard" />
    <option name="in" path="ActionManager" hit="Zoom In" />
    <option name="zoom" path="ActionManager" hit="Zoom In" />
    <option name="out" path="ActionManager" hit="Zoom Out" />
    <option name="zoom" path="ActionManager" hit="Zoom Out" />
  </configurable>
  <configurable id="reference.settingsdialog.IDE.editor.colors.Images" configurable_name="Images">
    <option name="images" hit="Images" />
    <option name="" hit="'Black' cell" />
    <option name="black" hit="'Black' cell" />
    <option name="cell" hit="'Black' cell" />
    <option name="" hit="'White' cell" />
    <option name="cell" hit="'White' cell" />
    <option name="white" hit="'White' cell" />
    <option name="background" hit="Background" />
    <option name="grid" hit="Grid line" />
    <option name="line" hit="Grid line" />
  </configurable>
</options>