<options>
  <configurable id="fileTemplates" configurable_name="File and Code Templates">
    <option name="body" hit="New Kotlin Function Body" />
    <option name="function" hit="New Kotlin Function Body" />
    <option name="kotlin" hit="New Kotlin Function Body" />
    <option name="new" hit="New Kotlin Function Body" />
    <option name="initializer" hit="New Kotlin Property Initializer" />
    <option name="kotlin" hit="New Kotlin Property Initializer" />
    <option name="new" hit="New Kotlin Property Initializer" />
    <option name="property" hit="New Kotlin Property Initializer" />
    <option name="body" hit="New Kotlin Secondary Constructor Body" />
    <option name="constructor" hit="New Kotlin Secondary Constructor Body" />
    <option name="kotlin" hit="New Kotlin Secondary Constructor Body" />
    <option name="new" hit="New Kotlin Secondary Constructor Body" />
    <option name="secondary" hit="New Kotlin Secondary Constructor Body" />
    <option name="compose-desktop-build" hit="compose-desktop-build.gradle" />
    <option name="gradle" hit="compose-desktop-build.gradle" />
    <option name="compose-desktop-main" hit="compose-desktop-main" />
    <option name="compose-desktop-run-configuration" hit="compose-desktop-run-configuration" />
    <option name="compose-gradle" hit="compose-gradle" />
    <option name="compose-gradle-wrapper" hit="compose-gradle-wrapper" />
    <option name="compose-settings" hit="compose-settings.gradle" />
    <option name="gradle" hit="compose-settings.gradle" />
  </configurable>
</options>