<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="and" path="ActionManager" hit="Collect dependencies and recheck" />
    <option name="collect" path="ActionManager" hit="Collect dependencies and recheck" />
    <option name="dependencies" path="ActionManager" hit="Collect dependencies and recheck" />
    <option name="recheck" path="ActionManager" hit="Collect dependencies and recheck" />
    <option name="dependencies" path="ActionManager" hit="Filter out safe dependencies" />
    <option name="filter" path="ActionManager" hit="Filter out safe dependencies" />
    <option name="out" path="ActionManager" hit="Filter out safe dependencies" />
    <option name="safe" path="ActionManager" hit="Filter out safe dependencies" />
    <option name="find" path="ActionManager" hit="Find Usages" />
    <option name="usages" path="ActionManager" hit="Find Usages" />
    <option name="find" path="ActionManager" hit="Find usages of related library" />
    <option name="library" path="ActionManager" hit="Find usages of related library" />
    <option name="of" path="ActionManager" hit="Find usages of related library" />
    <option name="related" path="ActionManager" hit="Find usages of related library" />
    <option name="usages" path="ActionManager" hit="Find usages of related library" />
    <option name="jump" path="ActionManager" hit="Jump To Source" />
    <option name="source" path="ActionManager" hit="Jump To Source" />
    <option name="to" path="ActionManager" hit="Jump To Source" />
    <option name="editor" path="ActionManager" hit="Open related file In editor" />
    <option name="file" path="ActionManager" hit="Open related file In editor" />
    <option name="in" path="ActionManager" hit="Open related file In editor" />
    <option name="open" path="ActionManager" hit="Open related file In editor" />
    <option name="related" path="ActionManager" hit="Open related file In editor" />
    <option name="refresh" path="ActionManager" hit="Refresh" />
    <option name="dependencies" path="ActionManager" hit="Show Safe Dependencies" />
    <option name="safe" path="ActionManager" hit="Show Safe Dependencies" />
    <option name="show" path="ActionManager" hit="Show Safe Dependencies" />
    <option name="against" path="ActionManager" hit="Show ToolWindow with imported dependencies checked against known vulnerabilities" />
    <option name="checked" path="ActionManager" hit="Show ToolWindow with imported dependencies checked against known vulnerabilities" />
    <option name="dependencies" path="ActionManager" hit="Show ToolWindow with imported dependencies checked against known vulnerabilities" />
    <option name="imported" path="ActionManager" hit="Show ToolWindow with imported dependencies checked against known vulnerabilities" />
    <option name="known" path="ActionManager" hit="Show ToolWindow with imported dependencies checked against known vulnerabilities" />
    <option name="show" path="ActionManager" hit="Show ToolWindow with imported dependencies checked against known vulnerabilities" />
    <option name="toolwindow" path="ActionManager" hit="Show ToolWindow with imported dependencies checked against known vulnerabilities" />
    <option name="vulnerabilities" path="ActionManager" hit="Show ToolWindow with imported dependencies checked against known vulnerabilities" />
    <option name="with" path="ActionManager" hit="Show ToolWindow with imported dependencies checked against known vulnerabilities" />
    <option name="dependencies" path="ActionManager" hit="Vulnerable Dependencies…" />
    <option name="vulnerable" path="ActionManager" hit="Vulnerable Dependencies…" />
  </configurable>
</options>