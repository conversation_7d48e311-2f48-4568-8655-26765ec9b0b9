<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="ai" path="ActionManager" hit="AI Assist" />
    <option name="assist" path="ActionManager" hit="AI Assist" />
    <option name="ai" path="ActionManager" hit="Generate or refactor with AI" />
    <option name="generate" path="ActionManager" hit="Generate or refactor with AI" />
    <option name="or" path="ActionManager" hit="Generate or refactor with AI" />
    <option name="refactor" path="ActionManager" hit="Generate or refactor with AI" />
    <option name="with" path="ActionManager" hit="Generate or refactor with AI" />
    <option name="test" path="ActionManager" hit="Test Zigzag" />
    <option name="zigzag" path="ActionManager" hit="Test Zigzag" />
    <option name="connection" path="ActionManager" hit="Test Zigzag connection" />
    <option name="test" path="ActionManager" hit="Test Zigzag connection" />
    <option name="zigzag" path="ActionManager" hit="Test Zigzag connection" />
  </configurable>
  <configurable id="com.wontlost.zigzag.settings" configurable_name="Zigzag">
    <option name="" hit=" API Configuration" />
    <option name="api" hit=" API Configuration" />
    <option name="configuration" hit=" API Configuration" />
    <option name="" hit=" Features" />
    <option name="features" hit=" Features" />
    <option name="" hit=" Generation Parameters" />
    <option name="generation" hit=" Generation Parameters" />
    <option name="parameters" hit=" Generation Parameters" />
    <option name="chat" hit="Enable chat panel" />
    <option name="enable" hit="Enable chat panel" />
    <option name="panel" hit="Enable chat panel" />
    <option name="debug" hit="Enable debug logging" />
    <option name="enable" hit="Enable debug logging" />
    <option name="logging" hit="Enable debug logging" />
    <option name="completions" hit="Enable inline completions" />
    <option name="enable" hit="Enable inline completions" />
    <option name="inline" hit="Enable inline completions" />
    <option name="max" hit="Max Tokens:" />
    <option name="tokens" hit="Max Tokens:" />
    <option name="model" hit="Model:" />
    <option name="api" hit="OpenAI API Key:" />
    <option name="key" hit="OpenAI API Key:" />
    <option name="openai" hit="OpenAI API Key:" />
    <option name="request" hit="Request Timeout (seconds):" />
    <option name="seconds" hit="Request Timeout (seconds):" />
    <option name="timeout" hit="Request Timeout (seconds):" />
    <option name="temperature" hit="Temperature:" />
    <option name="zigzag" hit="Zigzag" />
    <option name="5-turbo" hit="gpt-3.5-turbo" />
    <option name="gpt-3" hit="gpt-3.5-turbo" />
    <option name="gpt-4" hit="gpt-4" />
    <option name="gpt-4-turbo" hit="gpt-4-turbo" />
  </configurable>
</options>