<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="attach" path="ActionManager" hit="Attach Profiler to Process..." />
    <option name="process" path="ActionManager" hit="Attach Profiler to Process..." />
    <option name="profiler" path="ActionManager" hit="Attach Profiler to Process..." />
    <option name="to" path="ActionManager" hit="Attach Profiler to Process..." />
    <option name="an" path="ActionManager" hit="Attach to process with an available profiler" />
    <option name="attach" path="ActionManager" hit="Attach to process with an available profiler" />
    <option name="available" path="ActionManager" hit="Attach to process with an available profiler" />
    <option name="process" path="ActionManager" hit="Attach to process with an available profiler" />
    <option name="profiler" path="ActionManager" hit="Attach to process with an available profiler" />
    <option name="to" path="ActionManager" hit="Attach to process with an available profiler" />
    <option name="with" path="ActionManager" hit="Attach to process with an available profiler" />
    <option name="and" path="ActionManager" hit="CPU and Memory Live Charts" />
    <option name="charts" path="ActionManager" hit="CPU and Memory Live Charts" />
    <option name="cpu" path="ActionManager" hit="CPU and Memory Live Charts" />
    <option name="live" path="ActionManager" hit="CPU and Memory Live Charts" />
    <option name="memory" path="ActionManager" hit="CPU and Memory Live Charts" />
    <option name="capture" path="ActionManager" hit="Capture CPU usage data" />
    <option name="cpu" path="ActionManager" hit="Capture CPU usage data" />
    <option name="data" path="ActionManager" hit="Capture CPU usage data" />
    <option name="usage" path="ActionManager" hit="Capture CPU usage data" />
    <option name="flame" path="ActionManager" hit="New Flame Graph View" />
    <option name="graph" path="ActionManager" hit="New Flame Graph View" />
    <option name="new" path="ActionManager" hit="New Flame Graph View" />
    <option name="view" path="ActionManager" hit="New Flame Graph View" />
    <option name="open" path="ActionManager" hit="Open Profiler Snapshot" />
    <option name="profiler" path="ActionManager" hit="Open Profiler Snapshot" />
    <option name="snapshot" path="ActionManager" hit="Open Profiler Snapshot" />
    <option name="indexing" path="ActionManager" hit="Profile Indexing" />
    <option name="profile" path="ActionManager" hit="Profile Indexing" />
    <option name="indexing" path="ActionManager" hit="Profile Indexing on next restart" />
    <option name="next" path="ActionManager" hit="Profile Indexing on next restart" />
    <option name="on" path="ActionManager" hit="Profile Indexing on next restart" />
    <option name="profile" path="ActionManager" hit="Profile Indexing on next restart" />
    <option name="restart" path="ActionManager" hit="Profile Indexing on next restart" />
    <option name="finder" path="ActionManager" hit="Reveal in Finder" />
    <option name="in" path="ActionManager" hit="Reveal in Finder" />
    <option name="reveal" path="ActionManager" hit="Reveal in Finder" />
    <option name="async" path="ActionManager" hit="Start Async Profiler" />
    <option name="profiler" path="ActionManager" hit="Start Async Profiler" />
    <option name="start" path="ActionManager" hit="Start Async Profiler" />
    <option name="cpu" path="ActionManager" hit="Start CPU Usage Profiling" />
    <option name="profiling" path="ActionManager" hit="Start CPU Usage Profiling" />
    <option name="start" path="ActionManager" hit="Start CPU Usage Profiling" />
    <option name="usage" path="ActionManager" hit="Start CPU Usage Profiling" />
    <option name="async" path="ActionManager" hit="Start async profiler" />
    <option name="profiler" path="ActionManager" hit="Start async profiler" />
    <option name="start" path="ActionManager" hit="Start async profiler" />
  </configurable>
</options>