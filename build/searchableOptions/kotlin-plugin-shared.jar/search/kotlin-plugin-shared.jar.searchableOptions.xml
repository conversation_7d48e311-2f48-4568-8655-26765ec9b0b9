<options>
  <configurable id="reference.settingsdialog.IDE.editor.colors.Kotlin" configurable_name="<PERSON><PERSON><PERSON>">
    <option name="kotlin" hit="<PERSON><PERSON><PERSON>" />
    <option name="annotation" hit="Annotation//Annotation attribute name" />
    <option name="attribute" hit="Annotation//Annotation attribute name" />
    <option name="name" hit="Annotation//Annotation attribute name" />
    <option name="annotation" hit="Annotation//Annotation name" />
    <option name="name" hit="Annotation//Annotation name" />
    <option name="and" hit="Braces and Operators//Arrow" />
    <option name="arrow" hit="Braces and Operators//Arrow" />
    <option name="braces" hit="Braces and Operators//Arrow" />
    <option name="operators" hit="Braces and Operators//Arrow" />
    <option name="and" hit="Braces and Operators//Braces" />
    <option name="braces" hit="Braces and Operators//Braces" />
    <option name="operators" hit="Braces and Operators//Braces" />
    <option name="and" hit="Braces and Operators//Brackets" />
    <option name="braces" hit="Braces and Operators//Brackets" />
    <option name="brackets" hit="Braces and Operators//Brackets" />
    <option name="operators" hit="Braces and Operators//Brackets" />
    <option name="and" hit="Braces and Operators//Colon" />
    <option name="braces" hit="Braces and Operators//Colon" />
    <option name="colon" hit="Braces and Operators//Colon" />
    <option name="operators" hit="Braces and Operators//Colon" />
    <option name="and" hit="Braces and Operators//Comma" />
    <option name="braces" hit="Braces and Operators//Comma" />
    <option name="comma" hit="Braces and Operators//Comma" />
    <option name="operators" hit="Braces and Operators//Comma" />
    <option name="and" hit="Braces and Operators//Dot" />
    <option name="braces" hit="Braces and Operators//Dot" />
    <option name="dot" hit="Braces and Operators//Dot" />
    <option name="operators" hit="Braces and Operators//Dot" />
    <option name="and" hit="Braces and Operators//Double colon" />
    <option name="braces" hit="Braces and Operators//Double colon" />
    <option name="colon" hit="Braces and Operators//Double colon" />
    <option name="double" hit="Braces and Operators//Double colon" />
    <option name="operators" hit="Braces and Operators//Double colon" />
    <option name="and" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="arrow" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="braces" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="expression" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="lambda" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="operators" hit="Braces and Operators//Lambda expression braces and arrow" />
    <option name="and" hit="Braces and Operators//Non-null assertion" />
    <option name="assertion" hit="Braces and Operators//Non-null assertion" />
    <option name="braces" hit="Braces and Operators//Non-null assertion" />
    <option name="non-null" hit="Braces and Operators//Non-null assertion" />
    <option name="operators" hit="Braces and Operators//Non-null assertion" />
    <option name="and" hit="Braces and Operators//Operator sign" />
    <option name="braces" hit="Braces and Operators//Operator sign" />
    <option name="operator" hit="Braces and Operators//Operator sign" />
    <option name="operators" hit="Braces and Operators//Operator sign" />
    <option name="sign" hit="Braces and Operators//Operator sign" />
    <option name="and" hit="Braces and Operators//Parentheses" />
    <option name="braces" hit="Braces and Operators//Parentheses" />
    <option name="operators" hit="Braces and Operators//Parentheses" />
    <option name="parentheses" hit="Braces and Operators//Parentheses" />
    <option name="access" hit="Braces and Operators//Safe access dot" />
    <option name="and" hit="Braces and Operators//Safe access dot" />
    <option name="braces" hit="Braces and Operators//Safe access dot" />
    <option name="dot" hit="Braces and Operators//Safe access dot" />
    <option name="operators" hit="Braces and Operators//Safe access dot" />
    <option name="safe" hit="Braces and Operators//Safe access dot" />
    <option name="and" hit="Braces and Operators//Semicolon" />
    <option name="braces" hit="Braces and Operators//Semicolon" />
    <option name="operators" hit="Braces and Operators//Semicolon" />
    <option name="semicolon" hit="Braces and Operators//Semicolon" />
    <option name="and" hit="Braces and Operators//Type nullability marker" />
    <option name="braces" hit="Braces and Operators//Type nullability marker" />
    <option name="marker" hit="Braces and Operators//Type nullability marker" />
    <option name="nullability" hit="Braces and Operators//Type nullability marker" />
    <option name="operators" hit="Braces and Operators//Type nullability marker" />
    <option name="type" hit="Braces and Operators//Type nullability marker" />
    <option name="abstract" hit="Classes and Interfaces//Abstract class" />
    <option name="and" hit="Classes and Interfaces//Abstract class" />
    <option name="class" hit="Classes and Interfaces//Abstract class" />
    <option name="classes" hit="Classes and Interfaces//Abstract class" />
    <option name="interfaces" hit="Classes and Interfaces//Abstract class" />
    <option name="and" hit="Classes and Interfaces//Class" />
    <option name="class" hit="Classes and Interfaces//Class" />
    <option name="classes" hit="Classes and Interfaces//Class" />
    <option name="interfaces" hit="Classes and Interfaces//Class" />
    <option name="and" hit="Classes and Interfaces//Enum" />
    <option name="classes" hit="Classes and Interfaces//Enum" />
    <option name="enum" hit="Classes and Interfaces//Enum" />
    <option name="interfaces" hit="Classes and Interfaces//Enum" />
    <option name="and" hit="Classes and Interfaces//Enum entry" />
    <option name="classes" hit="Classes and Interfaces//Enum entry" />
    <option name="entry" hit="Classes and Interfaces//Enum entry" />
    <option name="enum" hit="Classes and Interfaces//Enum entry" />
    <option name="interfaces" hit="Classes and Interfaces//Enum entry" />
    <option name="and" hit="Classes and Interfaces//Interface" />
    <option name="classes" hit="Classes and Interfaces//Interface" />
    <option name="interface" hit="Classes and Interfaces//Interface" />
    <option name="interfaces" hit="Classes and Interfaces//Interface" />
    <option name="and" hit="Classes and Interfaces//Object" />
    <option name="classes" hit="Classes and Interfaces//Object" />
    <option name="interfaces" hit="Classes and Interfaces//Object" />
    <option name="object" hit="Classes and Interfaces//Object" />
    <option name="alias" hit="Classes and Interfaces//Type alias" />
    <option name="and" hit="Classes and Interfaces//Type alias" />
    <option name="classes" hit="Classes and Interfaces//Type alias" />
    <option name="interfaces" hit="Classes and Interfaces//Type alias" />
    <option name="type" hit="Classes and Interfaces//Type alias" />
    <option name="block" hit="Comments//Block comment" />
    <option name="comment" hit="Comments//Block comment" />
    <option name="comments" hit="Comments//Block comment" />
    <option name="comment" hit="Comments//KDoc//KDoc comment" />
    <option name="comments" hit="Comments//KDoc//KDoc comment" />
    <option name="kdoc" hit="Comments//KDoc//KDoc comment" />
    <option name="comments" hit="Comments//KDoc//KDoc tag" />
    <option name="kdoc" hit="Comments//KDoc//KDoc tag" />
    <option name="tag" hit="Comments//KDoc//KDoc tag" />
    <option name="comments" hit="Comments//KDoc//Link in KDoc tag" />
    <option name="in" hit="Comments//KDoc//Link in KDoc tag" />
    <option name="kdoc" hit="Comments//KDoc//Link in KDoc tag" />
    <option name="link" hit="Comments//KDoc//Link in KDoc tag" />
    <option name="tag" hit="Comments//KDoc//Link in KDoc tag" />
    <option name="comment" hit="Comments//Line comment" />
    <option name="comments" hit="Comments//Line comment" />
    <option name="line" hit="Comments//Line comment" />
    <option name="1" hit="Dsl//Style 1" />
    <option name="dsl" hit="Dsl//Style 1" />
    <option name="style" hit="Dsl//Style 1" />
    <option name="2" hit="Dsl//Style 2" />
    <option name="dsl" hit="Dsl//Style 2" />
    <option name="style" hit="Dsl//Style 2" />
    <option name="3" hit="Dsl//Style 3" />
    <option name="dsl" hit="Dsl//Style 3" />
    <option name="style" hit="Dsl//Style 3" />
    <option name="4" hit="Dsl//Style 4" />
    <option name="dsl" hit="Dsl//Style 4" />
    <option name="style" hit="Dsl//Style 4" />
    <option name="call" hit="Functions//Constructor call" />
    <option name="constructor" hit="Functions//Constructor call" />
    <option name="functions" hit="Functions//Constructor call" />
    <option name="call" hit="Functions//Dynamic function call" />
    <option name="dynamic" hit="Functions//Dynamic function call" />
    <option name="function" hit="Functions//Dynamic function call" />
    <option name="functions" hit="Functions//Dynamic function call" />
    <option name="call" hit="Functions//Extension function call" />
    <option name="extension" hit="Functions//Extension function call" />
    <option name="function" hit="Functions//Extension function call" />
    <option name="functions" hit="Functions//Extension function call" />
    <option name="call" hit="Functions//Function call" />
    <option name="function" hit="Functions//Function call" />
    <option name="functions" hit="Functions//Function call" />
    <option name="declaration" hit="Functions//Function declaration" />
    <option name="function" hit="Functions//Function declaration" />
    <option name="functions" hit="Functions//Function declaration" />
    <option name="call" hit="Functions//Package-level function call" />
    <option name="function" hit="Functions//Package-level function call" />
    <option name="functions" hit="Functions//Package-level function call" />
    <option name="package-level" hit="Functions//Package-level function call" />
    <option name="call" hit="Functions//Suspend function call" />
    <option name="function" hit="Functions//Suspend function call" />
    <option name="functions" hit="Functions//Suspend function call" />
    <option name="suspend" hit="Functions//Suspend function call" />
    <option name="keywords" hit="Keywords//'val'" />
    <option name="val" hit="Keywords//'val'" />
    <option name="keywords" hit="Keywords//'var'" />
    <option name="var" hit="Keywords//'var'" />
    <option name="keyword" hit="Keywords//Keyword" />
    <option name="keywords" hit="Keywords//Keyword" />
    <option name="keywords" hit="Keywords//Modifier" />
    <option name="modifier" hit="Keywords//Modifier" />
    <option name="label" hit="Label" />
    <option name="argument" hit="Named argument" />
    <option name="named" hit="Named argument" />
    <option name="number" hit="Number" />
    <option name="default" hit="Parameters//Lambda expression default parameter" />
    <option name="expression" hit="Parameters//Lambda expression default parameter" />
    <option name="lambda" hit="Parameters//Lambda expression default parameter" />
    <option name="parameter" hit="Parameters//Lambda expression default parameter" />
    <option name="parameters" hit="Parameters//Lambda expression default parameter" />
    <option name="parameter" hit="Parameters//Parameter" />
    <option name="parameters" hit="Parameters//Parameter" />
    <option name="parameter" hit="Parameters//Type parameter" />
    <option name="parameters" hit="Parameters//Type parameter" />
    <option name="type" hit="Parameters//Type parameter" />
    <option name="and" hit="Properties and Variables//Android Extensions synthetic properties" />
    <option name="android" hit="Properties and Variables//Android Extensions synthetic properties" />
    <option name="extensions" hit="Properties and Variables//Android Extensions synthetic properties" />
    <option name="properties" hit="Properties and Variables//Android Extensions synthetic properties" />
    <option name="synthetic" hit="Properties and Variables//Android Extensions synthetic properties" />
    <option name="variables" hit="Properties and Variables//Android Extensions synthetic properties" />
    <option name="and" hit="Properties and Variables//Backing field variable" />
    <option name="backing" hit="Properties and Variables//Backing field variable" />
    <option name="field" hit="Properties and Variables//Backing field variable" />
    <option name="properties" hit="Properties and Variables//Backing field variable" />
    <option name="variable" hit="Properties and Variables//Backing field variable" />
    <option name="variables" hit="Properties and Variables//Backing field variable" />
    <option name="and" hit="Properties and Variables//Dynamic property" />
    <option name="dynamic" hit="Properties and Variables//Dynamic property" />
    <option name="properties" hit="Properties and Variables//Dynamic property" />
    <option name="property" hit="Properties and Variables//Dynamic property" />
    <option name="variables" hit="Properties and Variables//Dynamic property" />
    <option name="and" hit="Properties and Variables//Extension property" />
    <option name="extension" hit="Properties and Variables//Extension property" />
    <option name="properties" hit="Properties and Variables//Extension property" />
    <option name="property" hit="Properties and Variables//Extension property" />
    <option name="variables" hit="Properties and Variables//Extension property" />
    <option name="and" hit="Properties and Variables//Instance property" />
    <option name="instance" hit="Properties and Variables//Instance property" />
    <option name="properties" hit="Properties and Variables//Instance property" />
    <option name="property" hit="Properties and Variables//Instance property" />
    <option name="variables" hit="Properties and Variables//Instance property" />
    <option name="and" hit="Properties and Variables//Instance property with custom property declarations" />
    <option name="custom" hit="Properties and Variables//Instance property with custom property declarations" />
    <option name="declarations" hit="Properties and Variables//Instance property with custom property declarations" />
    <option name="instance" hit="Properties and Variables//Instance property with custom property declarations" />
    <option name="properties" hit="Properties and Variables//Instance property with custom property declarations" />
    <option name="property" hit="Properties and Variables//Instance property with custom property declarations" />
    <option name="variables" hit="Properties and Variables//Instance property with custom property declarations" />
    <option name="with" hit="Properties and Variables//Instance property with custom property declarations" />
    <option name="and" hit="Properties and Variables//Local variable or value" />
    <option name="local" hit="Properties and Variables//Local variable or value" />
    <option name="or" hit="Properties and Variables//Local variable or value" />
    <option name="properties" hit="Properties and Variables//Local variable or value" />
    <option name="value" hit="Properties and Variables//Local variable or value" />
    <option name="variable" hit="Properties and Variables//Local variable or value" />
    <option name="variables" hit="Properties and Variables//Local variable or value" />
    <option name="and" hit="Properties and Variables//Package-level property" />
    <option name="package-level" hit="Properties and Variables//Package-level property" />
    <option name="properties" hit="Properties and Variables//Package-level property" />
    <option name="property" hit="Properties and Variables//Package-level property" />
    <option name="variables" hit="Properties and Variables//Package-level property" />
    <option name="and" hit="Properties and Variables//Package-level property with custom property declarations" />
    <option name="custom" hit="Properties and Variables//Package-level property with custom property declarations" />
    <option name="declarations" hit="Properties and Variables//Package-level property with custom property declarations" />
    <option name="package-level" hit="Properties and Variables//Package-level property with custom property declarations" />
    <option name="properties" hit="Properties and Variables//Package-level property with custom property declarations" />
    <option name="property" hit="Properties and Variables//Package-level property with custom property declarations" />
    <option name="variables" hit="Properties and Variables//Package-level property with custom property declarations" />
    <option name="with" hit="Properties and Variables//Package-level property with custom property declarations" />
    <option name="and" hit="Properties and Variables//Synthetic extension property" />
    <option name="extension" hit="Properties and Variables//Synthetic extension property" />
    <option name="properties" hit="Properties and Variables//Synthetic extension property" />
    <option name="property" hit="Properties and Variables//Synthetic extension property" />
    <option name="synthetic" hit="Properties and Variables//Synthetic extension property" />
    <option name="variables" hit="Properties and Variables//Synthetic extension property" />
    <option name="and" hit="Properties and Variables//Var (mutable variable, parameter or property)" />
    <option name="mutable" hit="Properties and Variables//Var (mutable variable, parameter or property)" />
    <option name="or" hit="Properties and Variables//Var (mutable variable, parameter or property)" />
    <option name="parameter" hit="Properties and Variables//Var (mutable variable, parameter or property)" />
    <option name="properties" hit="Properties and Variables//Var (mutable variable, parameter or property)" />
    <option name="property" hit="Properties and Variables//Var (mutable variable, parameter or property)" />
    <option name="var" hit="Properties and Variables//Var (mutable variable, parameter or property)" />
    <option name="variable" hit="Properties and Variables//Var (mutable variable, parameter or property)" />
    <option name="variables" hit="Properties and Variables//Var (mutable variable, parameter or property)" />
    <option name="and" hit="Properties and Variables//Variable as function call" />
    <option name="as" hit="Properties and Variables//Variable as function call" />
    <option name="call" hit="Properties and Variables//Variable as function call" />
    <option name="function" hit="Properties and Variables//Variable as function call" />
    <option name="properties" hit="Properties and Variables//Variable as function call" />
    <option name="variable" hit="Properties and Variables//Variable as function call" />
    <option name="variables" hit="Properties and Variables//Variable as function call" />
    <option name="and" hit="Properties and Variables//Variable as function-like call" />
    <option name="as" hit="Properties and Variables//Variable as function-like call" />
    <option name="call" hit="Properties and Variables//Variable as function-like call" />
    <option name="function-like" hit="Properties and Variables//Variable as function-like call" />
    <option name="properties" hit="Properties and Variables//Variable as function-like call" />
    <option name="variable" hit="Properties and Variables//Variable as function-like call" />
    <option name="variables" hit="Properties and Variables//Variable as function-like call" />
    <option name="a" hit="Properties and Variables//Variables and values captured in a closure" />
    <option name="and" hit="Properties and Variables//Variables and values captured in a closure" />
    <option name="captured" hit="Properties and Variables//Variables and values captured in a closure" />
    <option name="closure" hit="Properties and Variables//Variables and values captured in a closure" />
    <option name="in" hit="Properties and Variables//Variables and values captured in a closure" />
    <option name="properties" hit="Properties and Variables//Variables and values captured in a closure" />
    <option name="values" hit="Properties and Variables//Variables and values captured in a closure" />
    <option name="variables" hit="Properties and Variables//Variables and values captured in a closure" />
    <option name="highlighting" hit="Semantic highlighting" />
    <option name="semantic" hit="Semantic highlighting" />
    <option name="constant" hit="Smart-casts//Smart constant" />
    <option name="smart" hit="Smart-casts//Smart constant" />
    <option name="smart-casts" hit="Smart-casts//Smart constant" />
    <option name="implicit" hit="Smart-casts//Smart-cast implicit receiver" />
    <option name="receiver" hit="Smart-casts//Smart-cast implicit receiver" />
    <option name="smart-cast" hit="Smart-casts//Smart-cast implicit receiver" />
    <option name="smart-casts" hit="Smart-casts//Smart-cast implicit receiver" />
    <option name="smart-cast" hit="Smart-casts//Smart-cast value" />
    <option name="smart-casts" hit="Smart-casts//Smart-cast value" />
    <option name="value" hit="Smart-casts//Smart-cast value" />
    <option name="escape" hit="String//Escape Sequence//Invalid" />
    <option name="invalid" hit="String//Escape Sequence//Invalid" />
    <option name="sequence" hit="String//Escape Sequence//Invalid" />
    <option name="string" hit="String//Escape Sequence//Invalid" />
    <option name="and" hit="String//Escape in string and template braces" />
    <option name="braces" hit="String//Escape in string and template braces" />
    <option name="escape" hit="String//Escape in string and template braces" />
    <option name="in" hit="String//Escape in string and template braces" />
    <option name="string" hit="String//Escape in string and template braces" />
    <option name="template" hit="String//Escape in string and template braces" />
    <option name="string" hit="String//String text" />
    <option name="text" hit="String//String text" />
  </configurable>
  <configurable id="preferences.sourceCode.Kotlin" configurable_name="Kotlin">
    <option name="" path="Imports" hit=" names used" />
    <option name="names" path="Imports" hit=" names used" />
    <option name="used" path="Imports" hit=" names used" />
    <option name="a" path="Code Generation" hit="Add a space at line comment start" />
    <option name="add" path="Code Generation" hit="Add a space at line comment start" />
    <option name="at" path="Code Generation" hit="Add a space at line comment start" />
    <option name="comment" path="Code Generation" hit="Add a space at line comment start" />
    <option name="line" path="Code Generation" hit="Add a space at line comment start" />
    <option name="space" path="Code Generation" hit="Add a space at line comment start" />
    <option name="start" path="Code Generation" hit="Add a space at line comment start" />
    <option name="add" path="Code Generation" hit="Add spaces around block comments" />
    <option name="around" path="Code Generation" hit="Add spaces around block comments" />
    <option name="block" path="Code Generation" hit="Add spaces around block comments" />
    <option name="comments" path="Code Generation" hit="Add spaces around block comments" />
    <option name="spaces" path="Code Generation" hit="Add spaces around block comments" />
    <option name="after" path="Blank Lines" hit="After class header:" />
    <option name="class" path="Blank Lines" hit="After class header:" />
    <option name="header" path="Blank Lines" hit="After class header:" />
    <option name="around" path="Blank Lines" hit="Around 'when' branches with {}" />
    <option name="branches" path="Blank Lines" hit="Around 'when' branches with {}" />
    <option name="when" path="Blank Lines" hit="Around 'when' branches with {}" />
    <option name="with" path="Blank Lines" hit="Around 'when' branches with {}" />
    <option name="before" path="Blank Lines" hit="Before '}':" />
    <option name="annotation" path="Blank Lines" hit="Before declaration with comment or annotation" />
    <option name="before" path="Blank Lines" hit="Before declaration with comment or annotation" />
    <option name="comment" path="Blank Lines" hit="Before declaration with comment or annotation" />
    <option name="declaration" path="Blank Lines" hit="Before declaration with comment or annotation" />
    <option name="or" path="Blank Lines" hit="Before declaration with comment or annotation" />
    <option name="with" path="Blank Lines" hit="Before declaration with comment or annotation" />
    <option name="blank" path="Blank Lines" hit="Blank Lines" />
    <option name="lines" path="Blank Lines" hit="Blank Lines" />
    <option name="at" path="Code Generation" hit="Block comment at first column" />
    <option name="block" path="Code Generation" hit="Block comment at first column" />
    <option name="column" path="Code Generation" hit="Block comment at first column" />
    <option name="comment" path="Code Generation" hit="Block comment at first column" />
    <option name="first" path="Code Generation" hit="Block comment at first column" />
    <option name="code" path="Code Generation" hit="Code Generation" />
    <option name="generation" path="Code Generation" hit="Code Generation" />
    <option name="code" path="Code Generation" hit="Comment Code" />
    <option name="comment" path="Code Generation" hit="Comment Code" />
    <option name="continuation" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="indent" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="disable" hit="Disable" />
    <option name="enforce" path="Code Generation" hit="Enforce on reformat" />
    <option name="on" path="Code Generation" hit="Enforce on reformat" />
    <option name="reformat" path="Code Generation" hit="Enforce on reformat" />
    <option name="import" path="Imports" hit="Import Layout" />
    <option name="layout" path="Imports" hit="Import Layout" />
    <option name="aliases" path="Imports" hit="Import aliases separately" />
    <option name="import" path="Imports" hit="Import aliases separately" />
    <option name="separately" path="Imports" hit="Import aliases separately" />
    <option name="imports" path="Imports" hit="Imports" />
    <option name="code" path="Blank Lines" hit="In code:" />
    <option name="in" path="Blank Lines" hit="In code:" />
    <option name="declarations" path="Blank Lines" hit="In declarations:" />
    <option name="in" path="Blank Lines" hit="In declarations:" />
    <option name="indent" path="Tabs and Indents" hit="Indent:" />
    <option name="classes" path="Imports" hit="Insert imports for nested classes" />
    <option name="for" path="Imports" hit="Insert imports for nested classes" />
    <option name="imports" path="Imports" hit="Insert imports for nested classes" />
    <option name="insert" path="Imports" hit="Insert imports for nested classes" />
    <option name="nested" path="Imports" hit="Insert imports for nested classes" />
    <option name="and" path="Imports" hit="Java Statics and Enum Members" />
    <option name="enum" path="Imports" hit="Java Statics and Enum Members" />
    <option name="java" path="Imports" hit="Java Statics and Enum Members" />
    <option name="members" path="Imports" hit="Java Statics and Enum Members" />
    <option name="statics" path="Imports" hit="Java Statics and Enum Members" />
    <option name="empty" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="indents" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="keep" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="lines" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="on" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="blank" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="keep" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="lines" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="maximum" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="kotlin" hit="Kotlin" />
    <option name="coding" path="Load/Save" hit="Kotlin Coding Conventions" />
    <option name="conventions" path="Load/Save" hit="Kotlin Coding Conventions" />
    <option name="kotlin" path="Load/Save" hit="Kotlin Coding Conventions" />
    <option name="codestyle" path="Load/Save" hit="Kotlin obsolete IntelliJ IDEA codestyle" />
    <option name="idea" path="Load/Save" hit="Kotlin obsolete IntelliJ IDEA codestyle" />
    <option name="intellij" path="Load/Save" hit="Kotlin obsolete IntelliJ IDEA codestyle" />
    <option name="kotlin" path="Load/Save" hit="Kotlin obsolete IntelliJ IDEA codestyle" />
    <option name="obsolete" path="Load/Save" hit="Kotlin obsolete IntelliJ IDEA codestyle" />
    <option name="at" path="Code Generation" hit="Line comment at first column" />
    <option name="column" path="Code Generation" hit="Line comment at first column" />
    <option name="comment" path="Code Generation" hit="Line comment at first column" />
    <option name="first" path="Code Generation" hit="Line comment at first column" />
    <option name="line" path="Code Generation" hit="Line comment at first column" />
    <option name="load" path="Load/Save" hit="Load/Save" />
    <option name="save" path="Load/Save" hit="Load/Save" />
    <option name="loading" hit="Loading..." />
    <option name="blank" path="Blank Lines" hit="Minimum blank lines" />
    <option name="lines" path="Blank Lines" hit="Minimum blank lines" />
    <option name="minimum" path="Blank Lines" hit="Minimum blank lines" />
    <option name="other" path="Imports" hit="Other" />
    <option name="import" path="Imports" hit="Packages to Use Import with '*'" />
    <option name="packages" path="Imports" hit="Packages to Use Import with '*'" />
    <option name="to" path="Imports" hit="Packages to Use Import with '*'" />
    <option name="use" path="Imports" hit="Packages to Use Import with '*'" />
    <option name="with" path="Imports" hit="Packages to Use Import with '*'" />
    <option name="scheme" hit="Scheme:" />
    <option name="from" hit="Set from..." />
    <option name="set" hit="Set from..." />
    <option name="smart" path="Tabs and Indents" hit="Smart tabs" />
    <option name="tabs" path="Tabs and Indents" hit="Smart tabs" />
    <option name="spaces" path="Spaces" hit="Spaces" />
    <option name="size" path="Tabs and Indents" hit="Tab size:" />
    <option name="tab" path="Tabs and Indents" hit="Tab size:" />
    <option name="and" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="indents" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="tabs" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="symbols" path="Imports" hit="Top-Level Symbols" />
    <option name="top-level" path="Imports" hit="Top-Level Symbols" />
    <option name="comma" path="Other" hit="Trailing Comma" />
    <option name="trailing" path="Other" hit="Trailing Comma" />
    <option name="defaults" path="Load/Save" hit="Use defaults from:" />
    <option name="from" path="Load/Save" hit="Use defaults from:" />
    <option name="use" path="Load/Save" hit="Use defaults from:" />
    <option name="import" path="Imports" hit="Use import with '*'" />
    <option name="use" path="Imports" hit="Use import with '*'" />
    <option name="with" path="Imports" hit="Use import with '*'" />
    <option name="at" path="Imports" hit="Use import with '*' when at least" />
    <option name="import" path="Imports" hit="Use import with '*' when at least" />
    <option name="least" path="Imports" hit="Use import with '*' when at least" />
    <option name="use" path="Imports" hit="Use import with '*' when at least" />
    <option name="when" path="Imports" hit="Use import with '*' when at least" />
    <option name="with" path="Imports" hit="Use import with '*' when at least" />
    <option name="import" path="Imports" hit="Use single name import" />
    <option name="name" path="Imports" hit="Use single name import" />
    <option name="single" path="Imports" hit="Use single name import" />
    <option name="use" path="Imports" hit="Use single name import" />
    <option name="character" path="Tabs and Indents" hit="Use tab character" />
    <option name="tab" path="Tabs and Indents" hit="Use tab character" />
    <option name="use" path="Tabs and Indents" hit="Use tab character" />
    <option name="comma" path="Other" hit="Use trailing comma" />
    <option name="trailing" path="Other" hit="Use trailing comma" />
    <option name="use" path="Other" hit="Use trailing comma" />
    <option name="and" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="braces" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="wrapping" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="line" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="next" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="on" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="signs" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="and" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="line" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="next" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="on" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="signs" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="catch" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="" path="Spaces" hit="'catch' parentheses" />
    <option name="catch" path="Spaces" hit="'catch' parentheses" />
    <option name="parentheses" path="Spaces" hit="'catch' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="do" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="while" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="else" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="finally" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="" path="Spaces" hit="'for' parentheses" />
    <option name="for" path="Spaces" hit="'for' parentheses" />
    <option name="parentheses" path="Spaces" hit="'for' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="for" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="" path="Spaces" hit="'if' parentheses" />
    <option name="if" path="Spaces" hit="'if' parentheses" />
    <option name="parentheses" path="Spaces" hit="'if' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="if" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="switch" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="" path="Wrapping and Braces" hit="'try' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'try' statement" />
    <option name="try" path="Wrapping and Braces" hit="'try' statement" />
    <option name="" path="Wrapping and Braces" hit="'try-with-resources'" />
    <option name="try-with-resources" path="Wrapping and Braces" hit="'try-with-resources'" />
    <option name="" path="Spaces" hit="'when' parentheses" />
    <option name="parentheses" path="Spaces" hit="'when' parentheses" />
    <option name="when" path="Spaces" hit="'when' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="while" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="" path="Spaces" hit="'while' parentheses" />
    <option name="parentheses" path="Spaces" hit="'while' parentheses" />
    <option name="while" path="Spaces" hit="'while' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="while" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="+" path="Spaces" hit="Additive operators (+, -)" />
    <option name="-" path="Spaces" hit="Additive operators (+, -)" />
    <option name="additive" path="Spaces" hit="Additive operators (+, -)" />
    <option name="operators" path="Spaces" hit="Additive operators (+, -)" />
    <option name="after" path="Spaces" hit="After colon in new type definition" />
    <option name="colon" path="Spaces" hit="After colon in new type definition" />
    <option name="definition" path="Spaces" hit="After colon in new type definition" />
    <option name="in" path="Spaces" hit="After colon in new type definition" />
    <option name="new" path="Spaces" hit="After colon in new type definition" />
    <option name="type" path="Spaces" hit="After colon in new type definition" />
    <option name="after" path="Spaces" hit="After colon, before declaration type" />
    <option name="before" path="Spaces" hit="After colon, before declaration type" />
    <option name="colon" path="Spaces" hit="After colon, before declaration type" />
    <option name="declaration" path="Spaces" hit="After colon, before declaration type" />
    <option name="type" path="Spaces" hit="After colon, before declaration type" />
    <option name="after" path="Spaces" hit="After comma" />
    <option name="comma" path="Spaces" hit="After comma" />
    <option name="align" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="method" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="start" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="throws" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="to" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="align" path="Wrapping and Braces" hit="Align 'when' branches in columns" />
    <option name="branches" path="Wrapping and Braces" hit="Align 'when' branches in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align 'when' branches in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align 'when' branches in columns" />
    <option name="when" path="Wrapping and Braces" hit="Align 'when' branches in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="assignments" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="fields" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="multiline" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="parenthesised" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="when" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="align" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="methods" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="simple" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="variables" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="multiline" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="when" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="around" path="Spaces" hit="Around arrow in &quot;when&quot; clause" />
    <option name="arrow" path="Spaces" hit="Around arrow in &quot;when&quot; clause" />
    <option name="clause" path="Spaces" hit="Around arrow in &quot;when&quot; clause" />
    <option name="in" path="Spaces" hit="Around arrow in &quot;when&quot; clause" />
    <option name="when" path="Spaces" hit="Around arrow in &quot;when&quot; clause" />
    <option name="around" path="Spaces" hit="Around arrow in function types" />
    <option name="arrow" path="Spaces" hit="Around arrow in function types" />
    <option name="function" path="Spaces" hit="Around arrow in function types" />
    <option name="in" path="Spaces" hit="Around arrow in function types" />
    <option name="types" path="Spaces" hit="Around arrow in function types" />
    <option name="around" path="Spaces" hit="Around operators" />
    <option name="operators" path="Spaces" hit="Around operators" />
    <option name="array" path="Wrapping and Braces" hit="Array initializer" />
    <option name="initializer" path="Wrapping and Braces" hit="Array initializer" />
    <option name="assert" path="Wrapping and Braces" hit="Assert statement" />
    <option name="statement" path="Wrapping and Braces" hit="Assert statement" />
    <option name="+" path="Spaces" hit="Assignment operators (=, +=, ...)" />
    <option name="assignment" path="Spaces" hit="Assignment operators (=, +=, ...)" />
    <option name="operators" path="Spaces" hit="Assignment operators (=, +=, ...)" />
    <option name="assignment" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="line" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="next" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="on" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="sign" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="assignment" path="Wrapping and Braces" hit="Assignment statement" />
    <option name="statement" path="Wrapping and Braces" hit="Assignment statement" />
    <option name="before" path="Spaces" hit="Before colon in new type definition" />
    <option name="colon" path="Spaces" hit="Before colon in new type definition" />
    <option name="definition" path="Spaces" hit="Before colon in new type definition" />
    <option name="in" path="Spaces" hit="Before colon in new type definition" />
    <option name="new" path="Spaces" hit="Before colon in new type definition" />
    <option name="type" path="Spaces" hit="Before colon in new type definition" />
    <option name="after" path="Spaces" hit="Before colon, after declaration name" />
    <option name="before" path="Spaces" hit="Before colon, after declaration name" />
    <option name="colon" path="Spaces" hit="Before colon, after declaration name" />
    <option name="declaration" path="Spaces" hit="Before colon, after declaration name" />
    <option name="name" path="Spaces" hit="Before colon, after declaration name" />
    <option name="before" path="Spaces" hit="Before comma" />
    <option name="comma" path="Spaces" hit="Before comma" />
    <option name="arrow" path="Spaces" hit="Before lambda arrow" />
    <option name="before" path="Spaces" hit="Before lambda arrow" />
    <option name="lambda" path="Spaces" hit="Before lambda arrow" />
    <option name="before" path="Spaces" hit="Before parentheses" />
    <option name="parentheses" path="Spaces" hit="Before parentheses" />
    <option name="binary" path="Wrapping and Braces" hit="Binary expressions" />
    <option name="expressions" path="Wrapping and Braces" hit="Binary expressions" />
    <option name="braces" path="Wrapping and Braces" hit="Braces placement" />
    <option name="placement" path="Wrapping and Braces" hit="Braces placement" />
    <option name="builder" path="Wrapping and Braces" hit="Builder methods" />
    <option name="methods" path="Wrapping and Braces" hit="Builder methods" />
    <option name="calls" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="chained" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="method" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="annotations" path="Wrapping and Braces" hit="Class annotations" />
    <option name="class" path="Wrapping and Braces" hit="Class annotations" />
    <option name="at" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="column" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="comment" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="first" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="comments" path="Wrapping and Braces" hit="Comments" />
    <option name="control" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="in" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="line" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="one" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="statement" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="a" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="case" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="each" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="line" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="on" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="separate" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="elvis" path="Wrapping and Braces" hit="Elvis expressions" />
    <option name="expressions" path="Wrapping and Braces" hit="Elvis expressions" />
    <option name="ensure" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="exceeded" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="is" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="margin" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="not" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="right" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="constants" path="Wrapping and Braces" hit="Enum constants" />
    <option name="enum" path="Wrapping and Braces" hit="Enum constants" />
    <option name="equality" path="Spaces" hit="Equality operators (==, !=)" />
    <option name="operators" path="Spaces" hit="Equality operators (==, !=)" />
    <option name="body" path="Wrapping and Braces" hit="Expression body functions" />
    <option name="expression" path="Wrapping and Braces" hit="Expression body functions" />
    <option name="functions" path="Wrapping and Braces" hit="Expression body functions" />
    <option name="extends" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="implements" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="keyword" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="permits" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="extends" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="implements" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="list" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="permits" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="annotations" path="Wrapping and Braces" hit="Field annotations" />
    <option name="field" path="Wrapping and Braces" hit="Field annotations" />
    <option name="braces" path="Wrapping and Braces" hit="Force braces" />
    <option name="force" path="Wrapping and Braces" hit="Force braces" />
    <option name="declarations" path="Wrapping and Braces" hit="Group declarations" />
    <option name="group" path="Wrapping and Braces" hit="Group declarations" />
    <option name="at" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="hard" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="wrap" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="class" path="Wrapping and Braces" hit="In class declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In class declaration" />
    <option name="in" path="Wrapping and Braces" hit="In class declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="in" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="lambda" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In method declaration" />
    <option name="in" path="Wrapping and Braces" hit="In method declaration" />
    <option name="method" path="Wrapping and Braces" hit="In method declaration" />
    <option name="in" path="Spaces" hit="In simple one line methods" />
    <option name="line" path="Spaces" hit="In simple one line methods" />
    <option name="methods" path="Spaces" hit="In simple one line methods" />
    <option name="one" path="Spaces" hit="In simple one line methods" />
    <option name="simple" path="Spaces" hit="In simple one line methods" />
    <option name="break" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="case" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="from" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="branches" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="case" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="builder" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="indents" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="keep" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="methods" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="keep" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="reformatting" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="when" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="breaks" path="Wrapping and Braces" hit="Line breaks" />
    <option name="line" path="Wrapping and Braces" hit="Line breaks" />
    <option name="annotations" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="local" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="variable" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="logical" path="Spaces" hit="Logical operators (&amp;&amp;, ||)" />
    <option name="operators" path="Spaces" hit="Logical operators (&amp;&amp;, ||)" />
    <option name="annotations" path="Wrapping and Braces" hit="Method annotations" />
    <option name="method" path="Wrapping and Braces" hit="Method annotations" />
    <option name="arguments" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="call" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="method" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="declaration" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="method" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="parameters" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="method" path="Wrapping and Braces" hit="Method parentheses" />
    <option name="parentheses" path="Wrapping and Braces" hit="Method parentheses" />
    <option name="list" path="Wrapping and Braces" hit="Modifier list" />
    <option name="modifier" path="Wrapping and Braces" hit="Modifier list" />
    <option name="expressions" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="in" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="line" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="multiple" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="one" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="multiplicative" path="Spaces" hit="Multiplicative operators (*, /, %)" />
    <option name="operators" path="Spaces" hit="Multiplicative operators (*, /, %)" />
    <option name="after" path="Wrapping and Braces" hit="New line after '('" />
    <option name="line" path="Wrapping and Braces" hit="New line after '('" />
    <option name="new" path="Wrapping and Braces" hit="New line after '('" />
    <option name="after" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="line" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="new" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="after" path="Wrapping and Braces" hit="New line after multiline entry" />
    <option name="entry" path="Wrapping and Braces" hit="New line after multiline entry" />
    <option name="line" path="Wrapping and Braces" hit="New line after multiline entry" />
    <option name="multiline" path="Wrapping and Braces" hit="New line after multiline entry" />
    <option name="new" path="Wrapping and Braces" hit="New line after multiline entry" />
    <option name="line" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="next" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="on" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="operation" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="sign" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="other" path="Wrapping and Braces" hit="Other" />
    <option name="annotations" path="Wrapping and Braces" hit="Parameter annotations" />
    <option name="parameter" path="Wrapping and Braces" hit="Parameter annotations" />
    <option name="line" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="new" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="on" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="place" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="line" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="new" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="on" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="place" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="brace" path="Wrapping and Braces" hit="Put left brace on new line" />
    <option name="left" path="Wrapping and Braces" hit="Put left brace on new line" />
    <option name="line" path="Wrapping and Braces" hit="Put left brace on new line" />
    <option name="new" path="Wrapping and Braces" hit="Put left brace on new line" />
    <option name="on" path="Wrapping and Braces" hit="Put left brace on new line" />
    <option name="put" path="Wrapping and Braces" hit="Put left brace on new line" />
    <option name="operators" path="Spaces" hit="Range operators (.., ..&lt;)" />
    <option name="range" path="Spaces" hit="Range operators (.., ..&lt;)" />
    <option name="operators" path="Spaces" hit="Relational operators (&lt;, &gt;, &lt;=, &gt;=)" />
    <option name="relational" path="Spaces" hit="Relational operators (&lt;, &gt;, &lt;=, &gt;=)" />
    <option name="blocks" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="classes" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="lambdas" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="methods" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="else" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="if" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="special" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="treatment" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="call" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="chain" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="over" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="priority" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="take" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="wrapping" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="operation" path="Wrapping and Braces" hit="Ternary operation" />
    <option name="ternary" path="Wrapping and Braces" hit="Ternary operation" />
    <option name="keyword" path="Wrapping and Braces" hit="Throws keyword" />
    <option name="throws" path="Wrapping and Braces" hit="Throws keyword" />
    <option name="list" path="Wrapping and Braces" hit="Throws list" />
    <option name="throws" path="Wrapping and Braces" hit="Throws list" />
    <option name="+" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="++" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="-" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="--" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="operators" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="unary" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="continuation" path="Wrapping and Braces" hit="Use continuation indent" />
    <option name="indent" path="Wrapping and Braces" hit="Use continuation indent" />
    <option name="use" path="Wrapping and Braces" hit="Use continuation indent" />
    <option name="conditions" path="Wrapping and Braces" hit="Use continuation indent in conditions" />
    <option name="continuation" path="Wrapping and Braces" hit="Use continuation indent in conditions" />
    <option name="in" path="Wrapping and Braces" hit="Use continuation indent in conditions" />
    <option name="indent" path="Wrapping and Braces" hit="Use continuation indent in conditions" />
    <option name="use" path="Wrapping and Braces" hit="Use continuation indent in conditions" />
    <option name="guides" path="Wrapping and Braces" hit="Visual guides" />
    <option name="visual" path="Wrapping and Braces" hit="Visual guides" />
    <option name="after" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="list" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="modifier" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="at" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="margin" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="right" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="call" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="first" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="on" path="Wrapping and Braces" hit="Wrap on typing" />
    <option name="typing" path="Wrapping and Braces" hit="Wrap on typing" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap on typing" />
  </configurable>
</options>