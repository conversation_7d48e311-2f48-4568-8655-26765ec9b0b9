<options>
  <configurable id="fileTemplates" configurable_name="File and Code Templates">
    <option name="afterclass" hit="TestNG AfterClass Method" />
    <option name="method" hit="TestNG AfterClass Method" />
    <option name="testng" hit="TestNG AfterClass Method" />
    <option name="beforeclass" hit="TestNG BeforeClass Method" />
    <option name="method" hit="TestNG BeforeClass Method" />
    <option name="testng" hit="TestNG BeforeClass Method" />
    <option name="method" hit="TestNG Parameters Method" />
    <option name="parameters" hit="TestNG Parameters Method" />
    <option name="testng" hit="TestNG Parameters Method" />
    <option name="method" hit="TestNG SetUp Method" />
    <option name="setup" hit="TestNG SetUp Method" />
    <option name="testng" hit="TestNG SetUp Method" />
    <option name="method" hit="TestNG TearDown Method" />
    <option name="teardown" hit="TestNG TearDown Method" />
    <option name="testng" hit="TestNG TearDown Method" />
    <option name="class" hit="TestNG Test Class" />
    <option name="test" hit="TestNG Test Class" />
    <option name="testng" hit="TestNG Test Class" />
    <option name="method" hit="TestNG Test Method" />
    <option name="test" hit="TestNG Test Method" />
    <option name="testng" hit="TestNG Test Method" />
  </configurable>
</options>