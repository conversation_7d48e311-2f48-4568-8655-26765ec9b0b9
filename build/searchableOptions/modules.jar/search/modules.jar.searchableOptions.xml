<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="ab" path="ActionManager" hit="AB Experiment Demo Action" />
    <option name="action" path="ActionManager" hit="AB Experiment Demo Action" />
    <option name="demo" path="ActionManager" hit="AB Experiment Demo Action" />
    <option name="experiment" path="ActionManager" hit="AB Experiment Demo Action" />
    <option name="clear" path="ActionManager" hit="Clear" />
    <option name="clear" path="ActionManager" hit="Clear output" />
    <option name="output" path="ActionManager" hit="Clear output" />
    <option name="all" path="ActionManager" hit="Collapse All Cell Outputs" />
    <option name="cell" path="ActionManager" hit="Collapse All Cell Outputs" />
    <option name="collapse" path="ActionManager" hit="Collapse All Cell Outputs" />
    <option name="outputs" path="ActionManager" hit="Collapse All Cell Outputs" />
    <option name="all" path="ActionManager" hit="Collapse All Notebook Outputs" />
    <option name="collapse" path="ActionManager" hit="Collapse All Notebook Outputs" />
    <option name="notebook" path="ActionManager" hit="Collapse All Notebook Outputs" />
    <option name="outputs" path="ActionManager" hit="Collapse All Notebook Outputs" />
    <option name="cells" path="ActionManager" hit="Collapse Outputs of Selected Cells" />
    <option name="collapse" path="ActionManager" hit="Collapse Outputs of Selected Cells" />
    <option name="of" path="ActionManager" hit="Collapse Outputs of Selected Cells" />
    <option name="outputs" path="ActionManager" hit="Collapse Outputs of Selected Cells" />
    <option name="selected" path="ActionManager" hit="Collapse Outputs of Selected Cells" />
    <option name="collapse" path="ActionManager" hit="Collapse Selected Output" />
    <option name="output" path="ActionManager" hit="Collapse Selected Output" />
    <option name="selected" path="ActionManager" hit="Collapse Selected Output" />
    <option name="clipboard" path="ActionManager" hit="Copy image to clipboard" />
    <option name="copy" path="ActionManager" hit="Copy image to clipboard" />
    <option name="image" path="ActionManager" hit="Copy image to clipboard" />
    <option name="to" path="ActionManager" hit="Copy image to clipboard" />
    <option name="clipboard" path="ActionManager" hit="Copy to Clipboard" />
    <option name="copy" path="ActionManager" hit="Copy to Clipboard" />
    <option name="to" path="ActionManager" hit="Copy to Clipboard" />
    <option name="dtd" path="ActionManager" hit="Generate DTD from XML File" />
    <option name="file" path="ActionManager" hit="Generate DTD from XML File" />
    <option name="from" path="ActionManager" hit="Generate DTD from XML File" />
    <option name="generate" path="ActionManager" hit="Generate DTD from XML File" />
    <option name="xml" path="ActionManager" hit="Generate DTD from XML File" />
    <option name="document" path="ActionManager" hit="Generate XML Document from XSD Schema..." />
    <option name="from" path="ActionManager" hit="Generate XML Document from XSD Schema..." />
    <option name="generate" path="ActionManager" hit="Generate XML Document from XSD Schema..." />
    <option name="schema" path="ActionManager" hit="Generate XML Document from XSD Schema..." />
    <option name="xml" path="ActionManager" hit="Generate XML Document from XSD Schema..." />
    <option name="xsd" path="ActionManager" hit="Generate XML Document from XSD Schema..." />
    <option name="file" path="ActionManager" hit="Generate XSD Schema from XML File..." />
    <option name="from" path="ActionManager" hit="Generate XSD Schema from XML File..." />
    <option name="generate" path="ActionManager" hit="Generate XSD Schema from XML File..." />
    <option name="schema" path="ActionManager" hit="Generate XSD Schema from XML File..." />
    <option name="xml" path="ActionManager" hit="Generate XSD Schema from XML File..." />
    <option name="xsd" path="ActionManager" hit="Generate XSD Schema from XML File..." />
    <option name="as" path="ActionManager" hit="Save As" />
    <option name="save" path="ActionManager" hit="Save As" />
    <option name="as" path="ActionManager" hit="Save as" />
    <option name="save" path="ActionManager" hit="Save as" />
    <option name="new" path="ActionManager" hit="Start New UI Onboarding" />
    <option name="onboarding" path="ActionManager" hit="Start New UI Onboarding" />
    <option name="start" path="ActionManager" hit="Start New UI Onboarding" />
    <option name="ui" path="ActionManager" hit="Start New UI Onboarding" />
    <option name="settings" path="ActionManager" hit="Test Transfer Settings" />
    <option name="test" path="ActionManager" hit="Test Transfer Settings" />
    <option name="transfer" path="ActionManager" hit="Test Transfer Settings" />
    <option name="validate" path="ActionManager" hit="Validate" />
    <option name="current" path="ActionManager" hit="Validate the current XML file" />
    <option name="file" path="ActionManager" hit="Validate the current XML file" />
    <option name="the" path="ActionManager" hit="Validate the current XML file" />
    <option name="validate" path="ActionManager" hit="Validate the current XML file" />
    <option name="xml" path="ActionManager" hit="Validate the current XML file" />
    <option name="" path="ActionManager" hit="[INTERNAL] 3 Transfer: ONE" />
    <option name="3" path="ActionManager" hit="[INTERNAL] 3 Transfer: ONE" />
    <option name="internal" path="ActionManager" hit="[INTERNAL] 3 Transfer: ONE" />
    <option name="one" path="ActionManager" hit="[INTERNAL] 3 Transfer: ONE" />
    <option name="transfer" path="ActionManager" hit="[INTERNAL] 3 Transfer: ONE" />
    <option name="" path="ActionManager" hit="[INTERNAL] 4 Transfer: Error Dispatcher Dialog Single" />
    <option name="4" path="ActionManager" hit="[INTERNAL] 4 Transfer: Error Dispatcher Dialog Single" />
    <option name="dialog" path="ActionManager" hit="[INTERNAL] 4 Transfer: Error Dispatcher Dialog Single" />
    <option name="dispatcher" path="ActionManager" hit="[INTERNAL] 4 Transfer: Error Dispatcher Dialog Single" />
    <option name="error" path="ActionManager" hit="[INTERNAL] 4 Transfer: Error Dispatcher Dialog Single" />
    <option name="internal" path="ActionManager" hit="[INTERNAL] 4 Transfer: Error Dispatcher Dialog Single" />
    <option name="single" path="ActionManager" hit="[INTERNAL] 4 Transfer: Error Dispatcher Dialog Single" />
    <option name="transfer" path="ActionManager" hit="[INTERNAL] 4 Transfer: Error Dispatcher Dialog Single" />
    <option name="" path="ActionManager" hit="[INTERNAL] 5 Transfer: StartWizardAction" />
    <option name="5" path="ActionManager" hit="[INTERNAL] 5 Transfer: StartWizardAction" />
    <option name="internal" path="ActionManager" hit="[INTERNAL] 5 Transfer: StartWizardAction" />
    <option name="startwizardaction" path="ActionManager" hit="[INTERNAL] 5 Transfer: StartWizardAction" />
    <option name="transfer" path="ActionManager" hit="[INTERNAL] 5 Transfer: StartWizardAction" />
  </configurable>
  <configurable id="copyright.filetypes.SVG" configurable_name="SVG">
    <option name="a" hit="Add blank line after" />
    <option name="after" hit="Add blank line after" />
    <option name="blank" hit="Add blank line after" />
    <option name="dd" hit="Add blank line after" />
    <option name="line" hit="Add blank line after" />
    <option name="add" hit="Add blank line before" />
    <option name="before" hit="Add blank line before" />
    <option name="blank" hit="Add blank line before" />
    <option name="line" hit="Add blank line before" />
    <option name="afte" hit="After other comments" />
    <option name="comments" hit="After other comments" />
    <option name="other" hit="After other comments" />
    <option name="r" hit="After other comments" />
    <option name="bef" hit="Before other comments" />
    <option name="comments" hit="Before other comments" />
    <option name="ore" hit="Before other comments" />
    <option name="other" hit="Before other comments" />
    <option name="borders" hit="Borders" />
    <option name="box" hit="Box" />
    <option name="comment" hit="Comment Type" />
    <option name="type" hit="Comment Type" />
    <option name="length" hit="Length:" />
    <option name="copyright" hit="No copyright" />
    <option name="no" hit="No copyright" />
    <option name="each" hit="Prefix each line" />
    <option name="line" hit="Prefix each line" />
    <option name="prefix" hit="Prefix each line" />
    <option name="location" hit="Relative Location" />
    <option name="relative" hit="Relative Location" />
    <option name="svg" hit="SVG" />
    <option name="after" hit="Separator after" />
    <option name="separator" hit="Separator after" />
    <option name="before" hit="Separator before" />
    <option name="separator" hit="Separator before" />
    <option name="separator" hit="Separator:" />
    <option name="block" hit="Use block comment" />
    <option name="comment" hit="Use block comment" />
    <option name="use" hit="Use block comment" />
    <option name="custom" hit="Use custom formatting options" />
    <option name="formatting" hit="Use custom formatting options" />
    <option name="options" hit="Use custom formatting options" />
    <option name="use" hit="Use custom formatting options" />
    <option name="default" hit="Use default settings" />
    <option name="settings" hit="Use default settings" />
    <option name="use" hit="Use default settings" />
    <option name="comment" hit="Use line comment" />
    <option name="line" hit="Use line comment" />
    <option name="use" hit="Use line comment" />
  </configurable>
</options>