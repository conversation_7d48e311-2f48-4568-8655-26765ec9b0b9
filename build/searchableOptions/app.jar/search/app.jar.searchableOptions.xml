<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="add" path="ActionManager" hit="Add" />
    <option name="add" path="ActionManager" hit="Add to VCS" />
    <option name="to" path="ActionManager" hit="Add to VCS" />
    <option name="vcs" path="ActionManager" hit="Add to VCS" />
    <option name="already" path="ActionManager" hit="Already Unshelved" />
    <option name="unshelved" path="ActionManager" hit="Already Unshelved" />
    <option name="amend" path="ActionManager" hit="Amend Commit" />
    <option name="commit" path="ActionManager" hit="Amend Commit" />
    <option name="annotate" path="ActionManager" hit="Annotate" />
    <option name="annotate" path="ActionManager" hit="Annotate file" />
    <option name="file" path="ActionManager" hit="Annotate file" />
    <option name="annotate" path="ActionManager" hit="Annotate selected revision" />
    <option name="revision" path="ActionManager" hit="Annotate selected revision" />
    <option name="selected" path="ActionManager" hit="Annotate selected revision" />
    <option name="annotated" path="ActionManager" hit="Annotated Line" />
    <option name="line" path="ActionManager" hit="Annotated Line" />
    <option name="annotated" path="ActionManager" hit="Annotated Line Operations" />
    <option name="line" path="ActionManager" hit="Annotated Line Operations" />
    <option name="operations" path="ActionManager" hit="Annotated Line Operations" />
    <option name="apply" path="ActionManager" hit="Apply" />
    <option name="apply" path="ActionManager" hit="Apply Patch from Clipboard..." />
    <option name="clipboard" path="ActionManager" hit="Apply Patch from Clipboard..." />
    <option name="from" path="ActionManager" hit="Apply Patch from Clipboard..." />
    <option name="patch" path="ActionManager" hit="Apply Patch from Clipboard..." />
    <option name="apply" path="ActionManager" hit="Apply Patch..." />
    <option name="patch" path="ActionManager" hit="Apply Patch..." />
    <option name="a" path="ActionManager" hit="Apply a patch to the sources of the project" />
    <option name="apply" path="ActionManager" hit="Apply a patch to the sources of the project" />
    <option name="of" path="ActionManager" hit="Apply a patch to the sources of the project" />
    <option name="patch" path="ActionManager" hit="Apply a patch to the sources of the project" />
    <option name="project" path="ActionManager" hit="Apply a patch to the sources of the project" />
    <option name="sources" path="ActionManager" hit="Apply a patch to the sources of the project" />
    <option name="the" path="ActionManager" hit="Apply a patch to the sources of the project" />
    <option name="to" path="ActionManager" hit="Apply a patch to the sources of the project" />
    <option name="apply" path="ActionManager" hit="Apply selected shelf" />
    <option name="selected" path="ActionManager" hit="Apply selected shelf" />
    <option name="shelf" path="ActionManager" hit="Apply selected shelf" />
    <option name="apply" path="ActionManager" hit="Apply the reverse of the selected changes to the working copy" />
    <option name="changes" path="ActionManager" hit="Apply the reverse of the selected changes to the working copy" />
    <option name="copy" path="ActionManager" hit="Apply the reverse of the selected changes to the working copy" />
    <option name="of" path="ActionManager" hit="Apply the reverse of the selected changes to the working copy" />
    <option name="reverse" path="ActionManager" hit="Apply the reverse of the selected changes to the working copy" />
    <option name="selected" path="ActionManager" hit="Apply the reverse of the selected changes to the working copy" />
    <option name="the" path="ActionManager" hit="Apply the reverse of the selected changes to the working copy" />
    <option name="to" path="ActionManager" hit="Apply the reverse of the selected changes to the working copy" />
    <option name="working" path="ActionManager" hit="Apply the reverse of the selected changes to the working copy" />
    <option name="auto" path="ActionManager" hit="Auto Update…" />
    <option name="update" path="ActionManager" hit="Auto Update…" />
    <option name="bottom" path="ActionManager" hit="Bottom" />
    <option name="browse" path="ActionManager" hit="Browse Changes..." />
    <option name="changes" path="ActionManager" hit="Browse Changes..." />
    <option name="caret" path="ActionManager" hit="Change Under Caret" />
    <option name="change" path="ActionManager" hit="Change Under Caret" />
    <option name="under" path="ActionManager" hit="Change Under Caret" />
    <option name="change" path="ActionManager" hit="Change filtering criteria" />
    <option name="criteria" path="ActionManager" hit="Change filtering criteria" />
    <option name="filtering" path="ActionManager" hit="Change filtering criteria" />
    <option name="changelists" path="ActionManager" hit="Changelists" />
    <option name="but" path="ActionManager" hit="Check Ignored but not Excluded Directories" />
    <option name="check" path="ActionManager" hit="Check Ignored but not Excluded Directories" />
    <option name="directories" path="ActionManager" hit="Check Ignored but not Excluded Directories" />
    <option name="excluded" path="ActionManager" hit="Check Ignored but not Excluded Directories" />
    <option name="ignored" path="ActionManager" hit="Check Ignored but not Excluded Directories" />
    <option name="not" path="ActionManager" hit="Check Ignored but not Excluded Directories" />
    <option name="check" path="ActionManager" hit="Check Out" />
    <option name="out" path="ActionManager" hit="Check Out" />
    <option name="check" path="ActionManager" hit="Check Status" />
    <option name="status" path="ActionManager" hit="Check Status" />
    <option name="check" path="ActionManager" hit="Check VCS Log Index Data for Old Commits" />
    <option name="commits" path="ActionManager" hit="Check VCS Log Index Data for Old Commits" />
    <option name="data" path="ActionManager" hit="Check VCS Log Index Data for Old Commits" />
    <option name="for" path="ActionManager" hit="Check VCS Log Index Data for Old Commits" />
    <option name="index" path="ActionManager" hit="Check VCS Log Index Data for Old Commits" />
    <option name="log" path="ActionManager" hit="Check VCS Log Index Data for Old Commits" />
    <option name="old" path="ActionManager" hit="Check VCS Log Index Data for Old Commits" />
    <option name="vcs" path="ActionManager" hit="Check VCS Log Index Data for Old Commits" />
    <option name="check" path="ActionManager" hit="Check VCS Log Index Data for Selected Commits" />
    <option name="commits" path="ActionManager" hit="Check VCS Log Index Data for Selected Commits" />
    <option name="data" path="ActionManager" hit="Check VCS Log Index Data for Selected Commits" />
    <option name="for" path="ActionManager" hit="Check VCS Log Index Data for Selected Commits" />
    <option name="index" path="ActionManager" hit="Check VCS Log Index Data for Selected Commits" />
    <option name="log" path="ActionManager" hit="Check VCS Log Index Data for Selected Commits" />
    <option name="selected" path="ActionManager" hit="Check VCS Log Index Data for Selected Commits" />
    <option name="vcs" path="ActionManager" hit="Check VCS Log Index Data for Selected Commits" />
    <option name="and" path="ActionManager" hit="Check for new commits and refresh Log if necessary" />
    <option name="check" path="ActionManager" hit="Check for new commits and refresh Log if necessary" />
    <option name="commits" path="ActionManager" hit="Check for new commits and refresh Log if necessary" />
    <option name="for" path="ActionManager" hit="Check for new commits and refresh Log if necessary" />
    <option name="if" path="ActionManager" hit="Check for new commits and refresh Log if necessary" />
    <option name="log" path="ActionManager" hit="Check for new commits and refresh Log if necessary" />
    <option name="necessary" path="ActionManager" hit="Check for new commits and refresh Log if necessary" />
    <option name="new" path="ActionManager" hit="Check for new commits and refresh Log if necessary" />
    <option name="refresh" path="ActionManager" hit="Check for new commits and refresh Log if necessary" />
    <option name="check" path="ActionManager" hit="Check out selected files for editing" />
    <option name="editing" path="ActionManager" hit="Check out selected files for editing" />
    <option name="files" path="ActionManager" hit="Check out selected files for editing" />
    <option name="for" path="ActionManager" hit="Check out selected files for editing" />
    <option name="out" path="ActionManager" hit="Check out selected files for editing" />
    <option name="selected" path="ActionManager" hit="Check out selected files for editing" />
    <option name="and" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="be" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="check" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="directories" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="excluded" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="from" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="ignored" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="indexing" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="may" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="of" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="presense" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="search" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="which" path="ActionManager" hit="Check presense of ignored directories which may be excluded from indexing and search" />
    <option name="cherry-pick" path="ActionManager" hit="Cherry-Pick" />
    <option name="changes" path="ActionManager" hit="Cherry-Pick Selected Changes" />
    <option name="cherry-pick" path="ActionManager" hit="Cherry-Pick Selected Changes" />
    <option name="selected" path="ActionManager" hit="Cherry-Pick Selected Changes" />
    <option name="clear" path="ActionManager" hit="Clear" />
    <option name="cached" path="ActionManager" hit="Clears cached revisions" />
    <option name="clears" path="ActionManager" hit="Clears cached revisions" />
    <option name="revisions" path="ActionManager" hit="Clears cached revisions" />
    <option name="close" path="ActionManager" hit="Close Unmodified Tabs" />
    <option name="tabs" path="ActionManager" hit="Close Unmodified Tabs" />
    <option name="unmodified" path="ActionManager" hit="Close Unmodified Tabs" />
    <option name="all" path="ActionManager" hit="Close all non-modified editors" />
    <option name="close" path="ActionManager" hit="Close all non-modified editors" />
    <option name="editors" path="ActionManager" hit="Close all non-modified editors" />
    <option name="non-modified" path="ActionManager" hit="Close all non-modified editors" />
    <option name="branches" path="ActionManager" hit="Collapse Linear Branches" />
    <option name="collapse" path="ActionManager" hit="Collapse Linear Branches" />
    <option name="linear" path="ActionManager" hit="Collapse Linear Branches" />
    <option name="branches" path="ActionManager" hit="Collapse linear branches" />
    <option name="collapse" path="ActionManager" hit="Collapse linear branches" />
    <option name="linear" path="ActionManager" hit="Collapse linear branches" />
    <option name="columns" path="ActionManager" hit="Columns" />
    <option name="commit" path="ActionManager" hit="Commit" />
    <option name="commit" path="ActionManager" hit="Commit File" />
    <option name="file" path="ActionManager" hit="Commit File" />
    <option name="commit" path="ActionManager" hit="Commit Message History" />
    <option name="history" path="ActionManager" hit="Commit Message History" />
    <option name="message" path="ActionManager" hit="Commit Message History" />
    <option name="commit" path="ActionManager" hit="Commit Timestamp" />
    <option name="timestamp" path="ActionManager" hit="Commit Timestamp" />
    <option name="commit" path="ActionManager" hit="Commit selected files or directories" />
    <option name="directories" path="ActionManager" hit="Commit selected files or directories" />
    <option name="files" path="ActionManager" hit="Commit selected files or directories" />
    <option name="or" path="ActionManager" hit="Commit selected files or directories" />
    <option name="selected" path="ActionManager" hit="Commit selected files or directories" />
    <option name="commit" path="ActionManager" hit="Commit..." />
    <option name="compact" path="ActionManager" hit="Compact References View" />
    <option name="references" path="ActionManager" hit="Compact References View" />
    <option name="view" path="ActionManager" hit="Compact References View" />
    <option name="before" path="ActionManager" hit="Compare Before with Local" />
    <option name="compare" path="ActionManager" hit="Compare Before with Local" />
    <option name="local" path="ActionManager" hit="Compare Before with Local" />
    <option name="with" path="ActionManager" hit="Compare Before with Local" />
    <option name="compare" path="ActionManager" hit="Compare Versions" />
    <option name="versions" path="ActionManager" hit="Compare Versions" />
    <option name="compare" path="ActionManager" hit="Compare selected versions" />
    <option name="selected" path="ActionManager" hit="Compare selected versions" />
    <option name="versions" path="ActionManager" hit="Compare selected versions" />
    <option name="before" path="ActionManager" hit="Compare version before selected revision with current version" />
    <option name="compare" path="ActionManager" hit="Compare version before selected revision with current version" />
    <option name="current" path="ActionManager" hit="Compare version before selected revision with current version" />
    <option name="revision" path="ActionManager" hit="Compare version before selected revision with current version" />
    <option name="selected" path="ActionManager" hit="Compare version before selected revision with current version" />
    <option name="version" path="ActionManager" hit="Compare version before selected revision with current version" />
    <option name="with" path="ActionManager" hit="Compare version before selected revision with current version" />
    <option name="compare" path="ActionManager" hit="Compare version in selected revision with current version" />
    <option name="current" path="ActionManager" hit="Compare version in selected revision with current version" />
    <option name="in" path="ActionManager" hit="Compare version in selected revision with current version" />
    <option name="revision" path="ActionManager" hit="Compare version in selected revision with current version" />
    <option name="selected" path="ActionManager" hit="Compare version in selected revision with current version" />
    <option name="version" path="ActionManager" hit="Compare version in selected revision with current version" />
    <option name="with" path="ActionManager" hit="Compare version in selected revision with current version" />
    <option name="compare" path="ActionManager" hit="Compare with Latest Repository Version" />
    <option name="latest" path="ActionManager" hit="Compare with Latest Repository Version" />
    <option name="repository" path="ActionManager" hit="Compare with Latest Repository Version" />
    <option name="version" path="ActionManager" hit="Compare with Latest Repository Version" />
    <option name="with" path="ActionManager" hit="Compare with Latest Repository Version" />
    <option name="compare" path="ActionManager" hit="Compare with Local" />
    <option name="local" path="ActionManager" hit="Compare with Local" />
    <option name="with" path="ActionManager" hit="Compare with Local" />
    <option name="compare" path="ActionManager" hit="Compare with Revision..." />
    <option name="revision" path="ActionManager" hit="Compare with Revision..." />
    <option name="with" path="ActionManager" hit="Compare with Revision..." />
    <option name="compare" path="ActionManager" hit="Compare with Specified Revision..." />
    <option name="revision" path="ActionManager" hit="Compare with Specified Revision..." />
    <option name="specified" path="ActionManager" hit="Compare with Specified Revision..." />
    <option name="with" path="ActionManager" hit="Compare with Specified Revision..." />
    <option name="compare" path="ActionManager" hit="Compare with the Same Repository Version" />
    <option name="repository" path="ActionManager" hit="Compare with the Same Repository Version" />
    <option name="same" path="ActionManager" hit="Compare with the Same Repository Version" />
    <option name="the" path="ActionManager" hit="Compare with the Same Repository Version" />
    <option name="version" path="ActionManager" hit="Compare with the Same Repository Version" />
    <option name="with" path="ActionManager" hit="Compare with the Same Repository Version" />
    <option name="configure" path="ActionManager" hit="Configure Layout" />
    <option name="layout" path="ActionManager" hit="Configure Layout" />
    <option name="configure" path="ActionManager" hit="Configure presentation of the File History" />
    <option name="file" path="ActionManager" hit="Configure presentation of the File History" />
    <option name="history" path="ActionManager" hit="Configure presentation of the File History" />
    <option name="of" path="ActionManager" hit="Configure presentation of the File History" />
    <option name="presentation" path="ActionManager" hit="Configure presentation of the File History" />
    <option name="the" path="ActionManager" hit="Configure presentation of the File History" />
    <option name="configure" path="ActionManager" hit="Configure presentation of the Log" />
    <option name="log" path="ActionManager" hit="Configure presentation of the Log" />
    <option name="of" path="ActionManager" hit="Configure presentation of the Log" />
    <option name="presentation" path="ActionManager" hit="Configure presentation of the Log" />
    <option name="the" path="ActionManager" hit="Configure presentation of the Log" />
    <option name="configure" path="ActionManager" hit="Configure window layout" />
    <option name="layout" path="ActionManager" hit="Configure window layout" />
    <option name="window" path="ActionManager" hit="Configure window layout" />
    <option name="actions" path="ActionManager" hit="Context aware popup with list of commonly used VCS actions" />
    <option name="aware" path="ActionManager" hit="Context aware popup with list of commonly used VCS actions" />
    <option name="commonly" path="ActionManager" hit="Context aware popup with list of commonly used VCS actions" />
    <option name="context" path="ActionManager" hit="Context aware popup with list of commonly used VCS actions" />
    <option name="list" path="ActionManager" hit="Context aware popup with list of commonly used VCS actions" />
    <option name="of" path="ActionManager" hit="Context aware popup with list of commonly used VCS actions" />
    <option name="popup" path="ActionManager" hit="Context aware popup with list of commonly used VCS actions" />
    <option name="used" path="ActionManager" hit="Context aware popup with list of commonly used VCS actions" />
    <option name="vcs" path="ActionManager" hit="Context aware popup with list of commonly used VCS actions" />
    <option name="with" path="ActionManager" hit="Context aware popup with list of commonly used VCS actions" />
    <option name="convert" path="ActionManager" hit="Convert Schema..." />
    <option name="schema" path="ActionManager" hit="Convert Schema..." />
    <option name="another" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="converts" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="document" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="dtd" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="format" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="into" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="rnc" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="rng" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="schema" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="selected" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="the" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="xsd" path="ActionManager" hit="Converts the selected Schema document (RNG, RNC, XSD, DTD) into another format" />
    <option name="a" path="ActionManager" hit="Copies a patch file to the shelf" />
    <option name="copies" path="ActionManager" hit="Copies a patch file to the shelf" />
    <option name="file" path="ActionManager" hit="Copies a patch file to the shelf" />
    <option name="patch" path="ActionManager" hit="Copies a patch file to the shelf" />
    <option name="shelf" path="ActionManager" hit="Copies a patch file to the shelf" />
    <option name="the" path="ActionManager" hit="Copies a patch file to the shelf" />
    <option name="to" path="ActionManager" hit="Copies a patch file to the shelf" />
    <option name="commit" path="ActionManager" hit="Copy Commit Subject" />
    <option name="copy" path="ActionManager" hit="Copy Commit Subject" />
    <option name="subject" path="ActionManager" hit="Copy Commit Subject" />
    <option name="copy" path="ActionManager" hit="Copy JSON Pointer" />
    <option name="json" path="ActionManager" hit="Copy JSON Pointer" />
    <option name="pointer" path="ActionManager" hit="Copy JSON Pointer" />
    <option name="copy" path="ActionManager" hit="Copy Revision Number" />
    <option name="number" path="ActionManager" hit="Copy Revision Number" />
    <option name="revision" path="ActionManager" hit="Copy Revision Number" />
    <option name="as" path="ActionManager" hit="Copy as Patch to Clipboard" />
    <option name="clipboard" path="ActionManager" hit="Copy as Patch to Clipboard" />
    <option name="copy" path="ActionManager" hit="Copy as Patch to Clipboard" />
    <option name="patch" path="ActionManager" hit="Copy as Patch to Clipboard" />
    <option name="to" path="ActionManager" hit="Copy as Patch to Clipboard" />
    <option name="clipboard" path="ActionManager" hit="Copy revision numbers of selected commits to the clipboard" />
    <option name="commits" path="ActionManager" hit="Copy revision numbers of selected commits to the clipboard" />
    <option name="copy" path="ActionManager" hit="Copy revision numbers of selected commits to the clipboard" />
    <option name="numbers" path="ActionManager" hit="Copy revision numbers of selected commits to the clipboard" />
    <option name="of" path="ActionManager" hit="Copy revision numbers of selected commits to the clipboard" />
    <option name="revision" path="ActionManager" hit="Copy revision numbers of selected commits to the clipboard" />
    <option name="selected" path="ActionManager" hit="Copy revision numbers of selected commits to the clipboard" />
    <option name="the" path="ActionManager" hit="Copy revision numbers of selected commits to the clipboard" />
    <option name="to" path="ActionManager" hit="Copy revision numbers of selected commits to the clipboard" />
    <option name="changelist" path="ActionManager" hit="Copy selected changes to shelved changelist" />
    <option name="changes" path="ActionManager" hit="Copy selected changes to shelved changelist" />
    <option name="copy" path="ActionManager" hit="Copy selected changes to shelved changelist" />
    <option name="selected" path="ActionManager" hit="Copy selected changes to shelved changelist" />
    <option name="shelved" path="ActionManager" hit="Copy selected changes to shelved changelist" />
    <option name="to" path="ActionManager" hit="Copy selected changes to shelved changelist" />
    <option name="clipboard" path="ActionManager" hit="Copy subjects of selected commits to the clipboard" />
    <option name="commits" path="ActionManager" hit="Copy subjects of selected commits to the clipboard" />
    <option name="copy" path="ActionManager" hit="Copy subjects of selected commits to the clipboard" />
    <option name="of" path="ActionManager" hit="Copy subjects of selected commits to the clipboard" />
    <option name="selected" path="ActionManager" hit="Copy subjects of selected commits to the clipboard" />
    <option name="subjects" path="ActionManager" hit="Copy subjects of selected commits to the clipboard" />
    <option name="the" path="ActionManager" hit="Copy subjects of selected commits to the clipboard" />
    <option name="to" path="ActionManager" hit="Copy subjects of selected commits to the clipboard" />
    <option name="and" path="ActionManager" hit="Correct paths where to apply patches and unshelve" />
    <option name="apply" path="ActionManager" hit="Correct paths where to apply patches and unshelve" />
    <option name="correct" path="ActionManager" hit="Correct paths where to apply patches and unshelve" />
    <option name="patches" path="ActionManager" hit="Correct paths where to apply patches and unshelve" />
    <option name="paths" path="ActionManager" hit="Correct paths where to apply patches and unshelve" />
    <option name="to" path="ActionManager" hit="Correct paths where to apply patches and unshelve" />
    <option name="unshelve" path="ActionManager" hit="Correct paths where to apply patches and unshelve" />
    <option name="where" path="ActionManager" hit="Correct paths where to apply patches and unshelve" />
    <option name="changes" path="ActionManager" hit="Create Patch from Local Changes..." />
    <option name="create" path="ActionManager" hit="Create Patch from Local Changes..." />
    <option name="from" path="ActionManager" hit="Create Patch from Local Changes..." />
    <option name="local" path="ActionManager" hit="Create Patch from Local Changes..." />
    <option name="patch" path="ActionManager" hit="Create Patch from Local Changes..." />
    <option name="create" path="ActionManager" hit="Create Patch..." />
    <option name="patch" path="ActionManager" hit="Create Patch..." />
    <option name="create" path="ActionManager" hit="Create Patch…" />
    <option name="patch" path="ActionManager" hit="Create Patch…" />
    <option name="a" path="ActionManager" hit="Create a patch from the selected changes" />
    <option name="changes" path="ActionManager" hit="Create a patch from the selected changes" />
    <option name="create" path="ActionManager" hit="Create a patch from the selected changes" />
    <option name="from" path="ActionManager" hit="Create a patch from the selected changes" />
    <option name="patch" path="ActionManager" hit="Create a patch from the selected changes" />
    <option name="selected" path="ActionManager" hit="Create a patch from the selected changes" />
    <option name="the" path="ActionManager" hit="Create a patch from the selected changes" />
    <option name="changelist" path="ActionManager" hit="Create new changelist" />
    <option name="create" path="ActionManager" hit="Create new changelist" />
    <option name="new" path="ActionManager" hit="Create new changelist" />
    <option name="and" path="ActionManager" hit="Create patch from changes and copy it to clipboard" />
    <option name="changes" path="ActionManager" hit="Create patch from changes and copy it to clipboard" />
    <option name="clipboard" path="ActionManager" hit="Create patch from changes and copy it to clipboard" />
    <option name="copy" path="ActionManager" hit="Create patch from changes and copy it to clipboard" />
    <option name="create" path="ActionManager" hit="Create patch from changes and copy it to clipboard" />
    <option name="from" path="ActionManager" hit="Create patch from changes and copy it to clipboard" />
    <option name="it" path="ActionManager" hit="Create patch from changes and copy it to clipboard" />
    <option name="patch" path="ActionManager" hit="Create patch from changes and copy it to clipboard" />
    <option name="to" path="ActionManager" hit="Create patch from changes and copy it to clipboard" />
    <option name="creates" path="ActionManager" hit="Creates new HTML file" />
    <option name="file" path="ActionManager" hit="Creates new HTML file" />
    <option name="html" path="ActionManager" hit="Creates new HTML file" />
    <option name="new" path="ActionManager" hit="Creates new HTML file" />
    <option name="delete" path="ActionManager" hit="Delete" />
    <option name="changelist" path="ActionManager" hit="Delete Changelist" />
    <option name="delete" path="ActionManager" hit="Delete Changelist" />
    <option name="delete" path="ActionManager" hit="Delete selected shelf" />
    <option name="selected" path="ActionManager" hit="Delete selected shelf" />
    <option name="shelf" path="ActionManager" hit="Delete selected shelf" />
    <option name="diff" path="ActionManager" hit="Diff Preview Location" />
    <option name="location" path="ActionManager" hit="Diff Preview Location" />
    <option name="preview" path="ActionManager" hit="Diff Preview Location" />
    <option name="directory" path="ActionManager" hit="Directory" />
    <option name="changes" path="ActionManager" hit="Display changes to each merged commit separately" />
    <option name="commit" path="ActionManager" hit="Display changes to each merged commit separately" />
    <option name="display" path="ActionManager" hit="Display changes to each merged commit separately" />
    <option name="each" path="ActionManager" hit="Display changes to each merged commit separately" />
    <option name="merged" path="ActionManager" hit="Display changes to each merged commit separately" />
    <option name="separately" path="ActionManager" hit="Display changes to each merged commit separately" />
    <option name="to" path="ActionManager" hit="Display changes to each merged commit separately" />
    <option name="details" path="ActionManager" hit="Display details panel" />
    <option name="display" path="ActionManager" hit="Display details panel" />
    <option name="panel" path="ActionManager" hit="Display details panel" />
    <option name="edit" path="ActionManager" hit="Edit" />
    <option name="changelist" path="ActionManager" hit="Edit Changelist..." />
    <option name="edit" path="ActionManager" hit="Edit Changelist..." />
    <option name="edit" path="ActionManager" hit="Edit Source" />
    <option name="source" path="ActionManager" hit="Edit Source" />
    <option name="and" path="ActionManager" hit="Edit name and description of selected changelist" />
    <option name="changelist" path="ActionManager" hit="Edit name and description of selected changelist" />
    <option name="description" path="ActionManager" hit="Edit name and description of selected changelist" />
    <option name="edit" path="ActionManager" hit="Edit name and description of selected changelist" />
    <option name="name" path="ActionManager" hit="Edit name and description of selected changelist" />
    <option name="of" path="ActionManager" hit="Edit name and description of selected changelist" />
    <option name="selected" path="ActionManager" hit="Edit name and description of selected changelist" />
    <option name="emmet" path="ActionManager" hit="Emmet Preview" />
    <option name="preview" path="ActionManager" hit="Emmet Preview" />
    <option name="control" path="ActionManager" hit="Enable Version Control Integration…" />
    <option name="enable" path="ActionManager" hit="Enable Version Control Integration…" />
    <option name="integration" path="ActionManager" hit="Enable Version Control Integration…" />
    <option name="version" path="ActionManager" hit="Enable Version Control Integration…" />
    <option name="characters" path="ActionManager" hit="Encode XML/HTML Special Characters" />
    <option name="encode" path="ActionManager" hit="Encode XML/HTML Special Characters" />
    <option name="html" path="ActionManager" hit="Encode XML/HTML Special Characters" />
    <option name="special" path="ActionManager" hit="Encode XML/HTML Special Characters" />
    <option name="xml" path="ActionManager" hit="Encode XML/HTML Special Characters" />
    <option name="characters" path="ActionManager" hit="Escape XML/HTML special characters with entities" />
    <option name="entities" path="ActionManager" hit="Escape XML/HTML special characters with entities" />
    <option name="escape" path="ActionManager" hit="Escape XML/HTML special characters with entities" />
    <option name="html" path="ActionManager" hit="Escape XML/HTML special characters with entities" />
    <option name="special" path="ActionManager" hit="Escape XML/HTML special characters with entities" />
    <option name="with" path="ActionManager" hit="Escape XML/HTML special characters with entities" />
    <option name="xml" path="ActionManager" hit="Escape XML/HTML special characters with entities" />
    <option name="branches" path="ActionManager" hit="Expand Linear Branches" />
    <option name="expand" path="ActionManager" hit="Expand Linear Branches" />
    <option name="linear" path="ActionManager" hit="Expand Linear Branches" />
    <option name="branches" path="ActionManager" hit="Expand linear branches" />
    <option name="expand" path="ActionManager" hit="Expand linear branches" />
    <option name="linear" path="ActionManager" hit="Expand linear branches" />
    <option name="coverage" path="ActionManager" hit="Export coverage report to HTML" />
    <option name="export" path="ActionManager" hit="Export coverage report to HTML" />
    <option name="html" path="ActionManager" hit="Export coverage report to HTML" />
    <option name="report" path="ActionManager" hit="Export coverage report to HTML" />
    <option name="to" path="ActionManager" hit="Export coverage report to HTML" />
    <option name="filter" path="ActionManager" hit="Filter" />
    <option name="by" path="ActionManager" hit="Filter By" />
    <option name="filter" path="ActionManager" hit="Filter By" />
    <option name="filter" path="ActionManager" hit="Focus Text Filter" />
    <option name="focus" path="ActionManager" hit="Focus Text Filter" />
    <option name="text" path="ActionManager" hit="Focus Text Filter" />
    <option name="back" path="ActionManager" hit="Focus text filter or move focus back to the commits list" />
    <option name="commits" path="ActionManager" hit="Focus text filter or move focus back to the commits list" />
    <option name="filter" path="ActionManager" hit="Focus text filter or move focus back to the commits list" />
    <option name="focus" path="ActionManager" hit="Focus text filter or move focus back to the commits list" />
    <option name="list" path="ActionManager" hit="Focus text filter or move focus back to the commits list" />
    <option name="move" path="ActionManager" hit="Focus text filter or move focus back to the commits list" />
    <option name="or" path="ActionManager" hit="Focus text filter or move focus back to the commits list" />
    <option name="text" path="ActionManager" hit="Focus text filter or move focus back to the commits list" />
    <option name="the" path="ActionManager" hit="Focus text filter or move focus back to the commits list" />
    <option name="to" path="ActionManager" hit="Focus text filter or move focus back to the commits list" />
    <option name="force" path="ActionManager" hit="Force Push" />
    <option name="push" path="ActionManager" hit="Force Push" />
    <option name="coverage" path="ActionManager" hit="Generate Coverage Report…" />
    <option name="generate" path="ActionManager" hit="Generate Coverage Report…" />
    <option name="report" path="ActionManager" hit="Generate Coverage Report…" />
    <option name="a" path="ActionManager" hit="Generate a new XML tag according to schema information" />
    <option name="according" path="ActionManager" hit="Generate a new XML tag according to schema information" />
    <option name="generate" path="ActionManager" hit="Generate a new XML tag according to schema information" />
    <option name="information" path="ActionManager" hit="Generate a new XML tag according to schema information" />
    <option name="new" path="ActionManager" hit="Generate a new XML tag according to schema information" />
    <option name="schema" path="ActionManager" hit="Generate a new XML tag according to schema information" />
    <option name="tag" path="ActionManager" hit="Generate a new XML tag according to schema information" />
    <option name="to" path="ActionManager" hit="Generate a new XML tag according to schema information" />
    <option name="xml" path="ActionManager" hit="Generate a new XML tag according to schema information" />
    <option name="get" path="ActionManager" hit="Get" />
    <option name="control" path="ActionManager" hit="Get from Version Control..." />
    <option name="from" path="ActionManager" hit="Get from Version Control..." />
    <option name="get" path="ActionManager" hit="Get from Version Control..." />
    <option name="version" path="ActionManager" hit="Get from Version Control..." />
    <option name="from" path="ActionManager" hit="Get version from repository" />
    <option name="get" path="ActionManager" hit="Get version from repository" />
    <option name="repository" path="ActionManager" hit="Get version from repository" />
    <option name="version" path="ActionManager" hit="Get version from repository" />
    <option name="git" path="ActionManager" hit="Git" />
    <option name="branch" path="ActionManager" hit="Go To Hash/Branch/Tag" />
    <option name="go" path="ActionManager" hit="Go To Hash/Branch/Tag" />
    <option name="hash" path="ActionManager" hit="Go To Hash/Branch/Tag" />
    <option name="tag" path="ActionManager" hit="Go To Hash/Branch/Tag" />
    <option name="to" path="ActionManager" hit="Go To Hash/Branch/Tag" />
    <option name="child" path="ActionManager" hit="Go to Child Commit" />
    <option name="commit" path="ActionManager" hit="Go to Child Commit" />
    <option name="go" path="ActionManager" hit="Go to Child Commit" />
    <option name="to" path="ActionManager" hit="Go to Child Commit" />
    <option name="commit" path="ActionManager" hit="Go to Parent Commit" />
    <option name="go" path="ActionManager" hit="Go to Parent Commit" />
    <option name="parent" path="ActionManager" hit="Go to Parent Commit" />
    <option name="to" path="ActionManager" hit="Go to Parent Commit" />
    <option name="edit" path="ActionManager" hit="Go to next Emmet edit point" />
    <option name="emmet" path="ActionManager" hit="Go to next Emmet edit point" />
    <option name="go" path="ActionManager" hit="Go to next Emmet edit point" />
    <option name="next" path="ActionManager" hit="Go to next Emmet edit point" />
    <option name="point" path="ActionManager" hit="Go to next Emmet edit point" />
    <option name="to" path="ActionManager" hit="Go to next Emmet edit point" />
    <option name="change" path="ActionManager" hit="Go to next change" />
    <option name="go" path="ActionManager" hit="Go to next change" />
    <option name="next" path="ActionManager" hit="Go to next change" />
    <option name="to" path="ActionManager" hit="Go to next change" />
    <option name="edit" path="ActionManager" hit="Go to previous Emmet edit point" />
    <option name="emmet" path="ActionManager" hit="Go to previous Emmet edit point" />
    <option name="go" path="ActionManager" hit="Go to previous Emmet edit point" />
    <option name="point" path="ActionManager" hit="Go to previous Emmet edit point" />
    <option name="previous" path="ActionManager" hit="Go to previous Emmet edit point" />
    <option name="to" path="ActionManager" hit="Go to previous Emmet edit point" />
    <option name="change" path="ActionManager" hit="Go to previous change" />
    <option name="go" path="ActionManager" hit="Go to previous change" />
    <option name="previous" path="ActionManager" hit="Go to previous change" />
    <option name="to" path="ActionManager" hit="Go to previous change" />
    <option name="by" path="ActionManager" hit="Group By" />
    <option name="group" path="ActionManager" hit="Group By" />
    <option name="file" path="ActionManager" hit="HTML File" />
    <option name="html" path="ActionManager" hit="HTML File" />
    <option name="coverage" path="ActionManager" hit="Hide coverage" />
    <option name="hide" path="ActionManager" hit="Hide coverage" />
    <option name="coverage" path="ActionManager" hit="Hide coverage data" />
    <option name="data" path="ActionManager" hit="Hide coverage data" />
    <option name="hide" path="ActionManager" hit="Hide coverage data" />
    <option name="here" path="ActionManager" hit="History Up to Here" />
    <option name="history" path="ActionManager" hit="History Up to Here" />
    <option name="to" path="ActionManager" hit="History Up to Here" />
    <option name="up" path="ActionManager" hit="History Up to Here" />
    <option name="coverage" path="ActionManager" hit="Import External Coverage Report…" />
    <option name="external" path="ActionManager" hit="Import External Coverage Report…" />
    <option name="import" path="ActionManager" hit="Import External Coverage Report…" />
    <option name="report" path="ActionManager" hit="Import External Coverage Report…" />
    <option name="import" path="ActionManager" hit="Import Patches…" />
    <option name="patches" path="ActionManager" hit="Import Patches…" />
    <option name="a" path="ActionManager" hit="Import a report collected in CI from disk" />
    <option name="ci" path="ActionManager" hit="Import a report collected in CI from disk" />
    <option name="collected" path="ActionManager" hit="Import a report collected in CI from disk" />
    <option name="disk" path="ActionManager" hit="Import a report collected in CI from disk" />
    <option name="from" path="ActionManager" hit="Import a report collected in CI from disk" />
    <option name="import" path="ActionManager" hit="Import a report collected in CI from disk" />
    <option name="in" path="ActionManager" hit="Import a report collected in CI from disk" />
    <option name="report" path="ActionManager" hit="Import a report collected in CI from disk" />
    <option name="as" path="ActionManager" hit="Indexing was paused as it took longer than expected. Resume." />
    <option name="expected" path="ActionManager" hit="Indexing was paused as it took longer than expected. Resume." />
    <option name="indexing" path="ActionManager" hit="Indexing was paused as it took longer than expected. Resume." />
    <option name="it" path="ActionManager" hit="Indexing was paused as it took longer than expected. Resume." />
    <option name="longer" path="ActionManager" hit="Indexing was paused as it took longer than expected. Resume." />
    <option name="paused" path="ActionManager" hit="Indexing was paused as it took longer than expected. Resume." />
    <option name="resume" path="ActionManager" hit="Indexing was paused as it took longer than expected. Resume." />
    <option name="than" path="ActionManager" hit="Indexing was paused as it took longer than expected. Resume." />
    <option name="took" path="ActionManager" hit="Indexing was paused as it took longer than expected. Resume." />
    <option name="was" path="ActionManager" hit="Indexing was paused as it took longer than expected. Resume." />
    <option name="coverage" path="ActionManager" hit="Inspect previously opened coverage reports" />
    <option name="inspect" path="ActionManager" hit="Inspect previously opened coverage reports" />
    <option name="opened" path="ActionManager" hit="Inspect previously opened coverage reports" />
    <option name="previously" path="ActionManager" hit="Inspect previously opened coverage reports" />
    <option name="reports" path="ActionManager" hit="Inspect previously opened coverage reports" />
    <option name="integrate" path="ActionManager" hit="Integrate" />
    <option name="integrate" path="ActionManager" hit="Integrate Project" />
    <option name="project" path="ActionManager" hit="Integrate Project" />
    <option name="integrate" path="ActionManager" hit="Integrate project" />
    <option name="project" path="ActionManager" hit="Integrate project" />
    <option name="directories" path="ActionManager" hit="Integrate selected files or directories" />
    <option name="files" path="ActionManager" hit="Integrate selected files or directories" />
    <option name="integrate" path="ActionManager" hit="Integrate selected files or directories" />
    <option name="or" path="ActionManager" hit="Integrate selected files or directories" />
    <option name="selected" path="ActionManager" hit="Integrate selected files or directories" />
    <option name="intellisort" path="ActionManager" hit="IntelliSort" />
    <option name="and" path="ActionManager" hit="Invalidate Vcs Log Caches and Indexes" />
    <option name="caches" path="ActionManager" hit="Invalidate Vcs Log Caches and Indexes" />
    <option name="indexes" path="ActionManager" hit="Invalidate Vcs Log Caches and Indexes" />
    <option name="invalidate" path="ActionManager" hit="Invalidate Vcs Log Caches and Indexes" />
    <option name="log" path="ActionManager" hit="Invalidate Vcs Log Caches and Indexes" />
    <option name="vcs" path="ActionManager" hit="Invalidate Vcs Log Caches and Indexes" />
    <option name="from" path="ActionManager" hit="Load Inspection Profile from YAML" />
    <option name="inspection" path="ActionManager" hit="Load Inspection Profile from YAML" />
    <option name="load" path="ActionManager" hit="Load Inspection Profile from YAML" />
    <option name="profile" path="ActionManager" hit="Load Inspection Profile from YAML" />
    <option name="yaml" path="ActionManager" hit="Load Inspection Profile from YAML" />
    <option name="history" path="ActionManager" hit="Local History" />
    <option name="local" path="ActionManager" hit="Local History" />
    <option name="bottom" path="ActionManager" hit="Locate Diff Preview in the bottom" />
    <option name="diff" path="ActionManager" hit="Locate Diff Preview in the bottom" />
    <option name="in" path="ActionManager" hit="Locate Diff Preview in the bottom" />
    <option name="locate" path="ActionManager" hit="Locate Diff Preview in the bottom" />
    <option name="preview" path="ActionManager" hit="Locate Diff Preview in the bottom" />
    <option name="the" path="ActionManager" hit="Locate Diff Preview in the bottom" />
    <option name="diff" path="ActionManager" hit="Locate Diff Preview in the right" />
    <option name="in" path="ActionManager" hit="Locate Diff Preview in the right" />
    <option name="locate" path="ActionManager" hit="Locate Diff Preview in the right" />
    <option name="preview" path="ActionManager" hit="Locate Diff Preview in the right" />
    <option name="right" path="ActionManager" hit="Locate Diff Preview in the right" />
    <option name="the" path="ActionManager" hit="Locate Diff Preview in the right" />
    <option name="edges" path="ActionManager" hit="Long Edges" />
    <option name="long" path="ActionManager" hit="Long Edges" />
    <option name="coverage" path="ActionManager" hit="Manage Coverage Reports…" />
    <option name="manage" path="ActionManager" hit="Manage Coverage Reports…" />
    <option name="reports" path="ActionManager" hit="Manage Coverage Reports…" />
    <option name="dirty" path="ActionManager" hit="Mark File Dirty in VCS" />
    <option name="file" path="ActionManager" hit="Mark File Dirty in VCS" />
    <option name="in" path="ActionManager" hit="Mark File Dirty in VCS" />
    <option name="mark" path="ActionManager" hit="Mark File Dirty in VCS" />
    <option name="vcs" path="ActionManager" hit="Mark File Dirty in VCS" />
    <option name="case" path="ActionManager" hit="Match Case" />
    <option name="match" path="ActionManager" hit="Match Case" />
    <option name="branch" path="ActionManager" hit="Modify the latest commit of the current branch" />
    <option name="commit" path="ActionManager" hit="Modify the latest commit of the current branch" />
    <option name="current" path="ActionManager" hit="Modify the latest commit of the current branch" />
    <option name="latest" path="ActionManager" hit="Modify the latest commit of the current branch" />
    <option name="modify" path="ActionManager" hit="Modify the latest commit of the current branch" />
    <option name="of" path="ActionManager" hit="Modify the latest commit of the current branch" />
    <option name="the" path="ActionManager" hit="Modify the latest commit of the current branch" />
    <option name="module" path="ActionManager" hit="Module" />
    <option name="another" path="ActionManager" hit="Move Lines to Another Changelist..." />
    <option name="changelist" path="ActionManager" hit="Move Lines to Another Changelist..." />
    <option name="lines" path="ActionManager" hit="Move Lines to Another Changelist..." />
    <option name="move" path="ActionManager" hit="Move Lines to Another Changelist..." />
    <option name="to" path="ActionManager" hit="Move Lines to Another Changelist..." />
    <option name="another" path="ActionManager" hit="Move changes in selected lines to another changelist" />
    <option name="changelist" path="ActionManager" hit="Move changes in selected lines to another changelist" />
    <option name="changes" path="ActionManager" hit="Move changes in selected lines to another changelist" />
    <option name="in" path="ActionManager" hit="Move changes in selected lines to another changelist" />
    <option name="lines" path="ActionManager" hit="Move changes in selected lines to another changelist" />
    <option name="move" path="ActionManager" hit="Move changes in selected lines to another changelist" />
    <option name="selected" path="ActionManager" hit="Move changes in selected lines to another changelist" />
    <option name="to" path="ActionManager" hit="Move changes in selected lines to another changelist" />
    <option name="another" path="ActionManager" hit="Move selected changes to another changelist" />
    <option name="changelist" path="ActionManager" hit="Move selected changes to another changelist" />
    <option name="changes" path="ActionManager" hit="Move selected changes to another changelist" />
    <option name="move" path="ActionManager" hit="Move selected changes to another changelist" />
    <option name="selected" path="ActionManager" hit="Move selected changes to another changelist" />
    <option name="to" path="ActionManager" hit="Move selected changes to another changelist" />
    <option name="another" path="ActionManager" hit="Move to Another Changelist..." />
    <option name="changelist" path="ActionManager" hit="Move to Another Changelist..." />
    <option name="move" path="ActionManager" hit="Move to Another Changelist..." />
    <option name="to" path="ActionManager" hit="Move to Another Changelist..." />
    <option name="child" path="ActionManager" hit="Navigate to the child row in the commit graph" />
    <option name="commit" path="ActionManager" hit="Navigate to the child row in the commit graph" />
    <option name="graph" path="ActionManager" hit="Navigate to the child row in the commit graph" />
    <option name="in" path="ActionManager" hit="Navigate to the child row in the commit graph" />
    <option name="navigate" path="ActionManager" hit="Navigate to the child row in the commit graph" />
    <option name="row" path="ActionManager" hit="Navigate to the child row in the commit graph" />
    <option name="the" path="ActionManager" hit="Navigate to the child row in the commit graph" />
    <option name="to" path="ActionManager" hit="Navigate to the child row in the commit graph" />
    <option name="commit" path="ActionManager" hit="Navigate to the parent row in the commit graph" />
    <option name="graph" path="ActionManager" hit="Navigate to the parent row in the commit graph" />
    <option name="in" path="ActionManager" hit="Navigate to the parent row in the commit graph" />
    <option name="navigate" path="ActionManager" hit="Navigate to the parent row in the commit graph" />
    <option name="parent" path="ActionManager" hit="Navigate to the parent row in the commit graph" />
    <option name="row" path="ActionManager" hit="Navigate to the parent row in the commit graph" />
    <option name="the" path="ActionManager" hit="Navigate to the parent row in the commit graph" />
    <option name="to" path="ActionManager" hit="Navigate to the parent row in the commit graph" />
    <option name="changelist" path="ActionManager" hit="New Changelist..." />
    <option name="new" path="ActionManager" hit="New Changelist..." />
    <option name="change" path="ActionManager" hit="Next Change" />
    <option name="next" path="ActionManager" hit="Next Change" />
    <option name="edit" path="ActionManager" hit="Next Emmet Edit Point" />
    <option name="emmet" path="ActionManager" hit="Next Emmet Edit Point" />
    <option name="next" path="ActionManager" hit="Next Emmet Edit Point" />
    <option name="point" path="ActionManager" hit="Next Emmet Edit Point" />
    <option name="affect" path="ActionManager" hit="Only show changes that affect files selected in the &quot;Paths&quot; menu" />
    <option name="changes" path="ActionManager" hit="Only show changes that affect files selected in the &quot;Paths&quot; menu" />
    <option name="files" path="ActionManager" hit="Only show changes that affect files selected in the &quot;Paths&quot; menu" />
    <option name="in" path="ActionManager" hit="Only show changes that affect files selected in the &quot;Paths&quot; menu" />
    <option name="menu" path="ActionManager" hit="Only show changes that affect files selected in the &quot;Paths&quot; menu" />
    <option name="only" path="ActionManager" hit="Only show changes that affect files selected in the &quot;Paths&quot; menu" />
    <option name="paths" path="ActionManager" hit="Only show changes that affect files selected in the &quot;Paths&quot; menu" />
    <option name="selected" path="ActionManager" hit="Only show changes that affect files selected in the &quot;Paths&quot; menu" />
    <option name="show" path="ActionManager" hit="Only show changes that affect files selected in the &quot;Paths&quot; menu" />
    <option name="that" path="ActionManager" hit="Only show changes that affect files selected in the &quot;Paths&quot; menu" />
    <option name="the" path="ActionManager" hit="Only show changes that affect files selected in the &quot;Paths&quot; menu" />
    <option name="log" path="ActionManager" hit="Open New Vcs Log Tab" />
    <option name="new" path="ActionManager" hit="Open New Vcs Log Tab" />
    <option name="open" path="ActionManager" hit="Open New Vcs Log Tab" />
    <option name="tab" path="ActionManager" hit="Open New Vcs Log Tab" />
    <option name="vcs" path="ActionManager" hit="Open New Vcs Log Tab" />
    <option name="editor" path="ActionManager" hit="Open New Vcs Log Tab in Editor" />
    <option name="in" path="ActionManager" hit="Open New Vcs Log Tab in Editor" />
    <option name="log" path="ActionManager" hit="Open New Vcs Log Tab in Editor" />
    <option name="new" path="ActionManager" hit="Open New Vcs Log Tab in Editor" />
    <option name="open" path="ActionManager" hit="Open New Vcs Log Tab in Editor" />
    <option name="tab" path="ActionManager" hit="Open New Vcs Log Tab in Editor" />
    <option name="vcs" path="ActionManager" hit="Open New Vcs Log Tab in Editor" />
    <option name="open" path="ActionManager" hit="Open Repository Version" />
    <option name="repository" path="ActionManager" hit="Open Repository Version" />
    <option name="version" path="ActionManager" hit="Open Repository Version" />
    <option name="editor" path="ActionManager" hit="Open editor with selected revision of the file" />
    <option name="file" path="ActionManager" hit="Open editor with selected revision of the file" />
    <option name="of" path="ActionManager" hit="Open editor with selected revision of the file" />
    <option name="open" path="ActionManager" hit="Open editor with selected revision of the file" />
    <option name="revision" path="ActionManager" hit="Open editor with selected revision of the file" />
    <option name="selected" path="ActionManager" hit="Open editor with selected revision of the file" />
    <option name="the" path="ActionManager" hit="Open editor with selected revision of the file" />
    <option name="with" path="ActionManager" hit="Open editor with selected revision of the file" />
    <option name="log" path="ActionManager" hit="Open new tab with Vcs Log" />
    <option name="new" path="ActionManager" hit="Open new tab with Vcs Log" />
    <option name="open" path="ActionManager" hit="Open new tab with Vcs Log" />
    <option name="tab" path="ActionManager" hit="Open new tab with Vcs Log" />
    <option name="vcs" path="ActionManager" hit="Open new tab with Vcs Log" />
    <option name="with" path="ActionManager" hit="Open new tab with Vcs Log" />
    <option name="editor" path="ActionManager" hit="Open new tab with Vcs Log in editor" />
    <option name="in" path="ActionManager" hit="Open new tab with Vcs Log in editor" />
    <option name="log" path="ActionManager" hit="Open new tab with Vcs Log in editor" />
    <option name="new" path="ActionManager" hit="Open new tab with Vcs Log in editor" />
    <option name="open" path="ActionManager" hit="Open new tab with Vcs Log in editor" />
    <option name="tab" path="ActionManager" hit="Open new tab with Vcs Log in editor" />
    <option name="vcs" path="ActionManager" hit="Open new tab with Vcs Log in editor" />
    <option name="with" path="ActionManager" hit="Open new tab with Vcs Log in editor" />
    <option name="pop" path="ActionManager" hit="Pop" />
    <option name="pop" path="ActionManager" hit="Pop selected shelf" />
    <option name="selected" path="ActionManager" hit="Pop selected shelf" />
    <option name="shelf" path="ActionManager" hit="Pop selected shelf" />
    <option name="diff" path="ActionManager" hit="Preview Diff" />
    <option name="preview" path="ActionManager" hit="Preview Diff" />
    <option name="change" path="ActionManager" hit="Previous Change" />
    <option name="previous" path="ActionManager" hit="Previous Change" />
    <option name="edit" path="ActionManager" hit="Previous Emmet Edit Point" />
    <option name="emmet" path="ActionManager" hit="Previous Emmet Edit Point" />
    <option name="point" path="ActionManager" hit="Previous Emmet Edit Point" />
    <option name="previous" path="ActionManager" hit="Previous Emmet Edit Point" />
    <option name="control" path="ActionManager" hit="Project from Version Control..." />
    <option name="from" path="ActionManager" hit="Project from Version Control..." />
    <option name="project" path="ActionManager" hit="Project from Version Control..." />
    <option name="version" path="ActionManager" hit="Project from Version Control..." />
    <option name="push" path="ActionManager" hit="Push" />
    <option name="push" path="ActionManager" hit="Push..." />
    <option name="label" path="ActionManager" hit="Put Label…" />
    <option name="put" path="ActionManager" hit="Put Label…" />
    <option name="changes" path="ActionManager" hit="Recent Changes" />
    <option name="recent" path="ActionManager" hit="Recent Changes" />
    <option name="left" path="ActionManager" hit="References on the Left" />
    <option name="on" path="ActionManager" hit="References on the Left" />
    <option name="references" path="ActionManager" hit="References on the Left" />
    <option name="the" path="ActionManager" hit="References on the Left" />
    <option name="json" path="ActionManager" hit="Reformat JSON" />
    <option name="reformat" path="ActionManager" hit="Reformat JSON" />
    <option name="commit" path="ActionManager" hit="Reformat commit message" />
    <option name="message" path="ActionManager" hit="Reformat commit message" />
    <option name="reformat" path="ActionManager" hit="Reformat commit message" />
    <option name="refresh" path="ActionManager" hit="Refresh" />
    <option name="changes" path="ActionManager" hit="Refresh VCS changes" />
    <option name="refresh" path="ActionManager" hit="Refresh VCS changes" />
    <option name="vcs" path="ActionManager" hit="Refresh VCS changes" />
    <option name="file" path="ActionManager" hit="Refresh file history" />
    <option name="history" path="ActionManager" hit="Refresh file history" />
    <option name="refresh" path="ActionManager" hit="Refresh file history" />
    <option name="changes" path="ActionManager" hit="Refresh the list of committed changes" />
    <option name="committed" path="ActionManager" hit="Refresh the list of committed changes" />
    <option name="list" path="ActionManager" hit="Refresh the list of committed changes" />
    <option name="of" path="ActionManager" hit="Refresh the list of committed changes" />
    <option name="refresh" path="ActionManager" hit="Refresh the list of committed changes" />
    <option name="the" path="ActionManager" hit="Refresh the list of committed changes" />
    <option name="changes" path="ActionManager" hit="Refresh the list of incoming changes" />
    <option name="incoming" path="ActionManager" hit="Refresh the list of incoming changes" />
    <option name="list" path="ActionManager" hit="Refresh the list of incoming changes" />
    <option name="of" path="ActionManager" hit="Refresh the list of incoming changes" />
    <option name="refresh" path="ActionManager" hit="Refresh the list of incoming changes" />
    <option name="the" path="ActionManager" hit="Refresh the list of incoming changes" />
    <option name="regex" path="ActionManager" hit="Regex" />
    <option name="remove" path="ActionManager" hit="Remove" />
    <option name="all" path="ActionManager" hit="Remove changelist and move all changes to another" />
    <option name="and" path="ActionManager" hit="Remove changelist and move all changes to another" />
    <option name="another" path="ActionManager" hit="Remove changelist and move all changes to another" />
    <option name="changelist" path="ActionManager" hit="Remove changelist and move all changes to another" />
    <option name="changes" path="ActionManager" hit="Remove changelist and move all changes to another" />
    <option name="move" path="ActionManager" hit="Remove changelist and move all changes to another" />
    <option name="remove" path="ActionManager" hit="Remove changelist and move all changes to another" />
    <option name="to" path="ActionManager" hit="Remove changelist and move all changes to another" />
    <option name="from" path="ActionManager" hit="Remove from VCS" />
    <option name="remove" path="ActionManager" hit="Remove from VCS" />
    <option name="vcs" path="ActionManager" hit="Remove from VCS" />
    <option name="changelist" path="ActionManager" hit="Rename shelved changelist" />
    <option name="rename" path="ActionManager" hit="Rename shelved changelist" />
    <option name="shelved" path="ActionManager" hit="Rename shelved changelist" />
    <option name="rename" path="ActionManager" hit="Rename..." />
    <option name="replace" path="ActionManager" hit="Replace Structurally..." />
    <option name="structurally" path="ActionManager" hit="Replace Structurally..." />
    <option name="repository" path="ActionManager" hit="Repository" />
    <option name="restore" path="ActionManager" hit="Restore" />
    <option name="indexing" path="ActionManager" hit="Resume Indexing" />
    <option name="resume" path="ActionManager" hit="Resume Indexing" />
    <option name="changes" path="ActionManager" hit="Revert Changes" />
    <option name="revert" path="ActionManager" hit="Revert Changes" />
    <option name="changes" path="ActionManager" hit="Revert Selected Changes" />
    <option name="revert" path="ActionManager" hit="Revert Selected Changes" />
    <option name="selected" path="ActionManager" hit="Revert Selected Changes" />
    <option name="revert" path="ActionManager" hit="Revert to Revision" />
    <option name="revision" path="ActionManager" hit="Revert to Revision" />
    <option name="to" path="ActionManager" hit="Revert to Revision" />
    <option name="right" path="ActionManager" hit="Right" />
    <option name="file" path="ActionManager" hit="Rollback File..." />
    <option name="rollback" path="ActionManager" hit="Rollback File..." />
    <option name="lines" path="ActionManager" hit="Rollback Lines" />
    <option name="rollback" path="ActionManager" hit="Rollback Lines" />
    <option name="changes" path="ActionManager" hit="Rollback changes in selected lines" />
    <option name="in" path="ActionManager" hit="Rollback changes in selected lines" />
    <option name="lines" path="ActionManager" hit="Rollback changes in selected lines" />
    <option name="rollback" path="ActionManager" hit="Rollback changes in selected lines" />
    <option name="selected" path="ActionManager" hit="Rollback changes in selected lines" />
    <option name="rollback" path="ActionManager" hit="Rollback..." />
    <option name="names" path="ActionManager" hit="Root Names" />
    <option name="root" path="ActionManager" hit="Root Names" />
    <option name="checks" path="ActionManager" hit="Run Commit Checks" />
    <option name="commit" path="ActionManager" hit="Run Commit Checks" />
    <option name="run" path="ActionManager" hit="Run Commit Checks" />
    <option name="save" path="ActionManager" hit="Save To Shelf" />
    <option name="shelf" path="ActionManager" hit="Save To Shelf" />
    <option name="to" path="ActionManager" hit="Save To Shelf" />
    <option name="an" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="and" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="changes" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="code" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="external" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="file" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="from" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="patch" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="remove" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="save" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="the" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="them" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="to" path="ActionManager" hit="Save changes to an external patch file and remove them from the code" />
    <option name="added" path="ActionManager" hit="Schedule selected files to be added to VCS" />
    <option name="be" path="ActionManager" hit="Schedule selected files to be added to VCS" />
    <option name="files" path="ActionManager" hit="Schedule selected files to be added to VCS" />
    <option name="schedule" path="ActionManager" hit="Schedule selected files to be added to VCS" />
    <option name="selected" path="ActionManager" hit="Schedule selected files to be added to VCS" />
    <option name="to" path="ActionManager" hit="Schedule selected files to be added to VCS" />
    <option name="vcs" path="ActionManager" hit="Schedule selected files to be added to VCS" />
    <option name="be" path="ActionManager" hit="Schedule selected files to be removed from VCS" />
    <option name="files" path="ActionManager" hit="Schedule selected files to be removed from VCS" />
    <option name="from" path="ActionManager" hit="Schedule selected files to be removed from VCS" />
    <option name="removed" path="ActionManager" hit="Schedule selected files to be removed from VCS" />
    <option name="schedule" path="ActionManager" hit="Schedule selected files to be removed from VCS" />
    <option name="selected" path="ActionManager" hit="Schedule selected files to be removed from VCS" />
    <option name="to" path="ActionManager" hit="Schedule selected files to be removed from VCS" />
    <option name="vcs" path="ActionManager" hit="Schedule selected files to be removed from VCS" />
    <option name="changes" path="ActionManager" hit="Search In Changes" />
    <option name="in" path="ActionManager" hit="Search In Changes" />
    <option name="search" path="ActionManager" hit="Search In Changes" />
    <option name="search" path="ActionManager" hit="Search Structurally..." />
    <option name="structurally" path="ActionManager" hit="Search Structurally..." />
    <option name="columns" path="ActionManager" hit="Select columns to see in the table" />
    <option name="in" path="ActionManager" hit="Select columns to see in the table" />
    <option name="see" path="ActionManager" hit="Select columns to see in the table" />
    <option name="select" path="ActionManager" hit="Select columns to see in the table" />
    <option name="table" path="ActionManager" hit="Select columns to see in the table" />
    <option name="the" path="ActionManager" hit="Select columns to see in the table" />
    <option name="to" path="ActionManager" hit="Select columns to see in the table" />
    <option name="are" path="ActionManager" hit="Selected commits are tracked by different vcses" />
    <option name="by" path="ActionManager" hit="Selected commits are tracked by different vcses" />
    <option name="commits" path="ActionManager" hit="Selected commits are tracked by different vcses" />
    <option name="different" path="ActionManager" hit="Selected commits are tracked by different vcses" />
    <option name="selected" path="ActionManager" hit="Selected commits are tracked by different vcses" />
    <option name="tracked" path="ActionManager" hit="Selected commits are tracked by different vcses" />
    <option name="vcses" path="ActionManager" hit="Selected commits are tracked by different vcses" />
    <option name="active" path="ActionManager" hit="Set Active Changelist" />
    <option name="changelist" path="ActionManager" hit="Set Active Changelist" />
    <option name="set" path="ActionManager" hit="Set Active Changelist" />
    <option name="are" path="ActionManager" hit="Set changelist to which new changes are placed by default" />
    <option name="by" path="ActionManager" hit="Set changelist to which new changes are placed by default" />
    <option name="changelist" path="ActionManager" hit="Set changelist to which new changes are placed by default" />
    <option name="changes" path="ActionManager" hit="Set changelist to which new changes are placed by default" />
    <option name="default" path="ActionManager" hit="Set changelist to which new changes are placed by default" />
    <option name="new" path="ActionManager" hit="Set changelist to which new changes are placed by default" />
    <option name="placed" path="ActionManager" hit="Set changelist to which new changes are placed by default" />
    <option name="set" path="ActionManager" hit="Set changelist to which new changes are placed by default" />
    <option name="to" path="ActionManager" hit="Set changelist to which new changes are placed by default" />
    <option name="which" path="ActionManager" hit="Set changelist to which new changes are placed by default" />
    <option name="changes" path="ActionManager" hit="Shelve Changes..." />
    <option name="shelve" path="ActionManager" hit="Shelve Changes..." />
    <option name="shelve" path="ActionManager" hit="Shelve Silently" />
    <option name="silently" path="ActionManager" hit="Shelve Silently" />
    <option name="appropriate" path="ActionManager" hit="Shelve changes to appropriate shelved changelists" />
    <option name="changelists" path="ActionManager" hit="Shelve changes to appropriate shelved changelists" />
    <option name="changes" path="ActionManager" hit="Shelve changes to appropriate shelved changelists" />
    <option name="shelve" path="ActionManager" hit="Shelve changes to appropriate shelved changelists" />
    <option name="shelved" path="ActionManager" hit="Shelve changes to appropriate shelved changelists" />
    <option name="to" path="ActionManager" hit="Shelve changes to appropriate shelved changelists" />
    <option name="affected" path="ActionManager" hit="Show All Affected Files" />
    <option name="all" path="ActionManager" hit="Show All Affected Files" />
    <option name="files" path="ActionManager" hit="Show All Affected Files" />
    <option name="show" path="ActionManager" hit="Show All Affected Files" />
    <option name="changes" path="ActionManager" hit="Show Changes to Parents" />
    <option name="parents" path="ActionManager" hit="Show Changes to Parents" />
    <option name="show" path="ActionManager" hit="Show Changes to Parents" />
    <option name="to" path="ActionManager" hit="Show Changes to Parents" />
    <option name="combined" path="ActionManager" hit="Show Combined Diff" />
    <option name="diff" path="ActionManager" hit="Show Combined Diff" />
    <option name="show" path="ActionManager" hit="Show Combined Diff" />
    <option name="commit" path="ActionManager" hit="Show Commit Options" />
    <option name="options" path="ActionManager" hit="Show Commit Options" />
    <option name="show" path="ActionManager" hit="Show Commit Options" />
    <option name="commit" path="ActionManager" hit="Show Commit Tooltip" />
    <option name="show" path="ActionManager" hit="Show Commit Tooltip" />
    <option name="tooltip" path="ActionManager" hit="Show Commit Tooltip" />
    <option name="current" path="ActionManager" hit="Show Current Revision" />
    <option name="revision" path="ActionManager" hit="Show Current Revision" />
    <option name="show" path="ActionManager" hit="Show Current Revision" />
    <option name="details" path="ActionManager" hit="Show Details" />
    <option name="show" path="ActionManager" hit="Show Details" />
    <option name="diff" path="ActionManager" hit="Show Diff Preview" />
    <option name="preview" path="ActionManager" hit="Show Diff Preview" />
    <option name="show" path="ActionManager" hit="Show Diff Preview" />
    <option name="diff" path="ActionManager" hit="Show Diff Preview Panel" />
    <option name="panel" path="ActionManager" hit="Show Diff Preview Panel" />
    <option name="preview" path="ActionManager" hit="Show Diff Preview Panel" />
    <option name="show" path="ActionManager" hit="Show Diff Preview Panel" />
    <option name="diff" path="ActionManager" hit="Show Diff for Lines" />
    <option name="for" path="ActionManager" hit="Show Diff for Lines" />
    <option name="lines" path="ActionManager" hit="Show Diff for Lines" />
    <option name="show" path="ActionManager" hit="Show Diff for Lines" />
    <option name="diff" path="ActionManager" hit="Show Diff in Editor Tab" />
    <option name="editor" path="ActionManager" hit="Show Diff in Editor Tab" />
    <option name="in" path="ActionManager" hit="Show Diff in Editor Tab" />
    <option name="show" path="ActionManager" hit="Show Diff in Editor Tab" />
    <option name="tab" path="ActionManager" hit="Show Diff in Editor Tab" />
    <option name="diff" path="ActionManager" hit="Show Diff in Separate Window" />
    <option name="in" path="ActionManager" hit="Show Diff in Separate Window" />
    <option name="separate" path="ActionManager" hit="Show Diff in Separate Window" />
    <option name="show" path="ActionManager" hit="Show Diff in Separate Window" />
    <option name="window" path="ActionManager" hit="Show Diff in Separate Window" />
    <option name="diff" path="ActionManager" hit="Show Diff on Double-Click" />
    <option name="double-click" path="ActionManager" hit="Show Diff on Double-Click" />
    <option name="on" path="ActionManager" hit="Show Diff on Double-Click" />
    <option name="show" path="ActionManager" hit="Show Diff on Double-Click" />
    <option name="at" path="ActionManager" hit="Show File at Revision" />
    <option name="file" path="ActionManager" hit="Show File at Revision" />
    <option name="revision" path="ActionManager" hit="Show File at Revision" />
    <option name="show" path="ActionManager" hit="Show File at Revision" />
    <option name="history" path="ActionManager" hit="Show History" />
    <option name="show" path="ActionManager" hit="Show History" />
    <option name="for" path="ActionManager" hit="Show History for Selection…" />
    <option name="history" path="ActionManager" hit="Show History for Selection…" />
    <option name="selection" path="ActionManager" hit="Show History for Selection…" />
    <option name="show" path="ActionManager" hit="Show History for Selection…" />
    <option name="history" path="ActionManager" hit="Show History…" />
    <option name="show" path="ActionManager" hit="Show History…" />
    <option name="changes" path="ActionManager" hit="Show Local Changes" />
    <option name="local" path="ActionManager" hit="Show Local Changes" />
    <option name="show" path="ActionManager" hit="Show Local Changes" />
    <option name="affected" path="ActionManager" hit="Show Only Affected Changes" />
    <option name="changes" path="ActionManager" hit="Show Only Affected Changes" />
    <option name="only" path="ActionManager" hit="Show Only Affected Changes" />
    <option name="show" path="ActionManager" hit="Show Only Affected Changes" />
    <option name="a" path="ActionManager" hit="Show Only First Reference for a Commit in the Table" />
    <option name="commit" path="ActionManager" hit="Show Only First Reference for a Commit in the Table" />
    <option name="first" path="ActionManager" hit="Show Only First Reference for a Commit in the Table" />
    <option name="for" path="ActionManager" hit="Show Only First Reference for a Commit in the Table" />
    <option name="in" path="ActionManager" hit="Show Only First Reference for a Commit in the Table" />
    <option name="only" path="ActionManager" hit="Show Only First Reference for a Commit in the Table" />
    <option name="reference" path="ActionManager" hit="Show Only First Reference for a Commit in the Table" />
    <option name="show" path="ActionManager" hit="Show Only First Reference for a Commit in the Table" />
    <option name="table" path="ActionManager" hit="Show Only First Reference for a Commit in the Table" />
    <option name="the" path="ActionManager" hit="Show Only First Reference for a Commit in the Table" />
    <option name="diff" path="ActionManager" hit="Show Review Diff" />
    <option name="review" path="ActionManager" hit="Show Review Diff" />
    <option name="show" path="ActionManager" hit="Show Review Diff" />
    <option name="shelf" path="ActionManager" hit="Show Shelf" />
    <option name="show" path="ActionManager" hit="Show Shelf" />
    <option name="double-click" path="ActionManager" hit="Show Source on Double-Click" />
    <option name="on" path="ActionManager" hit="Show Source on Double-Click" />
    <option name="show" path="ActionManager" hit="Show Source on Double-Click" />
    <option name="source" path="ActionManager" hit="Show Source on Double-Click" />
    <option name="in" path="ActionManager" hit="Show Tag Names in the Table" />
    <option name="names" path="ActionManager" hit="Show Tag Names in the Table" />
    <option name="show" path="ActionManager" hit="Show Tag Names in the Table" />
    <option name="table" path="ActionManager" hit="Show Tag Names in the Table" />
    <option name="tag" path="ActionManager" hit="Show Tag Names in the Table" />
    <option name="the" path="ActionManager" hit="Show Tag Names in the Table" />
    <option name="console" path="ActionManager" hit="Show VCS Console Tab" />
    <option name="show" path="ActionManager" hit="Show VCS Console Tab" />
    <option name="tab" path="ActionManager" hit="Show VCS Console Tab" />
    <option name="vcs" path="ActionManager" hit="Show VCS Console Tab" />
    <option name="log" path="ActionManager" hit="Show Vcs Log" />
    <option name="show" path="ActionManager" hit="Show Vcs Log" />
    <option name="vcs" path="ActionManager" hit="Show Vcs Log" />
    <option name="all" path="ActionManager" hit="Show all changes made in the chosen revision" />
    <option name="changes" path="ActionManager" hit="Show all changes made in the chosen revision" />
    <option name="chosen" path="ActionManager" hit="Show all changes made in the chosen revision" />
    <option name="in" path="ActionManager" hit="Show all changes made in the chosen revision" />
    <option name="made" path="ActionManager" hit="Show all changes made in the chosen revision" />
    <option name="revision" path="ActionManager" hit="Show all changes made in the chosen revision" />
    <option name="show" path="ActionManager" hit="Show all changes made in the chosen revision" />
    <option name="the" path="ActionManager" hit="Show all changes made in the chosen revision" />
    <option name="as" path="ActionManager" hit="Show as JSON" />
    <option name="json" path="ActionManager" hit="Show as JSON" />
    <option name="show" path="ActionManager" hit="Show as JSON" />
    <option name="commit" path="ActionManager" hit="Show commit message history" />
    <option name="history" path="ActionManager" hit="Show commit message history" />
    <option name="message" path="ActionManager" hit="Show commit message history" />
    <option name="show" path="ActionManager" hit="Show commit message history" />
    <option name="all" path="ActionManager" hit="Show diff for all selected changes in one place" />
    <option name="changes" path="ActionManager" hit="Show diff for all selected changes in one place" />
    <option name="diff" path="ActionManager" hit="Show diff for all selected changes in one place" />
    <option name="for" path="ActionManager" hit="Show diff for all selected changes in one place" />
    <option name="in" path="ActionManager" hit="Show diff for all selected changes in one place" />
    <option name="one" path="ActionManager" hit="Show diff for all selected changes in one place" />
    <option name="place" path="ActionManager" hit="Show diff for all selected changes in one place" />
    <option name="selected" path="ActionManager" hit="Show diff for all selected changes in one place" />
    <option name="show" path="ActionManager" hit="Show diff for all selected changes in one place" />
    <option name="diff" path="ActionManager" hit="Show diff for selected lines" />
    <option name="for" path="ActionManager" hit="Show diff for selected lines" />
    <option name="lines" path="ActionManager" hit="Show diff for selected lines" />
    <option name="selected" path="ActionManager" hit="Show diff for selected lines" />
    <option name="show" path="ActionManager" hit="Show diff for selected lines" />
    <option name="files" path="ActionManager" hit="Show files modification info" />
    <option name="info" path="ActionManager" hit="Show files modification info" />
    <option name="modification" path="ActionManager" hit="Show files modification info" />
    <option name="show" path="ActionManager" hit="Show files modification info" />
    <option name="editor" path="ActionManager" hit="Show history for editor selection" />
    <option name="for" path="ActionManager" hit="Show history for editor selection" />
    <option name="history" path="ActionManager" hit="Show history for editor selection" />
    <option name="selection" path="ActionManager" hit="Show history for editor selection" />
    <option name="show" path="ActionManager" hit="Show history for editor selection" />
    <option name="file" path="ActionManager" hit="Show history for file until the selected revision" />
    <option name="for" path="ActionManager" hit="Show history for file until the selected revision" />
    <option name="history" path="ActionManager" hit="Show history for file until the selected revision" />
    <option name="revision" path="ActionManager" hit="Show history for file until the selected revision" />
    <option name="selected" path="ActionManager" hit="Show history for file until the selected revision" />
    <option name="show" path="ActionManager" hit="Show history for file until the selected revision" />
    <option name="the" path="ActionManager" hit="Show history for file until the selected revision" />
    <option name="until" path="ActionManager" hit="Show history for file until the selected revision" />
    <option name="file" path="ActionManager" hit="Show history of file" />
    <option name="history" path="ActionManager" hit="Show history of file" />
    <option name="of" path="ActionManager" hit="Show history of file" />
    <option name="show" path="ActionManager" hit="Show history of file" />
    <option name="are" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="branch" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="commits" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="current" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="edges" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="even" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="if" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="in" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="invisible" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="long" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="show" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="the" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="view" path="ActionManager" hit="Show long branch edges even if commits are invisible in the current view." />
    <option name="change" path="ActionManager" hit="Show popup for currently selected change" />
    <option name="currently" path="ActionManager" hit="Show popup for currently selected change" />
    <option name="for" path="ActionManager" hit="Show popup for currently selected change" />
    <option name="popup" path="ActionManager" hit="Show popup for currently selected change" />
    <option name="selected" path="ActionManager" hit="Show popup for currently selected change" />
    <option name="show" path="ActionManager" hit="Show popup for currently selected change" />
    <option name="abbreviation" path="ActionManager" hit="Show preview for emmet abbreviation" />
    <option name="emmet" path="ActionManager" hit="Show preview for emmet abbreviation" />
    <option name="for" path="ActionManager" hit="Show preview for emmet abbreviation" />
    <option name="preview" path="ActionManager" hit="Show preview for emmet abbreviation" />
    <option name="show" path="ActionManager" hit="Show preview for emmet abbreviation" />
    <option name="commit" path="ActionManager" hit="Show references on the left of commit message" />
    <option name="left" path="ActionManager" hit="Show references on the left of commit message" />
    <option name="message" path="ActionManager" hit="Show references on the left of commit message" />
    <option name="of" path="ActionManager" hit="Show references on the left of commit message" />
    <option name="on" path="ActionManager" hit="Show references on the left of commit message" />
    <option name="references" path="ActionManager" hit="Show references on the left of commit message" />
    <option name="show" path="ActionManager" hit="Show references on the left of commit message" />
    <option name="the" path="ActionManager" hit="Show references on the left of commit message" />
    <option name="also" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="and" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="as" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="author" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="blame" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="date" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="each" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="for" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="known" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="latest" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="line" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="modification" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="show" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="the" path="ActionManager" hit="Show the latest modification date and author for each line (also known as &quot;Blame&quot;)" />
    <option name="authored" path="ActionManager" hit="Show the time when the change was committed, rather than authored." />
    <option name="change" path="ActionManager" hit="Show the time when the change was committed, rather than authored." />
    <option name="committed" path="ActionManager" hit="Show the time when the change was committed, rather than authored." />
    <option name="rather" path="ActionManager" hit="Show the time when the change was committed, rather than authored." />
    <option name="show" path="ActionManager" hit="Show the time when the change was committed, rather than authored." />
    <option name="than" path="ActionManager" hit="Show the time when the change was committed, rather than authored." />
    <option name="the" path="ActionManager" hit="Show the time when the change was committed, rather than authored." />
    <option name="time" path="ActionManager" hit="Show the time when the change was committed, rather than authored." />
    <option name="was" path="ActionManager" hit="Show the time when the change was committed, rather than authored." />
    <option name="when" path="ActionManager" hit="Show the time when the change was committed, rather than authored." />
    <option name="commit" path="ActionManager" hit="Show tooltip for currently selected commit in the Log" />
    <option name="currently" path="ActionManager" hit="Show tooltip for currently selected commit in the Log" />
    <option name="for" path="ActionManager" hit="Show tooltip for currently selected commit in the Log" />
    <option name="in" path="ActionManager" hit="Show tooltip for currently selected commit in the Log" />
    <option name="log" path="ActionManager" hit="Show tooltip for currently selected commit in the Log" />
    <option name="selected" path="ActionManager" hit="Show tooltip for currently selected commit in the Log" />
    <option name="show" path="ActionManager" hit="Show tooltip for currently selected commit in the Log" />
    <option name="the" path="ActionManager" hit="Show tooltip for currently selected commit in the Log" />
    <option name="tooltip" path="ActionManager" hit="Show tooltip for currently selected commit in the Log" />
    <option name="already" path="ActionManager" hit="Show/Hide already unshelved" />
    <option name="hide" path="ActionManager" hit="Show/Hide already unshelved" />
    <option name="show" path="ActionManager" hit="Show/Hide already unshelved" />
    <option name="unshelved" path="ActionManager" hit="Show/Hide already unshelved" />
    <option name="a" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="branch" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="commit" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="hash" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="it" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="name" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="navigate" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="of" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="or" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="points" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="specify" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="tag" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="the" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="to" path="ActionManager" hit="Specify hash or name of a branch or a tag to navigate to the commit it points" />
    <option name="replace" path="ActionManager" hit="Structural Replace" />
    <option name="structural" path="ActionManager" hit="Structural Replace" />
    <option name="search" path="ActionManager" hit="Structural Search" />
    <option name="structural" path="ActionManager" hit="Structural Search" />
    <option name="abbreviation" path="ActionManager" hit="Surround selected code with Emmet abbreviation" />
    <option name="code" path="ActionManager" hit="Surround selected code with Emmet abbreviation" />
    <option name="emmet" path="ActionManager" hit="Surround selected code with Emmet abbreviation" />
    <option name="selected" path="ActionManager" hit="Surround selected code with Emmet abbreviation" />
    <option name="surround" path="ActionManager" hit="Surround selected code with Emmet abbreviation" />
    <option name="with" path="ActionManager" hit="Surround selected code with Emmet abbreviation" />
    <option name="emmet" path="ActionManager" hit="Surround with Emmet" />
    <option name="surround" path="ActionManager" hit="Surround with Emmet" />
    <option name="with" path="ActionManager" hit="Surround with Emmet" />
    <option name="commit" path="ActionManager" hit="Switch to Commit Dialog" />
    <option name="dialog" path="ActionManager" hit="Switch to Commit Dialog" />
    <option name="switch" path="ActionManager" hit="Switch to Commit Dialog" />
    <option name="to" path="ActionManager" hit="Switch to Commit Dialog" />
    <option name="names" path="ActionManager" hit="Tag Names" />
    <option name="tag" path="ActionManager" hit="Tag Names" />
    <option name="commit" path="ActionManager" hit="Toggle Commit UI..." />
    <option name="toggle" path="ActionManager" hit="Toggle Commit UI..." />
    <option name="ui" path="ActionManager" hit="Toggle Commit UI..." />
    <option name="intellisort" path="ActionManager" hit="Turn IntelliSort On/Off" />
    <option name="off" path="ActionManager" hit="Turn IntelliSort On/Off" />
    <option name="on" path="ActionManager" hit="Turn IntelliSort On/Off" />
    <option name="turn" path="ActionManager" hit="Turn IntelliSort On/Off" />
    <option name="unshelve" path="ActionManager" hit="Unshelve" />
    <option name="silently" path="ActionManager" hit="Unshelve Silently" />
    <option name="unshelve" path="ActionManager" hit="Unshelve Silently" />
    <option name="and" path="ActionManager" hit="Unshelve and Remove" />
    <option name="remove" path="ActionManager" hit="Unshelve and Remove" />
    <option name="unshelve" path="ActionManager" hit="Unshelve and Remove" />
    <option name="appropriate" path="ActionManager" hit="Unshelve changes to appropriate changelists" />
    <option name="changelists" path="ActionManager" hit="Unshelve changes to appropriate changelists" />
    <option name="changes" path="ActionManager" hit="Unshelve changes to appropriate changelists" />
    <option name="to" path="ActionManager" hit="Unshelve changes to appropriate changelists" />
    <option name="unshelve" path="ActionManager" hit="Unshelve changes to appropriate changelists" />
    <option name="changes" path="ActionManager" hit="Unshelve selected changes" />
    <option name="selected" path="ActionManager" hit="Unshelve selected changes" />
    <option name="unshelve" path="ActionManager" hit="Unshelve selected changes" />
    <option name="and" path="ActionManager" hit="Unshelve selected changes and remove them from the shelf" />
    <option name="changes" path="ActionManager" hit="Unshelve selected changes and remove them from the shelf" />
    <option name="from" path="ActionManager" hit="Unshelve selected changes and remove them from the shelf" />
    <option name="remove" path="ActionManager" hit="Unshelve selected changes and remove them from the shelf" />
    <option name="selected" path="ActionManager" hit="Unshelve selected changes and remove them from the shelf" />
    <option name="shelf" path="ActionManager" hit="Unshelve selected changes and remove them from the shelf" />
    <option name="the" path="ActionManager" hit="Unshelve selected changes and remove them from the shelf" />
    <option name="them" path="ActionManager" hit="Unshelve selected changes and remove them from the shelf" />
    <option name="unshelve" path="ActionManager" hit="Unshelve selected changes and remove them from the shelf" />
    <option name="unshelve" path="ActionManager" hit="Unshelve..." />
    <option name="update" path="ActionManager" hit="Update" />
    <option name="project" path="ActionManager" hit="Update Project" />
    <option name="update" path="ActionManager" hit="Update Project" />
    <option name="abbreviation" path="ActionManager" hit="Update existing HTML tag with Emmet abbreviation" />
    <option name="emmet" path="ActionManager" hit="Update existing HTML tag with Emmet abbreviation" />
    <option name="existing" path="ActionManager" hit="Update existing HTML tag with Emmet abbreviation" />
    <option name="html" path="ActionManager" hit="Update existing HTML tag with Emmet abbreviation" />
    <option name="tag" path="ActionManager" hit="Update existing HTML tag with Emmet abbreviation" />
    <option name="update" path="ActionManager" hit="Update existing HTML tag with Emmet abbreviation" />
    <option name="with" path="ActionManager" hit="Update existing HTML tag with Emmet abbreviation" />
    <option name="emmet" path="ActionManager" hit="Update tag with Emmet" />
    <option name="tag" path="ActionManager" hit="Update tag with Emmet" />
    <option name="update" path="ActionManager" hit="Update tag with Emmet" />
    <option name="with" path="ActionManager" hit="Update tag with Emmet" />
    <option name="group" path="ActionManager" hit="VCS Group" />
    <option name="vcs" path="ActionManager" hit="VCS Group" />
    <option name="label" path="ActionManager" hit="VCS Label" />
    <option name="vcs" path="ActionManager" hit="VCS Label" />
    <option name="operations" path="ActionManager" hit="VCS Operations" />
    <option name="vcs" path="ActionManager" hit="VCS Operations" />
    <option name="operations" path="ActionManager" hit="VCS Operations Popup..." />
    <option name="popup" path="ActionManager" hit="VCS Operations Popup..." />
    <option name="vcs" path="ActionManager" hit="VCS Operations Popup..." />
    <option name="history" path="ActionManager" hit="Validate Local History Storage" />
    <option name="local" path="ActionManager" hit="Validate Local History Storage" />
    <option name="storage" path="ActionManager" hit="Validate Local History Storage" />
    <option name="validate" path="ActionManager" hit="Validate Local History Storage" />
    <option name="control" path="ActionManager" hit="Version Control" />
    <option name="version" path="ActionManager" hit="Version Control" />
    <option name="control" path="ActionManager" hit="Version Control Group" />
    <option name="group" path="ActionManager" hit="Version Control Group" />
    <option name="version" path="ActionManager" hit="Version Control Group" />
    <option name="options" path="ActionManager" hit="View Options" />
    <option name="view" path="ActionManager" hit="View Options" />
    <option name="about" path="ActionManager" hit="View complete information about the selected changelist" />
    <option name="changelist" path="ActionManager" hit="View complete information about the selected changelist" />
    <option name="complete" path="ActionManager" hit="View complete information about the selected changelist" />
    <option name="information" path="ActionManager" hit="View complete information about the selected changelist" />
    <option name="selected" path="ActionManager" hit="View complete information about the selected changelist" />
    <option name="the" path="ActionManager" hit="View complete information about the selected changelist" />
    <option name="view" path="ActionManager" hit="View complete information about the selected changelist" />
    <option name="changes" path="ActionManager" hit="View history of committed changes" />
    <option name="committed" path="ActionManager" hit="View history of committed changes" />
    <option name="history" path="ActionManager" hit="View history of committed changes" />
    <option name="of" path="ActionManager" hit="View history of committed changes" />
    <option name="view" path="ActionManager" hit="View history of committed changes" />
    <option name="actions" path="ActionManager" hit="XML Actions" />
    <option name="xml" path="ActionManager" hit="XML Actions" />
    <option name="tag" path="ActionManager" hit="XML Tag..." />
    <option name="xml" path="ActionManager" hit="XML Tag..." />
  </configurable>
  <configurable id="editor.breadcrumbs" configurable_name="Breadcrumbs">
    <option name="xml" hit="XML" />
  </configurable>
  <configurable id="editor.breadcrumbs" configurable_name="Breadcrumbs">
    <option name="xhtml" hit="XHTML" />
  </configurable>
  <configurable id="editor.breadcrumbs" configurable_name="Breadcrumbs">
    <option name="html" hit="HTML" />
  </configurable>
  <configurable id="editor.breadcrumbs" configurable_name="Breadcrumbs">
    <option name="json" hit="JSON" />
  </configurable>
  <configurable id="editor.preferences.webOptions" configurable_name="HTML/CSS">
    <option name="add" hit="Add quotes for attribute value on typing '=' and attribute completion" />
    <option name="and" hit="Add quotes for attribute value on typing '=' and attribute completion" />
    <option name="attribute" hit="Add quotes for attribute value on typing '=' and attribute completion" />
    <option name="completion" hit="Add quotes for attribute value on typing '=' and attribute completion" />
    <option name="for" hit="Add quotes for attribute value on typing '=' and attribute completion" />
    <option name="on" hit="Add quotes for attribute value on typing '=' and attribute completion" />
    <option name="quotes" hit="Add quotes for attribute value on typing '=' and attribute completion" />
    <option name="typing" hit="Add quotes for attribute value on typing '=' and attribute completion" />
    <option name="value" hit="Add quotes for attribute value on typing '=' and attribute completion" />
    <option name="auto-close" hit="Auto-close tag on typing '&lt;/'" />
    <option name="on" hit="Auto-close tag on typing '&lt;/'" />
    <option name="tag" hit="Auto-close tag on typing '&lt;/'" />
    <option name="typing" hit="Auto-close tag on typing '&lt;/'" />
    <option name="css" hit="CSS" />
    <option name="css" hit="HTML/CSS" />
    <option name="html" hit="HTML/CSS" />
    <option name="closing" hit="Insert closing tag on tag completion" />
    <option name="completion" hit="Insert closing tag on tag completion" />
    <option name="insert" hit="Insert closing tag on tag completion" />
    <option name="on" hit="Insert closing tag on tag completion" />
    <option name="tag" hit="Insert closing tag on tag completion" />
    <option name="attributes" hit="Insert required attributes on tag completion" />
    <option name="completion" hit="Insert required attributes on tag completion" />
    <option name="insert" hit="Insert required attributes on tag completion" />
    <option name="on" hit="Insert required attributes on tag completion" />
    <option name="required" hit="Insert required attributes on tag completion" />
    <option name="tag" hit="Insert required attributes on tag completion" />
    <option name="completion" hit="Insert required subtags on tag completion" />
    <option name="insert" hit="Insert required subtags on tag completion" />
    <option name="on" hit="Insert required subtags on tag completion" />
    <option name="required" hit="Insert required subtags on tag completion" />
    <option name="subtags" hit="Insert required subtags on tag completion" />
    <option name="tag" hit="Insert required subtags on tag completion" />
    <option name="click" hit="Select whole CSS identifiers on double click" />
    <option name="css" hit="Select whole CSS identifiers on double click" />
    <option name="double" hit="Select whole CSS identifiers on double click" />
    <option name="identifiers" hit="Select whole CSS identifiers on double click" />
    <option name="on" hit="Select whole CSS identifiers on double click" />
    <option name="select" hit="Select whole CSS identifiers on double click" />
    <option name="whole" hit="Select whole CSS identifiers on double click" />
    <option name="editing" hit="Simultaneous '  ' editing" />
    <option name="simultaneous" hit="Simultaneous '  ' editing" />
    <option name="attribute" hit="Start attribute on tag completion" />
    <option name="completion" hit="Start attribute on tag completion" />
    <option name="on" hit="Start attribute on tag completion" />
    <option name="start" hit="Start attribute on tag completion" />
    <option name="tag" hit="Start attribute on tag completion" />
    <option name="html" hit="XML/HTML" />
    <option name="xml" hit="XML/HTML" />
  </configurable>
  <configurable id="editor.preferences.jsonOptions" configurable_name="JSON">
    <option name="add" hit="Automatically add quotes to property names when typing ':'" />
    <option name="automatically" hit="Automatically add quotes to property names when typing ':'" />
    <option name="names" hit="Automatically add quotes to property names when typing ':'" />
    <option name="property" hit="Automatically add quotes to property names when typing ':'" />
    <option name="quotes" hit="Automatically add quotes to property names when typing ':'" />
    <option name="to" hit="Automatically add quotes to property names when typing ':'" />
    <option name="typing" hit="Automatically add quotes to property names when typing ':'" />
    <option name="when" hit="Automatically add quotes to property names when typing ':'" />
    <option name="add" hit="Automatically add whitespace when typing ':' after property names" />
    <option name="after" hit="Automatically add whitespace when typing ':' after property names" />
    <option name="automatically" hit="Automatically add whitespace when typing ':' after property names" />
    <option name="names" hit="Automatically add whitespace when typing ':' after property names" />
    <option name="property" hit="Automatically add whitespace when typing ':' after property names" />
    <option name="typing" hit="Automatically add whitespace when typing ':' after property names" />
    <option name="when" hit="Automatically add whitespace when typing ':' after property names" />
    <option name="whitespace" hit="Automatically add whitespace when typing ':' after property names" />
    <option name="automatically" hit="Automatically manage commas when pasting JSON fragments" />
    <option name="commas" hit="Automatically manage commas when pasting JSON fragments" />
    <option name="fragments" hit="Automatically manage commas when pasting JSON fragments" />
    <option name="json" hit="Automatically manage commas when pasting JSON fragments" />
    <option name="manage" hit="Automatically manage commas when pasting JSON fragments" />
    <option name="pasting" hit="Automatically manage commas when pasting JSON fragments" />
    <option name="when" hit="Automatically manage commas when pasting JSON fragments" />
    <option name="after" hit="Automatically move ':' after the property name if typed inside quotes" />
    <option name="automatically" hit="Automatically move ':' after the property name if typed inside quotes" />
    <option name="if" hit="Automatically move ':' after the property name if typed inside quotes" />
    <option name="inside" hit="Automatically move ':' after the property name if typed inside quotes" />
    <option name="move" hit="Automatically move ':' after the property name if typed inside quotes" />
    <option name="name" hit="Automatically move ':' after the property name if typed inside quotes" />
    <option name="property" hit="Automatically move ':' after the property name if typed inside quotes" />
    <option name="quotes" hit="Automatically move ':' after the property name if typed inside quotes" />
    <option name="the" hit="Automatically move ':' after the property name if typed inside quotes" />
    <option name="typed" hit="Automatically move ':' after the property name if typed inside quotes" />
    <option name="after" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="array" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="automatically" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="comma" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="element" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="if" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="inside" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="move" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="or" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="property" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="quotes" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="the" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="value" hit="Automatically move comma after the property value or array element if inside quotes" />
    <option name="escape" hit="Escape text on paste in string literals" />
    <option name="in" hit="Escape text on paste in string literals" />
    <option name="literals" hit="Escape text on paste in string literals" />
    <option name="on" hit="Escape text on paste in string literals" />
    <option name="paste" hit="Escape text on paste in string literals" />
    <option name="string" hit="Escape text on paste in string literals" />
    <option name="text" hit="Escape text on paste in string literals" />
    <option name="after" hit="Insert missing comma after matching braces and quotes" />
    <option name="and" hit="Insert missing comma after matching braces and quotes" />
    <option name="braces" hit="Insert missing comma after matching braces and quotes" />
    <option name="comma" hit="Insert missing comma after matching braces and quotes" />
    <option name="insert" hit="Insert missing comma after matching braces and quotes" />
    <option name="matching" hit="Insert missing comma after matching braces and quotes" />
    <option name="missing" hit="Insert missing comma after matching braces and quotes" />
    <option name="quotes" hit="Insert missing comma after matching braces and quotes" />
    <option name="comma" hit="Insert missing comma on Enter" />
    <option name="enter" hit="Insert missing comma on Enter" />
    <option name="insert" hit="Insert missing comma on Enter" />
    <option name="missing" hit="Insert missing comma on Enter" />
    <option name="on" hit="Insert missing comma on Enter" />
    <option name="json" hit="JSON" />
  </configurable>
  <configurable id="reference.settingsdialog.IDE.editor.colors.VCS" configurable_name="VCS">
    <option name="background" hit="Background" />
    <option name="bold" hit="Bold" />
    <option name="bold" hit="Bold Underscored" />
    <option name="underscored" hit="Bold Underscored" />
    <option name="bordered" hit="Bordered" />
    <option name="dotted" hit="Dotted Line" />
    <option name="line" hit="Dotted Line" />
    <option name="effects" hit="Effects" />
    <option name="error" hit="Error stripe mark" />
    <option name="mark" hit="Error stripe mark" />
    <option name="stripe" hit="Error stripe mark" />
    <option name="font" hit="Font" />
    <option name="foreground" hit="Foreground" />
    <option name="from" hit="Inherit values from:" />
    <option name="inherit" hit="Inherit values from:" />
    <option name="values" hit="Inherit values from:" />
    <option name="italic" hit="Italic" />
    <option name="strikeout" hit="Strikeout" />
    <option name="underscored" hit="Underscored" />
    <option name="underwaved" hit="Underwaved" />
    <option name="vcs" hit="VCS" />
    <option name="added" hit="Editor Gutter//Added ignored lines border" />
    <option name="border" hit="Editor Gutter//Added ignored lines border" />
    <option name="editor" hit="Editor Gutter//Added ignored lines border" />
    <option name="gutter" hit="Editor Gutter//Added ignored lines border" />
    <option name="ignored" hit="Editor Gutter//Added ignored lines border" />
    <option name="lines" hit="Editor Gutter//Added ignored lines border" />
    <option name="added" hit="Editor Gutter//Added lines" />
    <option name="editor" hit="Editor Gutter//Added lines" />
    <option name="gutter" hit="Editor Gutter//Added lines" />
    <option name="lines" hit="Editor Gutter//Added lines" />
    <option name="border" hit="Editor Gutter//Border" />
    <option name="editor" hit="Editor Gutter//Border" />
    <option name="gutter" hit="Editor Gutter//Border" />
    <option name="changed" hit="Editor Gutter//Changed lines popup" />
    <option name="editor" hit="Editor Gutter//Changed lines popup" />
    <option name="gutter" hit="Editor Gutter//Changed lines popup" />
    <option name="lines" hit="Editor Gutter//Changed lines popup" />
    <option name="popup" hit="Editor Gutter//Changed lines popup" />
    <option name="border" hit="Editor Gutter//Deleted ignored lines border" />
    <option name="deleted" hit="Editor Gutter//Deleted ignored lines border" />
    <option name="editor" hit="Editor Gutter//Deleted ignored lines border" />
    <option name="gutter" hit="Editor Gutter//Deleted ignored lines border" />
    <option name="ignored" hit="Editor Gutter//Deleted ignored lines border" />
    <option name="lines" hit="Editor Gutter//Deleted ignored lines border" />
    <option name="deleted" hit="Editor Gutter//Deleted lines" />
    <option name="editor" hit="Editor Gutter//Deleted lines" />
    <option name="gutter" hit="Editor Gutter//Deleted lines" />
    <option name="lines" hit="Editor Gutter//Deleted lines" />
    <option name="border" hit="Editor Gutter//Modified ignored lines border" />
    <option name="editor" hit="Editor Gutter//Modified ignored lines border" />
    <option name="gutter" hit="Editor Gutter//Modified ignored lines border" />
    <option name="ignored" hit="Editor Gutter//Modified ignored lines border" />
    <option name="lines" hit="Editor Gutter//Modified ignored lines border" />
    <option name="modified" hit="Editor Gutter//Modified ignored lines border" />
    <option name="editor" hit="Editor Gutter//Modified lines" />
    <option name="gutter" hit="Editor Gutter//Modified lines" />
    <option name="lines" hit="Editor Gutter//Modified lines" />
    <option name="modified" hit="Editor Gutter//Modified lines" />
    <option name="editor" hit="Editor Gutter//Whitespace-modified lines" />
    <option name="gutter" hit="Editor Gutter//Whitespace-modified lines" />
    <option name="lines" hit="Editor Gutter//Whitespace-modified lines" />
    <option name="whitespace-modified" hit="Editor Gutter//Whitespace-modified lines" />
    <option name="#1" hit="VCS Annotations//Background color #1" />
    <option name="annotations" hit="VCS Annotations//Background color #1" />
    <option name="background" hit="VCS Annotations//Background color #1" />
    <option name="color" hit="VCS Annotations//Background color #1" />
    <option name="vcs" hit="VCS Annotations//Background color #1" />
    <option name="#2" hit="VCS Annotations//Background color #2" />
    <option name="annotations" hit="VCS Annotations//Background color #2" />
    <option name="background" hit="VCS Annotations//Background color #2" />
    <option name="color" hit="VCS Annotations//Background color #2" />
    <option name="vcs" hit="VCS Annotations//Background color #2" />
    <option name="#3" hit="VCS Annotations//Background color #3" />
    <option name="annotations" hit="VCS Annotations//Background color #3" />
    <option name="background" hit="VCS Annotations//Background color #3" />
    <option name="color" hit="VCS Annotations//Background color #3" />
    <option name="vcs" hit="VCS Annotations//Background color #3" />
    <option name="#4" hit="VCS Annotations//Background color #4" />
    <option name="annotations" hit="VCS Annotations//Background color #4" />
    <option name="background" hit="VCS Annotations//Background color #4" />
    <option name="color" hit="VCS Annotations//Background color #4" />
    <option name="vcs" hit="VCS Annotations//Background color #4" />
    <option name="#5" hit="VCS Annotations//Background color #5" />
    <option name="annotations" hit="VCS Annotations//Background color #5" />
    <option name="background" hit="VCS Annotations//Background color #5" />
    <option name="color" hit="VCS Annotations//Background color #5" />
    <option name="vcs" hit="VCS Annotations//Background color #5" />
    <option name="annotations" hit="VCS Annotations//Foreground" />
    <option name="foreground" hit="VCS Annotations//Foreground" />
    <option name="vcs" hit="VCS Annotations//Foreground" />
    <option name="annotations" hit="VCS Annotations//Foreground for last commit" />
    <option name="commit" hit="VCS Annotations//Foreground for last commit" />
    <option name="for" hit="VCS Annotations//Foreground for last commit" />
    <option name="foreground" hit="VCS Annotations//Foreground for last commit" />
    <option name="last" hit="VCS Annotations//Foreground for last commit" />
    <option name="vcs" hit="VCS Annotations//Foreground for last commit" />
  </configurable>
  <configurable id="reference.settingsdialog.IDE.editor.colors.HTML" configurable_name="HTML">
    <option name="html" hit="HTML" />
    <option name="attribute" hit="Attribute name" />
    <option name="name" hit="Attribute name" />
    <option name="attribute" hit="Attribute value" />
    <option name="value" hit="Attribute value" />
    <option name="comment" hit="Comment" />
    <option name="custom" hit="Custom Tag Name" />
    <option name="name" hit="Custom Tag Name" />
    <option name="tag" hit="Custom Tag Name" />
    <option name="entity" hit="Entity reference" />
    <option name="reference" hit="Entity reference" />
    <option name="code" hit="HTML code" />
    <option name="html" hit="HTML code" />
    <option name="fragment" hit="Injected Language Fragment" />
    <option name="injected" hit="Injected Language Fragment" />
    <option name="language" hit="Injected Language Fragment" />
    <option name="tag" hit="Tag" />
    <option name="name" hit="Tag name" />
    <option name="tag" hit="Tag name" />
    <option name="1" hit="Tag tree (level 1)" />
    <option name="level" hit="Tag tree (level 1)" />
    <option name="tag" hit="Tag tree (level 1)" />
    <option name="tree" hit="Tag tree (level 1)" />
    <option name="2" hit="Tag tree (level 2)" />
    <option name="level" hit="Tag tree (level 2)" />
    <option name="tag" hit="Tag tree (level 2)" />
    <option name="tree" hit="Tag tree (level 2)" />
    <option name="3" hit="Tag tree (level 3)" />
    <option name="level" hit="Tag tree (level 3)" />
    <option name="tag" hit="Tag tree (level 3)" />
    <option name="tree" hit="Tag tree (level 3)" />
    <option name="4" hit="Tag tree (level 4)" />
    <option name="level" hit="Tag tree (level 4)" />
    <option name="tag" hit="Tag tree (level 4)" />
    <option name="tree" hit="Tag tree (level 4)" />
    <option name="5" hit="Tag tree (level 5)" />
    <option name="level" hit="Tag tree (level 5)" />
    <option name="tag" hit="Tag tree (level 5)" />
    <option name="tree" hit="Tag tree (level 5)" />
    <option name="6" hit="Tag tree (level 6)" />
    <option name="level" hit="Tag tree (level 6)" />
    <option name="tag" hit="Tag tree (level 6)" />
    <option name="tree" hit="Tag tree (level 6)" />
  </configurable>
  <configurable id="reference.settingsdialog.IDE.editor.colors.JSON" configurable_name="JSON">
    <option name="json" hit="JSON" />
    <option name="block" hit="Block comment" />
    <option name="comment" hit="Block comment" />
    <option name="braces" hit="Braces" />
    <option name="brackets" hit="Brackets" />
    <option name="colon" hit="Colon" />
    <option name="comma" hit="Comma" />
    <option name="escape" hit="Invalid escape sequence" />
    <option name="invalid" hit="Invalid escape sequence" />
    <option name="sequence" hit="Invalid escape sequence" />
    <option name="keyword" hit="Keyword" />
    <option name="comment" hit="Line comment" />
    <option name="line" hit="Line comment" />
    <option name="number" hit="Number" />
    <option name="parameter" hit="Parameter" />
    <option name="key" hit="Property key" />
    <option name="property" hit="Property key" />
    <option name="highlighting" hit="Semantic highlighting" />
    <option name="semantic" hit="Semantic highlighting" />
    <option name="string" hit="String" />
    <option name="escape" hit="Valid escape sequence" />
    <option name="sequence" hit="Valid escape sequence" />
    <option name="valid" hit="Valid escape sequence" />
  </configurable>
  <configurable id="reference.settingsdialog.IDE.editor.colors.RegExp" configurable_name="RegExp">
    <option name="regexp" hit="RegExp" />
    <option name="bad" hit="Bad character" />
    <option name="character" hit="Bad character" />
    <option name="brace" hit="Brace" />
    <option name="bracket" hit="Bracket" />
    <option name="character" hit="Character class" />
    <option name="class" hit="Character class" />
    <option name="comma" hit="Comma" />
    <option name="comment" hit="Comment" />
    <option name="dot" hit="Dot" />
    <option name="character" hit="Escaped character" />
    <option name="escaped" hit="Escaped character" />
    <option name="inline" hit="Inline option" />
    <option name="option" hit="Inline option" />
    <option name="escape" hit="Invalid escape sequence" />
    <option name="invalid" hit="Invalid escape sequence" />
    <option name="sequence" hit="Invalid escape sequence" />
    <option name="groups" hit="Matched groups" />
    <option name="matched" hit="Matched groups" />
    <option name="name" hit="Name" />
    <option name="character" hit="Operator character" />
    <option name="operator" hit="Operator character" />
    <option name="parenthesis" hit="Parenthesis" />
    <option name="character" hit="Plain character" />
    <option name="plain" hit="Plain character" />
    <option name="quantifier" hit="Quantifier" />
    <option name="escape" hit="Quote escape" />
    <option name="quote" hit="Quote escape" />
    <option name="escape" hit="Redundant escape sequence" />
    <option name="redundant" hit="Redundant escape sequence" />
    <option name="sequence" hit="Redundant escape sequence" />
  </configurable>
  <configurable id="reference.settingsdialog.IDE.editor.colors.XML" configurable_name="XML">
    <option name="xml" hit="XML" />
    <option name="attribute" hit="Attribute Name" />
    <option name="name" hit="Attribute Name" />
    <option name="attribute" hit="Attribute Value" />
    <option name="value" hit="Attribute Value" />
    <option name="comment" hit="Comment" />
    <option name="custom" hit="Custom Tag Name" />
    <option name="name" hit="Custom Tag Name" />
    <option name="tag" hit="Custom Tag Name" />
    <option name="entity" hit="Entity Reference" />
    <option name="reference" hit="Entity Reference" />
    <option name="fragment" hit="Injected Language Fragment" />
    <option name="injected" hit="Injected Language Fragment" />
    <option name="language" hit="Injected Language Fragment" />
    <option name="matched" hit="Matched Tag" />
    <option name="tag" hit="Matched Tag" />
    <option name="namespace" hit="Namespace Prefix" />
    <option name="prefix" hit="Namespace Prefix" />
    <option name="prologue" hit="Prologue" />
    <option name="tag" hit="Tag" />
    <option name="data" hit="Tag Data" />
    <option name="tag" hit="Tag Data" />
    <option name="name" hit="Tag Name" />
    <option name="tag" hit="Tag Name" />
  </configurable>
  <configurable id="reference.idesettings.emmet" configurable_name="Emmet">
    <option name="add" hit="Add edit point at the end of template" />
    <option name="at" hit="Add edit point at the end of template" />
    <option name="edit" hit="Add edit point at the end of template" />
    <option name="end" hit="Add edit point at the end of template" />
    <option name="of" hit="Add edit point at the end of template" />
    <option name="point" hit="Add edit point at the end of template" />
    <option name="template" hit="Add edit point at the end of template" />
    <option name="the" hit="Add edit point at the end of template" />
    <option name="bem" hit="BEM" />
    <option name="comment" hit="Comment tags" />
    <option name="tags" hit="Comment tags" />
    <option name="custom" hit="Custom..." />
    <option name="class" hit="Element separator in class names:" />
    <option name="element" hit="Element separator in class names:" />
    <option name="in" hit="Element separator in class names:" />
    <option name="names" hit="Element separator in class names:" />
    <option name="separator" hit="Element separator in class names:" />
    <option name="emmet" hit="Emmet" />
    <option name="" hit="Enable XML/HTML Emmet" />
    <option name="emmet" hit="Enable XML/HTML Emmet" />
    <option name="enable" hit="Enable XML/HTML Emmet" />
    <option name="html" hit="Enable XML/HTML Emmet" />
    <option name="xml" hit="Enable XML/HTML Emmet" />
    <option name="abbreviation" hit="Enable abbreviation preview" />
    <option name="enable" hit="Enable abbreviation preview" />
    <option name="preview" hit="Enable abbreviation preview" />
    <option name="automatic" hit="Enable automatic URL recognition while wrapping text with   tag" />
    <option name="enable" hit="Enable automatic URL recognition while wrapping text with   tag" />
    <option name="recognition" hit="Enable automatic URL recognition while wrapping text with   tag" />
    <option name="tag" hit="Enable automatic URL recognition while wrapping text with   tag" />
    <option name="text" hit="Enable automatic URL recognition while wrapping text with   tag" />
    <option name="url" hit="Enable automatic URL recognition while wrapping text with   tag" />
    <option name="while" hit="Enable automatic URL recognition while wrapping text with   tag" />
    <option name="with" hit="Enable automatic URL recognition while wrapping text with   tag" />
    <option name="wrapping" hit="Enable automatic URL recognition while wrapping text with   tag" />
    <option name="enter" hit="Enter" />
    <option name="escape" hit="Escape" />
    <option name="abbreviation" hit="Expand abbreviation with" />
    <option name="expand" hit="Expand abbreviation with" />
    <option name="with" hit="Expand abbreviation with" />
    <option name="by" hit="Filters enabled by default" />
    <option name="default" hit="Filters enabled by default" />
    <option name="enabled" hit="Filters enabled by default" />
    <option name="filters" hit="Filters enabled by default" />
    <option name="class" hit="Modifier separator in class names:" />
    <option name="in" hit="Modifier separator in class names:" />
    <option name="modifier" hit="Modifier separator in class names:" />
    <option name="names" hit="Modifier separator in class names:" />
    <option name="separator" hit="Modifier separator in class names:" />
    <option name="element" hit="Short element prefix:" />
    <option name="prefix" hit="Short element prefix:" />
    <option name="short" hit="Short element prefix:" />
    <option name="line" hit="Single line" />
    <option name="single" hit="Single line" />
    <option name="space" hit="Space" />
    <option name="tab" hit="Tab" />
    <option name="line" hit="Trim line markers" />
    <option name="markers" hit="Trim line markers" />
    <option name="trim" hit="Trim line markers" />
    <option name="tuning" hit="XSL tuning" />
    <option name="xsl" hit="XSL tuning" />
  </configurable>
  <configurable id="coverage" configurable_name="Coverage">
    <option name="activate" hit="Activate Coverage View" />
    <option name="coverage" hit="Activate Coverage View" />
    <option name="view" hit="Activate Coverage View" />
    <option name="active" hit="Add to the active suites" />
    <option name="add" hit="Add to the active suites" />
    <option name="suites" hit="Add to the active suites" />
    <option name="the" hit="Add to the active suites" />
    <option name="to" hit="Add to the active suites" />
    <option name="coverage" hit="Coverage" />
    <option name="apply" hit="Do not apply collected coverage" />
    <option name="collected" hit="Do not apply collected coverage" />
    <option name="coverage" hit="Do not apply collected coverage" />
    <option name="do" hit="Do not apply collected coverage" />
    <option name="not" hit="Do not apply collected coverage" />
    <option name="active" hit="Replace active suites with the new one" />
    <option name="new" hit="Replace active suites with the new one" />
    <option name="one" hit="Replace active suites with the new one" />
    <option name="replace" hit="Replace active suites with the new one" />
    <option name="suites" hit="Replace active suites with the new one" />
    <option name="the" hit="Replace active suites with the new one" />
    <option name="with" hit="Replace active suites with the new one" />
    <option name="applying" hit="Show options before applying coverage to the editor" />
    <option name="before" hit="Show options before applying coverage to the editor" />
    <option name="coverage" hit="Show options before applying coverage to the editor" />
    <option name="editor" hit="Show options before applying coverage to the editor" />
    <option name="options" hit="Show options before applying coverage to the editor" />
    <option name="show" hit="Show options before applying coverage to the editor" />
    <option name="the" hit="Show options before applying coverage to the editor" />
    <option name="to" hit="Show options before applying coverage to the editor" />
    <option name="coverage" hit="When new coverage is gathered" />
    <option name="gathered" hit="When new coverage is gathered" />
    <option name="is" hit="When new coverage is gathered" />
    <option name="new" hit="When new coverage is gathered" />
    <option name="when" hit="When new coverage is gathered" />
  </configurable>
  <configurable id="preferences.externalResources" configurable_name="Schemas and DTDs">
    <option name="and" hit="Schemas and DTDs" />
    <option name="dtds" hit="Schemas and DTDs" />
    <option name="schemas" hit="Schemas and DTDs" />
  </configurable>
  <configurable id="xml.defaultSchemas" configurable_name="Default XML Schemas">
    <option name="default" hit="Default HTML language level" />
    <option name="html" hit="Default HTML language level" />
    <option name="language" hit="Default HTML language level" />
    <option name="level" hit="Default HTML language level" />
    <option name="default" hit="Default XML Schemas" />
    <option name="schemas" hit="Default XML Schemas" />
    <option name="xml" hit="Default XML Schemas" />
    <option name="4" hit="HTML 4 (&quot;http://www.w3.org/TR/html4/loose.dtd&quot;)" />
    <option name="dtd" hit="HTML 4 (&quot;http://www.w3.org/TR/html4/loose.dtd&quot;)" />
    <option name="html" hit="HTML 4 (&quot;http://www.w3.org/TR/html4/loose.dtd&quot;)" />
    <option name="html4" hit="HTML 4 (&quot;http://www.w3.org/TR/html4/loose.dtd&quot;)" />
    <option name="http" hit="HTML 4 (&quot;http://www.w3.org/TR/html4/loose.dtd&quot;)" />
    <option name="loose" hit="HTML 4 (&quot;http://www.w3.org/TR/html4/loose.dtd&quot;)" />
    <option name="org" hit="HTML 4 (&quot;http://www.w3.org/TR/html4/loose.dtd&quot;)" />
    <option name="tr" hit="HTML 4 (&quot;http://www.w3.org/TR/html4/loose.dtd&quot;)" />
    <option name="w3" hit="HTML 4 (&quot;http://www.w3.org/TR/html4/loose.dtd&quot;)" />
    <option name="www" hit="HTML 4 (&quot;http://www.w3.org/TR/html4/loose.dtd&quot;)" />
    <option name="5" hit="HTML 5" />
    <option name="html" hit="HTML 5" />
    <option name="doctype" hit="Other doctype:" />
    <option name="other" hit="Other doctype:" />
    <option name="0" hit="XML Schema 1.0" />
    <option name="1" hit="XML Schema 1.0" />
    <option name="schema" hit="XML Schema 1.0" />
    <option name="xml" hit="XML Schema 1.0" />
    <option name="1" hit="XML Schema 1.1" />
    <option name="schema" hit="XML Schema 1.1" />
    <option name="xml" hit="XML Schema 1.1" />
    <option name="schema" hit="XML Schema version" />
    <option name="version" hit="XML Schema version" />
    <option name="xml" hit="XML Schema version" />
  </configurable>
  <configurable id="xml.catalog" configurable_name="XML Catalog">
    <option name="catalog" hit="Catalog property file:" />
    <option name="file" hit="Catalog property file:" />
    <option name="property" hit="Catalog property file:" />
    <option name="catalog" hit="XML Catalog" />
    <option name="xml" hit="XML Catalog" />
  </configurable>
  <configurable id="preferences.sourceCode.HTML" configurable_name="HTML">
    <option name="a" path="Code Generation" hit="Add a space at line comment start" />
    <option name="add" path="Code Generation" hit="Add a space at line comment start" />
    <option name="at" path="Code Generation" hit="Add a space at line comment start" />
    <option name="comment" path="Code Generation" hit="Add a space at line comment start" />
    <option name="line" path="Code Generation" hit="Add a space at line comment start" />
    <option name="space" path="Code Generation" hit="Add a space at line comment start" />
    <option name="start" path="Code Generation" hit="Add a space at line comment start" />
    <option name="add" path="Code Generation" hit="Add spaces around block comments" />
    <option name="around" path="Code Generation" hit="Add spaces around block comments" />
    <option name="block" path="Code Generation" hit="Add spaces around block comments" />
    <option name="comments" path="Code Generation" hit="Add spaces around block comments" />
    <option name="spaces" path="Code Generation" hit="Add spaces around block comments" />
    <option name="after" path="Other" hit="After tag name" />
    <option name="name" path="Other" hit="After tag name" />
    <option name="tag" path="Other" hit="After tag name" />
    <option name="align" path="Other" hit="Align attributes" />
    <option name="attributes" path="Other" hit="Align attributes" />
    <option name="align" path="Other" hit="Align text" />
    <option name="text" path="Other" hit="Align text" />
    <option name="around" path="Other" hit="Around &quot;=&quot; in attribute" />
    <option name="attribute" path="Other" hit="Around &quot;=&quot; in attribute" />
    <option name="in" path="Other" hit="Around &quot;=&quot; in attribute" />
    <option name="arrangement" path="Arrangement" hit="Arrangement" />
    <option name="at" path="Code Generation" hit="Block comment at first column" />
    <option name="block" path="Code Generation" hit="Block comment at first column" />
    <option name="column" path="Code Generation" hit="Block comment at first column" />
    <option name="comment" path="Code Generation" hit="Block comment at first column" />
    <option name="first" path="Code Generation" hit="Block comment at first column" />
    <option name="chop" path="Other" hit="Chop down if long" />
    <option name="down" path="Other" hit="Chop down if long" />
    <option name="if" path="Other" hit="Chop down if long" />
    <option name="long" path="Other" hit="Chop down if long" />
    <option name="code" path="Code Generation" hit="Code Generation" />
    <option name="generation" path="Code Generation" hit="Code Generation" />
    <option name="comments" path="Code Generation" hit="Comments" />
    <option name="continuation" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="indent" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="default" path="Other" hit="Default: No" />
    <option name="no" path="Other" hit="Default: No" />
    <option name="disable" hit="Disable" />
    <option name="children" path="Other" hit="Do not indent children of:" />
    <option name="do" path="Other" hit="Do not indent children of:" />
    <option name="indent" path="Other" hit="Do not indent children of:" />
    <option name="not" path="Other" hit="Do not indent children of:" />
    <option name="of" path="Other" hit="Do not indent children of:" />
    <option name="do" path="Other" hit="Do not wrap" />
    <option name="not" path="Other" hit="Do not wrap" />
    <option name="wrap" path="Other" hit="Do not wrap" />
    <option name="break" path="Other" hit="Don't break if inline content:" />
    <option name="content" path="Other" hit="Don't break if inline content:" />
    <option name="don" path="Other" hit="Don't break if inline content:" />
    <option name="if" path="Other" hit="Don't break if inline content:" />
    <option name="inline" path="Other" hit="Don't break if inline content:" />
    <option name="t" path="Other" hit="Don't break if inline content:" />
    <option name="double" path="Other" hit="Double" />
    <option name="enforce" path="Other" hit="Enforce on format" />
    <option name="format" path="Other" hit="Enforce on format" />
    <option name="on" path="Other" hit="Enforce on format" />
    <option name="enforce" path="Code Generation" hit="Enforce on reformat" />
    <option name="on" path="Code Generation" hit="Enforce on reformat" />
    <option name="reformat" path="Code Generation" hit="Enforce on reformat" />
    <option name="generated" path="Other" hit="Generated quote marks:" />
    <option name="marks" path="Other" hit="Generated quote marks:" />
    <option name="quote" path="Other" hit="Generated quote marks:" />
    <option name="grouping" path="Arrangement" hit="Grouping rules:" />
    <option name="rules" path="Arrangement" hit="Grouping rules:" />
    <option name="html" hit="HTML" />
    <option name="at" path="Other" hit="Hard wrap at:" />
    <option name="hard" path="Other" hit="Hard wrap at:" />
    <option name="wrap" path="Other" hit="Hard wrap at:" />
    <option name="empty" path="Other" hit="In empty tag" />
    <option name="in" path="Other" hit="In empty tag" />
    <option name="tag" path="Other" hit="In empty tag" />
    <option name="indent" path="Tabs and Indents" hit="Indent:" />
    <option name="elements" path="Other" hit="Inline elements:" />
    <option name="inline" path="Other" hit="Inline elements:" />
    <option name="before" path="Other" hit="Insert new line before:" />
    <option name="insert" path="Other" hit="Insert new line before:" />
    <option name="line" path="Other" hit="Insert new line before:" />
    <option name="new" path="Other" hit="Insert new line before:" />
    <option name="blank" path="Other" hit="Keep blank lines:" />
    <option name="keep" path="Other" hit="Keep blank lines:" />
    <option name="lines" path="Other" hit="Keep blank lines:" />
    <option name="empty" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="indents" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="keep" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="lines" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="on" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="breaks" path="Other" hit="Keep line breaks" />
    <option name="keep" path="Other" hit="Keep line breaks" />
    <option name="line" path="Other" hit="Keep line breaks" />
    <option name="breaks" path="Other" hit="Keep line breaks in text" />
    <option name="in" path="Other" hit="Keep line breaks in text" />
    <option name="keep" path="Other" hit="Keep line breaks in text" />
    <option name="line" path="Other" hit="Keep line breaks in text" />
    <option name="text" path="Other" hit="Keep line breaks in text" />
    <option name="keep" path="Other" hit="Keep white spaces" />
    <option name="spaces" path="Other" hit="Keep white spaces" />
    <option name="white" path="Other" hit="Keep white spaces" />
    <option name="inside" path="Other" hit="Keep white spaces inside:" />
    <option name="keep" path="Other" hit="Keep white spaces inside:" />
    <option name="spaces" path="Other" hit="Keep white spaces inside:" />
    <option name="white" path="Other" hit="Keep white spaces inside:" />
    <option name="at" path="Code Generation" hit="Line comment at first column" />
    <option name="column" path="Code Generation" hit="Line comment at first column" />
    <option name="comment" path="Code Generation" hit="Line comment at first column" />
    <option name="first" path="Code Generation" hit="Line comment at first column" />
    <option name="line" path="Code Generation" hit="Line comment at first column" />
    <option name="loading" hit="Loading..." />
    <option name="matching" path="Arrangement" hit="Matching rules:" />
    <option name="rules" path="Arrangement" hit="Matching rules:" />
    <option name="never" path="Other" hit="Never" />
    <option name="after" path="Other" hit="New line after last attribute:" />
    <option name="attribute" path="Other" hit="New line after last attribute:" />
    <option name="last" path="Other" hit="New line after last attribute:" />
    <option name="line" path="Other" hit="New line after last attribute:" />
    <option name="new" path="Other" hit="New line after last attribute:" />
    <option name="attribute" path="Other" hit="New line before first attribute:" />
    <option name="before" path="Other" hit="New line before first attribute:" />
    <option name="first" path="Other" hit="New line before first attribute:" />
    <option name="line" path="Other" hit="New line before first attribute:" />
    <option name="new" path="Other" hit="New line before first attribute:" />
    <option name="no" path="Other" hit="No" />
    <option name="none" path="Other" hit="None" />
    <option name="other" path="Other" hit="Other" />
    <option name="before" path="Other" hit="Remove new line before:" />
    <option name="line" path="Other" hit="Remove new line before:" />
    <option name="new" path="Other" hit="Remove new line before:" />
    <option name="remove" path="Other" hit="Remove new line before:" />
    <option name="reset" path="Other" hit="Reset" />
    <option name="scheme" hit="Scheme:" />
    <option name="from" hit="Set from..." />
    <option name="set" hit="Set from..." />
    <option name="single" path="Other" hit="Single" />
    <option name="smart" path="Tabs and Indents" hit="Smart tabs" />
    <option name="tabs" path="Tabs and Indents" hit="Smart tabs" />
    <option name="spaces" path="Other" hit="Spaces" />
    <option name="120" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="80" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="guide" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="one" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="or" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="several" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="specify" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="size" path="Tabs and Indents" hit="Tab size:" />
    <option name="tab" path="Tabs and Indents" hit="Tab size:" />
    <option name="and" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="indents" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="tabs" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="and" path="Tabs and Indents" hit="Use HTML indents within   and   tags" />
    <option name="html" path="Tabs and Indents" hit="Use HTML indents within   and   tags" />
    <option name="indents" path="Tabs and Indents" hit="Use HTML indents within   and   tags" />
    <option name="tags" path="Tabs and Indents" hit="Use HTML indents within   and   tags" />
    <option name="use" path="Tabs and Indents" hit="Use HTML indents within   and   tags" />
    <option name="within" path="Tabs and Indents" hit="Use HTML indents within   and   tags" />
    <option name="character" path="Tabs and Indents" hit="Use tab character" />
    <option name="tab" path="Tabs and Indents" hit="Use tab character" />
    <option name="use" path="Tabs and Indents" hit="Use tab character" />
    <option name="guides" path="Other" hit="Visual guides:" />
    <option name="visual" path="Other" hit="Visual guides:" />
    <option name="multiline" path="Other" hit="When multiline" />
    <option name="when" path="Other" hit="When multiline" />
    <option name="always" path="Other" hit="Wrap always" />
    <option name="wrap" path="Other" hit="Wrap always" />
    <option name="attributes" path="Other" hit="Wrap attributes:" />
    <option name="wrap" path="Other" hit="Wrap attributes:" />
    <option name="if" path="Other" hit="Wrap if long" />
    <option name="long" path="Other" hit="Wrap if long" />
    <option name="wrap" path="Other" hit="Wrap if long" />
    <option name="on" path="Other" hit="Wrap on typing" />
    <option name="typing" path="Other" hit="Wrap on typing" />
    <option name="wrap" path="Other" hit="Wrap on typing" />
    <option name="text" path="Other" hit="Wrap text" />
    <option name="wrap" path="Other" hit="Wrap text" />
    <option name="yes" path="Other" hit="Yes" />
    <option name="columns" path="Other" hit="columns" />
    <option name="lines" path="Other" hit="lines" />
    <option name="if" path="Other" hit="or if tag size more than" />
    <option name="more" path="Other" hit="or if tag size more than" />
    <option name="or" path="Other" hit="or if tag size more than" />
    <option name="size" path="Other" hit="or if tag size more than" />
    <option name="tag" path="Other" hit="or if tag size more than" />
    <option name="than" path="Other" hit="or if tag size more than" />
  </configurable>
  <configurable id="preferences.sourceCode.JSON" configurable_name="JSON">
    <option name="blank" path="Blank Lines" hit="Blank Lines" />
    <option name="lines" path="Blank Lines" hit="Blank Lines" />
    <option name="continuation" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="indent" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="disable" hit="Disable" />
    <option name="code" path="Blank Lines" hit="In code:" />
    <option name="in" path="Blank Lines" hit="In code:" />
    <option name="indent" path="Tabs and Indents" hit="Indent:" />
    <option name="json" hit="JSON" />
    <option name="empty" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="indents" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="keep" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="lines" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="on" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="blank" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="keep" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="lines" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="maximum" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="loading" hit="Loading..." />
    <option name="scheme" hit="Scheme:" />
    <option name="from" hit="Set from..." />
    <option name="set" hit="Set from..." />
    <option name="smart" path="Tabs and Indents" hit="Smart tabs" />
    <option name="tabs" path="Tabs and Indents" hit="Smart tabs" />
    <option name="spaces" path="Spaces" hit="Spaces" />
    <option name="size" path="Tabs and Indents" hit="Tab size:" />
    <option name="tab" path="Tabs and Indents" hit="Tab size:" />
    <option name="and" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="indents" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="tabs" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="character" path="Tabs and Indents" hit="Use tab character" />
    <option name="tab" path="Tabs and Indents" hit="Use tab character" />
    <option name="use" path="Tabs and Indents" hit="Use tab character" />
    <option name="and" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="braces" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="wrapping" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="line" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="next" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="on" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="signs" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="and" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="line" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="next" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="on" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="signs" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="catch" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="do" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="while" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="else" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="finally" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="for" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="if" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="switch" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="" path="Wrapping and Braces" hit="'try' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'try' statement" />
    <option name="try" path="Wrapping and Braces" hit="'try' statement" />
    <option name="" path="Wrapping and Braces" hit="'try-with-resources'" />
    <option name="try-with-resources" path="Wrapping and Braces" hit="'try-with-resources'" />
    <option name="" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="while" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="while" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="after" path="Spaces" hit="After ':'" />
    <option name="after" path="Spaces" hit="After comma" />
    <option name="comma" path="Spaces" hit="After comma" />
    <option name="align" path="Wrapping and Braces" hit="Align" />
    <option name="align" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="method" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="start" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="throws" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="to" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="align" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="assignments" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="fields" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="multiline" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="parenthesised" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="when" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="align" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="methods" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="simple" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="variables" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="multiline" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="when" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="array" path="Wrapping and Braces" hit="Array initializer" />
    <option name="initializer" path="Wrapping and Braces" hit="Array initializer" />
    <option name="arrays" path="Wrapping and Braces" hit="Arrays" />
    <option name="assert" path="Wrapping and Braces" hit="Assert statement" />
    <option name="statement" path="Wrapping and Braces" hit="Assert statement" />
    <option name="assignment" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="line" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="next" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="on" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="sign" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="assignment" path="Wrapping and Braces" hit="Assignment statement" />
    <option name="statement" path="Wrapping and Braces" hit="Assignment statement" />
    <option name="before" path="Spaces" hit="Before ':'" />
    <option name="before" path="Spaces" hit="Before comma" />
    <option name="comma" path="Spaces" hit="Before comma" />
    <option name="binary" path="Wrapping and Braces" hit="Binary expressions" />
    <option name="expressions" path="Wrapping and Braces" hit="Binary expressions" />
    <option name="braces" path="Spaces" hit="Braces" />
    <option name="braces" path="Wrapping and Braces" hit="Braces placement" />
    <option name="placement" path="Wrapping and Braces" hit="Braces placement" />
    <option name="brackets" path="Spaces" hit="Brackets" />
    <option name="builder" path="Wrapping and Braces" hit="Builder methods" />
    <option name="methods" path="Wrapping and Braces" hit="Builder methods" />
    <option name="calls" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="chained" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="method" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="annotations" path="Wrapping and Braces" hit="Class annotations" />
    <option name="class" path="Wrapping and Braces" hit="Class annotations" />
    <option name="at" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="column" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="comment" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="first" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="comments" path="Wrapping and Braces" hit="Comments" />
    <option name="control" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="in" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="line" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="one" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="statement" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="a" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="case" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="each" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="line" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="on" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="separate" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="ensure" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="exceeded" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="is" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="margin" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="not" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="right" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="constants" path="Wrapping and Braces" hit="Enum constants" />
    <option name="enum" path="Wrapping and Braces" hit="Enum constants" />
    <option name="extends" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="implements" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="keyword" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="permits" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="extends" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="implements" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="list" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="permits" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="annotations" path="Wrapping and Braces" hit="Field annotations" />
    <option name="field" path="Wrapping and Braces" hit="Field annotations" />
    <option name="braces" path="Wrapping and Braces" hit="Force braces" />
    <option name="force" path="Wrapping and Braces" hit="Force braces" />
    <option name="declarations" path="Wrapping and Braces" hit="Group declarations" />
    <option name="group" path="Wrapping and Braces" hit="Group declarations" />
    <option name="at" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="hard" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="wrap" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="class" path="Wrapping and Braces" hit="In class declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In class declaration" />
    <option name="in" path="Wrapping and Braces" hit="In class declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="in" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="lambda" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In method declaration" />
    <option name="in" path="Wrapping and Braces" hit="In method declaration" />
    <option name="method" path="Wrapping and Braces" hit="In method declaration" />
    <option name="break" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="case" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="from" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="branches" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="case" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="builder" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="indents" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="keep" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="methods" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="keep" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="reformatting" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="when" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="breaks" path="Wrapping and Braces" hit="Line breaks" />
    <option name="line" path="Wrapping and Braces" hit="Line breaks" />
    <option name="annotations" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="local" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="variable" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="annotations" path="Wrapping and Braces" hit="Method annotations" />
    <option name="method" path="Wrapping and Braces" hit="Method annotations" />
    <option name="arguments" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="call" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="method" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="declaration" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="method" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="parameters" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="method" path="Wrapping and Braces" hit="Method parentheses" />
    <option name="parentheses" path="Wrapping and Braces" hit="Method parentheses" />
    <option name="list" path="Wrapping and Braces" hit="Modifier list" />
    <option name="modifier" path="Wrapping and Braces" hit="Modifier list" />
    <option name="expressions" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="in" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="line" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="multiple" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="one" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="after" path="Wrapping and Braces" hit="New line after '('" />
    <option name="line" path="Wrapping and Braces" hit="New line after '('" />
    <option name="new" path="Wrapping and Braces" hit="New line after '('" />
    <option name="after" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="line" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="new" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="objects" path="Wrapping and Braces" hit="Objects" />
    <option name="line" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="next" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="on" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="operation" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="sign" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="other" path="Wrapping and Braces" hit="Other" />
    <option name="annotations" path="Wrapping and Braces" hit="Parameter annotations" />
    <option name="parameter" path="Wrapping and Braces" hit="Parameter annotations" />
    <option name="line" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="new" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="on" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="place" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="line" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="new" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="on" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="place" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="blocks" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="classes" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="lambdas" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="methods" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="else" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="if" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="special" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="treatment" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="call" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="chain" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="over" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="priority" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="take" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="wrapping" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="operation" path="Wrapping and Braces" hit="Ternary operation" />
    <option name="ternary" path="Wrapping and Braces" hit="Ternary operation" />
    <option name="keyword" path="Wrapping and Braces" hit="Throws keyword" />
    <option name="throws" path="Wrapping and Braces" hit="Throws keyword" />
    <option name="list" path="Wrapping and Braces" hit="Throws list" />
    <option name="throws" path="Wrapping and Braces" hit="Throws list" />
    <option name="comma" path="Wrapping and Braces" hit="Trailing comma" />
    <option name="trailing" path="Wrapping and Braces" hit="Trailing comma" />
    <option name="guides" path="Wrapping and Braces" hit="Visual guides" />
    <option name="visual" path="Wrapping and Braces" hit="Visual guides" />
    <option name="within" path="Spaces" hit="Within" />
    <option name="after" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="list" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="modifier" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="at" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="margin" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="right" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="call" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="first" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="on" path="Wrapping and Braces" hit="Wrap on typing" />
    <option name="typing" path="Wrapping and Braces" hit="Wrap on typing" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap on typing" />
  </configurable>
  <configurable id="preferences.sourceCode.XML" configurable_name="XML">
    <option name="a" path="Code Generation" hit="Add a space at line comment start" />
    <option name="add" path="Code Generation" hit="Add a space at line comment start" />
    <option name="at" path="Code Generation" hit="Add a space at line comment start" />
    <option name="comment" path="Code Generation" hit="Add a space at line comment start" />
    <option name="line" path="Code Generation" hit="Add a space at line comment start" />
    <option name="space" path="Code Generation" hit="Add a space at line comment start" />
    <option name="start" path="Code Generation" hit="Add a space at line comment start" />
    <option name="add" path="Other" hit="Add new lines" />
    <option name="lines" path="Other" hit="Add new lines" />
    <option name="new" path="Other" hit="Add new lines" />
    <option name="add" path="Code Generation" hit="Add spaces around block comments" />
    <option name="around" path="Code Generation" hit="Add spaces around block comments" />
    <option name="block" path="Code Generation" hit="Add spaces around block comments" />
    <option name="comments" path="Code Generation" hit="Add spaces around block comments" />
    <option name="spaces" path="Code Generation" hit="Add spaces around block comments" />
    <option name="additional" path="Arrangement" hit="Additional Settings" />
    <option name="settings" path="Arrangement" hit="Additional Settings" />
    <option name="after" path="Other" hit="After tag name" />
    <option name="name" path="Other" hit="After tag name" />
    <option name="tag" path="Other" hit="After tag name" />
    <option name="align" path="Other" hit="Align attributes" />
    <option name="attributes" path="Other" hit="Align attributes" />
    <option name="always" path="Arrangement" hit="Always" />
    <option name="around" path="Other" hit="Around &quot;=&quot; in attribute" />
    <option name="attribute" path="Other" hit="Around &quot;=&quot; in attribute" />
    <option name="in" path="Other" hit="Around &quot;=&quot; in attribute" />
    <option name="arrangement" path="Arrangement" hit="Arrangement" />
    <option name="at" path="Code Generation" hit="Block comment at first column" />
    <option name="block" path="Code Generation" hit="Block comment at first column" />
    <option name="column" path="Code Generation" hit="Block comment at first column" />
    <option name="comment" path="Code Generation" hit="Block comment at first column" />
    <option name="first" path="Code Generation" hit="Block comment at first column" />
    <option name="cdata" path="Other" hit="CDATA" />
    <option name="chop" path="Other" hit="Chop down if long" />
    <option name="down" path="Other" hit="Chop down if long" />
    <option name="if" path="Other" hit="Chop down if long" />
    <option name="long" path="Other" hit="Chop down if long" />
    <option name="code" path="Code Generation" hit="Code Generation" />
    <option name="generation" path="Code Generation" hit="Code Generation" />
    <option name="comments" path="Code Generation" hit="Comments" />
    <option name="continuation" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="indent" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="default" path="Other" hit="Default: No" />
    <option name="no" path="Other" hit="Default: No" />
    <option name="disable" hit="Disable" />
    <option name="do" path="Other" hit="Do not wrap" />
    <option name="not" path="Other" hit="Do not wrap" />
    <option name="wrap" path="Other" hit="Do not wrap" />
    <option name="enforce" path="Code Generation" hit="Enforce on reformat" />
    <option name="on" path="Code Generation" hit="Enforce on reformat" />
    <option name="reformat" path="Code Generation" hit="Enforce on reformat" />
    <option name="force" path="Arrangement" hit="Force rearrange:" />
    <option name="rearrange" path="Arrangement" hit="Force rearrange:" />
    <option name="grouping" path="Arrangement" hit="Grouping rules:" />
    <option name="rules" path="Arrangement" hit="Grouping rules:" />
    <option name="at" path="Other" hit="Hard wrap at:" />
    <option name="hard" path="Other" hit="Hard wrap at:" />
    <option name="wrap" path="Other" hit="Hard wrap at:" />
    <option name="empty" path="Other" hit="In empty tag" />
    <option name="in" path="Other" hit="In empty tag" />
    <option name="tag" path="Other" hit="In empty tag" />
    <option name="indent" path="Tabs and Indents" hit="Indent:" />
    <option name="blank" path="Other" hit="Keep blank lines:" />
    <option name="keep" path="Other" hit="Keep blank lines:" />
    <option name="lines" path="Other" hit="Keep blank lines:" />
    <option name="empty" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="indents" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="keep" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="lines" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="on" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="breaks" path="Other" hit="Keep line breaks" />
    <option name="keep" path="Other" hit="Keep line breaks" />
    <option name="line" path="Other" hit="Keep line breaks" />
    <option name="breaks" path="Other" hit="Keep line breaks in text" />
    <option name="in" path="Other" hit="Keep line breaks in text" />
    <option name="keep" path="Other" hit="Keep line breaks in text" />
    <option name="line" path="Other" hit="Keep line breaks in text" />
    <option name="text" path="Other" hit="Keep line breaks in text" />
    <option name="keep" path="Other" hit="Keep white spaces" />
    <option name="spaces" path="Other" hit="Keep white spaces" />
    <option name="white" path="Other" hit="Keep white spaces" />
    <option name="inside" path="Other" hit="Keep whitespace inside" />
    <option name="keep" path="Other" hit="Keep whitespace inside" />
    <option name="whitespace" path="Other" hit="Keep whitespace inside" />
    <option name="at" path="Code Generation" hit="Line comment at first column" />
    <option name="column" path="Code Generation" hit="Line comment at first column" />
    <option name="comment" path="Code Generation" hit="Line comment at first column" />
    <option name="first" path="Code Generation" hit="Line comment at first column" />
    <option name="line" path="Code Generation" hit="Line comment at first column" />
    <option name="loading" hit="Loading..." />
    <option name="matching" path="Arrangement" hit="Matching rules:" />
    <option name="rules" path="Arrangement" hit="Matching rules:" />
    <option name="never" path="Arrangement" hit="Never" />
    <option name="no" path="Other" hit="No" />
    <option name="other" path="Other" hit="Other" />
    <option name="preserve" path="Other" hit="Preserve" />
    <option name="keep" path="Other" hit="Remove (keep with tags)" />
    <option name="remove" path="Other" hit="Remove (keep with tags)" />
    <option name="tags" path="Other" hit="Remove (keep with tags)" />
    <option name="with" path="Other" hit="Remove (keep with tags)" />
    <option name="reset" path="Other" hit="Reset" />
    <option name="scheme" hit="Scheme:" />
    <option name="from" hit="Set from..." />
    <option name="set" hit="Set from..." />
    <option name="smart" path="Tabs and Indents" hit="Smart tabs" />
    <option name="tabs" path="Tabs and Indents" hit="Smart tabs" />
    <option name="spaces" path="Other" hit="Spaces" />
    <option name="120" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="80" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="guide" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="one" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="or" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="several" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="specify" path="Other" hit="Specify one guide (80) or several (80, 120)" />
    <option name="size" path="Tabs and Indents" hit="Tab size:" />
    <option name="tab" path="Tabs and Indents" hit="Tab size:" />
    <option name="and" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="indents" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="tabs" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="character" path="Tabs and Indents" hit="Use tab character" />
    <option name="tab" path="Tabs and Indents" hit="Use tab character" />
    <option name="use" path="Tabs and Indents" hit="Use tab character" />
    <option name="code" path="Arrangement" hit="Use the current mode (toggled in the Reformat Code dialog)" />
    <option name="current" path="Arrangement" hit="Use the current mode (toggled in the Reformat Code dialog)" />
    <option name="dialog" path="Arrangement" hit="Use the current mode (toggled in the Reformat Code dialog)" />
    <option name="in" path="Arrangement" hit="Use the current mode (toggled in the Reformat Code dialog)" />
    <option name="mode" path="Arrangement" hit="Use the current mode (toggled in the Reformat Code dialog)" />
    <option name="reformat" path="Arrangement" hit="Use the current mode (toggled in the Reformat Code dialog)" />
    <option name="the" path="Arrangement" hit="Use the current mode (toggled in the Reformat Code dialog)" />
    <option name="toggled" path="Arrangement" hit="Use the current mode (toggled in the Reformat Code dialog)" />
    <option name="use" path="Arrangement" hit="Use the current mode (toggled in the Reformat Code dialog)" />
    <option name="guides" path="Other" hit="Visual guides:" />
    <option name="visual" path="Other" hit="Visual guides:" />
    <option name="around" path="Other" hit="Whitespace around:" />
    <option name="whitespace" path="Other" hit="Whitespace around:" />
    <option name="always" path="Other" hit="Wrap always" />
    <option name="wrap" path="Other" hit="Wrap always" />
    <option name="attributes" path="Other" hit="Wrap attributes:" />
    <option name="wrap" path="Other" hit="Wrap attributes:" />
    <option name="if" path="Other" hit="Wrap if long" />
    <option name="long" path="Other" hit="Wrap if long" />
    <option name="wrap" path="Other" hit="Wrap if long" />
    <option name="on" path="Other" hit="Wrap on typing" />
    <option name="typing" path="Other" hit="Wrap on typing" />
    <option name="wrap" path="Other" hit="Wrap on typing" />
    <option name="text" path="Other" hit="Wrap text" />
    <option name="wrap" path="Other" hit="Wrap text" />
    <option name="xml" hit="XML" />
    <option name="yes" path="Other" hit="Yes" />
    <option name="columns" path="Other" hit="columns" />
  </configurable>
  <configurable id="project.propVCSSupport.Mappings" configurable_name="Version Control" />
  <configurable id="project.propVCSSupport.Confirmation" configurable_name="Confirmation">
    <option name="add" hit="Add" />
    <option name="apply" hit="Apply to files created outside IntelliJ IDEA" />
    <option name="created" hit="Apply to files created outside IntelliJ IDEA" />
    <option name="files" hit="Apply to files created outside IntelliJ IDEA" />
    <option name="idea" hit="Apply to files created outside IntelliJ IDEA" />
    <option name="intellij" hit="Apply to files created outside IntelliJ IDEA" />
    <option name="outside" hit="Apply to files created outside IntelliJ IDEA" />
    <option name="to" hit="Apply to files created outside IntelliJ IDEA" />
    <option name="ask" hit="Ask" />
    <option name="ask" hit="Ask for confirmation to drop commits" />
    <option name="commits" hit="Ask for confirmation to drop commits" />
    <option name="confirmation" hit="Ask for confirmation to drop commits" />
    <option name="drop" hit="Ask for confirmation to drop commits" />
    <option name="for" hit="Ask for confirmation to drop commits" />
    <option name="to" hit="Ask for confirmation to drop commits" />
    <option name="ask" hit="Ask to unlock files set to read-only if you attempt to edit them" />
    <option name="attempt" hit="Ask to unlock files set to read-only if you attempt to edit them" />
    <option name="edit" hit="Ask to unlock files set to read-only if you attempt to edit them" />
    <option name="files" hit="Ask to unlock files set to read-only if you attempt to edit them" />
    <option name="if" hit="Ask to unlock files set to read-only if you attempt to edit them" />
    <option name="read-only" hit="Ask to unlock files set to read-only if you attempt to edit them" />
    <option name="set" hit="Ask to unlock files set to read-only if you attempt to edit them" />
    <option name="them" hit="Ask to unlock files set to read-only if you attempt to edit them" />
    <option name="to" hit="Ask to unlock files set to read-only if you attempt to edit them" />
    <option name="unlock" hit="Ask to unlock files set to read-only if you attempt to edit them" />
    <option name="you" hit="Ask to unlock files set to read-only if you attempt to edit them" />
    <option name="changes" hit="Changes" />
    <option name="check" hit="Check for conflicts with the server every" />
    <option name="conflicts" hit="Check for conflicts with the server every" />
    <option name="every" hit="Check for conflicts with the server every" />
    <option name="for" hit="Check for conflicts with the server every" />
    <option name="server" hit="Check for conflicts with the server every" />
    <option name="the" hit="Check for conflicts with the server every" />
    <option name="with" hit="Check for conflicts with the server every" />
    <option name="checkout" hit="Checkout" />
    <option name="confirmation" hit="Confirmation" />
    <option name="do" hit="Do nothing" />
    <option name="nothing" hit="Do nothing" />
    <option name="edit" hit="Edit" />
    <option name="enable" hit="Enable indexing for project" />
    <option name="for" hit="Enable indexing for project" />
    <option name="indexing" hit="Enable indexing for project" />
    <option name="project" hit="Enable indexing for project" />
    <option name="gutter" hit="Gutter" />
    <option name="contain" hit="Highlight directories that contain modified files in the Project tree" />
    <option name="directories" hit="Highlight directories that contain modified files in the Project tree" />
    <option name="files" hit="Highlight directories that contain modified files in the Project tree" />
    <option name="highlight" hit="Highlight directories that contain modified files in the Project tree" />
    <option name="in" hit="Highlight directories that contain modified files in the Project tree" />
    <option name="modified" hit="Highlight directories that contain modified files in the Project tree" />
    <option name="project" hit="Highlight directories that contain modified files in the Project tree" />
    <option name="that" hit="Highlight directories that contain modified files in the Project tree" />
    <option name="the" hit="Highlight directories that contain modified files in the Project tree" />
    <option name="tree" hit="Highlight directories that contain modified files in the Project tree" />
    <option name="changed" hit="Highlight files changed in the last" />
    <option name="files" hit="Highlight files changed in the last" />
    <option name="highlight" hit="Highlight files changed in the last" />
    <option name="in" hit="Highlight files changed in the last" />
    <option name="last" hit="Highlight files changed in the last" />
    <option name="the" hit="Highlight files changed in the last" />
    <option name="a" hit="Highlight lines with whitespace-only modifications with a different color" />
    <option name="color" hit="Highlight lines with whitespace-only modifications with a different color" />
    <option name="different" hit="Highlight lines with whitespace-only modifications with a different color" />
    <option name="highlight" hit="Highlight lines with whitespace-only modifications with a different color" />
    <option name="lines" hit="Highlight lines with whitespace-only modifications with a different color" />
    <option name="modifications" hit="Highlight lines with whitespace-only modifications with a different color" />
    <option name="whitespace-only" hit="Highlight lines with whitespace-only modifications with a different color" />
    <option name="with" hit="Highlight lines with whitespace-only modifications with a different color" />
    <option name="error" hit="Highlight modified lines in error stripe on the scrollbar" />
    <option name="highlight" hit="Highlight modified lines in error stripe on the scrollbar" />
    <option name="in" hit="Highlight modified lines in error stripe on the scrollbar" />
    <option name="lines" hit="Highlight modified lines in error stripe on the scrollbar" />
    <option name="modified" hit="Highlight modified lines in error stripe on the scrollbar" />
    <option name="on" hit="Highlight modified lines in error stripe on the scrollbar" />
    <option name="scrollbar" hit="Highlight modified lines in error stripe on the scrollbar" />
    <option name="stripe" hit="Highlight modified lines in error stripe on the scrollbar" />
    <option name="the" hit="Highlight modified lines in error stripe on the scrollbar" />
    <option name="gutter" hit="Highlight modified lines in the gutter" />
    <option name="highlight" hit="Highlight modified lines in the gutter" />
    <option name="in" hit="Highlight modified lines in the gutter" />
    <option name="lines" hit="Highlight modified lines in the gutter" />
    <option name="modified" hit="Highlight modified lines in the gutter" />
    <option name="the" hit="Highlight modified lines in the gutter" />
    <option name="history" hit="Limit history to" />
    <option name="limit" hit="Limit history to" />
    <option name="to" hit="Limit history to" />
    <option name="log" hit="Log" />
    <option name="remove" hit="Remove" />
    <option name="branches" hit="Restore workspace when switching branches" />
    <option name="restore" hit="Restore workspace when switching branches" />
    <option name="switching" hit="Restore workspace when switching branches" />
    <option name="when" hit="Restore workspace when switching branches" />
    <option name="workspace" hit="Restore workspace when switching branches" />
    <option name="finder" hit="Show in Finder" />
    <option name="in" hit="Show in Finder" />
    <option name="show" hit="Show in Finder" />
    <option name="before" hit="Show options before:" />
    <option name="options" hit="Show options before:" />
    <option name="show" hit="Show options before:" />
    <option name="status" hit="Status" />
    <option name="update" hit="Update" />
    <option name="are" hit="When files are created:" />
    <option name="created" hit="When files are created:" />
    <option name="files" hit="When files are created:" />
    <option name="when" hit="When files are created:" />
    <option name="are" hit="When files are deleted:" />
    <option name="deleted" hit="When files are deleted:" />
    <option name="files" hit="When files are deleted:" />
    <option name="when" hit="When files are deleted:" />
    <option name="created" hit="When patch is created:" />
    <option name="is" hit="When patch is created:" />
    <option name="patch" hit="When patch is created:" />
    <option name="when" hit="When patch is created:" />
    <option name="days" hit="days" />
    <option name="minutes" hit="minutes" />
    <option name="rows" hit="rows" />
  </configurable>
  <configurable id="project.propVCSSupport.DirectoryMappings" configurable_name="Directory Mappings">
    <option name="" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="-" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="all" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="and" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="base" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="content" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="descendants" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="directory" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="immediate" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="modules" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="of" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="project" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="roots" hit="   - Content roots of all modules, and all immediate descendants of project base directory" />
    <option name="directory" hit="Directory Mappings" />
    <option name="mappings" hit="Directory Mappings" />
  </configurable>
  <configurable id="project.propVCSSupport.Issue.Navigation" configurable_name="Issue Navigation">
    <option name="issue" hit="Issue Navigation" />
    <option name="navigation" hit="Issue Navigation" />
  </configurable>
  <configurable id="project.propVCSSupport.ChangelistConflict" configurable_name="Changelists">
    <option name="allow" hit="Allow putting changes within one file into different changelists" />
    <option name="changelists" hit="Allow putting changes within one file into different changelists" />
    <option name="changes" hit="Allow putting changes within one file into different changelists" />
    <option name="different" hit="Allow putting changes within one file into different changelists" />
    <option name="file" hit="Allow putting changes within one file into different changelists" />
    <option name="into" hit="Allow putting changes within one file into different changelists" />
    <option name="one" hit="Allow putting changes within one file into different changelists" />
    <option name="putting" hit="Allow putting changes within one file into different changelists" />
    <option name="within" hit="Allow putting changes within one file into different changelists" />
    <option name="changelists" hit="Changelists" />
    <option name="automatically" hit="Create changelists automatically" />
    <option name="changelists" hit="Create changelists automatically" />
    <option name="create" hit="Create changelists automatically" />
    <option name="changelist" hit="Inactive Changelist" />
    <option name="inactive" hit="Inactive Changelist" />
    <option name="an" hit="When an empty changelist becomes inactive:" />
    <option name="becomes" hit="When an empty changelist becomes inactive:" />
    <option name="changelist" hit="When an empty changelist becomes inactive:" />
    <option name="empty" hit="When an empty changelist becomes inactive:" />
    <option name="inactive" hit="When an empty changelist becomes inactive:" />
    <option name="when" hit="When an empty changelist becomes inactive:" />
  </configurable>
  <configurable id="project.propVCSSupport.CommitDialog" configurable_name="Commit">
    <option name="" hit="Analyze code" />
    <option name="analyze" hit="Analyze code" />
    <option name="code" hit="Analyze code" />
    <option name="before" hit="Before Commit" />
    <option name="commit" hit="Before Commit" />
    <option name="check" hit="Check TODO" />
    <option name="todo" hit="Check TODO" />
    <option name="choose" hit="Choose configuration" />
    <option name="configuration" hit="Choose configuration" />
    <option name="choose" hit="Choose profile" />
    <option name="profile" hit="Choose profile" />
    <option name="c" hit="Cleanup" />
    <option name="leanup" hit="Cleanup" />
    <option name="clear" hit="Clear initial commit message" />
    <option name="commit" hit="Clear initial commit message" />
    <option name="initial" hit="Clear initial commit message" />
    <option name="message" hit="Clear initial commit message" />
    <option name="commit" hit="Commit" />
    <option name="commit" hit="Commit Message Inspections" />
    <option name="inspections" hit="Commit Message Inspections" />
    <option name="message" hit="Commit Message Inspections" />
    <option name="configure" hit="Configure" />
    <option name="" hit="Optimize imports" />
    <option name="imports" hit="Optimize imports" />
    <option name="optimize" hit="Optimize imports" />
    <option name="code" hit="Rearrange code" />
    <option name="nge" hit="Rearrange code" />
    <option name="rearra" hit="Rearrange code" />
    <option name="" hit="Reformat code" />
    <option name="code" hit="Reformat code" />
    <option name="reformat" hit="Reformat code" />
    <option name="run" hit="Run Tests" />
    <option name="tests" hit="Run Tests" />
    <option name="copyright" hit="Update copyright" />
    <option name="update" hit="Update copyright" />
    <option name="commit" hit="Use non-modal commit interface" />
    <option name="interface" hit="Use non-modal commit interface" />
    <option name="non-modal" hit="Use non-modal commit interface" />
    <option name="use" hit="Use non-modal commit interface" />
  </configurable>
  <configurable id="Shelf.Project.Settings" configurable_name="Shelf">
    <option name="" hit=" Default location is  /shelf" />
    <option name="default" hit=" Default location is  /shelf" />
    <option name="is" hit=" Default location is  /shelf" />
    <option name="location" hit=" Default location is  /shelf" />
    <option name="shelf" hit=" Default location is  /shelf" />
    <option name="change" hit="Change Shelves Location..." />
    <option name="location" hit="Change Shelves Location..." />
    <option name="shelves" hit="Change Shelves Location..." />
    <option name="applied" hit="Remove successfully applied files from the shelf" />
    <option name="files" hit="Remove successfully applied files from the shelf" />
    <option name="from" hit="Remove successfully applied files from the shelf" />
    <option name="remove" hit="Remove successfully applied files from the shelf" />
    <option name="shelf" hit="Remove successfully applied files from the shelf" />
    <option name="successfully" hit="Remove successfully applied files from the shelf" />
    <option name="the" hit="Remove successfully applied files from the shelf" />
    <option name="shelf" hit="Shelf" />
    <option name="base" hit="Shelve base revisions of files under distributed version control systems" />
    <option name="control" hit="Shelve base revisions of files under distributed version control systems" />
    <option name="distributed" hit="Shelve base revisions of files under distributed version control systems" />
    <option name="files" hit="Shelve base revisions of files under distributed version control systems" />
    <option name="of" hit="Shelve base revisions of files under distributed version control systems" />
    <option name="revisions" hit="Shelve base revisions of files under distributed version control systems" />
    <option name="shelve" hit="Shelve base revisions of files under distributed version control systems" />
    <option name="systems" hit="Shelve base revisions of files under distributed version control systems" />
    <option name="under" hit="Shelve base revisions of files under distributed version control systems" />
    <option name="version" hit="Shelve base revisions of files under distributed version control systems" />
  </configurable>
  <configurable id="settings.json.schema" configurable_name="JSON Schema Mappings">
    <option name="" hit=" Please add a JSON Schema file and configure its usage" />
    <option name="a" hit=" Please add a JSON Schema file and configure its usage" />
    <option name="add" hit=" Please add a JSON Schema file and configure its usage" />
    <option name="and" hit=" Please add a JSON Schema file and configure its usage" />
    <option name="configure" hit=" Please add a JSON Schema file and configure its usage" />
    <option name="file" hit=" Please add a JSON Schema file and configure its usage" />
    <option name="its" hit=" Please add a JSON Schema file and configure its usage" />
    <option name="json" hit=" Please add a JSON Schema file and configure its usage" />
    <option name="please" hit=" Please add a JSON Schema file and configure its usage" />
    <option name="schema" hit=" Please add a JSON Schema file and configure its usage" />
    <option name="usage" hit=" Please add a JSON Schema file and configure its usage" />
    <option name="json" hit="JSON Schema Mappings" />
    <option name="mappings" hit="JSON Schema Mappings" />
    <option name="schema" hit="JSON Schema Mappings" />
  </configurable>
  <configurable id="settings.json.schema.catalog" configurable_name="Remote JSON Schemas">
    <option name="allow" hit="Allow downloading JSON Schemas from remote sources" />
    <option name="downloading" hit="Allow downloading JSON Schemas from remote sources" />
    <option name="from" hit="Allow downloading JSON Schemas from remote sources" />
    <option name="json" hit="Allow downloading JSON Schemas from remote sources" />
    <option name="remote" hit="Allow downloading JSON Schemas from remote sources" />
    <option name="schemas" hit="Allow downloading JSON Schemas from remote sources" />
    <option name="sources" hit="Allow downloading JSON Schemas from remote sources" />
    <option name="always" hit="Always download the most recent version of schemas" />
    <option name="download" hit="Always download the most recent version of schemas" />
    <option name="most" hit="Always download the most recent version of schemas" />
    <option name="of" hit="Always download the most recent version of schemas" />
    <option name="recent" hit="Always download the most recent version of schemas" />
    <option name="schemas" hit="Always download the most recent version of schemas" />
    <option name="the" hit="Always download the most recent version of schemas" />
    <option name="version" hit="Always download the most recent version of schemas" />
    <option name="json" hit="Remote JSON Schemas" />
    <option name="remote" hit="Remote JSON Schemas" />
    <option name="schemas" hit="Remote JSON Schemas" />
    <option name="catalog" hit="Use schemastore.org JSON Schema catalog" />
    <option name="json" hit="Use schemastore.org JSON Schema catalog" />
    <option name="org" hit="Use schemastore.org JSON Schema catalog" />
    <option name="schema" hit="Use schemastore.org JSON Schema catalog" />
    <option name="schemastore" hit="Use schemastore.org JSON Schema catalog" />
    <option name="use" hit="Use schemastore.org JSON Schema catalog" />
  </configurable>
  <configurable id="reference.settings.ide.settings.spelling" configurable_name="Spelling">
    <option name="accepted" hit="Accepted words:" />
    <option name="words" hit="Accepted words:" />
    <option name="custom" hit="Custom dictionaries (plain text word lists, hunspell):" />
    <option name="dictionaries" hit="Custom dictionaries (plain text word lists, hunspell):" />
    <option name="hunspell" hit="Custom dictionaries (plain text word lists, hunspell):" />
    <option name="lists" hit="Custom dictionaries (plain text word lists, hunspell):" />
    <option name="plain" hit="Custom dictionaries (plain text word lists, hunspell):" />
    <option name="text" hit="Custom dictionaries (plain text word lists, hunspell):" />
    <option name="word" hit="Custom dictionaries (plain text word lists, hunspell):" />
    <option name="spelling" hit="Spelling" />
    <option name="dictionary" hit="Use single dictionary for saving words:" />
    <option name="for" hit="Use single dictionary for saving words:" />
    <option name="saving" hit="Use single dictionary for saving words:" />
    <option name="single" hit="Use single dictionary for saving words:" />
    <option name="use" hit="Use single dictionary for saving words:" />
    <option name="words" hit="Use single dictionary for saving words:" />
    <option name="application-level" hit="application-level" />
    <option name="project-level" hit="project-level" />
  </configurable>
</options>