<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="add" path="ActionManager" hit="Add Property Files to Resource Bundle" />
    <option name="bundle" path="ActionManager" hit="Add Property Files to Resource Bundle" />
    <option name="files" path="ActionManager" hit="Add Property Files to Resource Bundle" />
    <option name="property" path="ActionManager" hit="Add Property Files to Resource Bundle" />
    <option name="resource" path="ActionManager" hit="Add Property Files to Resource Bundle" />
    <option name="to" path="ActionManager" hit="Add Property Files to Resource Bundle" />
    <option name="bundle" path="ActionManager" hit="Combine to Resource Bundle" />
    <option name="combine" path="ActionManager" hit="Combine to Resource Bundle" />
    <option name="resource" path="ActionManager" hit="Combine to Resource Bundle" />
    <option name="to" path="ActionManager" hit="Combine to Resource Bundle" />
    <option name="bundle" path="ActionManager" hit="Dissociate Resource Bundle" />
    <option name="dissociate" path="ActionManager" hit="Dissociate Resource Bundle" />
    <option name="resource" path="ActionManager" hit="Dissociate Resource Bundle" />
    <option name="bundle" path="ActionManager" hit="Resource Bundle" />
    <option name="resource" path="ActionManager" hit="Resource Bundle" />
  </configurable>
  <configurable id="reference.settingsdialog.IDE.editor.colors.Properties" configurable_name="Properties">
    <option name="properties" hit="Properties" />
    <option name="comment" hit="Comment" />
    <option name="code" hit="Invalid unicode code points" />
    <option name="invalid" hit="Invalid unicode code points" />
    <option name="points" hit="Invalid unicode code points" />
    <option name="unicode" hit="Invalid unicode code points" />
    <option name="key" hit="Key/value separator" />
    <option name="separator" hit="Key/value separator" />
    <option name="value" hit="Key/value separator" />
    <option name="key" hit="Property key" />
    <option name="property" hit="Property key" />
    <option name="property" hit="Property value" />
    <option name="value" hit="Property value" />
    <option name="escape" hit="Valid string escape" />
    <option name="string" hit="Valid string escape" />
    <option name="valid" hit="Valid string escape" />
  </configurable>
  <configurable id="preferences.sourceCode.Properties" configurable_name="Properties">
    <option name="disable" hit="Disable" />
    <option name="loading" hit="Loading..." />
    <option name="properties" hit="Properties" />
    <option name="scheme" hit="Scheme:" />
    <option name="from" hit="Set from..." />
    <option name="set" hit="Set from..." />
    <option name="align" hit="Align properties in column" />
    <option name="column" hit="Align properties in column" />
    <option name="in" hit="Align properties in column" />
    <option name="properties" hit="Align properties in column" />
    <option name="around" hit="Insert space around key-value delimiter" />
    <option name="delimiter" hit="Insert space around key-value delimiter" />
    <option name="insert" hit="Insert space around key-value delimiter" />
    <option name="key-value" hit="Insert space around key-value delimiter" />
    <option name="space" hit="Insert space around key-value delimiter" />
    <option name="blank" hit="Keep blank lines" />
    <option name="keep" hit="Keep blank lines" />
    <option name="lines" hit="Keep blank lines" />
    <option name="delimiter" hit="Key-value delimiter" />
    <option name="key-value" hit="Key-value delimiter" />
  </configurable>
</options>