<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="analyze" path="ActionManager" hit="Analyze Dependencies..." />
    <option name="dependencies" path="ActionManager" hit="Analyze Dependencies..." />
    <option name="build" path="ActionManager" hit="Composite Build Configuration" />
    <option name="composite" path="ActionManager" hit="Composite Build Configuration" />
    <option name="configuration" path="ActionManager" hit="Composite Build Configuration" />
    <option name="download" path="ActionManager" hit="Download Sources" />
    <option name="sources" path="ActionManager" hit="Download Sources" />
    <option name="all" path="ActionManager" hit="Download the source code of all libraries used in the project" />
    <option name="code" path="ActionManager" hit="Download the source code of all libraries used in the project" />
    <option name="download" path="ActionManager" hit="Download the source code of all libraries used in the project" />
    <option name="in" path="ActionManager" hit="Download the source code of all libraries used in the project" />
    <option name="libraries" path="ActionManager" hit="Download the source code of all libraries used in the project" />
    <option name="of" path="ActionManager" hit="Download the source code of all libraries used in the project" />
    <option name="project" path="ActionManager" hit="Download the source code of all libraries used in the project" />
    <option name="source" path="ActionManager" hit="Download the source code of all libraries used in the project" />
    <option name="the" path="ActionManager" hit="Download the source code of all libraries used in the project" />
    <option name="used" path="ActionManager" hit="Download the source code of all libraries used in the project" />
    <option name="execute" path="ActionManager" hit="Execute Gradle Task" />
    <option name="gradle" path="ActionManager" hit="Execute Gradle Task" />
    <option name="task" path="ActionManager" hit="Execute Gradle Task" />
    <option name="dependency" path="ActionManager" hit="Go to Gradle Dependency" />
    <option name="go" path="ActionManager" hit="Go to Gradle Dependency" />
    <option name="gradle" path="ActionManager" hit="Go to Gradle Dependency" />
    <option name="to" path="ActionManager" hit="Go to Gradle Dependency" />
    <option name="gradle" path="ActionManager" hit="Link Gradle Project" />
    <option name="link" path="ActionManager" hit="Link Gradle Project" />
    <option name="project" path="ActionManager" hit="Link Gradle Project" />
    <option name="by" path="ActionManager" hit="Link Gradle project described by this file" />
    <option name="described" path="ActionManager" hit="Link Gradle project described by this file" />
    <option name="file" path="ActionManager" hit="Link Gradle project described by this file" />
    <option name="gradle" path="ActionManager" hit="Link Gradle project described by this file" />
    <option name="link" path="ActionManager" hit="Link Gradle project described by this file" />
    <option name="project" path="ActionManager" hit="Link Gradle project described by this file" />
    <option name="this" path="ActionManager" hit="Link Gradle project described by this file" />
    <option name="config" path="ActionManager" hit="Navigates to dependency in parent Gradle config file" />
    <option name="dependency" path="ActionManager" hit="Navigates to dependency in parent Gradle config file" />
    <option name="file" path="ActionManager" hit="Navigates to dependency in parent Gradle config file" />
    <option name="gradle" path="ActionManager" hit="Navigates to dependency in parent Gradle config file" />
    <option name="in" path="ActionManager" hit="Navigates to dependency in parent Gradle config file" />
    <option name="navigates" path="ActionManager" hit="Navigates to dependency in parent Gradle config file" />
    <option name="parent" path="ActionManager" hit="Navigates to dependency in parent Gradle config file" />
    <option name="to" path="ActionManager" hit="Navigates to dependency in parent Gradle config file" />
    <option name="config" path="ActionManager" hit="Open Gradle Config" />
    <option name="gradle" path="ActionManager" hit="Open Gradle Config" />
    <option name="open" path="ActionManager" hit="Open Gradle Config" />
    <option name="config" path="ActionManager" hit="Opens Gradle config file for selected module or library" />
    <option name="file" path="ActionManager" hit="Opens Gradle config file for selected module or library" />
    <option name="for" path="ActionManager" hit="Opens Gradle config file for selected module or library" />
    <option name="gradle" path="ActionManager" hit="Opens Gradle config file for selected module or library" />
    <option name="library" path="ActionManager" hit="Opens Gradle config file for selected module or library" />
    <option name="module" path="ActionManager" hit="Opens Gradle config file for selected module or library" />
    <option name="opens" path="ActionManager" hit="Opens Gradle config file for selected module or library" />
    <option name="or" path="ActionManager" hit="Opens Gradle config file for selected module or library" />
    <option name="selected" path="ActionManager" hit="Opens Gradle config file for selected module or library" />
    <option name="dependencies" path="ActionManager" hit="Refresh Gradle Dependencies" />
    <option name="gradle" path="ActionManager" hit="Refresh Gradle Dependencies" />
    <option name="refresh" path="ActionManager" hit="Refresh Gradle Dependencies" />
    <option name="--refresh-dependencies" path="ActionManager" hit="Refresh dependencies in the Gradle cache using --refresh-dependencies argument" />
    <option name="argument" path="ActionManager" hit="Refresh dependencies in the Gradle cache using --refresh-dependencies argument" />
    <option name="cache" path="ActionManager" hit="Refresh dependencies in the Gradle cache using --refresh-dependencies argument" />
    <option name="dependencies" path="ActionManager" hit="Refresh dependencies in the Gradle cache using --refresh-dependencies argument" />
    <option name="gradle" path="ActionManager" hit="Refresh dependencies in the Gradle cache using --refresh-dependencies argument" />
    <option name="in" path="ActionManager" hit="Refresh dependencies in the Gradle cache using --refresh-dependencies argument" />
    <option name="refresh" path="ActionManager" hit="Refresh dependencies in the Gradle cache using --refresh-dependencies argument" />
    <option name="the" path="ActionManager" hit="Refresh dependencies in the Gradle cache using --refresh-dependencies argument" />
    <option name="using" path="ActionManager" hit="Refresh dependencies in the Gradle cache using --refresh-dependencies argument" />
    <option name="daemons" path="ActionManager" hit="Show Gradle Daemons" />
    <option name="gradle" path="ActionManager" hit="Show Gradle Daemons" />
    <option name="show" path="ActionManager" hit="Show Gradle Daemons" />
    <option name="mode" path="ActionManager" hit="Toggle Offline Mode" />
    <option name="offline" path="ActionManager" hit="Toggle Offline Mode" />
    <option name="toggle" path="ActionManager" hit="Toggle Offline Mode" />
    <option name="builds" path="ActionManager" hit="Toggle offline mode for Gradle builds" />
    <option name="for" path="ActionManager" hit="Toggle offline mode for Gradle builds" />
    <option name="gradle" path="ActionManager" hit="Toggle offline mode for Gradle builds" />
    <option name="mode" path="ActionManager" hit="Toggle offline mode for Gradle builds" />
    <option name="offline" path="ActionManager" hit="Toggle offline mode for Gradle builds" />
    <option name="toggle" path="ActionManager" hit="Toggle offline mode for Gradle builds" />
  </configurable>
  <configurable id="reference.settingsdialog.project.gradle" configurable_name="Gradle">
    <option name="" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="anti-virus" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="default" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="downloaded" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="e" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="files" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="g" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="gradle" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="location" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="on" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="override" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="software" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="stores" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="the" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="to" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="tune" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="where" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="windows" hit=" Override the default location where Gradle stores downloaded files, e.g. to tune anti-virus software on Windows" />
    <option name="a" hit="Allow collecting Gradle models in parallel during a project reload." />
    <option name="allow" hit="Allow collecting Gradle models in parallel during a project reload." />
    <option name="collecting" hit="Allow collecting Gradle models in parallel during a project reload." />
    <option name="during" hit="Allow collecting Gradle models in parallel during a project reload." />
    <option name="gradle" hit="Allow collecting Gradle models in parallel during a project reload." />
    <option name="in" hit="Allow collecting Gradle models in parallel during a project reload." />
    <option name="models" hit="Allow collecting Gradle models in parallel during a project reload." />
    <option name="parallel" hit="Allow collecting Gradle models in parallel during a project reload." />
    <option name="project" hit="Allow collecting Gradle models in parallel during a project reload." />
    <option name="reload" hit="Allow collecting Gradle models in parallel during a project reload." />
    <option name="auto-select" hit="Auto-select" />
    <option name="distribution" hit="Distribution:" />
    <option name="4+" hit="Enable parallel Gradle model fetching for Gradle 7.4+" />
    <option name="7" hit="Enable parallel Gradle model fetching for Gradle 7.4+" />
    <option name="enable" hit="Enable parallel Gradle model fetching for Gradle 7.4+" />
    <option name="fetching" hit="Enable parallel Gradle model fetching for Gradle 7.4+" />
    <option name="for" hit="Enable parallel Gradle model fetching for Gradle 7.4+" />
    <option name="gradle" hit="Enable parallel Gradle model fetching for Gradle 7.4+" />
    <option name="model" hit="Enable parallel Gradle model fetching for Gradle 7.4+" />
    <option name="parallel" hit="Enable parallel Gradle model fetching for Gradle 7.4+" />
    <option name="general" hit="General Settings" />
    <option name="settings" hit="General Settings" />
    <option name="gradle" hit="Gradle" />
    <option name="gradle" hit="Gradle Source" />
    <option name="source" hit="Gradle Source" />
    <option name="gradle" hit="Gradle VM options:" />
    <option name="options" hit="Gradle VM options:" />
    <option name="vm" hit="Gradle VM options:" />
    <option name="gradle" hit="Gradle user home:" />
    <option name="home" hit="Gradle user home:" />
    <option name="user" hit="Gradle user home:" />
    <option name="version" hit="Version:" />
  </configurable>
</options>