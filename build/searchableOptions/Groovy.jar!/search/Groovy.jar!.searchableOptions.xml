<options>
  <configurable id="fileTemplates" configurable_name="File and Code Templates">
    <option name="groovy" hit="Groovy JUnit SetUp Method" />
    <option name="junit" hit="Groovy JUnit SetUp Method" />
    <option name="method" hit="Groovy JUnit SetUp Method" />
    <option name="setup" hit="Groovy JUnit SetUp Method" />
    <option name="groovy" hit="Groovy JUnit TearDown Method" />
    <option name="junit" hit="Groovy JUnit TearDown Method" />
    <option name="method" hit="Groovy JUnit TearDown Method" />
    <option name="teardown" hit="Groovy JUnit TearDown Method" />
    <option name="case" hit="Groovy JUnit Test Case" />
    <option name="groovy" hit="Groovy JUnit Test Case" />
    <option name="junit" hit="Groovy JUnit Test Case" />
    <option name="test" hit="Groovy JUnit Test Case" />
    <option name="groovy" hit="Groovy JUnit Test Method" />
    <option name="junit" hit="Groovy JUnit Test Method" />
    <option name="method" hit="Groovy JUnit Test Method" />
    <option name="test" hit="Groovy JUnit Test Method" />
    <option name="body" hit="Groovy New Method Body" />
    <option name="groovy" hit="Groovy New Method Body" />
    <option name="method" hit="Groovy New Method Body" />
    <option name="new" hit="Groovy New Method Body" />
    <option name="method" hit="Spock Test Method" />
    <option name="spock" hit="Spock Test Method" />
    <option name="test" hit="Spock Test Method" />
    <option name="cleanup" hit="Spock cleanup Method" />
    <option name="method" hit="Spock cleanup Method" />
    <option name="spock" hit="Spock cleanup Method" />
    <option name="method" hit="Spock_SetUp_Method" />
    <option name="setup" hit="Spock_SetUp_Method" />
    <option name="spock" hit="Spock_SetUp_Method" />
    <option name="template" hit="template" />
  </configurable>
</options>