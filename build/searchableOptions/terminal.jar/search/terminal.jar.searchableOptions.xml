<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="clear" path="ActionManager" hit="Clear Command Prompt" />
    <option name="command" path="ActionManager" hit="Clear Command Prompt" />
    <option name="prompt" path="ActionManager" hit="Clear Command Prompt" />
    <option name="clear" path="ActionManager" hit="Clear Terminal" />
    <option name="terminal" path="ActionManager" hit="Clear Terminal" />
    <option name="close" path="ActionManager" hit="Close Session" />
    <option name="session" path="ActionManager" hit="Close Session" />
    <option name="close" path="ActionManager" hit="Close session if prompt is empty" />
    <option name="empty" path="ActionManager" hit="Close session if prompt is empty" />
    <option name="if" path="ActionManager" hit="Close session if prompt is empty" />
    <option name="is" path="ActionManager" hit="Close session if prompt is empty" />
    <option name="prompt" path="ActionManager" hit="Close session if prompt is empty" />
    <option name="session" path="ActionManager" hit="Close session if prompt is empty" />
    <option name="command" path="ActionManager" hit="Command Completion" />
    <option name="completion" path="ActionManager" hit="Command Completion" />
    <option name="copy" path="ActionManager" hit="Copy" />
    <option name="block" path="ActionManager" hit="Copy Block" />
    <option name="copy" path="ActionManager" hit="Copy Block" />
    <option name="clipboard" path="ActionManager" hit="Copy selected text to clipboard" />
    <option name="copy" path="ActionManager" hit="Copy selected text to clipboard" />
    <option name="selected" path="ActionManager" hit="Copy selected text to clipboard" />
    <option name="text" path="ActionManager" hit="Copy selected text to clipboard" />
    <option name="to" path="ActionManager" hit="Copy selected text to clipboard" />
    <option name="delete" path="ActionManager" hit="Delete Previous Word" />
    <option name="previous" path="ActionManager" hit="Delete Previous Word" />
    <option name="word" path="ActionManager" hit="Delete Previous Word" />
    <option name="escape" path="ActionManager" hit="Escape" />
    <option name="above" path="ActionManager" hit="Expand Block Selection Above" />
    <option name="block" path="ActionManager" hit="Expand Block Selection Above" />
    <option name="expand" path="ActionManager" hit="Expand Block Selection Above" />
    <option name="selection" path="ActionManager" hit="Expand Block Selection Above" />
    <option name="below" path="ActionManager" hit="Expand Block Selection Below" />
    <option name="block" path="ActionManager" hit="Expand Block Selection Below" />
    <option name="expand" path="ActionManager" hit="Expand Block Selection Below" />
    <option name="selection" path="ActionManager" hit="Expand Block Selection Below" />
    <option name="goto" path="ActionManager" hit="Goto Next Splitter" />
    <option name="next" path="ActionManager" hit="Goto Next Splitter" />
    <option name="splitter" path="ActionManager" hit="Goto Next Splitter" />
    <option name="goto" path="ActionManager" hit="Goto Previous Splitter" />
    <option name="previous" path="ActionManager" hit="Goto Previous Splitter" />
    <option name="splitter" path="ActionManager" hit="Goto Previous Splitter" />
    <option name="completion" path="ActionManager" hit="Insert Inline Completion Item" />
    <option name="inline" path="ActionManager" hit="Insert Inline Completion Item" />
    <option name="insert" path="ActionManager" hit="Insert Inline Completion Item" />
    <option name="item" path="ActionManager" hit="Insert Inline Completion Item" />
    <option name="left" path="ActionManager" hit="Move Tab Left" />
    <option name="move" path="ActionManager" hit="Move Tab Left" />
    <option name="tab" path="ActionManager" hit="Move Tab Left" />
    <option name="move" path="ActionManager" hit="Move Tab Right" />
    <option name="right" path="ActionManager" hit="Move Tab Right" />
    <option name="tab" path="ActionManager" hit="Move Tab Right" />
    <option name="editor" path="ActionManager" hit="Move to Editor" />
    <option name="move" path="ActionManager" hit="Move to Editor" />
    <option name="to" path="ActionManager" hit="Move to Editor" />
    <option name="new" path="ActionManager" hit="New Predefined Session" />
    <option name="predefined" path="ActionManager" hit="New Predefined Session" />
    <option name="session" path="ActionManager" hit="New Predefined Session" />
    <option name="current" path="ActionManager" hit="Open current file location in terminal" />
    <option name="file" path="ActionManager" hit="Open current file location in terminal" />
    <option name="in" path="ActionManager" hit="Open current file location in terminal" />
    <option name="location" path="ActionManager" hit="Open current file location in terminal" />
    <option name="open" path="ActionManager" hit="Open current file location in terminal" />
    <option name="terminal" path="ActionManager" hit="Open current file location in terminal" />
    <option name="in" path="ActionManager" hit="Open in Terminal" />
    <option name="open" path="ActionManager" hit="Open in Terminal" />
    <option name="terminal" path="ActionManager" hit="Open in Terminal" />
    <option name="paste" path="ActionManager" hit="Paste" />
    <option name="clipboard" path="ActionManager" hit="Paste from clipboard" />
    <option name="from" path="ActionManager" hit="Paste from clipboard" />
    <option name="paste" path="ActionManager" hit="Paste from clipboard" />
    <option name="rename" path="ActionManager" hit="Rename Session" />
    <option name="session" path="ActionManager" hit="Rename Session" />
    <option name="command" path="ActionManager" hit="Search in Command History" />
    <option name="history" path="ActionManager" hit="Search in Command History" />
    <option name="in" path="ActionManager" hit="Search in Command History" />
    <option name="search" path="ActionManager" hit="Search in Command History" />
    <option name="above" path="ActionManager" hit="Select Block Above" />
    <option name="block" path="ActionManager" hit="Select Block Above" />
    <option name="select" path="ActionManager" hit="Select Block Above" />
    <option name="below" path="ActionManager" hit="Select Block Below" />
    <option name="block" path="ActionManager" hit="Select Block Below" />
    <option name="select" path="ActionManager" hit="Select Block Below" />
    <option name="block" path="ActionManager" hit="Select Last Block" />
    <option name="last" path="ActionManager" hit="Select Last Block" />
    <option name="select" path="ActionManager" hit="Select Last Block" />
    <option name="prompt" path="ActionManager" hit="Select Prompt" />
    <option name="select" path="ActionManager" hit="Select Prompt" />
    <option name="documentation" path="ActionManager" hit="Show Documentation" />
    <option name="show" path="ActionManager" hit="Show Documentation" />
    <option name="down" path="ActionManager" hit="Split Down" />
    <option name="split" path="ActionManager" hit="Split Down" />
    <option name="right" path="ActionManager" hit="Split Right" />
    <option name="split" path="ActionManager" hit="Split Right" />
  </configurable>
  <configurable id="terminal" configurable_name="Terminal">
    <option name="application" hit="Application Settings" />
    <option name="settings" hit="Application Settings" />
    <option name="audible" hit="Audible bell" />
    <option name="bell" hit="Audible bell" />
    <option name="block" hit="Block" />
    <option name="close" hit="Close session when it ends" />
    <option name="ends" hit="Close session when it ends" />
    <option name="it" hit="Close session when it ends" />
    <option name="session" hit="Close session when it ends" />
    <option name="when" hit="Close session when it ends" />
    <option name="configure" hit="Configure terminal keybindings" />
    <option name="keybindings" hit="Configure terminal keybindings" />
    <option name="terminal" hit="Configure terminal keybindings" />
    <option name="clipboard" hit="Copy to clipboard on selection" />
    <option name="copy" hit="Copy to clipboard on selection" />
    <option name="on" hit="Copy to clipboard on selection" />
    <option name="selection" hit="Copy to clipboard on selection" />
    <option name="to" hit="Copy to clipboard on selection" />
    <option name="cursor" hit="Cursor shape:" />
    <option name="shape" hit="Cursor shape:" />
    <option name="default" hit="Default tab name:" />
    <option name="name" hit="Default tab name:" />
    <option name="tab" hit="Default tab name:" />
    <option name="enable" hit="Enable New Terminal" />
    <option name="new" hit="Enable New Terminal" />
    <option name="terminal" hit="Enable New Terminal" />
    <option name="environment" hit="Environment variables:" />
    <option name="variables" hit="Environment variables:" />
    <option name="highlight" hit="Highlight hyperlinks" />
    <option name="hyperlinks" hit="Highlight hyperlinks" />
    <option name="mouse" hit="Mouse reporting" />
    <option name="reporting" hit="Mouse reporting" />
    <option name="ide" hit="Override IDE shortcuts" />
    <option name="override" hit="Override IDE shortcuts" />
    <option name="shortcuts" hit="Override IDE shortcuts" />
    <option name="button" hit="Paste on middle mouse button click" />
    <option name="click" hit="Paste on middle mouse button click" />
    <option name="middle" hit="Paste on middle mouse button click" />
    <option name="mouse" hit="Paste on middle mouse button click" />
    <option name="on" hit="Paste on middle mouse button click" />
    <option name="paste" hit="Paste on middle mouse button click" />
    <option name="project" hit="Project Settings" />
    <option name="settings" hit="Project Settings" />
    <option name="commands" hit="Run Commands using IDE" />
    <option name="ide" hit="Run Commands using IDE" />
    <option name="run" hit="Run Commands using IDE" />
    <option name="using" hit="Run Commands using IDE" />
    <option name="integration" hit="Shell integration" />
    <option name="shell" hit="Shell integration" />
    <option name="path" hit="Shell path:" />
    <option name="shell" hit="Shell path:" />
    <option name="directory" hit="Start directory:" />
    <option name="start" hit="Start directory:" />
    <option name="terminal" hit="Terminal" />
    <option name="underline" hit="Underline" />
    <option name="as" hit="Use Option as Meta key" />
    <option name="key" hit="Use Option as Meta key" />
    <option name="meta" hit="Use Option as Meta key" />
    <option name="option" hit="Use Option as Meta key" />
    <option name="use" hit="Use Option as Meta key" />
    <option name="vertical" hit="Vertical" />
  </configurable>
</options>