<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="" path="ActionManager" hit="@TestDataPath Files" />
    <option name="files" path="ActionManager" hit="@TestDataPath Files" />
    <option name="testdatapath" path="ActionManager" hit="@TestDataPath Files" />
    <option name="" path="ActionManager" hit="@TestDataPath Methods" />
    <option name="methods" path="ActionManager" hit="@TestDataPath Methods" />
    <option name="testdatapath" path="ActionManager" hit="@TestDataPath Methods" />
    <option name="add" path="ActionManager" hit="Add Framework Support..." />
    <option name="framework" path="ActionManager" hit="Add Framework Support..." />
    <option name="support" path="ActionManager" hit="Add Framework Support..." />
    <option name="add" path="ActionManager" hit="Add Stepping Filter..." />
    <option name="filter" path="ActionManager" hit="Add Stepping Filter..." />
    <option name="stepping" path="ActionManager" hit="Add Stepping Filter..." />
    <option name="add" path="ActionManager" hit="Add as Library..." />
    <option name="as" path="ActionManager" hit="Add as Library..." />
    <option name="library" path="ActionManager" hit="Add as Library..." />
    <option name="add" path="ActionManager" hit="Add new module to the project" />
    <option name="module" path="ActionManager" hit="Add new module to the project" />
    <option name="new" path="ActionManager" hit="Add new module to the project" />
    <option name="project" path="ActionManager" hit="Add new module to the project" />
    <option name="the" path="ActionManager" hit="Add new module to the project" />
    <option name="to" path="ActionManager" hit="Add new module to the project" />
    <option name="adjust" path="ActionManager" hit="Adjust Range..." />
    <option name="range" path="ActionManager" hit="Adjust Range..." />
    <option name="afterclass" path="ActionManager" hit="AfterClass Method" />
    <option name="method" path="ActionManager" hit="AfterClass Method" />
    <option name="analyze" path="ActionManager" hit="Analyze Cyclic Dependencies..." />
    <option name="cyclic" path="ActionManager" hit="Analyze Cyclic Dependencies..." />
    <option name="dependencies" path="ActionManager" hit="Analyze Cyclic Dependencies..." />
    <option name="analyze" path="ActionManager" hit="Analyze Stack Trace or Thread Dump..." />
    <option name="dump" path="ActionManager" hit="Analyze Stack Trace or Thread Dump..." />
    <option name="or" path="ActionManager" hit="Analyze Stack Trace or Thread Dump..." />
    <option name="stack" path="ActionManager" hit="Analyze Stack Trace or Thread Dump..." />
    <option name="thread" path="ActionManager" hit="Analyze Stack Trace or Thread Dump..." />
    <option name="trace" path="ActionManager" hit="Analyze Stack Trace or Thread Dump..." />
    <option name="async" path="ActionManager" hit="Async Stack Traces" />
    <option name="stack" path="ActionManager" hit="Async Stack Traces" />
    <option name="traces" path="ActionManager" hit="Async Stack Traces" />
    <option name="auto" path="ActionManager" hit="Auto" />
    <option name="beans" path="ActionManager" hit="Beans" />
    <option name="beforeclass" path="ActionManager" hit="BeforeClass Method" />
    <option name="method" path="ActionManager" hit="BeforeClass Method" />
    <option name="analysis" path="ActionManager" hit="Browse code chosen analysis item used in cycles" />
    <option name="browse" path="ActionManager" hit="Browse code chosen analysis item used in cycles" />
    <option name="chosen" path="ActionManager" hit="Browse code chosen analysis item used in cycles" />
    <option name="code" path="ActionManager" hit="Browse code chosen analysis item used in cycles" />
    <option name="cycles" path="ActionManager" hit="Browse code chosen analysis item used in cycles" />
    <option name="in" path="ActionManager" hit="Browse code chosen analysis item used in cycles" />
    <option name="item" path="ActionManager" hit="Browse code chosen analysis item used in cycles" />
    <option name="used" path="ActionManager" hit="Browse code chosen analysis item used in cycles" />
    <option name="artifacts" path="ActionManager" hit="Build Artifacts..." />
    <option name="build" path="ActionManager" hit="Build Artifacts..." />
    <option name="build" path="ActionManager" hit="Build Module" />
    <option name="module" path="ActionManager" hit="Build Module" />
    <option name="build" path="ActionManager" hit="Build Project" />
    <option name="project" path="ActionManager" hit="Build Project" />
    <option name="calculate" path="ActionManager" hit="Calculate Retained Size..." />
    <option name="retained" path="ActionManager" hit="Calculate Retained Size..." />
    <option name="size" path="ActionManager" hit="Calculate Retained Size..." />
    <option name="active" path="ActionManager" hit="Change Active Spring Profiles…" />
    <option name="change" path="ActionManager" hit="Change Active Spring Profiles…" />
    <option name="profiles" path="ActionManager" hit="Change Active Spring Profiles…" />
    <option name="spring" path="ActionManager" hit="Change Active Spring Profiles…" />
    <option name="all" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="and" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="arguments" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="change" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="class" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="correct" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="field" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="method" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="of" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="or" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="parameter" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="references" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="return" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="the" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="type" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="variable" path="ActionManager" hit="Change type of the return type of the method, field, parameter, variable or class type arguments and correct all references" />
    <option name="a" path="ActionManager" hit="Change usages of a class to those of its superclass or interface" />
    <option name="change" path="ActionManager" hit="Change usages of a class to those of its superclass or interface" />
    <option name="class" path="ActionManager" hit="Change usages of a class to those of its superclass or interface" />
    <option name="interface" path="ActionManager" hit="Change usages of a class to those of its superclass or interface" />
    <option name="its" path="ActionManager" hit="Change usages of a class to those of its superclass or interface" />
    <option name="of" path="ActionManager" hit="Change usages of a class to those of its superclass or interface" />
    <option name="or" path="ActionManager" hit="Change usages of a class to those of its superclass or interface" />
    <option name="superclass" path="ActionManager" hit="Change usages of a class to those of its superclass or interface" />
    <option name="those" path="ActionManager" hit="Change usages of a class to those of its superclass or interface" />
    <option name="to" path="ActionManager" hit="Change usages of a class to those of its superclass or interface" />
    <option name="usages" path="ActionManager" hit="Change usages of a class to those of its superclass or interface" />
    <option name="collect" path="ActionManager" hit="Collect Java Expression Statistics" />
    <option name="expression" path="ActionManager" hit="Collect Java Expression Statistics" />
    <option name="java" path="ActionManager" hit="Collect Java Expression Statistics" />
    <option name="statistics" path="ActionManager" hit="Collect Java Expression Statistics" />
    <option name="and" path="ActionManager" hit="Compile And Reload File" />
    <option name="compile" path="ActionManager" hit="Compile And Reload File" />
    <option name="file" path="ActionManager" hit="Compile And Reload File" />
    <option name="reload" path="ActionManager" hit="Compile And Reload File" />
    <option name="all" path="ActionManager" hit="Compile all modified and dependent files in the module" />
    <option name="and" path="ActionManager" hit="Compile all modified and dependent files in the module" />
    <option name="compile" path="ActionManager" hit="Compile all modified and dependent files in the module" />
    <option name="dependent" path="ActionManager" hit="Compile all modified and dependent files in the module" />
    <option name="files" path="ActionManager" hit="Compile all modified and dependent files in the module" />
    <option name="in" path="ActionManager" hit="Compile all modified and dependent files in the module" />
    <option name="modified" path="ActionManager" hit="Compile all modified and dependent files in the module" />
    <option name="module" path="ActionManager" hit="Compile all modified and dependent files in the module" />
    <option name="the" path="ActionManager" hit="Compile all modified and dependent files in the module" />
    <option name="all" path="ActionManager" hit="Compile all modified and dependent files in the project" />
    <option name="and" path="ActionManager" hit="Compile all modified and dependent files in the project" />
    <option name="compile" path="ActionManager" hit="Compile all modified and dependent files in the project" />
    <option name="dependent" path="ActionManager" hit="Compile all modified and dependent files in the project" />
    <option name="files" path="ActionManager" hit="Compile all modified and dependent files in the project" />
    <option name="in" path="ActionManager" hit="Compile all modified and dependent files in the project" />
    <option name="modified" path="ActionManager" hit="Compile all modified and dependent files in the project" />
    <option name="project" path="ActionManager" hit="Compile all modified and dependent files in the project" />
    <option name="the" path="ActionManager" hit="Compile all modified and dependent files in the project" />
    <option name="compiler" path="ActionManager" hit="Compiler Reference Direct Inheritor Search" />
    <option name="direct" path="ActionManager" hit="Compiler Reference Direct Inheritor Search" />
    <option name="inheritor" path="ActionManager" hit="Compiler Reference Direct Inheritor Search" />
    <option name="reference" path="ActionManager" hit="Compiler Reference Direct Inheritor Search" />
    <option name="search" path="ActionManager" hit="Compiler Reference Direct Inheritor Search" />
    <option name="compiler" path="ActionManager" hit="Compiler Reference Find Usages" />
    <option name="find" path="ActionManager" hit="Compiler Reference Find Usages" />
    <option name="reference" path="ActionManager" hit="Compiler Reference Find Usages" />
    <option name="usages" path="ActionManager" hit="Compiler Reference Find Usages" />
    <option name="compiler" path="ActionManager" hit="Compiler Reference Functional Expression Search" />
    <option name="expression" path="ActionManager" hit="Compiler Reference Functional Expression Search" />
    <option name="functional" path="ActionManager" hit="Compiler Reference Functional Expression Search" />
    <option name="reference" path="ActionManager" hit="Compiler Reference Functional Expression Search" />
    <option name="search" path="ActionManager" hit="Compiler Reference Functional Expression Search" />
    <option name="configure" path="ActionManager" hit="Configure OpenAPI Sources…" />
    <option name="openapi" path="ActionManager" hit="Configure OpenAPI Sources…" />
    <option name="sources" path="ActionManager" hit="Configure OpenAPI Sources…" />
    <option name="all" path="ActionManager" hit="Configure default structure for all new projects" />
    <option name="configure" path="ActionManager" hit="Configure default structure for all new projects" />
    <option name="default" path="ActionManager" hit="Configure default structure for all new projects" />
    <option name="for" path="ActionManager" hit="Configure default structure for all new projects" />
    <option name="new" path="ActionManager" hit="Configure default structure for all new projects" />
    <option name="projects" path="ActionManager" hit="Configure default structure for all new projects" />
    <option name="structure" path="ActionManager" hit="Configure default structure for all new projects" />
    <option name="configure" path="ActionManager" hit="Configure project structure" />
    <option name="project" path="ActionManager" hit="Configure project structure" />
    <option name="structure" path="ActionManager" hit="Configure project structure" />
    <option name="constructor" path="ActionManager" hit="Constructor" />
    <option name="anonymous" path="ActionManager" hit="Convert Anonymous to Inner..." />
    <option name="convert" path="ActionManager" hit="Convert Anonymous to Inner..." />
    <option name="inner" path="ActionManager" hit="Convert Anonymous to Inner..." />
    <option name="to" path="ActionManager" hit="Convert Anonymous to Inner..." />
    <option name="convert" path="ActionManager" hit="Convert To Instance Method..." />
    <option name="instance" path="ActionManager" hit="Convert To Instance Method..." />
    <option name="method" path="ActionManager" hit="Convert To Instance Method..." />
    <option name="to" path="ActionManager" hit="Convert To Instance Method..." />
    <option name="an" path="ActionManager" hit="Convert anonymous class into an inner class" />
    <option name="anonymous" path="ActionManager" hit="Convert anonymous class into an inner class" />
    <option name="class" path="ActionManager" hit="Convert anonymous class into an inner class" />
    <option name="convert" path="ActionManager" hit="Convert anonymous class into an inner class" />
    <option name="inner" path="ActionManager" hit="Convert anonymous class into an inner class" />
    <option name="into" path="ActionManager" hit="Convert anonymous class into an inner class" />
    <option name="all" path="ActionManager" hit="Convert method or inner class to static and correct all references" />
    <option name="and" path="ActionManager" hit="Convert method or inner class to static and correct all references" />
    <option name="class" path="ActionManager" hit="Convert method or inner class to static and correct all references" />
    <option name="convert" path="ActionManager" hit="Convert method or inner class to static and correct all references" />
    <option name="correct" path="ActionManager" hit="Convert method or inner class to static and correct all references" />
    <option name="inner" path="ActionManager" hit="Convert method or inner class to static and correct all references" />
    <option name="method" path="ActionManager" hit="Convert method or inner class to static and correct all references" />
    <option name="or" path="ActionManager" hit="Convert method or inner class to static and correct all references" />
    <option name="references" path="ActionManager" hit="Convert method or inner class to static and correct all references" />
    <option name="static" path="ActionManager" hit="Convert method or inner class to static and correct all references" />
    <option name="to" path="ActionManager" hit="Convert method or inner class to static and correct all references" />
    <option name="all" path="ActionManager" hit="Convert static method to instance method and correct all references" />
    <option name="and" path="ActionManager" hit="Convert static method to instance method and correct all references" />
    <option name="convert" path="ActionManager" hit="Convert static method to instance method and correct all references" />
    <option name="correct" path="ActionManager" hit="Convert static method to instance method and correct all references" />
    <option name="instance" path="ActionManager" hit="Convert static method to instance method and correct all references" />
    <option name="method" path="ActionManager" hit="Convert static method to instance method and correct all references" />
    <option name="references" path="ActionManager" hit="Convert static method to instance method and correct all references" />
    <option name="static" path="ActionManager" hit="Convert static method to instance method and correct all references" />
    <option name="to" path="ActionManager" hit="Convert static method to instance method and correct all references" />
    <option name="create" path="ActionManager" hit="Create New Migration..." />
    <option name="migration" path="ActionManager" hit="Create New Migration..." />
    <option name="new" path="ActionManager" hit="Create New Migration..." />
    <option name="a" path="ActionManager" hit="Create a new project from scratch" />
    <option name="create" path="ActionManager" hit="Create a new project from scratch" />
    <option name="from" path="ActionManager" hit="Create a new project from scratch" />
    <option name="new" path="ActionManager" hit="Create a new project from scratch" />
    <option name="project" path="ActionManager" hit="Create a new project from scratch" />
    <option name="scratch" path="ActionManager" hit="Create a new project from scratch" />
    <option name="class" path="ActionManager" hit="Create new Java class" />
    <option name="create" path="ActionManager" hit="Create new Java class" />
    <option name="java" path="ActionManager" hit="Create new Java class" />
    <option name="new" path="ActionManager" hit="Create new Java class" />
    <option name="create" path="ActionManager" hit="Create new module-info.java" />
    <option name="java" path="ActionManager" hit="Create new module-info.java" />
    <option name="module-info" path="ActionManager" hit="Create new module-info.java" />
    <option name="new" path="ActionManager" hit="Create new module-info.java" />
    <option name="create" path="ActionManager" hit="Create new package-info.java" />
    <option name="java" path="ActionManager" hit="Create new package-info.java" />
    <option name="new" path="ActionManager" hit="Create new package-info.java" />
    <option name="package-info" path="ActionManager" hit="Create new package-info.java" />
    <option name="convert" path="ActionManager" hit="Create project structure for directory with existing sources or convert existing project model" />
    <option name="create" path="ActionManager" hit="Create project structure for directory with existing sources or convert existing project model" />
    <option name="directory" path="ActionManager" hit="Create project structure for directory with existing sources or convert existing project model" />
    <option name="existing" path="ActionManager" hit="Create project structure for directory with existing sources or convert existing project model" />
    <option name="for" path="ActionManager" hit="Create project structure for directory with existing sources or convert existing project model" />
    <option name="model" path="ActionManager" hit="Create project structure for directory with existing sources or convert existing project model" />
    <option name="or" path="ActionManager" hit="Create project structure for directory with existing sources or convert existing project model" />
    <option name="project" path="ActionManager" hit="Create project structure for directory with existing sources or convert existing project model" />
    <option name="sources" path="ActionManager" hit="Create project structure for directory with existing sources or convert existing project model" />
    <option name="structure" path="ActionManager" hit="Create project structure for directory with existing sources or convert existing project model" />
    <option name="with" path="ActionManager" hit="Create project structure for directory with existing sources or convert existing project model" />
    <option name="create" path="ActionManager" hit="Create..." />
    <option name="customize" path="ActionManager" hit="Customize Data Views..." />
    <option name="data" path="ActionManager" hit="Customize Data Views..." />
    <option name="views" path="ActionManager" hit="Customize Data Views..." />
    <option name="customize" path="ActionManager" hit="Customize Threads View..." />
    <option name="threads" path="ActionManager" hit="Customize Threads View..." />
    <option name="view" path="ActionManager" hit="Customize Threads View..." />
    <option name="database" path="ActionManager" hit="Database" />
    <option name="build" path="ActionManager" hit="Debug Build Process" />
    <option name="debug" path="ActionManager" hit="Debug Build Process" />
    <option name="process" path="ActionManager" hit="Debug Build Process" />
    <option name="all" path="ActionManager" hit="Disable SHA256 Checksum for All Libraries" />
    <option name="checksum" path="ActionManager" hit="Disable SHA256 Checksum for All Libraries" />
    <option name="disable" path="ActionManager" hit="Disable SHA256 Checksum for All Libraries" />
    <option name="for" path="ActionManager" hit="Disable SHA256 Checksum for All Libraries" />
    <option name="libraries" path="ActionManager" hit="Disable SHA256 Checksum for All Libraries" />
    <option name="sha256" path="ActionManager" hit="Disable SHA256 Checksum for All Libraries" />
    <option name="dump" path="ActionManager" hit="Dump Library Usage Statistics" />
    <option name="library" path="ActionManager" hit="Dump Library Usage Statistics" />
    <option name="statistics" path="ActionManager" hit="Dump Library Usage Statistics" />
    <option name="usage" path="ActionManager" hit="Dump Library Usage Statistics" />
    <option name="dump" path="ActionManager" hit="Dump Threads" />
    <option name="threads" path="ActionManager" hit="Dump Threads" />
    <option name="dump" path="ActionManager" hit="Dump UAST Tree" />
    <option name="tree" path="ActionManager" hit="Dump UAST Tree" />
    <option name="uast" path="ActionManager" hit="Dump UAST Tree" />
    <option name="by" path="ActionManager" hit="Dump UAST Tree By Each PsiElement" />
    <option name="dump" path="ActionManager" hit="Dump UAST Tree By Each PsiElement" />
    <option name="each" path="ActionManager" hit="Dump UAST Tree By Each PsiElement" />
    <option name="psielement" path="ActionManager" hit="Dump UAST Tree By Each PsiElement" />
    <option name="tree" path="ActionManager" hit="Dump UAST Tree By Each PsiElement" />
    <option name="uast" path="ActionManager" hit="Dump UAST Tree By Each PsiElement" />
    <option name="edit" path="ActionManager" hit="Edit..." />
    <option name="editframesource" path="ActionManager" hit="EditFrameSource" />
    <option name="all" path="ActionManager" hit="Enable SHA256 Checksum for All Libraries" />
    <option name="checksum" path="ActionManager" hit="Enable SHA256 Checksum for All Libraries" />
    <option name="enable" path="ActionManager" hit="Enable SHA256 Checksum for All Libraries" />
    <option name="for" path="ActionManager" hit="Enable SHA256 Checksum for All Libraries" />
    <option name="libraries" path="ActionManager" hit="Enable SHA256 Checksum for All Libraries" />
    <option name="sha256" path="ActionManager" hit="Enable SHA256 Checksum for All Libraries" />
    <option name="enable" path="ActionManager" hit="Enable Tracking With Hidden Memory View" />
    <option name="hidden" path="ActionManager" hit="Enable Tracking With Hidden Memory View" />
    <option name="memory" path="ActionManager" hit="Enable Tracking With Hidden Memory View" />
    <option name="tracking" path="ActionManager" hit="Enable Tracking With Hidden Memory View" />
    <option name="view" path="ActionManager" hit="Enable Tracking With Hidden Memory View" />
    <option name="with" path="ActionManager" hit="Enable Tracking With Hidden Memory View" />
    <option name="encapsulate" path="ActionManager" hit="Encapsulate Fields..." />
    <option name="fields" path="ActionManager" hit="Encapsulate Fields..." />
    <option name="endpoints" path="ActionManager" hit="Endpoints" />
    <option name="exclude" path="ActionManager" hit="Exclude from Validation" />
    <option name="from" path="ActionManager" hit="Exclude from Validation" />
    <option name="validation" path="ActionManager" hit="Exclude from Validation" />
    <option name="exit" path="ActionManager" hit="Exit" />
    <option name="export" path="ActionManager" hit="Export Threads..." />
    <option name="threads" path="ActionManager" hit="Export Threads..." />
    <option name="extract" path="ActionManager" hit="Extract Module..." />
    <option name="module" path="ActionManager" hit="Extract Module..." />
    <option name="a" path="ActionManager" hit="Extract a package to a separate module" />
    <option name="extract" path="ActionManager" hit="Extract a package to a separate module" />
    <option name="module" path="ActionManager" hit="Extract a package to a separate module" />
    <option name="package" path="ActionManager" hit="Extract a package to a separate module" />
    <option name="separate" path="ActionManager" hit="Extract a package to a separate module" />
    <option name="to" path="ActionManager" hit="Extract a package to a separate module" />
    <option name="filter" path="ActionManager" hit="Filter..." />
    <option name="data" path="ActionManager" hit="Find Unused Test Data" />
    <option name="find" path="ActionManager" hit="Find Unused Test Data" />
    <option name="test" path="ActionManager" hit="Find Unused Test Data" />
    <option name="unused" path="ActionManager" hit="Find Unused Test Data" />
    <option name="and" path="ActionManager" hit="Find and Replace Code Duplicates..." />
    <option name="code" path="ActionManager" hit="Find and Replace Code Duplicates..." />
    <option name="duplicates" path="ActionManager" hit="Find and Replace Code Duplicates..." />
    <option name="find" path="ActionManager" hit="Find and Replace Code Duplicates..." />
    <option name="replace" path="ActionManager" hit="Find and Replace Code Duplicates..." />
    <option name="a" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="be" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="call" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="can" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="code" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="constant" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="finds" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="in" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="into" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="method" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="of" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="scope" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="selected" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="that" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="transformed" path="ActionManager" hit="Finds code in selected scope that can be transformed into a call of selected method/constant" />
    <option name="force" path="ActionManager" hit="Force Return" />
    <option name="return" path="ActionManager" hit="Force Return" />
    <option name="a" path="ActionManager" hit="Force a method to return before it reaches a return statement" />
    <option name="before" path="ActionManager" hit="Force a method to return before it reaches a return statement" />
    <option name="force" path="ActionManager" hit="Force a method to return before it reaches a return statement" />
    <option name="it" path="ActionManager" hit="Force a method to return before it reaches a return statement" />
    <option name="method" path="ActionManager" hit="Force a method to return before it reaches a return statement" />
    <option name="reaches" path="ActionManager" hit="Force a method to return before it reaches a return statement" />
    <option name="return" path="ActionManager" hit="Force a method to return before it reaches a return statement" />
    <option name="statement" path="ActionManager" hit="Force a method to return before it reaches a return statement" />
    <option name="to" path="ActionManager" hit="Force a method to return before it reaches a return statement" />
    <option name="file" path="ActionManager" hit="Force recompilation for selected module, file or package" />
    <option name="for" path="ActionManager" hit="Force recompilation for selected module, file or package" />
    <option name="force" path="ActionManager" hit="Force recompilation for selected module, file or package" />
    <option name="module" path="ActionManager" hit="Force recompilation for selected module, file or package" />
    <option name="or" path="ActionManager" hit="Force recompilation for selected module, file or package" />
    <option name="package" path="ActionManager" hit="Force recompilation for selected module, file or package" />
    <option name="recompilation" path="ActionManager" hit="Force recompilation for selected module, file or package" />
    <option name="selected" path="ActionManager" hit="Force recompilation for selected module, file or package" />
    <option name="generate" path="ActionManager" hit="Generate Hierarchy Visitor..." />
    <option name="hierarchy" path="ActionManager" hit="Generate Hierarchy Visitor..." />
    <option name="visitor" path="ActionManager" hit="Generate Hierarchy Visitor..." />
    <option name="generate" path="ActionManager" hit="Generate JavaDoc..." />
    <option name="javadoc" path="ActionManager" hit="Generate JavaDoc..." />
    <option name="constructor" path="ActionManager" hit="Generate constructor" />
    <option name="generate" path="ActionManager" hit="Generate constructor" />
    <option name="createui" path="ActionManager" hit="Generate createUI(...)" />
    <option name="generate" path="ActionManager" hit="Generate createUI(...)" />
    <option name="and" path="ActionManager" hit="Generate equals() and hashCode()" />
    <option name="equals" path="ActionManager" hit="Generate equals() and hashCode()" />
    <option name="generate" path="ActionManager" hit="Generate equals() and hashCode()" />
    <option name="hashcode" path="ActionManager" hit="Generate equals() and hashCode()" />
    <option name="generate" path="ActionManager" hit="Generate getter" />
    <option name="getter" path="ActionManager" hit="Generate getter" />
    <option name="and" path="ActionManager" hit="Generate getter and setter" />
    <option name="generate" path="ActionManager" hit="Generate getter and setter" />
    <option name="getter" path="ActionManager" hit="Generate getter and setter" />
    <option name="setter" path="ActionManager" hit="Generate getter and setter" />
    <option name="generate" path="ActionManager" hit="Generate logger" />
    <option name="logger" path="ActionManager" hit="Generate logger" />
    <option name="descriptors" path="ActionManager" hit="Generate module-info Descriptors" />
    <option name="generate" path="ActionManager" hit="Generate module-info Descriptors" />
    <option name="module-info" path="ActionManager" hit="Generate module-info Descriptors" />
    <option name="9" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="all" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="and" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="files" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="for" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="generate" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="higher" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="in" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="java" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="module-info" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="modules" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="project" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="the" path="ActionManager" hit="Generate module-info files for all modules in the project (for Java 9 and higher)" />
    <option name="generate" path="ActionManager" hit="Generate setter" />
    <option name="setter" path="ActionManager" hit="Generate setter" />
    <option name="call" path="ActionManager" hit="Generate super method call" />
    <option name="generate" path="ActionManager" hit="Generate super method call" />
    <option name="method" path="ActionManager" hit="Generate super method call" />
    <option name="super" path="ActionManager" hit="Generate super method call" />
    <option name="generate" path="ActionManager" hit="Generate toString() method" />
    <option name="method" path="ActionManager" hit="Generate toString() method" />
    <option name="tostring" path="ActionManager" hit="Generate toString() method" />
    <option name="generated" path="ActionManager" hit="Generated Sources Root" />
    <option name="root" path="ActionManager" hit="Generated Sources Root" />
    <option name="sources" path="ActionManager" hit="Generated Sources Root" />
    <option name="dump" path="ActionManager" hit="Get Thread Dump" />
    <option name="get" path="ActionManager" hit="Get Thread Dump" />
    <option name="thread" path="ActionManager" hit="Get Thread Dump" />
    <option name="developing" path="ActionManager" hit="Get started developing plugins." />
    <option name="get" path="ActionManager" hit="Get started developing plugins." />
    <option name="plugins" path="ActionManager" hit="Get started developing plugins." />
    <option name="started" path="ActionManager" hit="Get started developing plugins." />
    <option name="getter" path="ActionManager" hit="Getter" />
    <option name="and" path="ActionManager" hit="Getter and Setter" />
    <option name="getter" path="ActionManager" hit="Getter and Setter" />
    <option name="setter" path="ActionManager" hit="Getter and Setter" />
    <option name="all" path="ActionManager" hit="Guess and Bind Remote Repositories for All Libraries" />
    <option name="and" path="ActionManager" hit="Guess and Bind Remote Repositories for All Libraries" />
    <option name="bind" path="ActionManager" hit="Guess and Bind Remote Repositories for All Libraries" />
    <option name="for" path="ActionManager" hit="Guess and Bind Remote Repositories for All Libraries" />
    <option name="guess" path="ActionManager" hit="Guess and Bind Remote Repositories for All Libraries" />
    <option name="libraries" path="ActionManager" hit="Guess and Bind Remote Repositories for All Libraries" />
    <option name="remote" path="ActionManager" hit="Guess and Bind Remote Repositories for All Libraries" />
    <option name="repositories" path="ActionManager" hit="Guess and Bind Remote Repositories for All Libraries" />
    <option name="hotswap" path="ActionManager" hit="HotSwap" />
    <option name="build" path="ActionManager" hit="If enabled build process will wait for debug connections when started" />
    <option name="connections" path="ActionManager" hit="If enabled build process will wait for debug connections when started" />
    <option name="debug" path="ActionManager" hit="If enabled build process will wait for debug connections when started" />
    <option name="enabled" path="ActionManager" hit="If enabled build process will wait for debug connections when started" />
    <option name="for" path="ActionManager" hit="If enabled build process will wait for debug connections when started" />
    <option name="if" path="ActionManager" hit="If enabled build process will wait for debug connections when started" />
    <option name="process" path="ActionManager" hit="If enabled build process will wait for debug connections when started" />
    <option name="started" path="ActionManager" hit="If enabled build process will wait for debug connections when started" />
    <option name="wait" path="ActionManager" hit="If enabled build process will wait for debug connections when started" />
    <option name="when" path="ActionManager" hit="If enabled build process will wait for debug connections when started" />
    <option name="will" path="ActionManager" hit="If enabled build process will wait for debug connections when started" />
    <option name="implement" path="ActionManager" hit="Implement Method" />
    <option name="method" path="ActionManager" hit="Implement Method" />
    <option name="from" path="ActionManager" hit="Implement Method (from Method Hierarchy)" />
    <option name="hierarchy" path="ActionManager" hit="Implement Method (from Method Hierarchy)" />
    <option name="implement" path="ActionManager" hit="Implement Method (from Method Hierarchy)" />
    <option name="method" path="ActionManager" hit="Implement Method (from Method Hierarchy)" />
    <option name="directory" path="ActionManager" hit="Import module from directory with existing sources or from existing project model" />
    <option name="existing" path="ActionManager" hit="Import module from directory with existing sources or from existing project model" />
    <option name="from" path="ActionManager" hit="Import module from directory with existing sources or from existing project model" />
    <option name="import" path="ActionManager" hit="Import module from directory with existing sources or from existing project model" />
    <option name="model" path="ActionManager" hit="Import module from directory with existing sources or from existing project model" />
    <option name="module" path="ActionManager" hit="Import module from directory with existing sources or from existing project model" />
    <option name="or" path="ActionManager" hit="Import module from directory with existing sources or from existing project model" />
    <option name="project" path="ActionManager" hit="Import module from directory with existing sources or from existing project model" />
    <option name="sources" path="ActionManager" hit="Import module from directory with existing sources or from existing project model" />
    <option name="with" path="ActionManager" hit="Import module from directory with existing sources or from existing project model" />
    <option name="infer" path="ActionManager" hit="Infer Nullity..." />
    <option name="nullity" path="ActionManager" hit="Infer Nullity..." />
    <option name="infer" path="ActionManager" hit="Infer nullity" />
    <option name="nullity" path="ActionManager" hit="Infer nullity" />
    <option name="intellij" path="ActionManager" hit="IntelliJ Profiler" />
    <option name="profiler" path="ActionManager" hit="IntelliJ Profiler" />
    <option name="interrupt" path="ActionManager" hit="Interrupt" />
    <option name="console" path="ActionManager" hit="JShell Console..." />
    <option name="jshell" path="ActionManager" hit="JShell Console..." />
    <option name="class" path="ActionManager" hit="Java Class" />
    <option name="java" path="ActionManager" hit="Java Class" />
    <option name="allocation" path="ActionManager" hit="Jump To Allocation Position" />
    <option name="jump" path="ActionManager" hit="Jump To Allocation Position" />
    <option name="position" path="ActionManager" hit="Jump To Allocation Position" />
    <option name="to" path="ActionManager" hit="Jump To Allocation Position" />
    <option name="jump" path="ActionManager" hit="Jump To Type Source" />
    <option name="source" path="ActionManager" hit="Jump To Type Source" />
    <option name="to" path="ActionManager" hit="Jump To Type Source" />
    <option name="type" path="ActionManager" hit="Jump To Type Source" />
    <option name="kubernetes" path="ActionManager" hit="Kubernetes" />
    <option name="console" path="ActionManager" hit="Launch JShell Console" />
    <option name="jshell" path="ActionManager" hit="Launch JShell Console" />
    <option name="launch" path="ActionManager" hit="Launch JShell Console" />
    <option name="external" path="ActionManager" hit="Link External Project" />
    <option name="link" path="ActionManager" hit="Link External Project" />
    <option name="project" path="ActionManager" hit="Link External Project" />
    <option name="current" path="ActionManager" hit="Link external project to the current IDE project" />
    <option name="external" path="ActionManager" hit="Link external project to the current IDE project" />
    <option name="ide" path="ActionManager" hit="Link external project to the current IDE project" />
    <option name="link" path="ActionManager" hit="Link external project to the current IDE project" />
    <option name="project" path="ActionManager" hit="Link external project to the current IDE project" />
    <option name="the" path="ActionManager" hit="Link external project to the current IDE project" />
    <option name="to" path="ActionManager" hit="Link external project to the current IDE project" />
    <option name="fields" path="ActionManager" hit="List Static Icon Fields Initializers" />
    <option name="icon" path="ActionManager" hit="List Static Icon Fields Initializers" />
    <option name="initializers" path="ActionManager" hit="List Static Icon Fields Initializers" />
    <option name="list" path="ActionManager" hit="List Static Icon Fields Initializers" />
    <option name="static" path="ActionManager" hit="List Static Icon Fields Initializers" />
    <option name="icons" path="ActionManager" hit="List Used Icons" />
    <option name="list" path="ActionManager" hit="List Used Icons" />
    <option name="used" path="ActionManager" hit="List Used Icons" />
    <option name="logger" path="ActionManager" hit="Logger" />
    <option name="make" path="ActionManager" hit="Make Static..." />
    <option name="static" path="ActionManager" hit="Make Static..." />
    <option name="a" path="ActionManager" hit="Mark directory as a sources root for generated files" />
    <option name="as" path="ActionManager" hit="Mark directory as a sources root for generated files" />
    <option name="directory" path="ActionManager" hit="Mark directory as a sources root for generated files" />
    <option name="files" path="ActionManager" hit="Mark directory as a sources root for generated files" />
    <option name="for" path="ActionManager" hit="Mark directory as a sources root for generated files" />
    <option name="generated" path="ActionManager" hit="Mark directory as a sources root for generated files" />
    <option name="mark" path="ActionManager" hit="Mark directory as a sources root for generated files" />
    <option name="root" path="ActionManager" hit="Mark directory as a sources root for generated files" />
    <option name="sources" path="ActionManager" hit="Mark directory as a sources root for generated files" />
    <option name="an" path="ActionManager" hit="Mark directory as an ordinary sources root" />
    <option name="as" path="ActionManager" hit="Mark directory as an ordinary sources root" />
    <option name="directory" path="ActionManager" hit="Mark directory as an ordinary sources root" />
    <option name="mark" path="ActionManager" hit="Mark directory as an ordinary sources root" />
    <option name="ordinary" path="ActionManager" hit="Mark directory as an ordinary sources root" />
    <option name="root" path="ActionManager" hit="Mark directory as an ordinary sources root" />
    <option name="sources" path="ActionManager" hit="Mark directory as an ordinary sources root" />
    <option name="module" path="ActionManager" hit="Module Settings" />
    <option name="settings" path="ActionManager" hit="Module Settings" />
    <option name="existing" path="ActionManager" hit="Module from Existing Sources..." />
    <option name="from" path="ActionManager" hit="Module from Existing Sources..." />
    <option name="module" path="ActionManager" hit="Module from Existing Sources..." />
    <option name="sources" path="ActionManager" hit="Module from Existing Sources..." />
    <option name="mute" path="ActionManager" hit="Mute Renderers" />
    <option name="renderers" path="ActionManager" hit="Mute Renderers" />
    <option name="class" path="ActionManager" hit="New Class Level Watch..." />
    <option name="level" path="ActionManager" hit="New Class Level Watch..." />
    <option name="new" path="ActionManager" hit="New Class Level Watch..." />
    <option name="watch" path="ActionManager" hit="New Class Level Watch..." />
    <option name="module" path="ActionManager" hit="New Module..." />
    <option name="new" path="ActionManager" hit="New Module..." />
    <option name="new" path="ActionManager" hit="New Project" />
    <option name="project" path="ActionManager" hit="New Project" />
    <option name="method" path="ActionManager" hit="Next Method Overload" />
    <option name="next" path="ActionManager" hit="Next Method Overload" />
    <option name="overload" path="ActionManager" hit="Next Method Overload" />
    <option name="a" path="ActionManager" hit="Open console with the navigatable stack trace or a thread dump" />
    <option name="console" path="ActionManager" hit="Open console with the navigatable stack trace or a thread dump" />
    <option name="dump" path="ActionManager" hit="Open console with the navigatable stack trace or a thread dump" />
    <option name="navigatable" path="ActionManager" hit="Open console with the navigatable stack trace or a thread dump" />
    <option name="open" path="ActionManager" hit="Open console with the navigatable stack trace or a thread dump" />
    <option name="or" path="ActionManager" hit="Open console with the navigatable stack trace or a thread dump" />
    <option name="stack" path="ActionManager" hit="Open console with the navigatable stack trace or a thread dump" />
    <option name="the" path="ActionManager" hit="Open console with the navigatable stack trace or a thread dump" />
    <option name="thread" path="ActionManager" hit="Open console with the navigatable stack trace or a thread dump" />
    <option name="trace" path="ActionManager" hit="Open console with the navigatable stack trace or a thread dump" />
    <option name="with" path="ActionManager" hit="Open console with the navigatable stack trace or a thread dump" />
    <option name="dialog" path="ActionManager" hit="Open settings dialog for selected module" />
    <option name="for" path="ActionManager" hit="Open settings dialog for selected module" />
    <option name="module" path="ActionManager" hit="Open settings dialog for selected module" />
    <option name="open" path="ActionManager" hit="Open settings dialog for selected module" />
    <option name="selected" path="ActionManager" hit="Open settings dialog for selected module" />
    <option name="settings" path="ActionManager" hit="Open settings dialog for selected module" />
    <option name="method" path="ActionManager" hit="Override Method" />
    <option name="override" path="ActionManager" hit="Override Method" />
    <option name="from" path="ActionManager" hit="Override Method (from Method Hierarchy)" />
    <option name="hierarchy" path="ActionManager" hit="Override Method (from Method Hierarchy)" />
    <option name="method" path="ActionManager" hit="Override Method (from Method Hierarchy)" />
    <option name="override" path="ActionManager" hit="Override Method (from Method Hierarchy)" />
    <option name="package" path="ActionManager" hit="Package" />
    <option name="file" path="ActionManager" hit="Package File" />
    <option name="package" path="ActionManager" hit="Package File" />
    <option name="method" path="ActionManager" hit="Parameters Method" />
    <option name="parameters" path="ActionManager" hit="Parameters Method" />
    <option name="persistence" path="ActionManager" hit="Persistence" />
    <option name="development" path="ActionManager" hit="Plugin Development" />
    <option name="plugin" path="ActionManager" hit="Plugin Development" />
    <option name="method" path="ActionManager" hit="Previous Method Overload" />
    <option name="overload" path="ActionManager" hit="Previous Method Overload" />
    <option name="previous" path="ActionManager" hit="Previous Method Overload" />
    <option name="project" path="ActionManager" hit="Project Structure..." />
    <option name="structure" path="ActionManager" hit="Project Structure..." />
    <option name="existing" path="ActionManager" hit="Project from Existing Sources..." />
    <option name="from" path="ActionManager" hit="Project from Existing Sources..." />
    <option name="project" path="ActionManager" hit="Project from Existing Sources..." />
    <option name="sources" path="ActionManager" hit="Project from Existing Sources..." />
    <option name="project" path="ActionManager" hit="Project..." />
    <option name="rebuild" path="ActionManager" hit="Rebuild" />
    <option name="project" path="ActionManager" hit="Rebuild Project" />
    <option name="rebuild" path="ActionManager" hit="Rebuild Project" />
    <option name="recent" path="ActionManager" hit="Recent Tests" />
    <option name="tests" path="ActionManager" hit="Recent Tests" />
    <option name="recompile" path="ActionManager" hit="Recompile" />
    <option name="all" path="ActionManager" hit="Recompile all files in the project" />
    <option name="files" path="ActionManager" hit="Recompile all files in the project" />
    <option name="in" path="ActionManager" hit="Recompile all files in the project" />
    <option name="project" path="ActionManager" hit="Recompile all files in the project" />
    <option name="recompile" path="ActionManager" hit="Recompile all files in the project" />
    <option name="the" path="ActionManager" hit="Recompile all files in the project" />
    <option name="changed" path="ActionManager" hit="Reload Changed Classes" />
    <option name="classes" path="ActionManager" hit="Reload Changed Classes" />
    <option name="reload" path="ActionManager" hit="Reload Changed Classes" />
    <option name="all" path="ActionManager" hit="Reload all changed classes into application being debugged (HotSwap)" />
    <option name="application" path="ActionManager" hit="Reload all changed classes into application being debugged (HotSwap)" />
    <option name="being" path="ActionManager" hit="Reload all changed classes into application being debugged (HotSwap)" />
    <option name="changed" path="ActionManager" hit="Reload all changed classes into application being debugged (HotSwap)" />
    <option name="classes" path="ActionManager" hit="Reload all changed classes into application being debugged (HotSwap)" />
    <option name="debugged" path="ActionManager" hit="Reload all changed classes into application being debugged (HotSwap)" />
    <option name="hotswap" path="ActionManager" hit="Reload all changed classes into application being debugged (HotSwap)" />
    <option name="into" path="ActionManager" hit="Reload all changed classes into application being debugged (HotSwap)" />
    <option name="reload" path="ActionManager" hit="Reload all changed classes into application being debugged (HotSwap)" />
    <option name="filter" path="ActionManager" hit="Remove Filter" />
    <option name="remove" path="ActionManager" hit="Remove Filter" />
    <option name="remove" path="ActionManager" hit="Remove Watch" />
    <option name="watch" path="ActionManager" hit="Remove Watch" />
    <option name="delegation" path="ActionManager" hit="Replace Inheritance with Delegation..." />
    <option name="inheritance" path="ActionManager" hit="Replace Inheritance with Delegation..." />
    <option name="replace" path="ActionManager" hit="Replace Inheritance with Delegation..." />
    <option name="with" path="ActionManager" hit="Replace Inheritance with Delegation..." />
    <option name="method" path="ActionManager" hit="Replace Method With Method Object..." />
    <option name="object" path="ActionManager" hit="Replace Method With Method Object..." />
    <option name="replace" path="ActionManager" hit="Replace Method With Method Object..." />
    <option name="with" path="ActionManager" hit="Replace Method With Method Object..." />
    <option name="access" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="accessor" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="class" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="direct" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="fields" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="in" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="methods" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="of" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="replace" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="selected" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="the" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="to" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="use" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="with" path="ActionManager" hit="Replace direct access to fields in the selected class with use of accessor methods" />
    <option name="delegation" path="ActionManager" hit="Replace inheritance with delegation" />
    <option name="inheritance" path="ActionManager" hit="Replace inheritance with delegation" />
    <option name="replace" path="ActionManager" hit="Replace inheritance with delegation" />
    <option name="with" path="ActionManager" hit="Replace inheritance with delegation" />
    <option name="all" path="ActionManager" hit="Resolve All Maven Libraries" />
    <option name="libraries" path="ActionManager" hit="Resolve All Maven Libraries" />
    <option name="maven" path="ActionManager" hit="Resolve All Maven Libraries" />
    <option name="resolve" path="ActionManager" hit="Resolve All Maven Libraries" />
    <option name="resume" path="ActionManager" hit="Resume" />
    <option name="javadoc" path="ActionManager" hit="Run the JavaDoc tool" />
    <option name="run" path="ActionManager" hit="Run the JavaDoc tool" />
    <option name="the" path="ActionManager" hit="Run the JavaDoc tool" />
    <option name="tool" path="ActionManager" hit="Run the JavaDoc tool" />
    <option name="a" path="ActionManager" hit="Save all threads information to a text file or clipboard" />
    <option name="all" path="ActionManager" hit="Save all threads information to a text file or clipboard" />
    <option name="clipboard" path="ActionManager" hit="Save all threads information to a text file or clipboard" />
    <option name="file" path="ActionManager" hit="Save all threads information to a text file or clipboard" />
    <option name="information" path="ActionManager" hit="Save all threads information to a text file or clipboard" />
    <option name="or" path="ActionManager" hit="Save all threads information to a text file or clipboard" />
    <option name="save" path="ActionManager" hit="Save all threads information to a text file or clipboard" />
    <option name="text" path="ActionManager" hit="Save all threads information to a text file or clipboard" />
    <option name="threads" path="ActionManager" hit="Save all threads information to a text file or clipboard" />
    <option name="to" path="ActionManager" hit="Save all threads information to a text file or clipboard" />
    <option name="and" path="ActionManager" hit="Select and build artifacts configured in the project" />
    <option name="artifacts" path="ActionManager" hit="Select and build artifacts configured in the project" />
    <option name="build" path="ActionManager" hit="Select and build artifacts configured in the project" />
    <option name="configured" path="ActionManager" hit="Select and build artifacts configured in the project" />
    <option name="in" path="ActionManager" hit="Select and build artifacts configured in the project" />
    <option name="project" path="ActionManager" hit="Select and build artifacts configured in the project" />
    <option name="select" path="ActionManager" hit="Select and build artifacts configured in the project" />
    <option name="the" path="ActionManager" hit="Select and build artifacts configured in the project" />
    <option name="method" path="ActionManager" hit="SetUp Method" />
    <option name="setup" path="ActionManager" hit="SetUp Method" />
    <option name="setter" path="ActionManager" hit="Setter" />
    <option name="affected" path="ActionManager" hit="Show Affected Tests" />
    <option name="show" path="ActionManager" hit="Show Affected Tests" />
    <option name="tests" path="ActionManager" hit="Show Affected Tests" />
    <option name="collection" path="ActionManager" hit="Show Collection History" />
    <option name="history" path="ActionManager" hit="Show Collection History" />
    <option name="show" path="ActionManager" hit="Show Collection History" />
    <option name="instances" path="ActionManager" hit="Show Instances" />
    <option name="show" path="ActionManager" hit="Show Instances" />
    <option name="import" path="ActionManager" hit="Show Library Usage Information of Import" />
    <option name="information" path="ActionManager" hit="Show Library Usage Information of Import" />
    <option name="library" path="ActionManager" hit="Show Library Usage Information of Import" />
    <option name="of" path="ActionManager" hit="Show Library Usage Information of Import" />
    <option name="show" path="ActionManager" hit="Show Library Usage Information of Import" />
    <option name="usage" path="ActionManager" hit="Show Library Usage Information of Import" />
    <option name="instances" path="ActionManager" hit="Show New Instances" />
    <option name="new" path="ActionManager" hit="Show New Instances" />
    <option name="show" path="ActionManager" hit="Show New Instances" />
    <option name="show" path="ActionManager" hit="Show Siblings" />
    <option name="siblings" path="ActionManager" hit="Show Siblings" />
    <option name="classes" path="ActionManager" hit="Show Tracked Classes Only" />
    <option name="only" path="ActionManager" hit="Show Tracked Classes Only" />
    <option name="show" path="ActionManager" hit="Show Tracked Classes Only" />
    <option name="tracked" path="ActionManager" hit="Show Tracked Classes Only" />
    <option name="instances" path="ActionManager" hit="Show With Instances Only" />
    <option name="only" path="ActionManager" hit="Show With Instances Only" />
    <option name="show" path="ActionManager" hit="Show With Instances Only" />
    <option name="with" path="ActionManager" hit="Show With Instances Only" />
    <option name="a" path="ActionManager" hit="Show a popup window with the symbol siblings content" />
    <option name="content" path="ActionManager" hit="Show a popup window with the symbol siblings content" />
    <option name="popup" path="ActionManager" hit="Show a popup window with the symbol siblings content" />
    <option name="show" path="ActionManager" hit="Show a popup window with the symbol siblings content" />
    <option name="siblings" path="ActionManager" hit="Show a popup window with the symbol siblings content" />
    <option name="symbol" path="ActionManager" hit="Show a popup window with the symbol siblings content" />
    <option name="the" path="ActionManager" hit="Show a popup window with the symbol siblings content" />
    <option name="window" path="ActionManager" hit="Show a popup window with the symbol siblings content" />
    <option name="with" path="ActionManager" hit="Show a popup window with the symbol siblings content" />
    <option name="related" path="ActionManager" hit="Show related stack..." />
    <option name="show" path="ActionManager" hit="Show related stack..." />
    <option name="stack" path="ActionManager" hit="Show related stack..." />
    <option name="show" path="ActionManager" hit="Show types" />
    <option name="types" path="ActionManager" hit="Show types" />
    <option name="a" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="creating" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="for" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="guide" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="necessary" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="new" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="project" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="start" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="steps" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="that" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="the" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="through" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="will" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="wizard" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="you" path="ActionManager" hit="Start the &quot;New Project&quot; Wizard that will guide you through the steps necessary for creating a new project." />
    <option name="block" path="ActionManager" hit="Step Out of Code Block" />
    <option name="code" path="ActionManager" hit="Step Out of Code Block" />
    <option name="of" path="ActionManager" hit="Step Out of Code Block" />
    <option name="out" path="ActionManager" hit="Step Out of Code Block" />
    <option name="step" path="ActionManager" hit="Step Out of Code Block" />
    <option name="for" path="ActionManager" hit="Structure for New Projects..." />
    <option name="new" path="ActionManager" hit="Structure for New Projects..." />
    <option name="projects" path="ActionManager" hit="Structure for New Projects..." />
    <option name="structure" path="ActionManager" hit="Structure for New Projects..." />
    <option name="call" path="ActionManager" hit="Super Method Call" />
    <option name="method" path="ActionManager" hit="Super Method Call" />
    <option name="super" path="ActionManager" hit="Super Method Call" />
    <option name="suspend" path="ActionManager" hit="Suspend" />
    <option name="breakpoint" path="ActionManager" hit="Switch to the Next Stopped Breakpoint" />
    <option name="next" path="ActionManager" hit="Switch to the Next Stopped Breakpoint" />
    <option name="stopped" path="ActionManager" hit="Switch to the Next Stopped Breakpoint" />
    <option name="switch" path="ActionManager" hit="Switch to the Next Stopped Breakpoint" />
    <option name="the" path="ActionManager" hit="Switch to the Next Stopped Breakpoint" />
    <option name="to" path="ActionManager" hit="Switch to the Next Stopped Breakpoint" />
    <option name="method" path="ActionManager" hit="TearDown Method" />
    <option name="teardown" path="ActionManager" hit="TearDown Method" />
    <option name="method" path="ActionManager" hit="Test Method" />
    <option name="test" path="ActionManager" hit="Test Method" />
    <option name="debug" path="ActionManager" hit="Throw Debug Exception" />
    <option name="exception" path="ActionManager" hit="Throw Debug Exception" />
    <option name="throw" path="ActionManager" hit="Throw Debug Exception" />
    <option name="exception" path="ActionManager" hit="Throw Exception" />
    <option name="throw" path="ActionManager" hit="Throw Exception" />
    <option name="an" path="ActionManager" hit="Throw an exception" />
    <option name="exception" path="ActionManager" hit="Throw an exception" />
    <option name="throw" path="ActionManager" hit="Throw an exception" />
    <option name="field" path="ActionManager" hit="Toggle Field Watchpoint" />
    <option name="toggle" path="ActionManager" hit="Toggle Field Watchpoint" />
    <option name="watchpoint" path="ActionManager" hit="Toggle Field Watchpoint" />
    <option name="breakpoint" path="ActionManager" hit="Toggle Method Breakpoint" />
    <option name="method" path="ActionManager" hit="Toggle Method Breakpoint" />
    <option name="toggle" path="ActionManager" hit="Toggle Method Breakpoint" />
    <option name="at" path="ActionManager" hit="Toggle field watchpoint for the field at caret" />
    <option name="caret" path="ActionManager" hit="Toggle field watchpoint for the field at caret" />
    <option name="field" path="ActionManager" hit="Toggle field watchpoint for the field at caret" />
    <option name="for" path="ActionManager" hit="Toggle field watchpoint for the field at caret" />
    <option name="the" path="ActionManager" hit="Toggle field watchpoint for the field at caret" />
    <option name="toggle" path="ActionManager" hit="Toggle field watchpoint for the field at caret" />
    <option name="watchpoint" path="ActionManager" hit="Toggle field watchpoint for the field at caret" />
    <option name="at" path="ActionManager" hit="Toggle method breakpoint for the method at caret" />
    <option name="breakpoint" path="ActionManager" hit="Toggle method breakpoint for the method at caret" />
    <option name="caret" path="ActionManager" hit="Toggle method breakpoint for the method at caret" />
    <option name="for" path="ActionManager" hit="Toggle method breakpoint for the method at caret" />
    <option name="method" path="ActionManager" hit="Toggle method breakpoint for the method at caret" />
    <option name="the" path="ActionManager" hit="Toggle method breakpoint for the method at caret" />
    <option name="toggle" path="ActionManager" hit="Toggle method breakpoint for the method at caret" />
    <option name="calls" path="ActionManager" hit="Trace Calls" />
    <option name="trace" path="ActionManager" hit="Trace Calls" />
    <option name="instances" path="ActionManager" hit="Track New Instances" />
    <option name="new" path="ActionManager" hit="Track New Instances" />
    <option name="track" path="ActionManager" hit="Track New Instances" />
    <option name="all" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="become" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="fields" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="into" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="its" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="method" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="object" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="on" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="own" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="parameters" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="so" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="that" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="the" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="turn" path="ActionManager" hit="Turn the method into its own object so that all the parameters become fields on that object" />
    <option name="migration" path="ActionManager" hit="Type Migration..." />
    <option name="type" path="ActionManager" hit="Type Migration..." />
    <option name="all" path="ActionManager" hit="Unbind Remote Repositories for All Libraries" />
    <option name="for" path="ActionManager" hit="Unbind Remote Repositories for All Libraries" />
    <option name="libraries" path="ActionManager" hit="Unbind Remote Repositories for All Libraries" />
    <option name="remote" path="ActionManager" hit="Unbind Remote Repositories for All Libraries" />
    <option name="repositories" path="ActionManager" hit="Unbind Remote Repositories for All Libraries" />
    <option name="unbind" path="ActionManager" hit="Unbind Remote Repositories for All Libraries" />
    <option name="generated" path="ActionManager" hit="Unmark Generated Sources Root" />
    <option name="root" path="ActionManager" hit="Unmark Generated Sources Root" />
    <option name="sources" path="ActionManager" hit="Unmark Generated Sources Root" />
    <option name="unmark" path="ActionManager" hit="Unmark Generated Sources Root" />
    <option name="jdk" path="ActionManager" hit="Update Project JDK" />
    <option name="project" path="ActionManager" hit="Update Project JDK" />
    <option name="update" path="ActionManager" hit="Update Project JDK" />
    <option name="artifacts" path="ActionManager" hit="Update the file in the corresponding artifacts" />
    <option name="corresponding" path="ActionManager" hit="Update the file in the corresponding artifacts" />
    <option name="file" path="ActionManager" hit="Update the file in the corresponding artifacts" />
    <option name="in" path="ActionManager" hit="Update the file in the corresponding artifacts" />
    <option name="the" path="ActionManager" hit="Update the file in the corresponding artifacts" />
    <option name="update" path="ActionManager" hit="Update the file in the corresponding artifacts" />
    <option name="interface" path="ActionManager" hit="Use Interface Where Possible..." />
    <option name="possible" path="ActionManager" hit="Use Interface Where Possible..." />
    <option name="use" path="ActionManager" hit="Use Interface Where Possible..." />
    <option name="where" path="ActionManager" hit="Use Interface Where Possible..." />
    <option name="text" path="ActionManager" hit="View Text" />
    <option name="view" path="ActionManager" hit="View Text" />
    <option name="as" path="ActionManager" hit="View as" />
    <option name="view" path="ActionManager" hit="View as" />
    <option name="a" path="ActionManager" hit="View text value of selected node in a separate pane" />
    <option name="in" path="ActionManager" hit="View text value of selected node in a separate pane" />
    <option name="node" path="ActionManager" hit="View text value of selected node in a separate pane" />
    <option name="of" path="ActionManager" hit="View text value of selected node in a separate pane" />
    <option name="pane" path="ActionManager" hit="View text value of selected node in a separate pane" />
    <option name="selected" path="ActionManager" hit="View text value of selected node in a separate pane" />
    <option name="separate" path="ActionManager" hit="View text value of selected node in a separate pane" />
    <option name="text" path="ActionManager" hit="View text value of selected node in a separate pane" />
    <option name="value" path="ActionManager" hit="View text value of selected node in a separate pane" />
    <option name="view" path="ActionManager" hit="View text value of selected node in a separate pane" />
    <option name="createui" path="ActionManager" hit="createUI(...)" />
    <option name="and" path="ActionManager" hit="equals() and hashCode()" />
    <option name="equals" path="ActionManager" hit="equals() and hashCode()" />
    <option name="hashcode" path="ActionManager" hit="equals() and hashCode()" />
    <option name="java" path="ActionManager" hit="module-info.java" />
    <option name="module-info" path="ActionManager" hit="module-info.java" />
    <option name="java" path="ActionManager" hit="package-info.java" />
    <option name="package-info" path="ActionManager" hit="package-info.java" />
    <option name="tostring" path="ActionManager" hit="toString()" />
  </configurable>
  <configurable id="editor.breadcrumbs" configurable_name="Breadcrumbs">
    <option name="java" hit="Java" />
  </configurable>
  <configurable id="reference.settingsdialog.IDE.editor.colors.JVM Logging" configurable_name="JVM Logging">
    <option name="jvm" hit="JVM Logging" />
    <option name="logging" hit="JVM Logging" />
    <option name="class" hit="Classes//Class name" />
    <option name="classes" hit="Classes//Class name" />
    <option name="name" hit="Classes//Class name" />
  </configurable>
  <configurable id="reference.settingsdialog.IDE.editor.colors.Java" configurable_name="Java">
    <option name="java" hit="Java" />
    <option name="annotation" hit="Annotations//Annotation attribute name" />
    <option name="annotations" hit="Annotations//Annotation attribute name" />
    <option name="attribute" hit="Annotations//Annotation attribute name" />
    <option name="name" hit="Annotations//Annotation attribute name" />
    <option name="annotation" hit="Annotations//Annotation name" />
    <option name="annotations" hit="Annotations//Annotation name" />
    <option name="name" hit="Annotations//Annotation name" />
    <option name="and" hit="Braces and Operators//Braces" />
    <option name="braces" hit="Braces and Operators//Braces" />
    <option name="operators" hit="Braces and Operators//Braces" />
    <option name="and" hit="Braces and Operators//Brackets" />
    <option name="braces" hit="Braces and Operators//Brackets" />
    <option name="brackets" hit="Braces and Operators//Brackets" />
    <option name="operators" hit="Braces and Operators//Brackets" />
    <option name="and" hit="Braces and Operators//Comma" />
    <option name="braces" hit="Braces and Operators//Comma" />
    <option name="comma" hit="Braces and Operators//Comma" />
    <option name="operators" hit="Braces and Operators//Comma" />
    <option name="and" hit="Braces and Operators//Dot" />
    <option name="braces" hit="Braces and Operators//Dot" />
    <option name="dot" hit="Braces and Operators//Dot" />
    <option name="operators" hit="Braces and Operators//Dot" />
    <option name="and" hit="Braces and Operators//Operator sign" />
    <option name="braces" hit="Braces and Operators//Operator sign" />
    <option name="operator" hit="Braces and Operators//Operator sign" />
    <option name="operators" hit="Braces and Operators//Operator sign" />
    <option name="sign" hit="Braces and Operators//Operator sign" />
    <option name="and" hit="Braces and Operators//Parentheses" />
    <option name="braces" hit="Braces and Operators//Parentheses" />
    <option name="operators" hit="Braces and Operators//Parentheses" />
    <option name="parentheses" hit="Braces and Operators//Parentheses" />
    <option name="and" hit="Braces and Operators//Semicolon" />
    <option name="braces" hit="Braces and Operators//Semicolon" />
    <option name="operators" hit="Braces and Operators//Semicolon" />
    <option name="semicolon" hit="Braces and Operators//Semicolon" />
    <option name="class" hit="Class Fields//Constant (static final field)" />
    <option name="constant" hit="Class Fields//Constant (static final field)" />
    <option name="field" hit="Class Fields//Constant (static final field)" />
    <option name="fields" hit="Class Fields//Constant (static final field)" />
    <option name="final" hit="Class Fields//Constant (static final field)" />
    <option name="static" hit="Class Fields//Constant (static final field)" />
    <option name="class" hit="Class Fields//Constant (static final imported field)" />
    <option name="constant" hit="Class Fields//Constant (static final imported field)" />
    <option name="field" hit="Class Fields//Constant (static final imported field)" />
    <option name="fields" hit="Class Fields//Constant (static final imported field)" />
    <option name="final" hit="Class Fields//Constant (static final imported field)" />
    <option name="imported" hit="Class Fields//Constant (static final imported field)" />
    <option name="static" hit="Class Fields//Constant (static final imported field)" />
    <option name="class" hit="Class Fields//Instance field" />
    <option name="field" hit="Class Fields//Instance field" />
    <option name="fields" hit="Class Fields//Instance field" />
    <option name="instance" hit="Class Fields//Instance field" />
    <option name="class" hit="Class Fields//Instance final field" />
    <option name="field" hit="Class Fields//Instance final field" />
    <option name="fields" hit="Class Fields//Instance final field" />
    <option name="final" hit="Class Fields//Instance final field" />
    <option name="instance" hit="Class Fields//Instance final field" />
    <option name="class" hit="Class Fields//Static field" />
    <option name="field" hit="Class Fields//Static field" />
    <option name="fields" hit="Class Fields//Static field" />
    <option name="static" hit="Class Fields//Static field" />
    <option name="class" hit="Class Fields//Static imported field" />
    <option name="field" hit="Class Fields//Static imported field" />
    <option name="fields" hit="Class Fields//Static imported field" />
    <option name="imported" hit="Class Fields//Static imported field" />
    <option name="static" hit="Class Fields//Static imported field" />
    <option name="abstract" hit="Classes and Interfaces//Abstract class" />
    <option name="and" hit="Classes and Interfaces//Abstract class" />
    <option name="class" hit="Classes and Interfaces//Abstract class" />
    <option name="classes" hit="Classes and Interfaces//Abstract class" />
    <option name="interfaces" hit="Classes and Interfaces//Abstract class" />
    <option name="and" hit="Classes and Interfaces//Anonymous class" />
    <option name="anonymous" hit="Classes and Interfaces//Anonymous class" />
    <option name="class" hit="Classes and Interfaces//Anonymous class" />
    <option name="classes" hit="Classes and Interfaces//Anonymous class" />
    <option name="interfaces" hit="Classes and Interfaces//Anonymous class" />
    <option name="and" hit="Classes and Interfaces//Class" />
    <option name="class" hit="Classes and Interfaces//Class" />
    <option name="classes" hit="Classes and Interfaces//Class" />
    <option name="interfaces" hit="Classes and Interfaces//Class" />
    <option name="and" hit="Classes and Interfaces//Enum" />
    <option name="classes" hit="Classes and Interfaces//Enum" />
    <option name="enum" hit="Classes and Interfaces//Enum" />
    <option name="interfaces" hit="Classes and Interfaces//Enum" />
    <option name="and" hit="Classes and Interfaces//Interface" />
    <option name="classes" hit="Classes and Interfaces//Interface" />
    <option name="interface" hit="Classes and Interfaces//Interface" />
    <option name="interfaces" hit="Classes and Interfaces//Interface" />
    <option name="block" hit="Comments//Block comment" />
    <option name="comment" hit="Comments//Block comment" />
    <option name="comments" hit="Comments//Block comment" />
    <option name="comments" hit="Comments//JavaDoc//Markup" />
    <option name="javadoc" hit="Comments//JavaDoc//Markup" />
    <option name="markup" hit="Comments//JavaDoc//Markup" />
    <option name="comments" hit="Comments//JavaDoc//Tag" />
    <option name="javadoc" hit="Comments//JavaDoc//Tag" />
    <option name="tag" hit="Comments//JavaDoc//Tag" />
    <option name="comments" hit="Comments//JavaDoc//Tag value" />
    <option name="javadoc" hit="Comments//JavaDoc//Tag value" />
    <option name="tag" hit="Comments//JavaDoc//Tag value" />
    <option name="value" hit="Comments//JavaDoc//Tag value" />
    <option name="comments" hit="Comments//JavaDoc//Text" />
    <option name="javadoc" hit="Comments//JavaDoc//Text" />
    <option name="text" hit="Comments//JavaDoc//Text" />
    <option name="comment" hit="Comments//Line comment" />
    <option name="comments" hit="Comments//Line comment" />
    <option name="line" hit="Comments//Line comment" />
    <option name="keyword" hit="Keyword" />
    <option name="abstract" hit="Methods//Abstract method" />
    <option name="method" hit="Methods//Abstract method" />
    <option name="methods" hit="Methods//Abstract method" />
    <option name="call" hit="Methods//Constructor call" />
    <option name="constructor" hit="Methods//Constructor call" />
    <option name="methods" hit="Methods//Constructor call" />
    <option name="constructor" hit="Methods//Constructor declaration" />
    <option name="declaration" hit="Methods//Constructor declaration" />
    <option name="methods" hit="Methods//Constructor declaration" />
    <option name="inherited" hit="Methods//Inherited method" />
    <option name="method" hit="Methods//Inherited method" />
    <option name="methods" hit="Methods//Inherited method" />
    <option name="call" hit="Methods//Method call" />
    <option name="method" hit="Methods//Method call" />
    <option name="methods" hit="Methods//Method call" />
    <option name="declaration" hit="Methods//Method declaration" />
    <option name="method" hit="Methods//Method declaration" />
    <option name="methods" hit="Methods//Method declaration" />
    <option name="call" hit="Methods//Static imported method call" />
    <option name="imported" hit="Methods//Static imported method call" />
    <option name="method" hit="Methods//Static imported method call" />
    <option name="methods" hit="Methods//Static imported method call" />
    <option name="static" hit="Methods//Static imported method call" />
    <option name="method" hit="Methods//Static method" />
    <option name="methods" hit="Methods//Static method" />
    <option name="static" hit="Methods//Static method" />
    <option name="number" hit="Number" />
    <option name="anonymous" hit="Parameters//Implicit anonymous class parameter" />
    <option name="class" hit="Parameters//Implicit anonymous class parameter" />
    <option name="implicit" hit="Parameters//Implicit anonymous class parameter" />
    <option name="parameter" hit="Parameters//Implicit anonymous class parameter" />
    <option name="parameters" hit="Parameters//Implicit anonymous class parameter" />
    <option name="lambda" hit="Parameters//Lambda parameter" />
    <option name="parameter" hit="Parameters//Lambda parameter" />
    <option name="parameters" hit="Parameters//Lambda parameter" />
    <option name="parameter" hit="Parameters//Parameter" />
    <option name="parameters" hit="Parameters//Parameter" />
    <option name="parameter" hit="Parameters//Reassigned parameter" />
    <option name="parameters" hit="Parameters//Reassigned parameter" />
    <option name="reassigned" hit="Parameters//Reassigned parameter" />
    <option name="parameter" hit="Parameters//Type parameter" />
    <option name="parameters" hit="Parameters//Type parameter" />
    <option name="type" hit="Parameters//Type parameter" />
    <option name="highlighting" hit="Semantic highlighting" />
    <option name="semantic" hit="Semantic highlighting" />
    <option name="escape" hit="String//Escape Sequence//Invalid" />
    <option name="invalid" hit="String//Escape Sequence//Invalid" />
    <option name="sequence" hit="String//Escape Sequence//Invalid" />
    <option name="string" hit="String//Escape Sequence//Invalid" />
    <option name="escape" hit="String//Escape Sequence//Valid" />
    <option name="sequence" hit="String//Escape Sequence//Valid" />
    <option name="string" hit="String//Escape Sequence//Valid" />
    <option name="valid" hit="String//Escape Sequence//Valid" />
    <option name="string" hit="String//String text" />
    <option name="text" hit="String//String text" />
    <option name="local" hit="Variables//Local variable" />
    <option name="variable" hit="Variables//Local variable" />
    <option name="variables" hit="Variables//Local variable" />
    <option name="local" hit="Variables//Reassigned local variable" />
    <option name="reassigned" hit="Variables//Reassigned local variable" />
    <option name="variable" hit="Variables//Reassigned local variable" />
    <option name="variables" hit="Variables//Reassigned local variable" />
    <option name="package-private" hit="Visibility//Package-private" />
    <option name="visibility" hit="Visibility//Package-private" />
    <option name="private" hit="Visibility//Private" />
    <option name="visibility" hit="Visibility//Private" />
    <option name="protected" hit="Visibility//Protected" />
    <option name="visibility" hit="Visibility//Protected" />
    <option name="public" hit="Visibility//Public" />
    <option name="visibility" hit="Visibility//Public" />
  </configurable>
  <configurable id="project.propDebugger" configurable_name="Debugger">
    <option name="agent" hit="Attach memory agent" />
    <option name="attach" hit="Attach memory agent" />
    <option name="memory" hit="Attach memory agent" />
    <option name="disable" hit="Disable JIT" />
    <option name="jit" hit="Disable JIT" />
    <option name="java" hit="Java" />
    <option name="debug" hit="Kill the debug process immediately" />
    <option name="immediately" hit="Kill the debug process immediately" />
    <option name="kill" hit="Kill the debug process immediately" />
    <option name="process" hit="Kill the debug process immediately" />
    <option name="the" hit="Kill the debug process immediately" />
    <option name="memory" hit="Shared memory" />
    <option name="shared" hit="Shared memory" />
    <option name="alternative" hit="Show alternative source switcher" />
    <option name="show" hit="Show alternative source switcher" />
    <option name="source" hit="Show alternative source switcher" />
    <option name="switcher" hit="Show alternative source switcher" />
    <option name="" hit="Socket" />
    <option name="socket" hit="Socket" />
    <option name="transport" hit="Transport:" />
  </configurable>
  <configurable id="Debugger_Data_Views_Java" configurable_name="Java">
    <option name="" hit="$val fields as local variables" />
    <option name="as" hit="$val fields as local variables" />
    <option name="fields" hit="$val fields as local variables" />
    <option name="local" hit="$val fields as local variables" />
    <option name="val" hit="$val fields as local variables" />
    <option name="variables" hit="$val fields as local variables" />
    <option name="auto" hit="Auto populate Throwable object's stack trace" />
    <option name="object" hit="Auto populate Throwable object's stack trace" />
    <option name="populate" hit="Auto populate Throwable object's stack trace" />
    <option name="s" hit="Auto populate Throwable object's stack trace" />
    <option name="stack" hit="Auto populate Throwable object's stack trace" />
    <option name="throwable" hit="Auto populate Throwable object's stack trace" />
    <option name="trace" hit="Auto populate Throwable object's stack trace" />
    <option name="autoscroll" hit="Autoscroll to new local variables" />
    <option name="local" hit="Autoscroll to new local variables" />
    <option name="new" hit="Autoscroll to new local variables" />
    <option name="to" hit="Autoscroll to new local variables" />
    <option name="variables" hit="Autoscroll to new local variables" />
    <option name="declared" hit="Declared type" />
    <option name="type" hit="Declared type" />
    <option name="enable" hit="Enable 'toString()' object view:" />
    <option name="object" hit="Enable 'toString()' object view:" />
    <option name="ostring" hit="Enable 'toString()' object view:" />
    <option name="t" hit="Enable 'toString()' object view:" />
    <option name="view" hit="Enable 'toString()' object view:" />
    <option name="alternative" hit="Enable alternative view for Collections classes" />
    <option name="classes" hit="Enable alternative view for Collections classes" />
    <option name="coll" hit="Enable alternative view for Collections classes" />
    <option name="ections" hit="Enable alternative view for Collections classes" />
    <option name="enable" hit="Enable alternative view for Collections classes" />
    <option name="for" hit="Enable alternative view for Collections classes" />
    <option name="view" hit="Enable alternative view for Collections classes" />
    <option name="all" hit="For all classes that override 'toString()' method" />
    <option name="classes" hit="For all classes that override 'toString()' method" />
    <option name="for" hit="For all classes that override 'toString()' method" />
    <option name="method" hit="For all classes that override 'toString()' method" />
    <option name="override" hit="For all classes that override 'toString()' method" />
    <option name="that" hit="For all classes that override 'toString()' method" />
    <option name="tostring" hit="For all classes that override 'toString()' method" />
    <option name="classes" hit="For classes from the list:" />
    <option name="for" hit="For classes from the list:" />
    <option name="from" hit="For classes from the list:" />
    <option name="list" hit="For classes from the list:" />
    <option name="the" hit="For classes from the list:" />
    <option name="fully" hit="Fully qualified names" />
    <option name="names" hit="Fully qualified names" />
    <option name="qualified" hit="Fully qualified names" />
    <option name="are" hit="Gray out blocks of code that are predicted to be unreachable" />
    <option name="be" hit="Gray out blocks of code that are predicted to be unreachable" />
    <option name="blocks" hit="Gray out blocks of code that are predicted to be unreachable" />
    <option name="code" hit="Gray out blocks of code that are predicted to be unreachable" />
    <option name="gray" hit="Gray out blocks of code that are predicted to be unreachable" />
    <option name="of" hit="Gray out blocks of code that are predicted to be unreachable" />
    <option name="out" hit="Gray out blocks of code that are predicted to be unreachable" />
    <option name="predicted" hit="Gray out blocks of code that are predicted to be unreachable" />
    <option name="that" hit="Gray out blocks of code that are predicted to be unreachable" />
    <option name="to" hit="Gray out blocks of code that are predicted to be unreachable" />
    <option name="unreachable" hit="Gray out blocks of code that are predicted to be unreachable" />
    <option name="and" hit="Hide null elements in arrays and collections" />
    <option name="arrays" hit="Hide null elements in arrays and collections" />
    <option name="collections" hit="Hide null elements in arrays and collections" />
    <option name="elements" hit="Hide null elements in arrays and collections" />
    <option name="hide" hit="Hide null elements in arrays and collections" />
    <option name="in" hit="Hide null elements in arrays and collections" />
    <option name="null" hit="Hide null elements in arrays and collections" />
    <option name="java" hit="Java" />
    <option name="id" hit="Object id" />
    <option name="object" hit="Object id" />
    <option name="analysis" hit="Predict condition values and exceptions based on data flow analysis" />
    <option name="and" hit="Predict condition values and exceptions based on data flow analysis" />
    <option name="based" hit="Predict condition values and exceptions based on data flow analysis" />
    <option name="condition" hit="Predict condition values and exceptions based on data flow analysis" />
    <option name="data" hit="Predict condition values and exceptions based on data flow analysis" />
    <option name="exceptions" hit="Predict condition values and exceptions based on data flow analysis" />
    <option name="flow" hit="Predict condition values and exceptions based on data flow analysis" />
    <option name="on" hit="Predict condition values and exceptions based on data flow analysis" />
    <option name="predict" hit="Predict condition values and exceptions based on data flow analysis" />
    <option name="values" hit="Predict condition values and exceptions based on data flow analysis" />
    <option name="show" hit="Show" />
    <option name="for" hit="Show hex value for primitives" />
    <option name="hex" hit="Show hex value for primitives" />
    <option name="primitives" hit="Show hex value for primitives" />
    <option name="show" hit="Show hex value for primitives" />
    <option name="value" hit="Show hex value for primitives" />
    <option name="for" hit="Show type for strings" />
    <option name="show" hit="Show type for strings" />
    <option name="strings" hit="Show type for strings" />
    <option name="type" hit="Show type for strings" />
    <option name="" hit="Static fields" />
    <option name="fields" hit="Static fields" />
    <option name="static" hit="Static fields" />
    <option name="fields" hit="Static final fields" />
    <option name="final" hit="Static final fields" />
    <option name="static" hit="Static final fields" />
    <option name="fields" hit="Synthetic fields" />
    <option name="s" hit="Synthetic fields" />
    <option name="ynthetic" hit="Synthetic fields" />
  </configurable>
  <configurable id="reference.idesettings.debugger.typerenderers" configurable_name="Java Type Renderers">
    <option name="append" hit="Append default children" />
    <option name="children" hit="Append default children" />
    <option name="default" hit="Append default children" />
    <option name="apply" hit="Apply renderer to objects of type (fully qualified name):" />
    <option name="fully" hit="Apply renderer to objects of type (fully qualified name):" />
    <option name="name" hit="Apply renderer to objects of type (fully qualified name):" />
    <option name="objects" hit="Apply renderer to objects of type (fully qualified name):" />
    <option name="of" hit="Apply renderer to objects of type (fully qualified name):" />
    <option name="qualified" hit="Apply renderer to objects of type (fully qualified name):" />
    <option name="renderer" hit="Apply renderer to objects of type (fully qualified name):" />
    <option name="to" hit="Apply renderer to objects of type (fully qualified name):" />
    <option name="type" hit="Apply renderer to objects of type (fully qualified name):" />
    <option name="java" hit="Java Type Renderers" />
    <option name="renderers" hit="Java Type Renderers" />
    <option name="type" hit="Java Type Renderers" />
    <option name="on-demand" hit="On-demand" />
    <option name="name" hit="Renderer name:" />
    <option name="renderer" hit="Renderer name:" />
    <option name="and" hit="Show type and object id" />
    <option name="id" hit="Show type and object id" />
    <option name="object" hit="Show type and object id" />
    <option name="show" hit="Show type and object id" />
    <option name="type" hit="Show type and object id" />
    <option name="a" hit="Test if a node can be expanded (optional):" />
    <option name="be" hit="Test if a node can be expanded (optional):" />
    <option name="can" hit="Test if a node can be expanded (optional):" />
    <option name="expanded" hit="Test if a node can be expanded (optional):" />
    <option name="if" hit="Test if a node can be expanded (optional):" />
    <option name="node" hit="Test if a node can be expanded (optional):" />
    <option name="optional" hit="Test if a node can be expanded (optional):" />
    <option name="test" hit="Test if a node can be expanded (optional):" />
    <option name="default" hit="Use default renderer" />
    <option name="renderer" hit="Use default renderer" />
    <option name="use" hit="Use default renderer" />
    <option name="expression" hit="Use following expression:" />
    <option name="following" hit="Use following expression:" />
    <option name="use" hit="Use following expression:" />
    <option name="expressions" hit="Use list of expressions:" />
    <option name="list" hit="Use list of expressions:" />
    <option name="of" hit="Use list of expressions:" />
    <option name="use" hit="Use list of expressions:" />
    <option name="a" hit="When expanding a node" />
    <option name="expanding" hit="When expanding a node" />
    <option name="node" hit="When expanding a node" />
    <option name="when" hit="When expanding a node" />
    <option name="a" hit="When rendering a node" />
    <option name="node" hit="When rendering a node" />
    <option name="rendering" hit="When rendering a node" />
    <option name="when" hit="When rendering a node" />
  </configurable>
  <configurable id="debugger.stepping" configurable_name="Stepping">
    <option name="" hit="Always" />
    <option name="always" hit="Always" />
    <option name="a" hit="Always do smart step into" />
    <option name="do" hit="Always do smart step into" />
    <option name="into" hit="Always do smart step into" />
    <option name="lways" hit="Always do smart step into" />
    <option name="smart" hit="Always do smart step into" />
    <option name="step" hit="Always do smart step into" />
    <option name="as" hit="Ask" />
    <option name="k" hit="Ask" />
    <option name="classes" hit="Do not step into the classes" />
    <option name="do" hit="Do not step into the classes" />
    <option name="into" hit="Do not step into the classes" />
    <option name="not" hit="Do not step into the classes" />
    <option name="step" hit="Do not step into the classes" />
    <option name="the" hit="Do not step into the classes" />
    <option name="and" hit="Evaluate finally blocks on pop frame and early return:" />
    <option name="blocks" hit="Evaluate finally blocks on pop frame and early return:" />
    <option name="early" hit="Evaluate finally blocks on pop frame and early return:" />
    <option name="evaluate" hit="Evaluate finally blocks on pop frame and early return:" />
    <option name="finally" hit="Evaluate finally blocks on pop frame and early return:" />
    <option name="frame" hit="Evaluate finally blocks on pop frame and early return:" />
    <option name="on" hit="Evaluate finally blocks on pop frame and early return:" />
    <option name="pop" hit="Evaluate finally blocks on pop frame and early return:" />
    <option name="return" hit="Evaluate finally blocks on pop frame and early return:" />
    <option name="filters" hit="Hide stack frames using stepping filters" />
    <option name="frames" hit="Hide stack frames using stepping filters" />
    <option name="hide" hit="Hide stack frames using stepping filters" />
    <option name="stack" hit="Hide stack frames using stepping filters" />
    <option name="stepping" hit="Hide stack frames using stepping filters" />
    <option name="using" hit="Hide stack frames using stepping filters" />
    <option name="java" hit="Java" />
    <option name="ever" hit="Never" />
    <option name="n" hit="Never" />
    <option name="current" hit="Resume only the current thread" />
    <option name="only" hit="Resume only the current thread" />
    <option name="resume" hit="Resume only the current thread" />
    <option name="the" hit="Resume only the current thread" />
    <option name="thread" hit="Resume only the current thread" />
    <option name="class" hit="Skip class loaders" />
    <option name="l" hit="Skip class loaders" />
    <option name="oaders" hit="Skip class loaders" />
    <option name="skip" hit="Skip class loaders" />
    <option name="constructors" hit="Skip constructors" />
    <option name="skip" hit="Skip constructors" />
    <option name="getters" hit="Skip simple getters" />
    <option name="simple" hit="Skip simple getters" />
    <option name="skip" hit="Skip simple getters" />
    <option name="methods" hit="Skip synthetic methods" />
    <option name="p" hit="Skip synthetic methods" />
    <option name="ski" hit="Skip synthetic methods" />
    <option name="synthetic" hit="Skip synthetic methods" />
  </configurable>
  <configurable id="debugger.hotswap" configurable_name="HotSwap">
    <option name="" hit="Always" />
    <option name="always" hit="Always" />
    <option name="as" hit="Ask" />
    <option name="k" hit="Ask" />
    <option name="before" hit="Build project before reloading classes" />
    <option name="build" hit="Build project before reloading classes" />
    <option name="classes" hit="Build project before reloading classes" />
    <option name="project" hit="Build project before reloading classes" />
    <option name="reloading" hit="Build project before reloading classes" />
    <option name="enable" hit="Enable 'JVM will hang' warning" />
    <option name="hang" hit="Enable 'JVM will hang' warning" />
    <option name="jvm" hit="Enable 'JVM will hang' warning" />
    <option name="warning" hit="Enable 'JVM will hang' warning" />
    <option name="will" hit="Enable 'JVM will hang' warning" />
    <option name="java" hit="Java" />
    <option name="" hit="Never" />
    <option name="never" hit="Never" />
    <option name="after" hit="Reload classes after compilation:" />
    <option name="classes" hit="Reload classes after compilation:" />
    <option name="compilation" hit="Reload classes after compilation:" />
    <option name="reload" hit="Reload classes after compilation:" />
  </configurable>
  <configurable id="promo.database" configurable_name="Database">
    <option name="database" hit="Database" />
    <option name="get" hit="Get IntelliJ IDEA Ultimate" />
    <option name="idea" hit="Get IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Get IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Get IntelliJ IDEA Ultimate" />
    <option name="idea" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="to" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="upgrade" hit="Upgrade to IntelliJ IDEA Ultimate" />
  </configurable>
  <configurable id="promo.kubernetes" configurable_name="Kubernetes">
    <option name="get" hit="Get IntelliJ IDEA Ultimate" />
    <option name="idea" hit="Get IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Get IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Get IntelliJ IDEA Ultimate" />
    <option name="kubernetes" hit="Kubernetes" />
    <option name="idea" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="to" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="upgrade" hit="Upgrade to IntelliJ IDEA Ultimate" />
  </configurable>
  <configurable id="promo.javascript" configurable_name="JavaScript">
    <option name="get" hit="Get IntelliJ IDEA Ultimate" />
    <option name="idea" hit="Get IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Get IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Get IntelliJ IDEA Ultimate" />
    <option name="javascript" hit="JavaScript" />
    <option name="idea" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="to" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="upgrade" hit="Upgrade to IntelliJ IDEA Ultimate" />
  </configurable>
  <configurable id="promo.typescript" configurable_name="TypeScript">
    <option name="get" hit="Get IntelliJ IDEA Ultimate" />
    <option name="idea" hit="Get IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Get IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Get IntelliJ IDEA Ultimate" />
    <option name="typescript" hit="TypeScript" />
    <option name="idea" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="to" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="upgrade" hit="Upgrade to IntelliJ IDEA Ultimate" />
  </configurable>
  <configurable id="promo.swagger" configurable_name="OpenAPI Specifications">
    <option name="get" hit="Get IntelliJ IDEA Ultimate" />
    <option name="idea" hit="Get IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Get IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Get IntelliJ IDEA Ultimate" />
    <option name="openapi" hit="OpenAPI Specifications" />
    <option name="specifications" hit="OpenAPI Specifications" />
    <option name="idea" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="to" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="upgrade" hit="Upgrade to IntelliJ IDEA Ultimate" />
  </configurable>
  <configurable id="promo.profiler" configurable_name="Java Profiler">
    <option name="get" hit="Get IntelliJ IDEA Ultimate" />
    <option name="idea" hit="Get IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Get IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Get IntelliJ IDEA Ultimate" />
    <option name="java" hit="Java Profiler" />
    <option name="profiler" hit="Java Profiler" />
    <option name="idea" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="intellij" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="to" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="ultimate" hit="Upgrade to IntelliJ IDEA Ultimate" />
    <option name="upgrade" hit="Upgrade to IntelliJ IDEA Ultimate" />
  </configurable>
  <configurable id="preferences.sourceCode.Java" configurable_name="Java">
    <option name="absolute" path="Tabs and Indents" hit="Absolute label indent" />
    <option name="indent" path="Tabs and Indents" hit="Absolute label indent" />
    <option name="label" path="Tabs and Indents" hit="Absolute label indent" />
    <option name="a" path="Code Generation" hit="Add a space at line comment start" />
    <option name="add" path="Code Generation" hit="Add a space at line comment start" />
    <option name="at" path="Code Generation" hit="Add a space at line comment start" />
    <option name="comment" path="Code Generation" hit="Add a space at line comment start" />
    <option name="line" path="Code Generation" hit="Add a space at line comment start" />
    <option name="space" path="Code Generation" hit="Add a space at line comment start" />
    <option name="start" path="Code Generation" hit="Add a space at line comment start" />
    <option name="add" path="Code Generation" hit="Add spaces around block comments" />
    <option name="around" path="Code Generation" hit="Add spaces around block comments" />
    <option name="block" path="Code Generation" hit="Add spaces around block comments" />
    <option name="comments" path="Code Generation" hit="Add spaces around block comments" />
    <option name="spaces" path="Code Generation" hit="Add spaces around block comments" />
    <option name="after" path="Blank Lines" hit="After anonymous class header:" />
    <option name="anonymous" path="Blank Lines" hit="After anonymous class header:" />
    <option name="class" path="Blank Lines" hit="After anonymous class header:" />
    <option name="header" path="Blank Lines" hit="After anonymous class header:" />
    <option name="after" path="Blank Lines" hit="After class header:" />
    <option name="class" path="Blank Lines" hit="After class header:" />
    <option name="header" path="Blank Lines" hit="After class header:" />
    <option name="after" path="Blank Lines" hit="After imports:" />
    <option name="imports" path="Blank Lines" hit="After imports:" />
    <option name="after" path="Blank Lines" hit="After package statement:" />
    <option name="package" path="Blank Lines" hit="After package statement:" />
    <option name="statement" path="Blank Lines" hit="After package statement:" />
    <option name="always" path="Imports" hit="Always" />
    <option name="annotations" path="Code Generation" hit="Annotations to Copy" />
    <option name="copy" path="Code Generation" hit="Annotations to Copy" />
    <option name="to" path="Code Generation" hit="Annotations to Copy" />
    <option name="around" path="Blank Lines" hit="Around class:" />
    <option name="class" path="Blank Lines" hit="Around class:" />
    <option name="around" path="Blank Lines" hit="Around field in interface:" />
    <option name="field" path="Blank Lines" hit="Around field in interface:" />
    <option name="in" path="Blank Lines" hit="Around field in interface:" />
    <option name="interface" path="Blank Lines" hit="Around field in interface:" />
    <option name="around" path="Blank Lines" hit="Around field:" />
    <option name="field" path="Blank Lines" hit="Around field:" />
    <option name="around" path="Blank Lines" hit="Around initializer:" />
    <option name="initializer" path="Blank Lines" hit="Around initializer:" />
    <option name="around" path="Blank Lines" hit="Around method in interface:" />
    <option name="in" path="Blank Lines" hit="Around method in interface:" />
    <option name="interface" path="Blank Lines" hit="Around method in interface:" />
    <option name="method" path="Blank Lines" hit="Around method in interface:" />
    <option name="around" path="Blank Lines" hit="Around method:" />
    <option name="method" path="Blank Lines" hit="Around method:" />
    <option name="arrangement" path="Arrangement" hit="Arrangement" />
    <option name="before" path="Blank Lines" hit="Before '}':" />
    <option name="before" path="Blank Lines" hit="Before class end:" />
    <option name="class" path="Blank Lines" hit="Before class end:" />
    <option name="end" path="Blank Lines" hit="Before class end:" />
    <option name="before" path="Blank Lines" hit="Before imports:" />
    <option name="imports" path="Blank Lines" hit="Before imports:" />
    <option name="before" path="Blank Lines" hit="Before method body:" />
    <option name="body" path="Blank Lines" hit="Before method body:" />
    <option name="method" path="Blank Lines" hit="Before method body:" />
    <option name="before" path="Blank Lines" hit="Before package statement:" />
    <option name="package" path="Blank Lines" hit="Before package statement:" />
    <option name="statement" path="Blank Lines" hit="Before package statement:" />
    <option name="and" path="Blank Lines" hit="Between header and package:" />
    <option name="between" path="Blank Lines" hit="Between header and package:" />
    <option name="header" path="Blank Lines" hit="Between header and package:" />
    <option name="package" path="Blank Lines" hit="Between header and package:" />
    <option name="blank" path="Blank Lines" hit="Blank Lines" />
    <option name="lines" path="Blank Lines" hit="Blank Lines" />
    <option name="at" path="Code Generation" hit="Block comment at first column" />
    <option name="block" path="Code Generation" hit="Block comment at first column" />
    <option name="column" path="Code Generation" hit="Block comment at first column" />
    <option name="comment" path="Code Generation" hit="Block comment at first column" />
    <option name="first" path="Code Generation" hit="Block comment at first column" />
    <option name="class" path="Imports" hit="Class count to use import with '*':" />
    <option name="count" path="Imports" hit="Class count to use import with '*':" />
    <option name="import" path="Imports" hit="Class count to use import with '*':" />
    <option name="to" path="Imports" hit="Class count to use import with '*':" />
    <option name="use" path="Imports" hit="Class count to use import with '*':" />
    <option name="with" path="Imports" hit="Class count to use import with '*':" />
    <option name="code" path="Code Generation" hit="Code Generation" />
    <option name="generation" path="Code Generation" hit="Code Generation" />
    <option name="code" path="Code Generation" hit="Comment Code" />
    <option name="comment" path="Code Generation" hit="Comment Code" />
    <option name="continuation" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="indent" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="default" path="Code Generation" hit="Default Visibility" />
    <option name="visibility" path="Code Generation" hit="Default Visibility" />
    <option name="disable" hit="Disable" />
    <option name="class" path="Tabs and Indents" hit="Do not indent top level class members" />
    <option name="do" path="Tabs and Indents" hit="Do not indent top level class members" />
    <option name="indent" path="Tabs and Indents" hit="Do not indent top level class members" />
    <option name="level" path="Tabs and Indents" hit="Do not indent top level class members" />
    <option name="members" path="Tabs and Indents" hit="Do not indent top level class members" />
    <option name="not" path="Tabs and Indents" hit="Do not indent top level class members" />
    <option name="top" path="Tabs and Indents" hit="Do not indent top level class members" />
    <option name="enable" path="JavaDoc" hit="Enable JavaDoc formatting" />
    <option name="formatting" path="JavaDoc" hit="Enable JavaDoc formatting" />
    <option name="javadoc" path="JavaDoc" hit="Enable JavaDoc formatting" />
    <option name="enforce" path="Code Generation" hit="Enforce on reformat" />
    <option name="on" path="Code Generation" hit="Enforce on reformat" />
    <option name="reformat" path="Code Generation" hit="Enforce on reformat" />
    <option name="" path="Code Generation" hit="Escalate" />
    <option name="escalate" path="Code Generation" hit="Escalate" />
    <option name="field" path="Code Generation" hit="Field:" />
    <option name="general" path="Imports" hit="General" />
    <option name="grouping" path="Arrangement" hit="Grouping rules:" />
    <option name="rules" path="Arrangement" hit="Grouping rules:" />
    <option name="already" path="Imports" hit="If not already imported" />
    <option name="if" path="Imports" hit="If not already imported" />
    <option name="imported" path="Imports" hit="If not already imported" />
    <option name="not" path="Imports" hit="If not already imported" />
    <option name="import" path="Imports" hit="Import Layout" />
    <option name="layout" path="Imports" hit="Import Layout" />
    <option name="imports" path="Imports" hit="Imports" />
    <option name="code" path="Blank Lines" hit="In code:" />
    <option name="in" path="Blank Lines" hit="In code:" />
    <option name="declarations" path="Blank Lines" hit="In declarations:" />
    <option name="in" path="Blank Lines" hit="In declarations:" />
    <option name="indent" path="Tabs and Indents" hit="Indent:" />
    <option name="annotation" path="Code Generation" hit="Insert @Override annotation" />
    <option name="insert" path="Code Generation" hit="Insert @Override annotation" />
    <option name="override" path="Code Generation" hit="Insert @Override annotation" />
    <option name="classes" path="Imports" hit="Insert imports for inner classes" />
    <option name="for" path="Imports" hit="Insert imports for inner classes" />
    <option name="imports" path="Imports" hit="Insert imports for inner classes" />
    <option name="inner" path="Imports" hit="Insert imports for inner classes" />
    <option name="insert" path="Imports" hit="Insert imports for inner classes" />
    <option name="java" hit="Java" />
    <option name="javadoc" path="JavaDoc" hit="JavaDoc" />
    <option name="empty" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="indents" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="keep" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="lines" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="on" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="blank" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="keep" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="lines" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="maximum" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="indent" path="Tabs and Indents" hit="Label indent:" />
    <option name="label" path="Tabs and Indents" hit="Label indent:" />
    <option name="body" path="Code Generation" hit="Lambda Body" />
    <option name="lambda" path="Code Generation" hit="Lambda Body" />
    <option name="imports" path="Imports" hit="Layout static imports separately" />
    <option name="layout" path="Imports" hit="Layout static imports separately" />
    <option name="separately" path="Imports" hit="Layout static imports separately" />
    <option name="static" path="Imports" hit="Layout static imports separately" />
    <option name="at" path="Code Generation" hit="Line comment at first column" />
    <option name="column" path="Code Generation" hit="Line comment at first column" />
    <option name="comment" path="Code Generation" hit="Line comment at first column" />
    <option name="first" path="Code Generation" hit="Line comment at first column" />
    <option name="line" path="Code Generation" hit="Line comment at first column" />
    <option name="loading" hit="Loading..." />
    <option name="local" path="Code Generation" hit="Local variable:" />
    <option name="variable" path="Code Generation" hit="Local variable:" />
    <option name="final" path="Code Generation" hit="Make generated local variables final" />
    <option name="generated" path="Code Generation" hit="Make generated local variables final" />
    <option name="local" path="Code Generation" hit="Make generated local variables final" />
    <option name="make" path="Code Generation" hit="Make generated local variables final" />
    <option name="variables" path="Code Generation" hit="Make generated local variables final" />
    <option name="final" path="Code Generation" hit="Make generated parameters final" />
    <option name="generated" path="Code Generation" hit="Make generated parameters final" />
    <option name="make" path="Code Generation" hit="Make generated parameters final" />
    <option name="parameters" path="Code Generation" hit="Make generated parameters final" />
    <option name="matching" path="Arrangement" hit="Matching rules:" />
    <option name="rules" path="Arrangement" hit="Matching rules:" />
    <option name="blank" path="Blank Lines" hit="Minimum blank lines" />
    <option name="lines" path="Blank Lines" hit="Minimum blank lines" />
    <option name="minimum" path="Blank Lines" hit="Minimum blank lines" />
    <option name="name" path="Code Generation" hit="Name prefix:" />
    <option name="prefix" path="Code Generation" hit="Name prefix:" />
    <option name="name" path="Code Generation" hit="Name suffix:" />
    <option name="suffix" path="Code Generation" hit="Name suffix:" />
    <option name="count" path="Imports" hit="Names count to use static import with '*':" />
    <option name="import" path="Imports" hit="Names count to use static import with '*':" />
    <option name="names" path="Imports" hit="Names count to use static import with '*':" />
    <option name="static" path="Imports" hit="Names count to use static import with '*':" />
    <option name="to" path="Imports" hit="Names count to use static import with '*':" />
    <option name="use" path="Imports" hit="Names count to use static import with '*':" />
    <option name="with" path="Imports" hit="Names count to use static import with '*':" />
    <option name="naming" path="Code Generation" hit="Naming" />
    <option name="add" path="Imports" hit="Never, use short name and add import" />
    <option name="and" path="Imports" hit="Never, use short name and add import" />
    <option name="import" path="Imports" hit="Never, use short name and add import" />
    <option name="name" path="Imports" hit="Never, use short name and add import" />
    <option name="never" path="Imports" hit="Never, use short name and add import" />
    <option name="short" path="Imports" hit="Never, use short name and add import" />
    <option name="use" path="Imports" hit="Never, use short name and add import" />
    <option name="method" path="Code Generation" hit="Override Method Signature" />
    <option name="override" path="Code Generation" hit="Override Method Signature" />
    <option name="signature" path="Code Generation" hit="Override Method Signature" />
    <option name="kage" path="Code Generation" hit="Package local" />
    <option name="local" path="Code Generation" hit="Package local" />
    <option name="pac" path="Code Generation" hit="Package local" />
    <option name="parameter" path="Code Generation" hit="Parameter:" />
    <option name="longer" path="Code Generation" hit="Prefer longer names" />
    <option name="names" path="Code Generation" hit="Prefer longer names" />
    <option name="prefer" path="Code Generation" hit="Prefer longer names" />
    <option name="pri" path="Code Generation" hit="Private" />
    <option name="vate" path="Code Generation" hit="Private" />
    <option name="otected" path="Code Generation" hit="Protected" />
    <option name="pr" path="Code Generation" hit="Protected" />
    <option name="blic" path="Code Generation" hit="Public" />
    <option name="pu" path="Code Generation" hit="Public" />
    <option name="modifier" path="Code Generation" hit="Repeat synchronized modifier" />
    <option name="repeat" path="Code Generation" hit="Repeat synchronized modifier" />
    <option name="synchronized" path="Code Generation" hit="Repeat synchronized modifier" />
    <option name="isnull" path="Code Generation" hit="Replace null-check with Objects::nonNull or Objects::isNull" />
    <option name="nonnull" path="Code Generation" hit="Replace null-check with Objects::nonNull or Objects::isNull" />
    <option name="null-check" path="Code Generation" hit="Replace null-check with Objects::nonNull or Objects::isNull" />
    <option name="objects" path="Code Generation" hit="Replace null-check with Objects::nonNull or Objects::isNull" />
    <option name="or" path="Code Generation" hit="Replace null-check with Objects::nonNull or Objects::isNull" />
    <option name="replace" path="Code Generation" hit="Replace null-check with Objects::nonNull or Objects::isNull" />
    <option name="with" path="Code Generation" hit="Replace null-check with Objects::nonNull or Objects::isNull" />
    <option name="scheme" hit="Scheme:" />
    <option name="from" hit="Set from..." />
    <option name="set" hit="Set from..." />
    <option name="smart" path="Tabs and Indents" hit="Smart tabs" />
    <option name="tabs" path="Tabs and Indents" hit="Smart tabs" />
    <option name="spaces" path="Spaces" hit="Spaces" />
    <option name="field" path="Code Generation" hit="Static field:" />
    <option name="static" path="Code Generation" hit="Static field:" />
    <option name="subclass" path="Code Generation" hit="Subclass:" />
    <option name="size" path="Tabs and Indents" hit="Tab size:" />
    <option name="tab" path="Tabs and Indents" hit="Tab size:" />
    <option name="and" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="indents" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="tabs" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="class" path="Code Generation" hit="Test class:" />
    <option name="test" path="Code Generation" hit="Test class:" />
    <option name="declaration" path="Code Generation" hit="Use 'var' for local variable declaration" />
    <option name="for" path="Code Generation" hit="Use 'var' for local variable declaration" />
    <option name="local" path="Code Generation" hit="Use 'var' for local variable declaration" />
    <option name="use" path="Code Generation" hit="Use 'var' for local variable declaration" />
    <option name="var" path="Code Generation" hit="Use 'var' for local variable declaration" />
    <option name="variable" path="Code Generation" hit="Use 'var' for local variable declaration" />
    <option name="and" path="Code Generation" hit="Use Class::isInstance and Class::cast when possible" />
    <option name="cast" path="Code Generation" hit="Use Class::isInstance and Class::cast when possible" />
    <option name="class" path="Code Generation" hit="Use Class::isInstance and Class::cast when possible" />
    <option name="isinstance" path="Code Generation" hit="Use Class::isInstance and Class::cast when possible" />
    <option name="possible" path="Code Generation" hit="Use Class::isInstance and Class::cast when possible" />
    <option name="use" path="Code Generation" hit="Use Class::isInstance and Class::cast when possible" />
    <option name="when" path="Code Generation" hit="Use Class::isInstance and Class::cast when possible" />
    <option name="etc" path="Code Generation" hit="Use Integer::sum, etc. when possible" />
    <option name="integer" path="Code Generation" hit="Use Integer::sum, etc. when possible" />
    <option name="possible" path="Code Generation" hit="Use Integer::sum, etc. when possible" />
    <option name="sum" path="Code Generation" hit="Use Integer::sum, etc. when possible" />
    <option name="use" path="Code Generation" hit="Use Integer::sum, etc. when possible" />
    <option name="when" path="Code Generation" hit="Use Integer::sum, etc. when possible" />
    <option name="annotations" path="Code Generation" hit="Use external annotations" />
    <option name="external" path="Code Generation" hit="Use external annotations" />
    <option name="use" path="Code Generation" hit="Use external annotations" />
    <option name="class" path="Imports" hit="Use fully qualified class names" />
    <option name="fully" path="Imports" hit="Use fully qualified class names" />
    <option name="names" path="Imports" hit="Use fully qualified class names" />
    <option name="qualified" path="Imports" hit="Use fully qualified class names" />
    <option name="use" path="Imports" hit="Use fully qualified class names" />
    <option name="class" path="Imports" hit="Use fully qualified class names in JavaDoc:" />
    <option name="fully" path="Imports" hit="Use fully qualified class names in JavaDoc:" />
    <option name="in" path="Imports" hit="Use fully qualified class names in JavaDoc:" />
    <option name="javadoc" path="Imports" hit="Use fully qualified class names in JavaDoc:" />
    <option name="names" path="Imports" hit="Use fully qualified class names in JavaDoc:" />
    <option name="qualified" path="Imports" hit="Use fully qualified class names in JavaDoc:" />
    <option name="use" path="Imports" hit="Use fully qualified class names in JavaDoc:" />
    <option name="expression" path="Tabs and Indents" hit="Use indents relative to expression start" />
    <option name="indents" path="Tabs and Indents" hit="Use indents relative to expression start" />
    <option name="relative" path="Tabs and Indents" hit="Use indents relative to expression start" />
    <option name="start" path="Tabs and Indents" hit="Use indents relative to expression start" />
    <option name="to" path="Tabs and Indents" hit="Use indents relative to expression start" />
    <option name="use" path="Tabs and Indents" hit="Use indents relative to expression start" />
    <option name="class" path="Imports" hit="Use single class import" />
    <option name="import" path="Imports" hit="Use single class import" />
    <option name="single" path="Imports" hit="Use single class import" />
    <option name="use" path="Imports" hit="Use single class import" />
    <option name="character" path="Tabs and Indents" hit="Use tab character" />
    <option name="tab" path="Tabs and Indents" hit="Use tab character" />
    <option name="use" path="Tabs and Indents" hit="Use tab character" />
    <option name="declaration" path="Code Generation" hit="Variable declaration" />
    <option name="variable" path="Code Generation" hit="Variable declaration" />
    <option name="and" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="braces" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="wrapping" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="line" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="next" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="on" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="signs" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="and" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="line" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="next" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="on" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="signs" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="" path="Spaces" hit="'catch' keyword" />
    <option name="catch" path="Spaces" hit="'catch' keyword" />
    <option name="keyword" path="Spaces" hit="'catch' keyword" />
    <option name="" path="Spaces" hit="'catch' left brace" />
    <option name="brace" path="Spaces" hit="'catch' left brace" />
    <option name="catch" path="Spaces" hit="'catch' left brace" />
    <option name="left" path="Spaces" hit="'catch' left brace" />
    <option name="" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="catch" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="" path="Spaces" hit="'catch' parentheses" />
    <option name="catch" path="Spaces" hit="'catch' parentheses" />
    <option name="parentheses" path="Spaces" hit="'catch' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="do" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="while" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="" path="Spaces" hit="'do' left brace" />
    <option name="brace" path="Spaces" hit="'do' left brace" />
    <option name="do" path="Spaces" hit="'do' left brace" />
    <option name="left" path="Spaces" hit="'do' left brace" />
    <option name="" path="Spaces" hit="'else' keyword" />
    <option name="else" path="Spaces" hit="'else' keyword" />
    <option name="keyword" path="Spaces" hit="'else' keyword" />
    <option name="" path="Spaces" hit="'else' left brace" />
    <option name="brace" path="Spaces" hit="'else' left brace" />
    <option name="else" path="Spaces" hit="'else' left brace" />
    <option name="left" path="Spaces" hit="'else' left brace" />
    <option name="" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="else" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="" path="Spaces" hit="'finally' keyword" />
    <option name="finally" path="Spaces" hit="'finally' keyword" />
    <option name="keyword" path="Spaces" hit="'finally' keyword" />
    <option name="" path="Spaces" hit="'finally' left brace" />
    <option name="brace" path="Spaces" hit="'finally' left brace" />
    <option name="finally" path="Spaces" hit="'finally' left brace" />
    <option name="left" path="Spaces" hit="'finally' left brace" />
    <option name="" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="finally" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="" path="Spaces" hit="'for' left brace" />
    <option name="brace" path="Spaces" hit="'for' left brace" />
    <option name="for" path="Spaces" hit="'for' left brace" />
    <option name="left" path="Spaces" hit="'for' left brace" />
    <option name="" path="Spaces" hit="'for' parentheses" />
    <option name="for" path="Spaces" hit="'for' parentheses" />
    <option name="parentheses" path="Spaces" hit="'for' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="for" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="" path="Spaces" hit="'if' left brace" />
    <option name="brace" path="Spaces" hit="'if' left brace" />
    <option name="if" path="Spaces" hit="'if' left brace" />
    <option name="left" path="Spaces" hit="'if' left brace" />
    <option name="" path="Spaces" hit="'if' parentheses" />
    <option name="if" path="Spaces" hit="'if' parentheses" />
    <option name="parentheses" path="Spaces" hit="'if' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="if" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="" path="Spaces" hit="'switch' left brace" />
    <option name="brace" path="Spaces" hit="'switch' left brace" />
    <option name="left" path="Spaces" hit="'switch' left brace" />
    <option name="switch" path="Spaces" hit="'switch' left brace" />
    <option name="" path="Spaces" hit="'switch' parentheses" />
    <option name="parentheses" path="Spaces" hit="'switch' parentheses" />
    <option name="switch" path="Spaces" hit="'switch' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="switch" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="" path="Spaces" hit="'synchronized' left brace" />
    <option name="brace" path="Spaces" hit="'synchronized' left brace" />
    <option name="left" path="Spaces" hit="'synchronized' left brace" />
    <option name="synchronized" path="Spaces" hit="'synchronized' left brace" />
    <option name="" path="Spaces" hit="'synchronized' parentheses" />
    <option name="parentheses" path="Spaces" hit="'synchronized' parentheses" />
    <option name="synchronized" path="Spaces" hit="'synchronized' parentheses" />
    <option name="" path="Spaces" hit="'try' left brace" />
    <option name="brace" path="Spaces" hit="'try' left brace" />
    <option name="left" path="Spaces" hit="'try' left brace" />
    <option name="try" path="Spaces" hit="'try' left brace" />
    <option name="" path="Spaces" hit="'try' parentheses" />
    <option name="parentheses" path="Spaces" hit="'try' parentheses" />
    <option name="try" path="Spaces" hit="'try' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'try' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'try' statement" />
    <option name="try" path="Wrapping and Braces" hit="'try' statement" />
    <option name="" path="Wrapping and Braces" hit="'try-with-resources'" />
    <option name="try-with-resources" path="Wrapping and Braces" hit="'try-with-resources'" />
    <option name="" path="Spaces" hit="'while' keyword" />
    <option name="keyword" path="Spaces" hit="'while' keyword" />
    <option name="while" path="Spaces" hit="'while' keyword" />
    <option name="" path="Spaces" hit="'while' left brace" />
    <option name="brace" path="Spaces" hit="'while' left brace" />
    <option name="left" path="Spaces" hit="'while' left brace" />
    <option name="while" path="Spaces" hit="'while' left brace" />
    <option name="" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="while" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="" path="Spaces" hit="'while' parentheses" />
    <option name="parentheses" path="Spaces" hit="'while' parentheses" />
    <option name="while" path="Spaces" hit="'while' parentheses" />
    <option name="" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="while" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="+" path="Spaces" hit="Additive operators (+, -)" />
    <option name="-" path="Spaces" hit="Additive operators (+, -)" />
    <option name="additive" path="Spaces" hit="Additive operators (+, -)" />
    <option name="operators" path="Spaces" hit="Additive operators (+, -)" />
    <option name="after" path="Spaces" hit="After ':'" />
    <option name="after" path="Spaces" hit="After '?'" />
    <option name="after" path="Spaces" hit="After 'for' semicolon" />
    <option name="for" path="Spaces" hit="After 'for' semicolon" />
    <option name="semicolon" path="Spaces" hit="After 'for' semicolon" />
    <option name="after" path="Spaces" hit="After closing angle bracket" />
    <option name="angle" path="Spaces" hit="After closing angle bracket" />
    <option name="bracket" path="Spaces" hit="After closing angle bracket" />
    <option name="closing" path="Spaces" hit="After closing angle bracket" />
    <option name="after" path="Spaces" hit="After comma" />
    <option name="comma" path="Spaces" hit="After comma" />
    <option name="after" path="JavaDoc" hit="After description" />
    <option name="description" path="JavaDoc" hit="After description" />
    <option name="after" path="JavaDoc" hit="After parameter descriptions" />
    <option name="descriptions" path="JavaDoc" hit="After parameter descriptions" />
    <option name="parameter" path="JavaDoc" hit="After parameter descriptions" />
    <option name="after" path="JavaDoc" hit="After return tag" />
    <option name="return" path="JavaDoc" hit="After return tag" />
    <option name="tag" path="JavaDoc" hit="After return tag" />
    <option name="after" path="Spaces" hit="After type cast" />
    <option name="cast" path="Spaces" hit="After type cast" />
    <option name="type" path="Spaces" hit="After type cast" />
    <option name="align" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="method" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="start" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="throws" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="to" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="align" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="assignments" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="fields" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="align" path="JavaDoc" hit="Align parameter descriptions" />
    <option name="descriptions" path="JavaDoc" hit="Align parameter descriptions" />
    <option name="parameter" path="JavaDoc" hit="Align parameter descriptions" />
    <option name="align" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="multiline" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="parenthesised" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="when" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="align" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="methods" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="simple" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="align" path="JavaDoc" hit="Align thrown exception descriptions" />
    <option name="descriptions" path="JavaDoc" hit="Align thrown exception descriptions" />
    <option name="exception" path="JavaDoc" hit="Align thrown exception descriptions" />
    <option name="thrown" path="JavaDoc" hit="Align thrown exception descriptions" />
    <option name="align" path="Wrapping and Braces" hit="Align types in multi-catch" />
    <option name="in" path="Wrapping and Braces" hit="Align types in multi-catch" />
    <option name="multi-catch" path="Wrapping and Braces" hit="Align types in multi-catch" />
    <option name="types" path="Wrapping and Braces" hit="Align types in multi-catch" />
    <option name="align" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="variables" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="multiline" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="when" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="alignment" path="JavaDoc" hit="Alignment" />
    <option name="angle" path="Spaces" hit="Angle brackets" />
    <option name="brackets" path="Spaces" hit="Angle brackets" />
    <option name="annotation" path="Spaces" hit="Annotation array initializer left brace" />
    <option name="array" path="Spaces" hit="Annotation array initializer left brace" />
    <option name="brace" path="Spaces" hit="Annotation array initializer left brace" />
    <option name="initializer" path="Spaces" hit="Annotation array initializer left brace" />
    <option name="left" path="Spaces" hit="Annotation array initializer left brace" />
    <option name="annotation" path="Wrapping and Braces" hit="Annotation parameters" />
    <option name="parameters" path="Wrapping and Braces" hit="Annotation parameters" />
    <option name="annotation" path="Spaces" hit="Annotation parentheses" />
    <option name="parentheses" path="Spaces" hit="Annotation parentheses" />
    <option name="annotation" path="Spaces" hit="Around '=' in annotation value pair" />
    <option name="around" path="Spaces" hit="Around '=' in annotation value pair" />
    <option name="in" path="Spaces" hit="Around '=' in annotation value pair" />
    <option name="pair" path="Spaces" hit="Around '=' in annotation value pair" />
    <option name="value" path="Spaces" hit="Around '=' in annotation value pair" />
    <option name="around" path="Spaces" hit="Around operators" />
    <option name="operators" path="Spaces" hit="Around operators" />
    <option name="around" path="Spaces" hit="Around type bounds" />
    <option name="bounds" path="Spaces" hit="Around type bounds" />
    <option name="type" path="Spaces" hit="Around type bounds" />
    <option name="array" path="Wrapping and Braces" hit="Array initializer" />
    <option name="initializer" path="Wrapping and Braces" hit="Array initializer" />
    <option name="array" path="Spaces" hit="Array initializer braces" />
    <option name="braces" path="Spaces" hit="Array initializer braces" />
    <option name="initializer" path="Spaces" hit="Array initializer braces" />
    <option name="array" path="Spaces" hit="Array initializer left brace" />
    <option name="brace" path="Spaces" hit="Array initializer left brace" />
    <option name="initializer" path="Spaces" hit="Array initializer left brace" />
    <option name="left" path="Spaces" hit="Array initializer left brace" />
    <option name="assert" path="Wrapping and Braces" hit="Assert statement" />
    <option name="statement" path="Wrapping and Braces" hit="Assert statement" />
    <option name="+" path="Spaces" hit="Assignment operators (=, +=, ...)" />
    <option name="assignment" path="Spaces" hit="Assignment operators (=, +=, ...)" />
    <option name="operators" path="Spaces" hit="Assignment operators (=, +=, ...)" />
    <option name="assignment" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="line" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="next" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="on" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="sign" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="assignment" path="Wrapping and Braces" hit="Assignment statement" />
    <option name="statement" path="Wrapping and Braces" hit="Assignment statement" />
    <option name="before" path="Spaces" hit="Before ':'" />
    <option name="before" path="Spaces" hit="Before '?'" />
    <option name="before" path="Spaces" hit="Before 'for' semicolon" />
    <option name="for" path="Spaces" hit="Before 'for' semicolon" />
    <option name="semicolon" path="Spaces" hit="Before 'for' semicolon" />
    <option name="before" path="Spaces" hit="Before colon in foreach" />
    <option name="colon" path="Spaces" hit="Before colon in foreach" />
    <option name="foreach" path="Spaces" hit="Before colon in foreach" />
    <option name="in" path="Spaces" hit="Before colon in foreach" />
    <option name="before" path="Spaces" hit="Before comma" />
    <option name="comma" path="Spaces" hit="Before comma" />
    <option name="before" path="Spaces" hit="Before keywords" />
    <option name="keywords" path="Spaces" hit="Before keywords" />
    <option name="before" path="Spaces" hit="Before left brace" />
    <option name="brace" path="Spaces" hit="Before left brace" />
    <option name="left" path="Spaces" hit="Before left brace" />
    <option name="angle" path="Spaces" hit="Before opening angle bracket" />
    <option name="before" path="Spaces" hit="Before opening angle bracket" />
    <option name="bracket" path="Spaces" hit="Before opening angle bracket" />
    <option name="opening" path="Spaces" hit="Before opening angle bracket" />
    <option name="before" path="Spaces" hit="Before parentheses" />
    <option name="parentheses" path="Spaces" hit="Before parentheses" />
    <option name="binary" path="Wrapping and Braces" hit="Binary expressions" />
    <option name="expressions" path="Wrapping and Braces" hit="Binary expressions" />
    <option name="bitwise" path="Spaces" hit="Bitwise operators (&amp;, |, ^)" />
    <option name="operators" path="Spaces" hit="Bitwise operators (&amp;, |, ^)" />
    <option name="blank" path="JavaDoc" hit="Blank lines" />
    <option name="lines" path="JavaDoc" hit="Blank lines" />
    <option name="braces" path="Wrapping and Braces" hit="Braces placement" />
    <option name="placement" path="Wrapping and Braces" hit="Braces placement" />
    <option name="brackets" path="Spaces" hit="Brackets" />
    <option name="builder" path="Wrapping and Braces" hit="Builder methods" />
    <option name="methods" path="Wrapping and Braces" hit="Builder methods" />
    <option name="calls" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="chained" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="method" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="annotations" path="Wrapping and Braces" hit="Class annotations" />
    <option name="class" path="Wrapping and Braces" hit="Class annotations" />
    <option name="brace" path="Spaces" hit="Class left brace" />
    <option name="class" path="Spaces" hit="Class left brace" />
    <option name="left" path="Spaces" hit="Class left brace" />
    <option name="braces" path="Spaces" hit="Code braces" />
    <option name="code" path="Spaces" hit="Code braces" />
    <option name="at" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="column" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="comment" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="first" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="comments" path="Wrapping and Braces" hit="Comments" />
    <option name="control" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="in" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="line" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="one" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="statement" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="deconstruction" path="Spaces" hit="Deconstruction list" />
    <option name="list" path="Spaces" hit="Deconstruction list" />
    <option name="deconstruction" path="Wrapping and Braces" hit="Deconstruction patterns" />
    <option name="patterns" path="Wrapping and Braces" hit="Deconstruction patterns" />
    <option name="after" path="Wrapping and Braces" hit="Do not wrap after single annotation" />
    <option name="annotation" path="Wrapping and Braces" hit="Do not wrap after single annotation" />
    <option name="do" path="Wrapping and Braces" hit="Do not wrap after single annotation" />
    <option name="not" path="Wrapping and Braces" hit="Do not wrap after single annotation" />
    <option name="single" path="Wrapping and Braces" hit="Do not wrap after single annotation" />
    <option name="wrap" path="Wrapping and Braces" hit="Do not wrap after single annotation" />
    <option name="comments" path="JavaDoc" hit="Do not wrap one line comments" />
    <option name="do" path="JavaDoc" hit="Do not wrap one line comments" />
    <option name="line" path="JavaDoc" hit="Do not wrap one line comments" />
    <option name="not" path="JavaDoc" hit="Do not wrap one line comments" />
    <option name="one" path="JavaDoc" hit="Do not wrap one line comments" />
    <option name="wrap" path="JavaDoc" hit="Do not wrap one line comments" />
    <option name="a" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="case" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="each" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="line" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="on" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="separate" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="array" path="Spaces" hit="Empty array initializer braces" />
    <option name="braces" path="Spaces" hit="Empty array initializer braces" />
    <option name="empty" path="Spaces" hit="Empty array initializer braces" />
    <option name="initializer" path="Spaces" hit="Empty array initializer braces" />
    <option name="call" path="Spaces" hit="Empty method call parentheses" />
    <option name="empty" path="Spaces" hit="Empty method call parentheses" />
    <option name="method" path="Spaces" hit="Empty method call parentheses" />
    <option name="parentheses" path="Spaces" hit="Empty method call parentheses" />
    <option name="declaration" path="Spaces" hit="Empty method declaration parentheses" />
    <option name="empty" path="Spaces" hit="Empty method declaration parentheses" />
    <option name="method" path="Spaces" hit="Empty method declaration parentheses" />
    <option name="parentheses" path="Spaces" hit="Empty method declaration parentheses" />
    <option name="asterisks" path="JavaDoc" hit="Enable leading asterisks" />
    <option name="enable" path="JavaDoc" hit="Enable leading asterisks" />
    <option name="leading" path="JavaDoc" hit="Enable leading asterisks" />
    <option name="ensure" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="exceeded" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="is" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="margin" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="not" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="right" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="constants" path="Wrapping and Braces" hit="Enum constants" />
    <option name="enum" path="Wrapping and Braces" hit="Enum constants" />
    <option name="annotations" path="Wrapping and Braces" hit="Enum field annotations" />
    <option name="enum" path="Wrapping and Braces" hit="Enum field annotations" />
    <option name="field" path="Wrapping and Braces" hit="Enum field annotations" />
    <option name="equality" path="Spaces" hit="Equality operators (==, !=)" />
    <option name="operators" path="Spaces" hit="Equality operators (==, !=)" />
    <option name="extends" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="implements" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="keyword" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="permits" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="extends" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="implements" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="list" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="permits" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="annotations" path="Wrapping and Braces" hit="Field annotations" />
    <option name="field" path="Wrapping and Braces" hit="Field annotations" />
    <option name="braces" path="Wrapping and Braces" hit="Force braces" />
    <option name="force" path="Wrapping and Braces" hit="Force braces" />
    <option name="empty" path="JavaDoc" hit="Generate &quot;&lt;p&gt;&quot; on empty lines" />
    <option name="generate" path="JavaDoc" hit="Generate &quot;&lt;p&gt;&quot; on empty lines" />
    <option name="lines" path="JavaDoc" hit="Generate &quot;&lt;p&gt;&quot; on empty lines" />
    <option name="on" path="JavaDoc" hit="Generate &quot;&lt;p&gt;&quot; on empty lines" />
    <option name="p" path="JavaDoc" hit="Generate &quot;&lt;p&gt;&quot; on empty lines" />
    <option name="declarations" path="Wrapping and Braces" hit="Group declarations" />
    <option name="group" path="Wrapping and Braces" hit="Group declarations" />
    <option name="grouping" path="Spaces" hit="Grouping parentheses" />
    <option name="parentheses" path="Spaces" hit="Grouping parentheses" />
    <option name="at" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="hard" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="wrap" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="class" path="Wrapping and Braces" hit="In class declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In class declaration" />
    <option name="in" path="Wrapping and Braces" hit="In class declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="in" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="lambda" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In method declaration" />
    <option name="in" path="Wrapping and Braces" hit="In method declaration" />
    <option name="method" path="Wrapping and Braces" hit="In method declaration" />
    <option name="in" path="Spaces" hit="In ternary operator (?:)" />
    <option name="operator" path="Spaces" hit="In ternary operator (?:)" />
    <option name="ternary" path="Spaces" hit="In ternary operator (?:)" />
    <option name="break" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="case" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="from" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="branches" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="case" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="continuation" path="JavaDoc" hit="Indent continuation lines" />
    <option name="indent" path="JavaDoc" hit="Indent continuation lines" />
    <option name="lines" path="JavaDoc" hit="Indent continuation lines" />
    <option name="braces" path="Spaces" hit="Inside one line enum braces" />
    <option name="enum" path="Spaces" hit="Inside one line enum braces" />
    <option name="inside" path="Spaces" hit="Inside one line enum braces" />
    <option name="line" path="Spaces" hit="Inside one line enum braces" />
    <option name="one" path="Spaces" hit="Inside one line enum braces" />
    <option name="invalid" path="JavaDoc" hit="Invalid tags" />
    <option name="tags" path="JavaDoc" hit="Invalid tags" />
    <option name="builder" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="indents" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="keep" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="methods" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="empty" path="JavaDoc" hit="Keep empty @param tags" />
    <option name="keep" path="JavaDoc" hit="Keep empty @param tags" />
    <option name="param" path="JavaDoc" hit="Keep empty @param tags" />
    <option name="tags" path="JavaDoc" hit="Keep empty @param tags" />
    <option name="empty" path="JavaDoc" hit="Keep empty @return tags" />
    <option name="keep" path="JavaDoc" hit="Keep empty @return tags" />
    <option name="return" path="JavaDoc" hit="Keep empty @return tags" />
    <option name="tags" path="JavaDoc" hit="Keep empty @return tags" />
    <option name="empty" path="JavaDoc" hit="Keep empty @throws tags" />
    <option name="keep" path="JavaDoc" hit="Keep empty @throws tags" />
    <option name="tags" path="JavaDoc" hit="Keep empty @throws tags" />
    <option name="throws" path="JavaDoc" hit="Keep empty @throws tags" />
    <option name="empty" path="JavaDoc" hit="Keep empty lines" />
    <option name="keep" path="JavaDoc" hit="Keep empty lines" />
    <option name="lines" path="JavaDoc" hit="Keep empty lines" />
    <option name="invalid" path="JavaDoc" hit="Keep invalid tags" />
    <option name="keep" path="JavaDoc" hit="Keep invalid tags" />
    <option name="tags" path="JavaDoc" hit="Keep invalid tags" />
    <option name="keep" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="reformatting" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="when" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="arrow" path="Spaces" hit="Lambda arrow" />
    <option name="lambda" path="Spaces" hit="Lambda arrow" />
    <option name="breaks" path="Wrapping and Braces" hit="Line breaks" />
    <option name="line" path="Wrapping and Braces" hit="Line breaks" />
    <option name="annotations" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="local" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="variable" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="logical" path="Spaces" hit="Logical operators (&amp;&amp;, ||)" />
    <option name="operators" path="Spaces" hit="Logical operators (&amp;&amp;, ||)" />
    <option name="annotations" path="Wrapping and Braces" hit="Method annotations" />
    <option name="method" path="Wrapping and Braces" hit="Method annotations" />
    <option name="arguments" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="call" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="method" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="call" path="Spaces" hit="Method call parentheses" />
    <option name="method" path="Spaces" hit="Method call parentheses" />
    <option name="parentheses" path="Spaces" hit="Method call parentheses" />
    <option name="declaration" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="method" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="parameters" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="declaration" path="Spaces" hit="Method declaration parentheses" />
    <option name="method" path="Spaces" hit="Method declaration parentheses" />
    <option name="parentheses" path="Spaces" hit="Method declaration parentheses" />
    <option name="brace" path="Spaces" hit="Method left brace" />
    <option name="left" path="Spaces" hit="Method left brace" />
    <option name="method" path="Spaces" hit="Method left brace" />
    <option name="method" path="Wrapping and Braces" hit="Method parentheses" />
    <option name="parentheses" path="Wrapping and Braces" hit="Method parentheses" />
    <option name="colon" path="Spaces" hit="Method reference double colon" />
    <option name="double" path="Spaces" hit="Method reference double colon" />
    <option name="method" path="Spaces" hit="Method reference double colon" />
    <option name="reference" path="Spaces" hit="Method reference double colon" />
    <option name="list" path="Wrapping and Braces" hit="Modifier list" />
    <option name="modifier" path="Wrapping and Braces" hit="Modifier list" />
    <option name="line" path="Wrapping and Braces" hit="Move ';' to the new line" />
    <option name="move" path="Wrapping and Braces" hit="Move ';' to the new line" />
    <option name="new" path="Wrapping and Braces" hit="Move ';' to the new line" />
    <option name="the" path="Wrapping and Braces" hit="Move ';' to the new line" />
    <option name="to" path="Wrapping and Braces" hit="Move ';' to the new line" />
    <option name="expressions" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="in" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="line" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="multiple" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="one" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="multiplicative" path="Spaces" hit="Multiplicative operators (*, /, %)" />
    <option name="operators" path="Spaces" hit="Multiplicative operators (*, /, %)" />
    <option name="after" path="Wrapping and Braces" hit="New line after '('" />
    <option name="line" path="Wrapping and Braces" hit="New line after '('" />
    <option name="new" path="Wrapping and Braces" hit="New line after '('" />
    <option name="after" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="line" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="new" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="body" path="Wrapping and Braces" hit="New line when body is presented" />
    <option name="is" path="Wrapping and Braces" hit="New line when body is presented" />
    <option name="line" path="Wrapping and Braces" hit="New line when body is presented" />
    <option name="new" path="Wrapping and Braces" hit="New line when body is presented" />
    <option name="presented" path="Wrapping and Braces" hit="New line when body is presented" />
    <option name="when" path="Wrapping and Braces" hit="New line when body is presented" />
    <option name="line" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="next" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="on" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="operation" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="sign" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="other" path="Wrapping and Braces" hit="Other" />
    <option name="annotations" path="Wrapping and Braces" hit="Parameter annotations" />
    <option name="parameter" path="Wrapping and Braces" hit="Parameter annotations" />
    <option name="descriptions" path="JavaDoc" hit="Parameter descriptions on new line" />
    <option name="line" path="JavaDoc" hit="Parameter descriptions on new line" />
    <option name="new" path="JavaDoc" hit="Parameter descriptions on new line" />
    <option name="on" path="JavaDoc" hit="Parameter descriptions on new line" />
    <option name="parameter" path="JavaDoc" hit="Parameter descriptions on new line" />
    <option name="line" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="new" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="on" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="place" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="line" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="new" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="on" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="place" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="feeds" path="JavaDoc" hit="Preserve line feeds" />
    <option name="line" path="JavaDoc" hit="Preserve line feeds" />
    <option name="preserve" path="JavaDoc" hit="Preserve line feeds" />
    <option name="components" path="Wrapping and Braces" hit="Record components" />
    <option name="record" path="Wrapping and Braces" hit="Record components" />
    <option name="header" path="Spaces" hit="Record header" />
    <option name="record" path="Spaces" hit="Record header" />
    <option name="operators" path="Spaces" hit="Relational operators (&lt;, &gt;, &lt;=, &gt;=)" />
    <option name="relational" path="Spaces" hit="Relational operators (&lt;, &gt;, &lt;=, &gt;=)" />
    <option name="operators" path="Spaces" hit="Shift operators (&lt;&lt;, &gt;&gt;, &gt;&gt;&gt;)" />
    <option name="shift" path="Spaces" hit="Shift operators (&lt;&lt;, &gt;&gt;, &gt;&gt;&gt;)" />
    <option name="blocks" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="classes" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="lambdas" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="methods" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="else" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="if" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="special" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="treatment" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="call" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="chain" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="over" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="priority" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="take" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="wrapping" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="operation" path="Wrapping and Braces" hit="Ternary operation" />
    <option name="ternary" path="Wrapping and Braces" hit="Ternary operation" />
    <option name="blocks" path="Wrapping and Braces" hit="Text blocks" />
    <option name="text" path="Wrapping and Braces" hit="Text blocks" />
    <option name="keyword" path="Wrapping and Braces" hit="Throws keyword" />
    <option name="throws" path="Wrapping and Braces" hit="Throws keyword" />
    <option name="list" path="Wrapping and Braces" hit="Throws list" />
    <option name="throws" path="Wrapping and Braces" hit="Throws list" />
    <option name="arguments" path="Spaces" hit="Type arguments" />
    <option name="type" path="Spaces" hit="Type arguments" />
    <option name="cast" path="Spaces" hit="Type cast parentheses" />
    <option name="parentheses" path="Spaces" hit="Type cast parentheses" />
    <option name="type" path="Spaces" hit="Type cast parentheses" />
    <option name="parameters" path="Spaces" hit="Type parameters" />
    <option name="type" path="Spaces" hit="Type parameters" />
    <option name="in" path="Wrapping and Braces" hit="Types in multi-catch" />
    <option name="multi-catch" path="Wrapping and Braces" hit="Types in multi-catch" />
    <option name="types" path="Wrapping and Braces" hit="Types in multi-catch" />
    <option name="+" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="++" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="-" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="--" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="operators" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="unary" path="Spaces" hit="Unary operators (!, -, +, ++, --)" />
    <option name="exception" path="JavaDoc" hit="Use @throws rather than @exception" />
    <option name="rather" path="JavaDoc" hit="Use @throws rather than @exception" />
    <option name="than" path="JavaDoc" hit="Use @throws rather than @exception" />
    <option name="throws" path="JavaDoc" hit="Use @throws rather than @exception" />
    <option name="use" path="JavaDoc" hit="Use @throws rather than @exception" />
    <option name="guides" path="Wrapping and Braces" hit="Visual guides" />
    <option name="visual" path="Wrapping and Braces" hit="Visual guides" />
    <option name="within" path="Spaces" hit="Within" />
    <option name="after" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="list" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="modifier" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="at" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="margin" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="right" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="call" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="first" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="on" path="Wrapping and Braces" hit="Wrap on typing" />
    <option name="typing" path="Wrapping and Braces" hit="Wrap on typing" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap on typing" />
  </configurable>
  <configurable id="copyright.filetypes.JAVA" configurable_name="Java">
    <option name="a" hit="Add blank line after" />
    <option name="after" hit="Add blank line after" />
    <option name="blank" hit="Add blank line after" />
    <option name="dd" hit="Add blank line after" />
    <option name="line" hit="Add blank line after" />
    <option name="add" hit="Add blank line before" />
    <option name="before" hit="Add blank line before" />
    <option name="blank" hit="Add blank line before" />
    <option name="line" hit="Add blank line before" />
    <option name="afte" hit="After other comments" />
    <option name="comments" hit="After other comments" />
    <option name="other" hit="After other comments" />
    <option name="r" hit="After other comments" />
    <option name="before" hit="Before class" />
    <option name="class" hit="Before class" />
    <option name="before" hit="Before imports" />
    <option name="imports" hit="Before imports" />
    <option name="bef" hit="Before other comments" />
    <option name="comments" hit="Before other comments" />
    <option name="ore" hit="Before other comments" />
    <option name="other" hit="Before other comments" />
    <option name="before" hit="Before package" />
    <option name="package" hit="Before package" />
    <option name="borders" hit="Borders" />
    <option name="box" hit="Box" />
    <option name="comment" hit="Comment Type" />
    <option name="type" hit="Comment Type" />
    <option name="java" hit="Java" />
    <option name="length" hit="Length:" />
    <option name="file" hit="Location in File" />
    <option name="in" hit="Location in File" />
    <option name="location" hit="Location in File" />
    <option name="copyright" hit="No copyright" />
    <option name="no" hit="No copyright" />
    <option name="each" hit="Prefix each line" />
    <option name="line" hit="Prefix each line" />
    <option name="prefix" hit="Prefix each line" />
    <option name="location" hit="Relative Location" />
    <option name="relative" hit="Relative Location" />
    <option name="after" hit="Separator after" />
    <option name="separator" hit="Separator after" />
    <option name="before" hit="Separator before" />
    <option name="separator" hit="Separator before" />
    <option name="separator" hit="Separator:" />
    <option name="block" hit="Use block comment" />
    <option name="comment" hit="Use block comment" />
    <option name="use" hit="Use block comment" />
    <option name="custom" hit="Use custom formatting options" />
    <option name="formatting" hit="Use custom formatting options" />
    <option name="options" hit="Use custom formatting options" />
    <option name="use" hit="Use custom formatting options" />
    <option name="default" hit="Use default settings" />
    <option name="settings" hit="Use default settings" />
    <option name="use" hit="Use default settings" />
    <option name="comment" hit="Use line comment" />
    <option name="line" hit="Use line comment" />
    <option name="use" hit="Use line comment" />
  </configurable>
  <configurable id="copyright.filetypes.SPI" configurable_name="SPI">
    <option name="a" hit="Add blank line after" />
    <option name="after" hit="Add blank line after" />
    <option name="blank" hit="Add blank line after" />
    <option name="dd" hit="Add blank line after" />
    <option name="line" hit="Add blank line after" />
    <option name="add" hit="Add blank line before" />
    <option name="before" hit="Add blank line before" />
    <option name="blank" hit="Add blank line before" />
    <option name="line" hit="Add blank line before" />
    <option name="afte" hit="After other comments" />
    <option name="comments" hit="After other comments" />
    <option name="other" hit="After other comments" />
    <option name="r" hit="After other comments" />
    <option name="bef" hit="Before other comments" />
    <option name="comments" hit="Before other comments" />
    <option name="ore" hit="Before other comments" />
    <option name="other" hit="Before other comments" />
    <option name="borders" hit="Borders" />
    <option name="box" hit="Box" />
    <option name="comment" hit="Comment Type" />
    <option name="type" hit="Comment Type" />
    <option name="length" hit="Length:" />
    <option name="copyright" hit="No copyright" />
    <option name="no" hit="No copyright" />
    <option name="each" hit="Prefix each line" />
    <option name="line" hit="Prefix each line" />
    <option name="prefix" hit="Prefix each line" />
    <option name="location" hit="Relative Location" />
    <option name="relative" hit="Relative Location" />
    <option name="spi" hit="SPI" />
    <option name="after" hit="Separator after" />
    <option name="separator" hit="Separator after" />
    <option name="before" hit="Separator before" />
    <option name="separator" hit="Separator before" />
    <option name="separator" hit="Separator:" />
    <option name="block" hit="Use block comment" />
    <option name="comment" hit="Use block comment" />
    <option name="use" hit="Use block comment" />
    <option name="custom" hit="Use custom formatting options" />
    <option name="formatting" hit="Use custom formatting options" />
    <option name="options" hit="Use custom formatting options" />
    <option name="use" hit="Use custom formatting options" />
    <option name="default" hit="Use default settings" />
    <option name="settings" hit="Use default settings" />
    <option name="use" hit="Use default settings" />
    <option name="comment" hit="Use line comment" />
    <option name="line" hit="Use line comment" />
    <option name="use" hit="Use line comment" />
  </configurable>
  <configurable id="project.propCompiler" configurable_name="Compiler">
    <option name="" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="a" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="accepted" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="and" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="any" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="directories" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="exactly" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="more" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="name" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="negate" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="number" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="of" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="one" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="or" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="path" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="pattern" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="patterns" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="restrict" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="roots" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="separate" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="separator" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="source" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="specified" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="symbol" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="symbols" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="the" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="to" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="use" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="wildcards" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="with" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="zero" hit=" Use ; to separate patterns and ! to negate a pattern. Accepted wildcards: ?   exactly one symbol; *   zero or more symbols; /   path separator; /**/   any number of directories;  :    restrict to source roots with the specified name" />
    <option name="" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="are" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="be" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="clear" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="cleared" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="contents" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="directories" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="directory" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="enabled" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="entire" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="generated" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="if" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="is" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="of" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="on" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="option" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="output" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="rebuild" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="sources" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="stored" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="the" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="warning" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="where" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="will" hit=" WARNING! If option 'Clear output directory on rebuild' is enabled, the entire contents of directories where generated sources are stored WILL BE CLEARED on rebuild." />
    <option name="" hit="(may require larger heap size)" />
    <option name="heap" hit="(may require larger heap size)" />
    <option name="larger" hit="(may require larger heap size)" />
    <option name="may" hit="(may require larger heap size)" />
    <option name="require" hit="(may require larger heap size)" />
    <option name="size" hit="(may require larger heap size)" />
    <option name="" hit="(only works while not running / debugging)" />
    <option name="debugging" hit="(only works while not running / debugging)" />
    <option name="not" hit="(only works while not running / debugging)" />
    <option name="only" hit="(only works while not running / debugging)" />
    <option name="running" hit="(only works while not running / debugging)" />
    <option name="while" hit="(only works while not running / debugging)" />
    <option name="works" hit="(only works while not running / debugging)" />
    <option name="add" hit="Add runtime assertions for notnull-annotated methods and parameters" />
    <option name="and" hit="Add runtime assertions for notnull-annotated methods and parameters" />
    <option name="assertions" hit="Add runtime assertions for notnull-annotated methods and parameters" />
    <option name="for" hit="Add runtime assertions for notnull-annotated methods and parameters" />
    <option name="methods" hit="Add runtime assertions for notnull-annotated methods and parameters" />
    <option name="notnull-annotated" hit="Add runtime assertions for notnull-annotated methods and parameters" />
    <option name="parameters" hit="Add runtime assertions for notnull-annotated methods and parameters" />
    <option name="runtime" hit="Add runtime assertions for notnull-annotated methods and parameters" />
    <option name="automatically" hit="Automatically show first error in editor" />
    <option name="editor" hit="Automatically show first error in editor" />
    <option name="error" hit="Automatically show first error in editor" />
    <option name="first" hit="Automatically show first error in editor" />
    <option name="in" hit="Automatically show first error in editor" />
    <option name="show" hit="Automatically show first error in editor" />
    <option name="automatically" hit="Build project automatically" />
    <option name="build" hit="Build project automatically" />
    <option name="project" hit="Build project automatically" />
    <option name="clear" hit="Clear output directory on rebuild" />
    <option name="directory" hit="Clear output directory on rebuild" />
    <option name="on" hit="Clear output directory on rebuild" />
    <option name="output" hit="Clear output directory on rebuild" />
    <option name="rebuild" hit="Clear output directory on rebuild" />
    <option name="compile" hit="Compile independent modules in parallel" />
    <option name="in" hit="Compile independent modules in parallel" />
    <option name="independent" hit="Compile independent modules in parallel" />
    <option name="modules" hit="Compile independent modules in parallel" />
    <option name="parallel" hit="Compile independent modules in parallel" />
    <option name="compiler" hit="Compiler" />
    <option name="annotations" hit="Configure annotations..." />
    <option name="configure" hit="Configure annotations..." />
    <option name="build" hit="Display notification on build completion" />
    <option name="completion" hit="Display notification on build completion" />
    <option name="display" hit="Display notification on build completion" />
    <option name="notification" hit="Display notification on build completion" />
    <option name="on" hit="Display notification on build completion" />
    <option name="change" hit="Rebuild module on dependency change" />
    <option name="dependency" hit="Rebuild module on dependency change" />
    <option name="module" hit="Rebuild module on dependency change" />
    <option name="on" hit="Rebuild module on dependency change" />
    <option name="rebuild" hit="Rebuild module on dependency change" />
    <option name="patterns" hit="Resource patterns:" />
    <option name="resource" hit="Resource patterns:" />
    <option name="build" hit="Shared build process VM options:" />
    <option name="options" hit="Shared build process VM options:" />
    <option name="process" hit="Shared build process VM options:" />
    <option name="shared" hit="Shared build process VM options:" />
    <option name="vm" hit="Shared build process VM options:" />
    <option name="build" hit="Shared build process heap size (Mbytes):" />
    <option name="heap" hit="Shared build process heap size (Mbytes):" />
    <option name="mbytes" hit="Shared build process heap size (Mbytes):" />
    <option name="process" hit="Shared build process heap size (Mbytes):" />
    <option name="shared" hit="Shared build process heap size (Mbytes):" />
    <option name="size" hit="Shared build process heap size (Mbytes):" />
    <option name="build" hit="User-local build process VM options (overrides Shared options):" />
    <option name="options" hit="User-local build process VM options (overrides Shared options):" />
    <option name="overrides" hit="User-local build process VM options (overrides Shared options):" />
    <option name="process" hit="User-local build process VM options (overrides Shared options):" />
    <option name="shared" hit="User-local build process VM options (overrides Shared options):" />
    <option name="user-local" hit="User-local build process VM options (overrides Shared options):" />
    <option name="vm" hit="User-local build process VM options (overrides Shared options):" />
    <option name="build" hit="User-local build process heap size (Mbytes) (overrides Shared size):" />
    <option name="heap" hit="User-local build process heap size (Mbytes) (overrides Shared size):" />
    <option name="mbytes" hit="User-local build process heap size (Mbytes) (overrides Shared size):" />
    <option name="overrides" hit="User-local build process heap size (Mbytes) (overrides Shared size):" />
    <option name="process" hit="User-local build process heap size (Mbytes) (overrides Shared size):" />
    <option name="shared" hit="User-local build process heap size (Mbytes) (overrides Shared size):" />
    <option name="size" hit="User-local build process heap size (Mbytes) (overrides Shared size):" />
    <option name="user-local" hit="User-local build process heap size (Mbytes) (overrides Shared size):" />
  </configurable>
  <configurable id="reference.projectsettings.compiler.excludes" configurable_name="Excludes">
    <option name="excludes" hit="Excludes" />
  </configurable>
  <configurable id="reference.projectsettings.compiler.javacompiler" configurable_name="Java Compiler">
    <option name="1" hit="1.1" />
    <option name="1" hit="1.2" />
    <option name="2" hit="1.2" />
    <option name="1" hit="1.3" />
    <option name="3" hit="1.3" />
    <option name="1" hit="1.4" />
    <option name="4" hit="1.4" />
    <option name="10" hit="10" />
    <option name="11" hit="11" />
    <option name="12" hit="12" />
    <option name="13" hit="13" />
    <option name="14" hit="14" />
    <option name="15" hit="15" />
    <option name="16" hit="16" />
    <option name="17" hit="17" />
    <option name="18" hit="18" />
    <option name="19" hit="19" />
    <option name="20" hit="20" />
    <option name="21" hit="21" />
    <option name="22" hit="22" />
    <option name="5" hit="5" />
    <option name="6" hit="6" />
    <option name="7" hit="7" />
    <option name="8" hit="8" />
    <option name="9" hit="9" />
    <option name="eclipse" hit="Eclipse" />
    <option name="groovy-eclipse" hit="Groovy-Eclipse" />
    <option name="compiler" hit="Java Compiler" />
    <option name="java" hit="Java Compiler" />
    <option name="javac" hit="Javac" />
    <option name="bytecode" hit="Per-module bytecode version:" />
    <option name="per-module" hit="Per-module bytecode version:" />
    <option name="version" hit="Per-module bytecode version:" />
    <option name="bytecode" hit="Project bytecode version:" />
    <option name="project" hit="Project bytecode version:" />
    <option name="version" hit="Project bytecode version:" />
    <option name="--release" hit="Use '--release' option for cross-compilation (Java 9 and later)" />
    <option name="9" hit="Use '--release' option for cross-compilation (Java 9 and later)" />
    <option name="and" hit="Use '--release' option for cross-compilation (Java 9 and later)" />
    <option name="cross-compilation" hit="Use '--release' option for cross-compilation (Java 9 and later)" />
    <option name="for" hit="Use '--release' option for cross-compilation (Java 9 and later)" />
    <option name="java" hit="Use '--release' option for cross-compilation (Java 9 and later)" />
    <option name="later" hit="Use '--release' option for cross-compilation (Java 9 and later)" />
    <option name="option" hit="Use '--release' option for cross-compilation (Java 9 and later)" />
    <option name="use" hit="Use '--release' option for cross-compilation (Java 9 and later)" />
    <option name="compiler" hit="Use compiler:" />
    <option name="use" hit="Use compiler:" />
  </configurable>
  <configurable id="reference.projectsettings.compiler.javacompiler" configurable_name="Java Compiler">
    <option name="" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="configurations" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="cross-platform" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="for" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="in" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="paths" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="recommended" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="additional" hit="Additional command line parameters:" />
    <option name="command" hit="Additional command line parameters:" />
    <option name="line" hit="Additional command line parameters:" />
    <option name="parameters" hit="Additional command line parameters:" />
    <option name="debugging" hit="Generate debugging info" />
    <option name="generate" hit="Generate debugging info" />
    <option name="info" hit="Generate debugging info" />
    <option name="generate" hit="Generate no warnings" />
    <option name="no" hit="Generate no warnings" />
    <option name="warnings" hit="Generate no warnings" />
    <option name="javac" hit="Javac Options" />
    <option name="options" hit="Javac Options" />
    <option name="compiler" hit="Override compiler parameters per-module:" />
    <option name="override" hit="Override compiler parameters per-module:" />
    <option name="parameters" hit="Override compiler parameters per-module:" />
    <option name="per-module" hit="Override compiler parameters per-module:" />
    <option name="deprecated" hit="Report use of deprecated features" />
    <option name="features" hit="Report use of deprecated features" />
    <option name="of" hit="Report use of deprecated features" />
    <option name="report" hit="Report use of deprecated features" />
    <option name="use" hit="Report use of deprecated features" />
    <option name="compiler" hit="Use compiler from module target JDK when possible" />
    <option name="from" hit="Use compiler from module target JDK when possible" />
    <option name="jdk" hit="Use compiler from module target JDK when possible" />
    <option name="module" hit="Use compiler from module target JDK when possible" />
    <option name="possible" hit="Use compiler from module target JDK when possible" />
    <option name="target" hit="Use compiler from module target JDK when possible" />
    <option name="use" hit="Use compiler from module target JDK when possible" />
    <option name="when" hit="Use compiler from module target JDK when possible" />
  </configurable>
  <configurable id="reference.projectsettings.compiler.javacompiler" configurable_name="Java Compiler">
    <option name="" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="configurations" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="cross-platform" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="for" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="in" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="paths" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="recommended" hit="('/' recommended in paths for cross-platform configurations)" />
    <option name="additional" hit="Additional command line parameters:" />
    <option name="command" hit="Additional command line parameters:" />
    <option name="line" hit="Additional command line parameters:" />
    <option name="parameters" hit="Additional command line parameters:" />
    <option name="eclipse" hit="Eclipse Options" />
    <option name="options" hit="Eclipse Options" />
    <option name="debugging" hit="Generate debugging info" />
    <option name="generate" hit="Generate debugging info" />
    <option name="info" hit="Generate debugging info" />
    <option name="generate" hit="Generate no warnings" />
    <option name="no" hit="Generate no warnings" />
    <option name="warnings" hit="Generate no warnings" />
    <option name="compiler" hit="Override compiler parameters per-module:" />
    <option name="override" hit="Override compiler parameters per-module:" />
    <option name="parameters" hit="Override compiler parameters per-module:" />
    <option name="per-module" hit="Override compiler parameters per-module:" />
    <option name="batch" hit="Path to ECJ batch compiler tool (leave empty to use bundled):" />
    <option name="bundled" hit="Path to ECJ batch compiler tool (leave empty to use bundled):" />
    <option name="compiler" hit="Path to ECJ batch compiler tool (leave empty to use bundled):" />
    <option name="ecj" hit="Path to ECJ batch compiler tool (leave empty to use bundled):" />
    <option name="empty" hit="Path to ECJ batch compiler tool (leave empty to use bundled):" />
    <option name="leave" hit="Path to ECJ batch compiler tool (leave empty to use bundled):" />
    <option name="path" hit="Path to ECJ batch compiler tool (leave empty to use bundled):" />
    <option name="to" hit="Path to ECJ batch compiler tool (leave empty to use bundled):" />
    <option name="tool" hit="Path to ECJ batch compiler tool (leave empty to use bundled):" />
    <option name="use" hit="Path to ECJ batch compiler tool (leave empty to use bundled):" />
    <option name="errors" hit="Proceed on errors" />
    <option name="on" hit="Proceed on errors" />
    <option name="proceed" hit="Proceed on errors" />
    <option name="deprecated" hit="Report use of deprecated features" />
    <option name="features" hit="Report use of deprecated features" />
    <option name="of" hit="Report use of deprecated features" />
    <option name="report" hit="Report use of deprecated features" />
    <option name="use" hit="Report use of deprecated features" />
  </configurable>
  <configurable id="reference.projectsettings.compiler.annotationProcessors" configurable_name="Annotation Processors">
    <option name="annotation" hit="Annotation Processors" />
    <option name="processors" hit="Annotation Processors" />
    <option name="annotation" hit="Annotation processor options:" />
    <option name="options" hit="Annotation processor options:" />
    <option name="processor" hit="Annotation processor options:" />
    <option name="annotation" hit="Annotation processors:" />
    <option name="processors" hit="Annotation processors:" />
    <option name="annotation" hit="Enable annotation processing" />
    <option name="enable" hit="Enable annotation processing" />
    <option name="processing" hit="Enable annotation processing" />
    <option name="content" hit="Module content root" />
    <option name="module" hit="Module content root" />
    <option name="root" hit="Module content root" />
    <option name="directory" hit="Module output directory" />
    <option name="module" hit="Module output directory" />
    <option name="output" hit="Module output directory" />
    <option name="classpath" hit="Obtain processors from project classpath" />
    <option name="from" hit="Obtain processors from project classpath" />
    <option name="obtain" hit="Obtain processors from project classpath" />
    <option name="processors" hit="Obtain processors from project classpath" />
    <option name="project" hit="Obtain processors from project classpath" />
    <option name="path" hit="Processor path:" />
    <option name="processor" hit="Processor path:" />
    <option name="directory" hit="Production sources directory:" />
    <option name="production" hit="Production sources directory:" />
    <option name="sources" hit="Production sources directory:" />
    <option name="-proc" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="a" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="before" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="compiling" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="in" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="java" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="mode" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="only" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="processors" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="run" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="separate" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="step" hit="Run processors in a separate step before compiling java (-proc:only mode) " />
    <option name="generated" hit="Store generated sources relative to:" />
    <option name="relative" hit="Store generated sources relative to:" />
    <option name="sources" hit="Store generated sources relative to:" />
    <option name="store" hit="Store generated sources relative to:" />
    <option name="to" hit="Store generated sources relative to:" />
    <option name="directory" hit="Test sources directory:" />
    <option name="sources" hit="Test sources directory:" />
    <option name="test" hit="Test sources directory:" />
    <option name="--processor-module-path" hit="Use --processor-module-path compiler option (for Java 9 and later)" />
    <option name="9" hit="Use --processor-module-path compiler option (for Java 9 and later)" />
    <option name="and" hit="Use --processor-module-path compiler option (for Java 9 and later)" />
    <option name="compiler" hit="Use --processor-module-path compiler option (for Java 9 and later)" />
    <option name="for" hit="Use --processor-module-path compiler option (for Java 9 and later)" />
    <option name="java" hit="Use --processor-module-path compiler option (for Java 9 and later)" />
    <option name="later" hit="Use --processor-module-path compiler option (for Java 9 and later)" />
    <option name="option" hit="Use --processor-module-path compiler option (for Java 9 and later)" />
    <option name="use" hit="Use --processor-module-path compiler option (for Java 9 and later)" />
  </configurable>
  <configurable id="project.validation" configurable_name="Validation">
    <option name="exclude" hit="Exclude From Validation:" />
    <option name="from" hit="Exclude From Validation:" />
    <option name="validation" hit="Exclude From Validation:" />
    <option name="build" hit="Validate on build" />
    <option name="on" hit="Validate on build" />
    <option name="validate" hit="Validate on build" />
    <option name="validation" hit="Validation" />
    <option name="validators" hit="Validators:" />
  </configurable>
  <configurable id="reference.projectsettings.compiler.rmicompiler" configurable_name="RMI Compiler">
    <option name="additional" hit="Additional command line parameters:" />
    <option name="command" hit="Additional command line parameters:" />
    <option name="line" hit="Additional command line parameters:" />
    <option name="parameters" hit="Additional command line parameters:" />
    <option name="enable" hit="Enable RMI stubs generation" />
    <option name="generation" hit="Enable RMI stubs generation" />
    <option name="rmi" hit="Enable RMI stubs generation" />
    <option name="stubs" hit="Enable RMI stubs generation" />
    <option name="generate" hit="Generate IIOP stubs" />
    <option name="iiop" hit="Generate IIOP stubs" />
    <option name="stubs" hit="Generate IIOP stubs" />
    <option name="debugging" hit="Generate debugging info" />
    <option name="generate" hit="Generate debugging info" />
    <option name="info" hit="Generate debugging info" />
    <option name="generate" hit="Generate no warnings" />
    <option name="no" hit="Generate no warnings" />
    <option name="warnings" hit="Generate no warnings" />
    <option name="compiler" hit="RMI Compiler" />
    <option name="rmi" hit="RMI Compiler" />
  </configurable>
  <configurable id="reference.idesettings.debugger.capture" configurable_name="Async Stack Traces">
    <option name="async" hit="Async Stack Traces" />
    <option name="stack" hit="Async Stack Traces" />
    <option name="traces" hit="Async Stack Traces" />
    <option name="based" hit="Breakpoints based:" />
    <option name="breakpoints" hit="Breakpoints based:" />
    <option name="capture" hit="Capture local variables (may greatly slow down the execution)" />
    <option name="down" hit="Capture local variables (may greatly slow down the execution)" />
    <option name="execution" hit="Capture local variables (may greatly slow down the execution)" />
    <option name="greatly" hit="Capture local variables (may greatly slow down the execution)" />
    <option name="local" hit="Capture local variables (may greatly slow down the execution)" />
    <option name="may" hit="Capture local variables (may greatly slow down the execution)" />
    <option name="slow" hit="Capture local variables (may greatly slow down the execution)" />
    <option name="the" hit="Capture local variables (may greatly slow down the execution)" />
    <option name="variables" hit="Capture local variables (may greatly slow down the execution)" />
    <option name="annotations" hit="Configure Annotations..." />
    <option name="configure" hit="Configure Annotations..." />
    <option name="agent" hit="Instrumenting agent (requires debugger restart)" />
    <option name="debugger" hit="Instrumenting agent (requires debugger restart)" />
    <option name="instrumenting" hit="Instrumenting agent (requires debugger restart)" />
    <option name="requires" hit="Instrumenting agent (requires debugger restart)" />
    <option name="restart" hit="Instrumenting agent (requires debugger restart)" />
  </configurable>
  <configurable id="jvm.logging" configurable_name="JVM Logging">
    <option name="apache" hit="Apache Commons Logging" />
    <option name="commons" hit="Apache Commons Logging" />
    <option name="logging" hit="Apache Commons Logging" />
    <option name="jvm" hit="JVM Logging" />
    <option name="logging" hit="JVM Logging" />
    <option name="java" hit="Java" />
    <option name="log4j" hit="Log4j" />
    <option name="log4j2" hit="Log4j2" />
    <option name="logger" hit="Logger:" />
    <option name="slf4j" hit="Slf4j" />
    <option name="unspecified" hit="Unspecified" />
  </configurable>
  <configurable id="com.intellij.jarRepository.settings.RemoteRepositoriesConfigurable" configurable_name="Remote Jar Repositories">
    <option name="add" hit="Add" />
    <option name="artifactory" hit="Artifactory or Nexus Service URLs:" />
    <option name="nexus" hit="Artifactory or Nexus Service URLs:" />
    <option name="or" hit="Artifactory or Nexus Service URLs:" />
    <option name="service" hit="Artifactory or Nexus Service URLs:" />
    <option name="urls" hit="Artifactory or Nexus Service URLs:" />
    <option name="edit" hit="Edit" />
    <option name="jar" hit="Maven Jar Repositories:" />
    <option name="maven" hit="Maven Jar Repositories:" />
    <option name="repositories" hit="Maven Jar Repositories:" />
    <option name="jar" hit="Remote Jar Repositories" />
    <option name="remote" hit="Remote Jar Repositories" />
    <option name="repositories" hit="Remote Jar Repositories" />
    <option name="remove" hit="Remove" />
    <option name="defaults" hit="Reset Defaults" />
    <option name="reset" hit="Reset Defaults" />
    <option name="test" hit="Test" />
  </configurable>
</options>