<options>
  <configurable id="IntelliLang.Advanced" configurable_name="Advanced">
    <option name="add" hit="Add @Language annotation or comment if needed" />
    <option name="annotation" hit="Add @Language annotation or comment if needed" />
    <option name="comment" hit="Add @Language annotation or comment if needed" />
    <option name="if" hit="Add @Language annotation or comment if needed" />
    <option name="language" hit="Add @Language annotation or comment if needed" />
    <option name="needed" hit="Add @Language annotation or comment if needed" />
    <option name="or" hit="Add @Language annotation or comment if needed" />
    <option name="advanced" hit="Advanced" />
    <option name="analyze" hit="Analyze references" />
    <option name="references" hit="Analyze references" />
    <option name="annotation" hit="Annotation Classes" />
    <option name="classes" hit="Annotation Classes" />
    <option name="concatenations" hit="Convert undefined operands to text in concatenations" />
    <option name="convert" hit="Convert undefined operands to text in concatenations" />
    <option name="in" hit="Convert undefined operands to text in concatenations" />
    <option name="operands" hit="Convert undefined operands to text in concatenations" />
    <option name="text" hit="Convert undefined operands to text in concatenations" />
    <option name="to" hit="Convert undefined operands to text in concatenations" />
    <option name="undefined" hit="Convert undefined operands to text in concatenations" />
    <option name="analyze" hit="Do not analyze anything (fast)" />
    <option name="anything" hit="Do not analyze anything (fast)" />
    <option name="do" hit="Do not analyze anything (fast)" />
    <option name="fast" hit="Do not analyze anything (fast)" />
    <option name="not" hit="Do not analyze anything (fast)" />
    <option name="illegalargumentexception" hit="Instrument with IllegalArgumentException" />
    <option name="instrument" hit="Instrument with IllegalArgumentException" />
    <option name="with" hit="Instrument with IllegalArgumentException" />
    <option name="assertions" hit="Instrument with assertions" />
    <option name="instrument" hit="Instrument with assertions" />
    <option name="with" hit="Instrument with assertions" />
    <option name="annotation" hit="Language annotation class" />
    <option name="class" hit="Language annotation class" />
    <option name="language" hit="Language annotation class" />
    <option name="assignments" hit="Look for variable assignments" />
    <option name="for" hit="Look for variable assignments" />
    <option name="look" hit="Look for variable assignments" />
    <option name="variable" hit="Look for variable assignments" />
    <option name="instrumentation" hit="No runtime instrumentation" />
    <option name="no" hit="No runtime instrumentation" />
    <option name="runtime" hit="No runtime instrumentation" />
    <option name="annotation" hit="Pattern annotation class" />
    <option name="class" hit="Pattern annotation class" />
    <option name="pattern" hit="Pattern annotation class" />
    <option name="performance" hit="Performance" />
    <option name="pattern" hit="Runtime Pattern Validation" />
    <option name="runtime" hit="Runtime Pattern Validation" />
    <option name="validation" hit="Runtime Pattern Validation" />
    <option name="annotation" hit="Substitution annotation class" />
    <option name="class" hit="Substitution annotation class" />
    <option name="substitution" hit="Substitution annotation class" />
    <option name="analysis" hit="Use dataflow analysis (slow)" />
    <option name="dataflow" hit="Use dataflow analysis (slow)" />
    <option name="slow" hit="Use dataflow analysis (slow)" />
    <option name="use" hit="Use dataflow analysis (slow)" />
  </configurable>
</options>