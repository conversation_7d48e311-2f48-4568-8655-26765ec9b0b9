<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="add" path="ActionManager" hit="Add Group to Events Test Scheme" />
    <option name="events" path="ActionManager" hit="Add Group to Events Test Scheme" />
    <option name="group" path="ActionManager" hit="Add Group to Events Test Scheme" />
    <option name="scheme" path="ActionManager" hit="Add Group to Events Test Scheme" />
    <option name="test" path="ActionManager" hit="Add Group to Events Test Scheme" />
    <option name="to" path="ActionManager" hit="Add Group to Events Test Scheme" />
    <option name="all" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="allows" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="collector" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="data" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="from" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="group" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="it" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="locally" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="new" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="test" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="the" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="to" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="use" path="ActionManager" hit="Allows all data from the group. Use it to test new collector locally." />
    <option name="clean" path="ActionManager" hit="Clean Up Event Log" />
    <option name="event" path="ActionManager" hit="Clean Up Event Log" />
    <option name="log" path="ActionManager" hit="Clean Up Event Log" />
    <option name="up" path="ActionManager" hit="Clean Up Event Log" />
    <option name="clean" path="ActionManager" hit="Clean Up Events Test Scheme" />
    <option name="events" path="ActionManager" hit="Clean Up Events Test Scheme" />
    <option name="scheme" path="ActionManager" hit="Clean Up Events Test Scheme" />
    <option name="test" path="ActionManager" hit="Clean Up Events Test Scheme" />
    <option name="up" path="ActionManager" hit="Clean Up Events Test Scheme" />
    <option name="collect" path="ActionManager" hit="Collect Statistics from Collector..." />
    <option name="collector" path="ActionManager" hit="Collect Statistics from Collector..." />
    <option name="from" path="ActionManager" hit="Collect Statistics from Collector..." />
    <option name="statistics" path="ActionManager" hit="Collect Statistics from Collector..." />
    <option name="configure" path="ActionManager" hit="Configure Events Scheme File" />
    <option name="events" path="ActionManager" hit="Configure Events Scheme File" />
    <option name="file" path="ActionManager" hit="Configure Events Scheme File" />
    <option name="scheme" path="ActionManager" hit="Configure Events Scheme File" />
    <option name="edit" path="ActionManager" hit="Edit Events Test Scheme" />
    <option name="events" path="ActionManager" hit="Edit Events Test Scheme" />
    <option name="scheme" path="ActionManager" hit="Edit Events Test Scheme" />
    <option name="test" path="ActionManager" hit="Edit Events Test Scheme" />
    <option name="collectors" path="ActionManager" hit="Gather statistics collectors' scheme from running IDE instance" />
    <option name="from" path="ActionManager" hit="Gather statistics collectors' scheme from running IDE instance" />
    <option name="gather" path="ActionManager" hit="Gather statistics collectors' scheme from running IDE instance" />
    <option name="ide" path="ActionManager" hit="Gather statistics collectors' scheme from running IDE instance" />
    <option name="instance" path="ActionManager" hit="Gather statistics collectors' scheme from running IDE instance" />
    <option name="running" path="ActionManager" hit="Gather statistics collectors' scheme from running IDE instance" />
    <option name="scheme" path="ActionManager" hit="Gather statistics collectors' scheme from running IDE instance" />
    <option name="statistics" path="ActionManager" hit="Gather statistics collectors' scheme from running IDE instance" />
    <option name="all" path="ActionManager" hit="Invokes all state collectors and records result to event log" />
    <option name="and" path="ActionManager" hit="Invokes all state collectors and records result to event log" />
    <option name="collectors" path="ActionManager" hit="Invokes all state collectors and records result to event log" />
    <option name="event" path="ActionManager" hit="Invokes all state collectors and records result to event log" />
    <option name="invokes" path="ActionManager" hit="Invokes all state collectors and records result to event log" />
    <option name="log" path="ActionManager" hit="Invokes all state collectors and records result to event log" />
    <option name="records" path="ActionManager" hit="Invokes all state collectors and records result to event log" />
    <option name="result" path="ActionManager" hit="Invokes all state collectors and records result to event log" />
    <option name="state" path="ActionManager" hit="Invokes all state collectors and records result to event log" />
    <option name="to" path="ActionManager" hit="Invokes all state collectors and records result to event log" />
    <option name="custom" path="ActionManager" hit="Load Events Scheme from the custom folder" />
    <option name="events" path="ActionManager" hit="Load Events Scheme from the custom folder" />
    <option name="folder" path="ActionManager" hit="Load Events Scheme from the custom folder" />
    <option name="from" path="ActionManager" hit="Load Events Scheme from the custom folder" />
    <option name="load" path="ActionManager" hit="Load Events Scheme from the custom folder" />
    <option name="scheme" path="ActionManager" hit="Load Events Scheme from the custom folder" />
    <option name="the" path="ActionManager" hit="Load Events Scheme from the custom folder" />
    <option name="editor" path="ActionManager" hit="Open Event Log in Editor" />
    <option name="event" path="ActionManager" hit="Open Event Log in Editor" />
    <option name="in" path="ActionManager" hit="Open Event Log in Editor" />
    <option name="log" path="ActionManager" hit="Open Event Log in Editor" />
    <option name="open" path="ActionManager" hit="Open Event Log in Editor" />
    <option name="file" path="ActionManager" hit="Open FUS Scheme File" />
    <option name="fus" path="ActionManager" hit="Open FUS Scheme File" />
    <option name="open" path="ActionManager" hit="Open FUS Scheme File" />
    <option name="scheme" path="ActionManager" hit="Open FUS Scheme File" />
    <option name="file" path="ActionManager" hit="Open FUS Test Scheme File" />
    <option name="fus" path="ActionManager" hit="Open FUS Test Scheme File" />
    <option name="open" path="ActionManager" hit="Open FUS Test Scheme File" />
    <option name="scheme" path="ActionManager" hit="Open FUS Test Scheme File" />
    <option name="test" path="ActionManager" hit="Open FUS Test Scheme File" />
    <option name="event" path="ActionManager" hit="Open Statistics Event Log" />
    <option name="log" path="ActionManager" hit="Open Statistics Event Log" />
    <option name="open" path="ActionManager" hit="Open Statistics Event Log" />
    <option name="statistics" path="ActionManager" hit="Open Statistics Event Log" />
    <option name="a" path="ActionManager" hit="Opens a toolwindow with statistics event log" />
    <option name="event" path="ActionManager" hit="Opens a toolwindow with statistics event log" />
    <option name="log" path="ActionManager" hit="Opens a toolwindow with statistics event log" />
    <option name="opens" path="ActionManager" hit="Opens a toolwindow with statistics event log" />
    <option name="statistics" path="ActionManager" hit="Opens a toolwindow with statistics event log" />
    <option name="toolwindow" path="ActionManager" hit="Opens a toolwindow with statistics event log" />
    <option name="with" path="ActionManager" hit="Opens a toolwindow with statistics event log" />
    <option name="active" path="ActionManager" hit="Opens active event log file in IDE" />
    <option name="event" path="ActionManager" hit="Opens active event log file in IDE" />
    <option name="file" path="ActionManager" hit="Opens active event log file in IDE" />
    <option name="ide" path="ActionManager" hit="Opens active event log file in IDE" />
    <option name="in" path="ActionManager" hit="Opens active event log file in IDE" />
    <option name="log" path="ActionManager" hit="Opens active event log file in IDE" />
    <option name="opens" path="ActionManager" hit="Opens active event log file in IDE" />
    <option name="events" path="ActionManager" hit="Opens events scheme file in IDE" />
    <option name="file" path="ActionManager" hit="Opens events scheme file in IDE" />
    <option name="ide" path="ActionManager" hit="Opens events scheme file in IDE" />
    <option name="in" path="ActionManager" hit="Opens events scheme file in IDE" />
    <option name="opens" path="ActionManager" hit="Opens events scheme file in IDE" />
    <option name="scheme" path="ActionManager" hit="Opens events scheme file in IDE" />
    <option name="events" path="ActionManager" hit="Opens events test scheme file in IDE" />
    <option name="file" path="ActionManager" hit="Opens events test scheme file in IDE" />
    <option name="ide" path="ActionManager" hit="Opens events test scheme file in IDE" />
    <option name="in" path="ActionManager" hit="Opens events test scheme file in IDE" />
    <option name="opens" path="ActionManager" hit="Opens events test scheme file in IDE" />
    <option name="scheme" path="ActionManager" hit="Opens events test scheme file in IDE" />
    <option name="test" path="ActionManager" hit="Opens events test scheme file in IDE" />
    <option name="changed" path="ActionManager" hit="Record Changed Settings to Event Log" />
    <option name="event" path="ActionManager" hit="Record Changed Settings to Event Log" />
    <option name="log" path="ActionManager" hit="Record Changed Settings to Event Log" />
    <option name="record" path="ActionManager" hit="Record Changed Settings to Event Log" />
    <option name="settings" path="ActionManager" hit="Record Changed Settings to Event Log" />
    <option name="to" path="ActionManager" hit="Record Changed Settings to Event Log" />
    <option name="collectors" path="ActionManager" hit="Record State Collectors to Event Log" />
    <option name="event" path="ActionManager" hit="Record State Collectors to Event Log" />
    <option name="log" path="ActionManager" hit="Record State Collectors to Event Log" />
    <option name="record" path="ActionManager" hit="Record State Collectors to Event Log" />
    <option name="state" path="ActionManager" hit="Record State Collectors to Event Log" />
    <option name="to" path="ActionManager" hit="Record State Collectors to Event Log" />
    <option name="all" path="ActionManager" hit="Removes all groups registered in the test scheme" />
    <option name="groups" path="ActionManager" hit="Removes all groups registered in the test scheme" />
    <option name="in" path="ActionManager" hit="Removes all groups registered in the test scheme" />
    <option name="registered" path="ActionManager" hit="Removes all groups registered in the test scheme" />
    <option name="removes" path="ActionManager" hit="Removes all groups registered in the test scheme" />
    <option name="scheme" path="ActionManager" hit="Removes all groups registered in the test scheme" />
    <option name="test" path="ActionManager" hit="Removes all groups registered in the test scheme" />
    <option name="the" path="ActionManager" hit="Removes all groups registered in the test scheme" />
    <option name="all" path="ActionManager" hit="Removes all not send event log files" />
    <option name="event" path="ActionManager" hit="Removes all not send event log files" />
    <option name="files" path="ActionManager" hit="Removes all not send event log files" />
    <option name="log" path="ActionManager" hit="Removes all not send event log files" />
    <option name="not" path="ActionManager" hit="Removes all not send event log files" />
    <option name="removes" path="ActionManager" hit="Removes all not send event log files" />
    <option name="send" path="ActionManager" hit="Removes all not send event log files" />
    <option name="event" path="ActionManager" hit="Send Feature Usage Event Log" />
    <option name="feature" path="ActionManager" hit="Send Feature Usage Event Log" />
    <option name="log" path="ActionManager" hit="Send Feature Usage Event Log" />
    <option name="send" path="ActionManager" hit="Send Feature Usage Event Log" />
    <option name="usage" path="ActionManager" hit="Send Feature Usage Event Log" />
    <option name="event" path="ActionManager" hit="Sends event log files to qa server" />
    <option name="files" path="ActionManager" hit="Sends event log files to qa server" />
    <option name="log" path="ActionManager" hit="Sends event log files to qa server" />
    <option name="qa" path="ActionManager" hit="Sends event log files to qa server" />
    <option name="sends" path="ActionManager" hit="Sends event log files to qa server" />
    <option name="server" path="ActionManager" hit="Sends event log files to qa server" />
    <option name="to" path="ActionManager" hit="Sends event log files to qa server" />
    <option name="collectors" path="ActionManager" hit="Show FUS Collectors Scheme" />
    <option name="fus" path="ActionManager" hit="Show FUS Collectors Scheme" />
    <option name="scheme" path="ActionManager" hit="Show FUS Collectors Scheme" />
    <option name="show" path="ActionManager" hit="Show FUS Collectors Scheme" />
    <option name="psi" path="ActionManager" hit="Show PSI Structure..." />
    <option name="show" path="ActionManager" hit="Show PSI Structure..." />
    <option name="structure" path="ActionManager" hit="Show PSI Structure..." />
    <option name="events" path="ActionManager" hit="Test Parsing Events Scheme..." />
    <option name="parsing" path="ActionManager" hit="Test Parsing Events Scheme..." />
    <option name="scheme" path="ActionManager" hit="Test Parsing Events Scheme..." />
    <option name="test" path="ActionManager" hit="Test Parsing Events Scheme..." />
    <option name="events" path="ActionManager" hit="Update Events Scheme" />
    <option name="scheme" path="ActionManager" hit="Update Events Scheme" />
    <option name="update" path="ActionManager" hit="Update Events Scheme" />
    <option name="also" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="events" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="file" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="from" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="if" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="main" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="necessary" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="re-loads" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="scheme" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="server" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="test" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="the" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="updates" path="ActionManager" hit="Updates the main events scheme from the server if necessary. Also re-loads events test scheme from the file." />
    <option name="debugger" path="ActionManager" hit="Use Dialog For PSI Debugger" />
    <option name="dialog" path="ActionManager" hit="Use Dialog For PSI Debugger" />
    <option name="for" path="ActionManager" hit="Use Dialog For PSI Debugger" />
    <option name="psi" path="ActionManager" hit="Use Dialog For PSI Debugger" />
    <option name="use" path="ActionManager" hit="Use Dialog For PSI Debugger" />
    <option name="current" path="ActionManager" hit="View PSI Structure of Current File..." />
    <option name="file" path="ActionManager" hit="View PSI Structure of Current File..." />
    <option name="of" path="ActionManager" hit="View PSI Structure of Current File..." />
    <option name="psi" path="ActionManager" hit="View PSI Structure of Current File..." />
    <option name="structure" path="ActionManager" hit="View PSI Structure of Current File..." />
    <option name="view" path="ActionManager" hit="View PSI Structure of Current File..." />
    <option name="psi" path="ActionManager" hit="View PSI Structure..." />
    <option name="structure" path="ActionManager" hit="View PSI Structure..." />
    <option name="view" path="ActionManager" hit="View PSI Structure..." />
  </configurable>
</options>