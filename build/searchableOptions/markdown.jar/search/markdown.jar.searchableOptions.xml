<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="align" path="ActionManager" hit="Align Center" />
    <option name="center" path="ActionManager" hit="Align Center" />
    <option name="align" path="ActionManager" hit="Align Left" />
    <option name="left" path="ActionManager" hit="Align Left" />
    <option name="align" path="ActionManager" hit="Align Right" />
    <option name="right" path="ActionManager" hit="Align Right" />
    <option name="bold" path="ActionManager" hit="Bold" />
    <option name="clean" path="ActionManager" hit="Clean Up Markdown Extensions External Files…" />
    <option name="extensions" path="ActionManager" hit="Clean Up Markdown Extensions External Files…" />
    <option name="external" path="ActionManager" hit="Clean Up Markdown Extensions External Files…" />
    <option name="files" path="ActionManager" hit="Clean Up Markdown Extensions External Files…" />
    <option name="markdown" path="ActionManager" hit="Clean Up Markdown Extensions External Files…" />
    <option name="up" path="ActionManager" hit="Clean Up Markdown Extensions External Files…" />
    <option name="all" path="ActionManager" hit="Cleans up external files for all Markdown extensions" />
    <option name="cleans" path="ActionManager" hit="Cleans up external files for all Markdown extensions" />
    <option name="extensions" path="ActionManager" hit="Cleans up external files for all Markdown extensions" />
    <option name="external" path="ActionManager" hit="Cleans up external files for all Markdown extensions" />
    <option name="files" path="ActionManager" hit="Cleans up external files for all Markdown extensions" />
    <option name="for" path="ActionManager" hit="Cleans up external files for all Markdown extensions" />
    <option name="markdown" path="ActionManager" hit="Cleans up external files for all Markdown extensions" />
    <option name="up" path="ActionManager" hit="Cleans up external files for all Markdown extensions" />
    <option name="code" path="ActionManager" hit="Code" />
    <option name="configure" path="ActionManager" hit="Configure Pandoc…" />
    <option name="pandoc" path="ActionManager" hit="Configure Pandoc…" />
    <option name="a" path="ActionManager" hit="Convert an inline link to a reference-style link" />
    <option name="an" path="ActionManager" hit="Convert an inline link to a reference-style link" />
    <option name="convert" path="ActionManager" hit="Convert an inline link to a reference-style link" />
    <option name="inline" path="ActionManager" hit="Convert an inline link to a reference-style link" />
    <option name="link" path="ActionManager" hit="Convert an inline link to a reference-style link" />
    <option name="reference-style" path="ActionManager" hit="Convert an inline link to a reference-style link" />
    <option name="to" path="ActionManager" hit="Convert an inline link to a reference-style link" />
    <option name="convert" path="ActionManager" hit="Convert to Reference" />
    <option name="reference" path="ActionManager" hit="Convert to Reference" />
    <option name="to" path="ActionManager" hit="Convert to Reference" />
    <option name="create" path="ActionManager" hit="Create Link" />
    <option name="link" path="ActionManager" hit="Create Link" />
    <option name="change" path="ActionManager" hit="Create Or Change List" />
    <option name="create" path="ActionManager" hit="Create Or Change List" />
    <option name="list" path="ActionManager" hit="Create Or Change List" />
    <option name="or" path="ActionManager" hit="Create Or Change List" />
    <option name="decrease" path="ActionManager" hit="Decrease Header Level" />
    <option name="header" path="ActionManager" hit="Decrease Header Level" />
    <option name="level" path="ActionManager" hit="Decrease Header Level" />
    <option name="decrease" path="ActionManager" hit="Decrease Preview Font Size" />
    <option name="font" path="ActionManager" hit="Decrease Preview Font Size" />
    <option name="preview" path="ActionManager" hit="Decrease Preview Font Size" />
    <option name="size" path="ActionManager" hit="Decrease Preview Font Size" />
    <option name="a" path="ActionManager" hit="Decrease level of a header by caret" />
    <option name="by" path="ActionManager" hit="Decrease level of a header by caret" />
    <option name="caret" path="ActionManager" hit="Decrease level of a header by caret" />
    <option name="decrease" path="ActionManager" hit="Decrease level of a header by caret" />
    <option name="header" path="ActionManager" hit="Decrease level of a header by caret" />
    <option name="level" path="ActionManager" hit="Decrease level of a header by caret" />
    <option name="of" path="ActionManager" hit="Decrease level of a header by caret" />
    <option name="export" path="ActionManager" hit="Export Markdown File To…" />
    <option name="file" path="ActionManager" hit="Export Markdown File To…" />
    <option name="markdown" path="ActionManager" hit="Export Markdown File To…" />
    <option name="to" path="ActionManager" hit="Export Markdown File To…" />
    <option name="an" path="ActionManager" hit="Format text as an inline code span" />
    <option name="as" path="ActionManager" hit="Format text as an inline code span" />
    <option name="code" path="ActionManager" hit="Format text as an inline code span" />
    <option name="format" path="ActionManager" hit="Format text as an inline code span" />
    <option name="inline" path="ActionManager" hit="Format text as an inline code span" />
    <option name="span" path="ActionManager" hit="Format text as an inline code span" />
    <option name="text" path="ActionManager" hit="Format text as an inline code span" />
    <option name="as" path="ActionManager" hit="Format text as bold (strong emphasis)" />
    <option name="bold" path="ActionManager" hit="Format text as bold (strong emphasis)" />
    <option name="emphasis" path="ActionManager" hit="Format text as bold (strong emphasis)" />
    <option name="format" path="ActionManager" hit="Format text as bold (strong emphasis)" />
    <option name="strong" path="ActionManager" hit="Format text as bold (strong emphasis)" />
    <option name="text" path="ActionManager" hit="Format text as bold (strong emphasis)" />
    <option name="as" path="ActionManager" hit="Format text as italic (emphasis)" />
    <option name="emphasis" path="ActionManager" hit="Format text as italic (emphasis)" />
    <option name="format" path="ActionManager" hit="Format text as italic (emphasis)" />
    <option name="italic" path="ActionManager" hit="Format text as italic (emphasis)" />
    <option name="text" path="ActionManager" hit="Format text as italic (emphasis)" />
    <option name="a" path="ActionManager" hit="Format text with a strikethrough" />
    <option name="format" path="ActionManager" hit="Format text with a strikethrough" />
    <option name="strikethrough" path="ActionManager" hit="Format text with a strikethrough" />
    <option name="text" path="ActionManager" hit="Format text with a strikethrough" />
    <option name="with" path="ActionManager" hit="Format text with a strikethrough" />
    <option name="contents" path="ActionManager" hit="Generate Table Of Contents" />
    <option name="generate" path="ActionManager" hit="Generate Table Of Contents" />
    <option name="of" path="ActionManager" hit="Generate Table Of Contents" />
    <option name="table" path="ActionManager" hit="Generate Table Of Contents" />
    <option name="document" path="ActionManager" hit="Import Word Document…" />
    <option name="import" path="ActionManager" hit="Import Word Document…" />
    <option name="word" path="ActionManager" hit="Import Word Document…" />
    <option name="header" path="ActionManager" hit="Increase Header Level" />
    <option name="increase" path="ActionManager" hit="Increase Header Level" />
    <option name="level" path="ActionManager" hit="Increase Header Level" />
    <option name="font" path="ActionManager" hit="Increase Preview Font Size" />
    <option name="increase" path="ActionManager" hit="Increase Preview Font Size" />
    <option name="preview" path="ActionManager" hit="Increase Preview Font Size" />
    <option name="size" path="ActionManager" hit="Increase Preview Font Size" />
    <option name="a" path="ActionManager" hit="Increase level of a header by caret" />
    <option name="by" path="ActionManager" hit="Increase level of a header by caret" />
    <option name="caret" path="ActionManager" hit="Increase level of a header by caret" />
    <option name="header" path="ActionManager" hit="Increase level of a header by caret" />
    <option name="increase" path="ActionManager" hit="Increase level of a header by caret" />
    <option name="level" path="ActionManager" hit="Increase level of a header by caret" />
    <option name="of" path="ActionManager" hit="Increase level of a header by caret" />
    <option name="column" path="ActionManager" hit="Insert Column Left" />
    <option name="insert" path="ActionManager" hit="Insert Column Left" />
    <option name="left" path="ActionManager" hit="Insert Column Left" />
    <option name="column" path="ActionManager" hit="Insert Column Right" />
    <option name="insert" path="ActionManager" hit="Insert Column Right" />
    <option name="right" path="ActionManager" hit="Insert Column Right" />
    <option name="image" path="ActionManager" hit="Insert Image" />
    <option name="insert" path="ActionManager" hit="Insert Image" />
    <option name="above" path="ActionManager" hit="Insert Row Above" />
    <option name="insert" path="ActionManager" hit="Insert Row Above" />
    <option name="row" path="ActionManager" hit="Insert Row Above" />
    <option name="below" path="ActionManager" hit="Insert Row Below" />
    <option name="insert" path="ActionManager" hit="Insert Row Below" />
    <option name="row" path="ActionManager" hit="Insert Row Below" />
    <option name="insert" path="ActionManager" hit="Insert Table" />
    <option name="table" path="ActionManager" hit="Insert Table" />
    <option name="an" path="ActionManager" hit="Insert an image at the caret position" />
    <option name="at" path="ActionManager" hit="Insert an image at the caret position" />
    <option name="caret" path="ActionManager" hit="Insert an image at the caret position" />
    <option name="image" path="ActionManager" hit="Insert an image at the caret position" />
    <option name="insert" path="ActionManager" hit="Insert an image at the caret position" />
    <option name="position" path="ActionManager" hit="Insert an image at the caret position" />
    <option name="the" path="ActionManager" hit="Insert an image at the caret position" />
    <option name="insert" path="ActionManager" hit="Insert…" />
    <option name="italic" path="ActionManager" hit="Italic" />
    <option name="column" path="ActionManager" hit="Move Column Left" />
    <option name="left" path="ActionManager" hit="Move Column Left" />
    <option name="move" path="ActionManager" hit="Move Column Left" />
    <option name="column" path="ActionManager" hit="Move Column Right" />
    <option name="move" path="ActionManager" hit="Move Column Right" />
    <option name="right" path="ActionManager" hit="Move Column Right" />
    <option name="down" path="ActionManager" hit="Move Row Down" />
    <option name="move" path="ActionManager" hit="Move Row Down" />
    <option name="row" path="ActionManager" hit="Move Row Down" />
    <option name="move" path="ActionManager" hit="Move Row Up" />
    <option name="row" path="ActionManager" hit="Move Row Up" />
    <option name="up" path="ActionManager" hit="Move Row Up" />
    <option name="current" path="ActionManager" hit="Open Devtools Window For The Current Markdown Preview" />
    <option name="devtools" path="ActionManager" hit="Open Devtools Window For The Current Markdown Preview" />
    <option name="for" path="ActionManager" hit="Open Devtools Window For The Current Markdown Preview" />
    <option name="markdown" path="ActionManager" hit="Open Devtools Window For The Current Markdown Preview" />
    <option name="open" path="ActionManager" hit="Open Devtools Window For The Current Markdown Preview" />
    <option name="preview" path="ActionManager" hit="Open Devtools Window For The Current Markdown Preview" />
    <option name="the" path="ActionManager" hit="Open Devtools Window For The Current Markdown Preview" />
    <option name="window" path="ActionManager" hit="Open Devtools Window For The Current Markdown Preview" />
    <option name="column" path="ActionManager" hit="Remove Column" />
    <option name="remove" path="ActionManager" hit="Remove Column" />
    <option name="remove" path="ActionManager" hit="Remove Row" />
    <option name="row" path="ActionManager" hit="Remove Row" />
    <option name="font" path="ActionManager" hit="Reset Preview Font Size" />
    <option name="preview" path="ActionManager" hit="Reset Preview Font Size" />
    <option name="reset" path="ActionManager" hit="Reset Preview Font Size" />
    <option name="size" path="ActionManager" hit="Reset Preview Font Size" />
    <option name="cells" path="ActionManager" hit="Select Column Cells" />
    <option name="column" path="ActionManager" hit="Select Column Cells" />
    <option name="select" path="ActionManager" hit="Select Column Cells" />
    <option name="row" path="ActionManager" hit="Select Row" />
    <option name="select" path="ActionManager" hit="Select Row" />
    <option name="alignment" path="ActionManager" hit="Set Column Alignment" />
    <option name="column" path="ActionManager" hit="Set Column Alignment" />
    <option name="set" path="ActionManager" hit="Set Column Alignment" />
    <option name="header" path="ActionManager" hit="Set Header Style" />
    <option name="set" path="ActionManager" hit="Set Header Style" />
    <option name="style" path="ActionManager" hit="Set Header Style" />
    <option name="strikethrough" path="ActionManager" hit="Strikethrough" />
    <option name="scroll" path="ActionManager" hit="Sync Scroll" />
    <option name="sync" path="ActionManager" hit="Sync Scroll" />
    <option name="editor" path="ActionManager" hit="Sync the preview panel scroll with the editor" />
    <option name="panel" path="ActionManager" hit="Sync the preview panel scroll with the editor" />
    <option name="preview" path="ActionManager" hit="Sync the preview panel scroll with the editor" />
    <option name="scroll" path="ActionManager" hit="Sync the preview panel scroll with the editor" />
    <option name="sync" path="ActionManager" hit="Sync the preview panel scroll with the editor" />
    <option name="the" path="ActionManager" hit="Sync the preview panel scroll with the editor" />
    <option name="with" path="ActionManager" hit="Sync the preview panel scroll with the editor" />
    <option name="a" path="ActionManager" hit="Wrap the selected text as a link" />
    <option name="as" path="ActionManager" hit="Wrap the selected text as a link" />
    <option name="link" path="ActionManager" hit="Wrap the selected text as a link" />
    <option name="selected" path="ActionManager" hit="Wrap the selected text as a link" />
    <option name="text" path="ActionManager" hit="Wrap the selected text as a link" />
    <option name="the" path="ActionManager" hit="Wrap the selected text as a link" />
    <option name="wrap" path="ActionManager" hit="Wrap the selected text as a link" />
  </configurable>
  <configurable id="editor.breadcrumbs" configurable_name="Breadcrumbs">
    <option name="markdown" hit="Markdown" />
  </configurable>
  <configurable id="Settings.Markdown.SmartKeys" configurable_name="Markdown">
    <option name="adjust" hit="Adjust indentation on type" />
    <option name="indentation" hit="Adjust indentation on type" />
    <option name="on" hit="Adjust indentation on type" />
    <option name="type" hit="Adjust indentation on type" />
    <option name="break" hit="Insert HTML line break (' ') instead of new line inside table cells" />
    <option name="cells" hit="Insert HTML line break (' ') instead of new line inside table cells" />
    <option name="html" hit="Insert HTML line break (' ') instead of new line inside table cells" />
    <option name="insert" hit="Insert HTML line break (' ') instead of new line inside table cells" />
    <option name="inside" hit="Insert HTML line break (' ') instead of new line inside table cells" />
    <option name="instead" hit="Insert HTML line break (' ') instead of new line inside table cells" />
    <option name="line" hit="Insert HTML line break (' ') instead of new line inside table cells" />
    <option name="new" hit="Insert HTML line break (' ') instead of new line inside table cells" />
    <option name="of" hit="Insert HTML line break (' ') instead of new line inside table cells" />
    <option name="table" hit="Insert HTML line break (' ') instead of new line inside table cells" />
    <option name="drag-and-drop" hit="Insert links to images or files on drag-and-drop" />
    <option name="files" hit="Insert links to images or files on drag-and-drop" />
    <option name="images" hit="Insert links to images or files on drag-and-drop" />
    <option name="insert" hit="Insert links to images or files on drag-and-drop" />
    <option name="links" hit="Insert links to images or files on drag-and-drop" />
    <option name="on" hit="Insert links to images or files on drag-and-drop" />
    <option name="or" hit="Insert links to images or files on drag-and-drop" />
    <option name="to" hit="Insert links to images or files on drag-and-drop" />
    <option name="lists" hit="Lists" />
    <option name="markdown" hit="Markdown" />
    <option name="other" hit="Other" />
    <option name="reformat" hit="Reformat table when typing" />
    <option name="table" hit="Reformat table when typing" />
    <option name="typing" hit="Reformat table when typing" />
    <option name="when" hit="Reformat table when typing" />
    <option name="list" hit="Renumber list when typing" />
    <option name="renumber" hit="Renumber list when typing" />
    <option name="typing" hit="Renumber list when typing" />
    <option name="when" hit="Renumber list when typing" />
    <option name="tables" hit="Tables" />
    <option name="insert" hit="Use Shift+Enter to insert new table row" />
    <option name="new" hit="Use Shift+Enter to insert new table row" />
    <option name="row" hit="Use Shift+Enter to insert new table row" />
    <option name="shift+enter" hit="Use Shift+Enter to insert new table row" />
    <option name="table" hit="Use Shift+Enter to insert new table row" />
    <option name="to" hit="Use Shift+Enter to insert new table row" />
    <option name="use" hit="Use Shift+Enter to insert new table row" />
    <option name="cells" hit="Use Tab/Shift+Tab to navigate table cells" />
    <option name="navigate" hit="Use Tab/Shift+Tab to navigate table cells" />
    <option name="shift+tab" hit="Use Tab/Shift+Tab to navigate table cells" />
    <option name="tab" hit="Use Tab/Shift+Tab to navigate table cells" />
    <option name="table" hit="Use Tab/Shift+Tab to navigate table cells" />
    <option name="to" hit="Use Tab/Shift+Tab to navigate table cells" />
    <option name="use" hit="Use Tab/Shift+Tab to navigate table cells" />
    <option name="and" hit="Use smart Enter and Backspace" />
    <option name="backspace" hit="Use smart Enter and Backspace" />
    <option name="enter" hit="Use smart Enter and Backspace" />
    <option name="smart" hit="Use smart Enter and Backspace" />
    <option name="use" hit="Use smart Enter and Backspace" />
  </configurable>
  <configurable id="reference.settingsdialog.IDE.editor.colors.Markdown" configurable_name="Markdown">
    <option name="markdown" hit="Markdown" />
    <option name="blockquote" hit="Blockquote//Blockquote" />
    <option name="blockquote" hit="Blockquote//Blockquote marker" />
    <option name="marker" hit="Blockquote//Blockquote marker" />
    <option name="block" hit="Code//Code block" />
    <option name="code" hit="Code//Code block" />
    <option name="code" hit="Code//Code fence" />
    <option name="fence" hit="Code//Code fence" />
    <option name="code" hit="Code//Code span" />
    <option name="span" hit="Code//Code span" />
    <option name="code" hit="Code//Code span marker" />
    <option name="marker" hit="Code//Code span marker" />
    <option name="span" hit="Code//Code span marker" />
    <option name="definition" hit="Definition list//Definition" />
    <option name="list" hit="Definition list//Definition" />
    <option name="definition" hit="Definition list//Definition list" />
    <option name="list" hit="Definition list//Definition list" />
    <option name="definition" hit="Definition list//Definition marker" />
    <option name="list" hit="Definition list//Definition marker" />
    <option name="marker" hit="Definition list//Definition marker" />
    <option name="definition" hit="Definition list//Term" />
    <option name="list" hit="Definition list//Term" />
    <option name="term" hit="Definition list//Term" />
    <option name="block" hit="HTML//HTML block" />
    <option name="html" hit="HTML//HTML block" />
    <option name="html" hit="HTML//Inline HTML" />
    <option name="inline" hit="HTML//Inline HTML" />
    <option name="1st" hit="Header//1st level header" />
    <option name="header" hit="Header//1st level header" />
    <option name="level" hit="Header//1st level header" />
    <option name="2nd" hit="Header//2nd level header" />
    <option name="header" hit="Header//2nd level header" />
    <option name="level" hit="Header//2nd level header" />
    <option name="3rd" hit="Header//3rd level header" />
    <option name="header" hit="Header//3rd level header" />
    <option name="level" hit="Header//3rd level header" />
    <option name="4th" hit="Header//4th level header" />
    <option name="header" hit="Header//4th level header" />
    <option name="level" hit="Header//4th level header" />
    <option name="5th" hit="Header//5th level header" />
    <option name="header" hit="Header//5th level header" />
    <option name="level" hit="Header//5th level header" />
    <option name="6th" hit="Header//6th level header" />
    <option name="header" hit="Header//6th level header" />
    <option name="level" hit="Header//6th level header" />
    <option name="header" hit="Header//Header marker" />
    <option name="marker" hit="Header//Header marker" />
    <option name="horizontal" hit="Horizontal rule" />
    <option name="rule" hit="Horizontal rule" />
    <option name="auto" hit="Links//Auto link" />
    <option name="link" hit="Links//Auto link" />
    <option name="links" hit="Links//Auto link" />
    <option name="explicit" hit="Links//Explicit link" />
    <option name="link" hit="Links//Explicit link" />
    <option name="links" hit="Links//Explicit link" />
    <option name="image" hit="Links//Image" />
    <option name="links" hit="Links//Image" />
    <option name="definition" hit="Links//Link definition" />
    <option name="link" hit="Links//Link definition" />
    <option name="links" hit="Links//Link definition" />
    <option name="destination" hit="Links//Link destination" />
    <option name="link" hit="Links//Link destination" />
    <option name="links" hit="Links//Link destination" />
    <option name="label" hit="Links//Link label" />
    <option name="link" hit="Links//Link label" />
    <option name="links" hit="Links//Link label" />
    <option name="link" hit="Links//Link text" />
    <option name="links" hit="Links//Link text" />
    <option name="text" hit="Links//Link text" />
    <option name="link" hit="Links//Link title" />
    <option name="links" hit="Links//Link title" />
    <option name="title" hit="Links//Link title" />
    <option name="link" hit="Links//Reference link" />
    <option name="links" hit="Links//Reference link" />
    <option name="reference" hit="Links//Reference link" />
    <option name="item" hit="Lists//List item" />
    <option name="list" hit="Lists//List item" />
    <option name="lists" hit="Lists//List item" />
    <option name="list" hit="Lists//List marker" />
    <option name="lists" hit="Lists//List marker" />
    <option name="marker" hit="Lists//List marker" />
    <option name="list" hit="Lists//Ordered list" />
    <option name="lists" hit="Lists//Ordered list" />
    <option name="ordered" hit="Lists//Ordered list" />
    <option name="list" hit="Lists//Unordered list" />
    <option name="lists" hit="Lists//Unordered list" />
    <option name="unordered" hit="Lists//Unordered list" />
    <option name="bold" hit="Style//Bold marker" />
    <option name="marker" hit="Style//Bold marker" />
    <option name="style" hit="Style//Bold marker" />
    <option name="bold" hit="Style//Bold text" />
    <option name="style" hit="Style//Bold text" />
    <option name="text" hit="Style//Bold text" />
    <option name="italic" hit="Style//Italic marker" />
    <option name="marker" hit="Style//Italic marker" />
    <option name="style" hit="Style//Italic marker" />
    <option name="italic" hit="Style//Italic text" />
    <option name="style" hit="Style//Italic text" />
    <option name="text" hit="Style//Italic text" />
    <option name="strikethrough" hit="Style//Strikethrough" />
    <option name="style" hit="Style//Strikethrough" />
    <option name="separator" hit="Table separator" />
    <option name="table" hit="Table separator" />
    <option name="text" hit="Text" />
  </configurable>
  <configurable id="preferences.sourceCode.Markdown" configurable_name="Markdown">
    <option name="around" path="Blank Lines" hit="Around block elements" />
    <option name="block" path="Blank Lines" hit="Around block elements" />
    <option name="elements" path="Blank Lines" hit="Around block elements" />
    <option name="around" path="Blank Lines" hit="Around header" />
    <option name="header" path="Blank Lines" hit="Around header" />
    <option name="between" path="Blank Lines" hit="Between paragraphs" />
    <option name="paragraphs" path="Blank Lines" hit="Between paragraphs" />
    <option name="blank" path="Blank Lines" hit="Blank Lines" />
    <option name="lines" path="Blank Lines" hit="Blank Lines" />
    <option name="continuation" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="indent" path="Tabs and Indents" hit="Continuation indent:" />
    <option name="disable" hit="Disable" />
    <option name="indent" path="Tabs and Indents" hit="Indent:" />
    <option name="empty" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="indents" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="keep" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="lines" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="on" path="Tabs and Indents" hit="Keep indents on empty lines" />
    <option name="blank" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="keep" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="lines" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="maximum" path="Blank Lines" hit="Keep maximum blank lines" />
    <option name="loading" hit="Loading..." />
    <option name="markdown" hit="Markdown" />
    <option name="blank" path="Blank Lines" hit="Minimum blank lines" />
    <option name="lines" path="Blank Lines" hit="Minimum blank lines" />
    <option name="minimum" path="Blank Lines" hit="Minimum blank lines" />
    <option name="scheme" hit="Scheme:" />
    <option name="from" hit="Set from..." />
    <option name="set" hit="Set from..." />
    <option name="smart" path="Tabs and Indents" hit="Smart tabs" />
    <option name="tabs" path="Tabs and Indents" hit="Smart tabs" />
    <option name="spaces" path="Spaces" hit="Spaces" />
    <option name="size" path="Tabs and Indents" hit="Tab size:" />
    <option name="tab" path="Tabs and Indents" hit="Tab size:" />
    <option name="and" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="indents" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="tabs" path="Tabs and Indents" hit="Tabs and Indents" />
    <option name="character" path="Tabs and Indents" hit="Use tab character" />
    <option name="tab" path="Tabs and Indents" hit="Use tab character" />
    <option name="use" path="Tabs and Indents" hit="Use tab character" />
    <option name="and" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="braces" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="wrapping" path="Wrapping and Braces" hit="Wrapping and Braces" />
    <option name="" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="line" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="next" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="on" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="signs" path="Wrapping and Braces" hit="':' signs on next line" />
    <option name="" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="and" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="line" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="next" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="on" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="signs" path="Wrapping and Braces" hit="'?' and ':' signs on next line" />
    <option name="" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="catch" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'catch' on new line" />
    <option name="" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="do" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="while" path="Wrapping and Braces" hit="'do ... while()' statement" />
    <option name="" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="else" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'else' on new line" />
    <option name="" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="finally" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'finally' on new line" />
    <option name="" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="for" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'for()' statement" />
    <option name="" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="if" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'if()' statement" />
    <option name="" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="switch" path="Wrapping and Braces" hit="'switch' statement" />
    <option name="" path="Wrapping and Braces" hit="'try' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'try' statement" />
    <option name="try" path="Wrapping and Braces" hit="'try' statement" />
    <option name="" path="Wrapping and Braces" hit="'try-with-resources'" />
    <option name="try-with-resources" path="Wrapping and Braces" hit="'try-with-resources'" />
    <option name="" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="line" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="new" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="on" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="while" path="Wrapping and Braces" hit="'while' on new line" />
    <option name="" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="statement" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="while" path="Wrapping and Braces" hit="'while()' statement" />
    <option name="after" path="Spaces" hit="After blockquote marker" />
    <option name="blockquote" path="Spaces" hit="After blockquote marker" />
    <option name="marker" path="Spaces" hit="After blockquote marker" />
    <option name="after" path="Spaces" hit="After header symbol" />
    <option name="header" path="Spaces" hit="After header symbol" />
    <option name="symbol" path="Spaces" hit="After header symbol" />
    <option name="after" path="Spaces" hit="After list marker" />
    <option name="list" path="Spaces" hit="After list marker" />
    <option name="marker" path="Spaces" hit="After list marker" />
    <option name="align" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="method" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="start" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="throws" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="to" path="Wrapping and Braces" hit="Align 'throws' to method start" />
    <option name="align" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="assignments" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align assignments in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="fields" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align fields in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="multiline" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="parenthesised" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="when" path="Wrapping and Braces" hit="Align parenthesised when multiline" />
    <option name="align" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="methods" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="simple" path="Wrapping and Braces" hit="Align simple methods in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="columns" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="in" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="variables" path="Wrapping and Braces" hit="Align variables in columns" />
    <option name="align" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="multiline" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="when" path="Wrapping and Braces" hit="Align when multiline" />
    <option name="array" path="Wrapping and Braces" hit="Array initializer" />
    <option name="initializer" path="Wrapping and Braces" hit="Array initializer" />
    <option name="assert" path="Wrapping and Braces" hit="Assert statement" />
    <option name="statement" path="Wrapping and Braces" hit="Assert statement" />
    <option name="assignment" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="line" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="next" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="on" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="sign" path="Wrapping and Braces" hit="Assignment sign on next line" />
    <option name="assignment" path="Wrapping and Braces" hit="Assignment statement" />
    <option name="statement" path="Wrapping and Braces" hit="Assignment statement" />
    <option name="between" path="Spaces" hit="Between words" />
    <option name="words" path="Spaces" hit="Between words" />
    <option name="binary" path="Wrapping and Braces" hit="Binary expressions" />
    <option name="expressions" path="Wrapping and Braces" hit="Binary expressions" />
    <option name="braces" path="Wrapping and Braces" hit="Braces placement" />
    <option name="placement" path="Wrapping and Braces" hit="Braces placement" />
    <option name="builder" path="Wrapping and Braces" hit="Builder methods" />
    <option name="methods" path="Wrapping and Braces" hit="Builder methods" />
    <option name="calls" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="chained" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="method" path="Wrapping and Braces" hit="Chained method calls" />
    <option name="annotations" path="Wrapping and Braces" hit="Class annotations" />
    <option name="class" path="Wrapping and Braces" hit="Class annotations" />
    <option name="at" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="column" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="comment" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="first" path="Wrapping and Braces" hit="Comment at first column" />
    <option name="comments" path="Wrapping and Braces" hit="Comments" />
    <option name="control" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="in" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="line" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="one" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="statement" path="Wrapping and Braces" hit="Control statement in one line" />
    <option name="a" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="case" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="each" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="line" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="on" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="separate" path="Wrapping and Braces" hit="Each 'case' on a separate line" />
    <option name="ensure" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="exceeded" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="is" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="margin" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="not" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="right" path="Wrapping and Braces" hit="Ensure right margin is not exceeded" />
    <option name="constants" path="Wrapping and Braces" hit="Enum constants" />
    <option name="enum" path="Wrapping and Braces" hit="Enum constants" />
    <option name="extends" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="implements" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="keyword" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="permits" path="Wrapping and Braces" hit="Extends/implements/permits keyword" />
    <option name="extends" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="implements" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="list" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="permits" path="Wrapping and Braces" hit="Extends/implements/permits list" />
    <option name="annotations" path="Wrapping and Braces" hit="Field annotations" />
    <option name="field" path="Wrapping and Braces" hit="Field annotations" />
    <option name="braces" path="Wrapping and Braces" hit="Force braces" />
    <option name="force" path="Wrapping and Braces" hit="Force braces" />
    <option name="force" path="Spaces" hit="Force one space" />
    <option name="one" path="Spaces" hit="Force one space" />
    <option name="space" path="Spaces" hit="Force one space" />
    <option name="format" path="Wrapping and Braces" hit="Format tables" />
    <option name="tables" path="Wrapping and Braces" hit="Format tables" />
    <option name="declarations" path="Wrapping and Braces" hit="Group declarations" />
    <option name="group" path="Wrapping and Braces" hit="Group declarations" />
    <option name="at" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="hard" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="wrap" path="Wrapping and Braces" hit="Hard wrap at:" />
    <option name="class" path="Wrapping and Braces" hit="In class declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In class declaration" />
    <option name="in" path="Wrapping and Braces" hit="In class declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="in" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="lambda" path="Wrapping and Braces" hit="In lambda declaration" />
    <option name="declaration" path="Wrapping and Braces" hit="In method declaration" />
    <option name="in" path="Wrapping and Braces" hit="In method declaration" />
    <option name="method" path="Wrapping and Braces" hit="In method declaration" />
    <option name="break" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="case" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="from" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'break' from 'case'" />
    <option name="branches" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="case" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="indent" path="Wrapping and Braces" hit="Indent 'case' branches" />
    <option name="arrows" path="Wrapping and Braces" hit="Insert block quote arrows" />
    <option name="block" path="Wrapping and Braces" hit="Insert block quote arrows" />
    <option name="insert" path="Wrapping and Braces" hit="Insert block quote arrows" />
    <option name="quote" path="Wrapping and Braces" hit="Insert block quote arrows" />
    <option name="builder" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="indents" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="keep" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="methods" path="Wrapping and Braces" hit="Keep builder methods indents" />
    <option name="blocks" path="Wrapping and Braces" hit="Keep line breaks inside text blocks" />
    <option name="breaks" path="Wrapping and Braces" hit="Keep line breaks inside text blocks" />
    <option name="inside" path="Wrapping and Braces" hit="Keep line breaks inside text blocks" />
    <option name="keep" path="Wrapping and Braces" hit="Keep line breaks inside text blocks" />
    <option name="line" path="Wrapping and Braces" hit="Keep line breaks inside text blocks" />
    <option name="text" path="Wrapping and Braces" hit="Keep line breaks inside text blocks" />
    <option name="keep" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="reformatting" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="when" path="Wrapping and Braces" hit="Keep when reformatting" />
    <option name="breaks" path="Wrapping and Braces" hit="Line breaks" />
    <option name="line" path="Wrapping and Braces" hit="Line breaks" />
    <option name="annotations" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="local" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="variable" path="Wrapping and Braces" hit="Local variable annotations" />
    <option name="annotations" path="Wrapping and Braces" hit="Method annotations" />
    <option name="method" path="Wrapping and Braces" hit="Method annotations" />
    <option name="arguments" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="call" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="method" path="Wrapping and Braces" hit="Method call arguments" />
    <option name="declaration" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="method" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="parameters" path="Wrapping and Braces" hit="Method declaration parameters" />
    <option name="method" path="Wrapping and Braces" hit="Method parentheses" />
    <option name="parentheses" path="Wrapping and Braces" hit="Method parentheses" />
    <option name="list" path="Wrapping and Braces" hit="Modifier list" />
    <option name="modifier" path="Wrapping and Braces" hit="Modifier list" />
    <option name="expressions" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="in" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="line" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="multiple" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="one" path="Wrapping and Braces" hit="Multiple expressions in one line" />
    <option name="after" path="Wrapping and Braces" hit="New line after '('" />
    <option name="line" path="Wrapping and Braces" hit="New line after '('" />
    <option name="new" path="Wrapping and Braces" hit="New line after '('" />
    <option name="after" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="line" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="new" path="Wrapping and Braces" hit="New line after '{'" />
    <option name="line" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="next" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="on" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="operation" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="sign" path="Wrapping and Braces" hit="Operation sign on next line" />
    <option name="other" path="Wrapping and Braces" hit="Other" />
    <option name="annotations" path="Wrapping and Braces" hit="Parameter annotations" />
    <option name="parameter" path="Wrapping and Braces" hit="Parameter annotations" />
    <option name="line" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="new" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="on" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="place" path="Wrapping and Braces" hit="Place ')' on new line" />
    <option name="line" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="new" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="on" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="place" path="Wrapping and Braces" hit="Place '}' on new line" />
    <option name="blocks" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple blocks in one line" />
    <option name="classes" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple classes in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="lambdas" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple lambdas in one line" />
    <option name="in" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="line" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="methods" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="one" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="simple" path="Wrapping and Braces" hit="Simple methods in one line" />
    <option name="else" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="if" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="special" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="treatment" path="Wrapping and Braces" hit="Special 'else if' treatment" />
    <option name="call" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="chain" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="over" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="priority" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="take" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="wrapping" path="Wrapping and Braces" hit="Take priority over call chain wrapping" />
    <option name="operation" path="Wrapping and Braces" hit="Ternary operation" />
    <option name="ternary" path="Wrapping and Braces" hit="Ternary operation" />
    <option name="keyword" path="Wrapping and Braces" hit="Throws keyword" />
    <option name="throws" path="Wrapping and Braces" hit="Throws keyword" />
    <option name="list" path="Wrapping and Braces" hit="Throws list" />
    <option name="throws" path="Wrapping and Braces" hit="Throws list" />
    <option name="guides" path="Wrapping and Braces" hit="Visual guides" />
    <option name="visual" path="Wrapping and Braces" hit="Visual guides" />
    <option name="reformatting" path="Wrapping and Braces" hit="When reformatting" />
    <option name="when" path="Wrapping and Braces" hit="When reformatting" />
    <option name="after" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="list" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="modifier" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap after modifier list" />
    <option name="at" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="margin" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="right" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap at right margin" />
    <option name="call" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="first" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap first call" />
    <option name="long" path="Wrapping and Braces" hit="Wrap long text" />
    <option name="text" path="Wrapping and Braces" hit="Wrap long text" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap long text" />
    <option name="on" path="Wrapping and Braces" hit="Wrap on typing" />
    <option name="typing" path="Wrapping and Braces" hit="Wrap on typing" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap on typing" />
    <option name="block" path="Wrapping and Braces" hit="Wrap text inside block quotes" />
    <option name="inside" path="Wrapping and Braces" hit="Wrap text inside block quotes" />
    <option name="quotes" path="Wrapping and Braces" hit="Wrap text inside block quotes" />
    <option name="text" path="Wrapping and Braces" hit="Wrap text inside block quotes" />
    <option name="wrap" path="Wrapping and Braces" hit="Wrap text inside block quotes" />
  </configurable>
  <configurable id="Settings.Markdown" configurable_name="Markdown">
    <option name="be" hit="Detect commands that can be run right from Markdown files" />
    <option name="can" hit="Detect commands that can be run right from Markdown files" />
    <option name="commands" hit="Detect commands that can be run right from Markdown files" />
    <option name="detect" hit="Detect commands that can be run right from Markdown files" />
    <option name="files" hit="Detect commands that can be run right from Markdown files" />
    <option name="from" hit="Detect commands that can be run right from Markdown files" />
    <option name="markdown" hit="Detect commands that can be run right from Markdown files" />
    <option name="right" hit="Detect commands that can be run right from Markdown files" />
    <option name="run" hit="Detect commands that can be run right from Markdown files" />
    <option name="that" hit="Detect commands that can be run right from Markdown files" />
    <option name="but" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="different" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="documents" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="docx" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="extensions" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="group" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="html" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="md" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="name" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="pdf" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="same" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="the" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="with" hit="Group documents with the same name, but different extensions (.md, .html, .docx, .pdf)" />
    <option name="code" hit="Inject languages in code fences" />
    <option name="fences" hit="Inject languages in code fences" />
    <option name="in" hit="Inject languages in code fences" />
    <option name="inject" hit="Inject languages in code fences" />
    <option name="languages" hit="Inject languages in code fences" />
    <option name="markdown" hit="Markdown" />
    <option name="pandoc" hit="Pandoc Settings" />
    <option name="settings" hit="Pandoc Settings" />
    <option name="executable" hit="Path to Pandoc executable:" />
    <option name="pandoc" hit="Path to Pandoc executable:" />
    <option name="path" hit="Path to Pandoc executable:" />
    <option name="to" hit="Path to Pandoc executable:" />
    <option name="from" hit="Save images from Microsoft Word to:" />
    <option name="images" hit="Save images from Microsoft Word to:" />
    <option name="microsoft" hit="Save images from Microsoft Word to:" />
    <option name="save" hit="Save images from Microsoft Word to:" />
    <option name="to" hit="Save images from Microsoft Word to:" />
    <option name="word" hit="Save images from Microsoft Word to:" />
    <option name="code" hit="Show problems in code fences" />
    <option name="fences" hit="Show problems in code fences" />
    <option name="in" hit="Show problems in code fences" />
    <option name="problems" hit="Show problems in code fences" />
    <option name="show" hit="Show problems in code fences" />
    <option name="test" hit="Test" />
    <option name="are" hit="There are no available preview providers." />
    <option name="available" hit="There are no available preview providers." />
    <option name="no" hit="There are no available preview providers." />
    <option name="preview" hit="There are no available preview providers." />
    <option name="providers" hit="There are no available preview providers." />
    <option name="there" hit="There are no available preview providers." />
  </configurable>
</options>