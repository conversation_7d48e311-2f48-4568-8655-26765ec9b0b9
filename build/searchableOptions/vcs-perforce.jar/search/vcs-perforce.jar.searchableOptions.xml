<options>
  <configurable id="preferences.keymap" configurable_name="Keymap">
    <option name="check" path="ActionManager" hit="Check Login State" />
    <option name="login" path="ActionManager" hit="Check Login State" />
    <option name="state" path="ActionManager" hit="Check Login State" />
    <option name="checkout" path="ActionManager" hit="Checkout the file or dir from Perforce" />
    <option name="dir" path="ActionManager" hit="Checkout the file or dir from Perforce" />
    <option name="file" path="ActionManager" hit="Checkout the file or dir from Perforce" />
    <option name="from" path="ActionManager" hit="Checkout the file or dir from Perforce" />
    <option name="or" path="ActionManager" hit="Checkout the file or dir from Perforce" />
    <option name="perforce" path="ActionManager" hit="Checkout the file or dir from Perforce" />
    <option name="the" path="ActionManager" hit="Checkout the file or dir from Perforce" />
    <option name="edit" path="ActionManager" hit="Edit" />
    <option name="force" path="ActionManager" hit="Force Refresh" />
    <option name="refresh" path="ActionManager" hit="Force Refresh" />
    <option name="all" path="ActionManager" hit="Force Refresh (re-query all files states)" />
    <option name="files" path="ActionManager" hit="Force Refresh (re-query all files states)" />
    <option name="force" path="ActionManager" hit="Force Refresh (re-query all files states)" />
    <option name="re-query" path="ActionManager" hit="Force Refresh (re-query all files states)" />
    <option name="refresh" path="ActionManager" hit="Force Refresh (re-query all files states)" />
    <option name="states" path="ActionManager" hit="Force Refresh (re-query all files states)" />
    <option name="perforce" path="ActionManager" hit="Perforce" />
    <option name="perforce" path="ActionManager" hit="Perforce Workspace" />
    <option name="workspace" path="ActionManager" hit="Perforce Workspace" />
    <option name="0" path="ActionManager" hit="Perforce workspace: {0}" />
    <option name="perforce" path="ActionManager" hit="Perforce workspace: {0}" />
    <option name="workspace" path="ActionManager" hit="Perforce workspace: {0}" />
    <option name="all" path="ActionManager" hit="Resolve All" />
    <option name="resolve" path="ActionManager" hit="Resolve All" />
    <option name="resolve" path="ActionManager" hit="Resolve..." />
    <option name="files" path="ActionManager" hit="Revert Unchanged Files" />
    <option name="revert" path="ActionManager" hit="Revert Unchanged Files" />
    <option name="unchanged" path="ActionManager" hit="Revert Unchanged Files" />
    <option name="graph" path="ActionManager" hit="Revision Graph..." />
    <option name="revision" path="ActionManager" hit="Revision Graph..." />
    <option name="changelist" path="ActionManager" hit="Set jobs for changelist" />
    <option name="for" path="ActionManager" hit="Set jobs for changelist" />
    <option name="jobs" path="ActionManager" hit="Set jobs for changelist" />
    <option name="set" path="ActionManager" hit="Set jobs for changelist" />
    <option name="in" path="ActionManager" hit="Shelve in Perforce..." />
    <option name="perforce" path="ActionManager" hit="Shelve in Perforce..." />
    <option name="shelve" path="ActionManager" hit="Shelve in Perforce..." />
    <option name="revision" path="ActionManager" hit="Sync to This Revision" />
    <option name="sync" path="ActionManager" hit="Sync to This Revision" />
    <option name="this" path="ActionManager" hit="Sync to This Revision" />
    <option name="to" path="ActionManager" hit="Sync to This Revision" />
    <option name="time-lapse" path="ActionManager" hit="Time-lapse View..." />
    <option name="view" path="ActionManager" hit="Time-lapse View..." />
    <option name="and" path="ActionManager" hit="Toggle between offline and online mode" />
    <option name="between" path="ActionManager" hit="Toggle between offline and online mode" />
    <option name="mode" path="ActionManager" hit="Toggle between offline and online mode" />
    <option name="offline" path="ActionManager" hit="Toggle between offline and online mode" />
    <option name="online" path="ActionManager" hit="Toggle between offline and online mode" />
    <option name="toggle" path="ActionManager" hit="Toggle between offline and online mode" />
    <option name="unshelve" path="ActionManager" hit="Unshelve" />
    <option name="and" path="ActionManager" hit="Unshelve and Delete" />
    <option name="delete" path="ActionManager" hit="Unshelve and Delete" />
    <option name="unshelve" path="ActionManager" hit="Unshelve and Delete" />
    <option name="changelist" path="ActionManager" hit="Unshelve selected changes to the default changelist" />
    <option name="changes" path="ActionManager" hit="Unshelve selected changes to the default changelist" />
    <option name="default" path="ActionManager" hit="Unshelve selected changes to the default changelist" />
    <option name="selected" path="ActionManager" hit="Unshelve selected changes to the default changelist" />
    <option name="the" path="ActionManager" hit="Unshelve selected changes to the default changelist" />
    <option name="to" path="ActionManager" hit="Unshelve selected changes to the default changelist" />
    <option name="unshelve" path="ActionManager" hit="Unshelve selected changes to the default changelist" />
    <option name="and" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="changelist" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="changes" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="default" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="delete" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="from" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="selected" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="shelf" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="the" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="them" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="to" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="unshelve" path="ActionManager" hit="Unshelve selected changes to the default changelist and delete them from the shelf" />
    <option name="control" path="ActionManager" hit="Version Control" />
    <option name="version" path="ActionManager" hit="Version Control" />
    <option name="offline" path="ActionManager" hit="Work Offline" />
    <option name="work" path="ActionManager" hit="Work Offline" />
  </configurable>
  <configurable id="vcs.Perforce" configurable_name="Perforce">
    <option name="" hit="'/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log/p4output.log'" />
    <option name="build" hit="'/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log/p4output.log'" />
    <option name="idea-sandbox" hit="'/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log/p4output.log'" />
    <option name="ideaprojects" hit="'/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log/p4output.log'" />
    <option name="log" hit="'/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log/p4output.log'" />
    <option name="p4output" hit="'/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log/p4output.log'" />
    <option name="rpang" hit="'/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log/p4output.log'" />
    <option name="system" hit="'/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log/p4output.log'" />
    <option name="users" hit="'/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log/p4output.log'" />
    <option name="zigzag" hit="'/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log/p4output.log'" />
    <option name="commands" hit="Dump Perforce Commands" />
    <option name="dump" hit="Dump Perforce Commands" />
    <option name="perforce" hit="Dump Perforce Commands" />
    <option name="enable" hit="Enable Perforce Jobs support" />
    <option name="jobs" hit="Enable Perforce Jobs support" />
    <option name="perforce" hit="Enable Perforce Jobs support" />
    <option name="support" hit="Enable Perforce Jobs support" />
    <option name="executable" hit="Find ignored files using P4 executable" />
    <option name="files" hit="Find ignored files using P4 executable" />
    <option name="find" hit="Find ignored files using P4 executable" />
    <option name="ignored" hit="Find ignored files using P4 executable" />
    <option name="p4" hit="Find ignored files using P4 executable" />
    <option name="using" hit="Find ignored files using P4 executable" />
    <option name="file" hit="Log File:" />
    <option name="log" hit="Log File:" />
    <option name="executable" hit="Path to P4 executable:" />
    <option name="p4" hit="Path to P4 executable:" />
    <option name="path" hit="Path to P4 executable:" />
    <option name="to" hit="Path to P4 executable:" />
    <option name="executable" hit="Path to P4VC executable:" />
    <option name="p4vc" hit="Path to P4VC executable:" />
    <option name="path" hit="Path to P4VC executable:" />
    <option name="to" hit="Path to P4VC executable:" />
    <option name="perforce" hit="Perforce" />
    <option name="server" hit="Server timeout" />
    <option name="timeout" hit="Server timeout" />
    <option name="action" hit="Show branching history (for &quot;File History&quot; action, &quot;Compare With...&quot; list)" />
    <option name="branching" hit="Show branching history (for &quot;File History&quot; action, &quot;Compare With...&quot; list)" />
    <option name="compare" hit="Show branching history (for &quot;File History&quot; action, &quot;Compare With...&quot; list)" />
    <option name="file" hit="Show branching history (for &quot;File History&quot; action, &quot;Compare With...&quot; list)" />
    <option name="for" hit="Show branching history (for &quot;File History&quot; action, &quot;Compare With...&quot; list)" />
    <option name="history" hit="Show branching history (for &quot;File History&quot; action, &quot;Compare With...&quot; list)" />
    <option name="list" hit="Show branching history (for &quot;File History&quot; action, &quot;Compare With...&quot; list)" />
    <option name="show" hit="Show branching history (for &quot;File History&quot; action, &quot;Compare With...&quot; list)" />
    <option name="with" hit="Show branching history (for &quot;File History&quot; action, &quot;Compare With...&quot; list)" />
    <option name="changelists" hit="Show integrated changelists in committed changes" />
    <option name="changes" hit="Show integrated changelists in committed changes" />
    <option name="committed" hit="Show integrated changelists in committed changes" />
    <option name="in" hit="Show integrated changelists in committed changes" />
    <option name="integrated" hit="Show integrated changelists in committed changes" />
    <option name="show" hit="Show integrated changelists in committed changes" />
    <option name="automatically" hit="Switch to offline mode automatically if Perforce is unavailable" />
    <option name="if" hit="Switch to offline mode automatically if Perforce is unavailable" />
    <option name="is" hit="Switch to offline mode automatically if Perforce is unavailable" />
    <option name="mode" hit="Switch to offline mode automatically if Perforce is unavailable" />
    <option name="offline" hit="Switch to offline mode automatically if Perforce is unavailable" />
    <option name="perforce" hit="Switch to offline mode automatically if Perforce is unavailable" />
    <option name="switch" hit="Switch to offline mode automatically if Perforce is unavailable" />
    <option name="to" hit="Switch to offline mode automatically if Perforce is unavailable" />
    <option name="unavailable" hit="Switch to offline mode automatically if Perforce is unavailable" />
    <option name="" hit="Test Connection" />
    <option name="connection" hit="Test Connection" />
    <option name="test" hit="Test Connection" />
    <option name="authentication" hit="Use login authentication" />
    <option name="login" hit="Use login authentication" />
    <option name="use" hit="Use login authentication" />
    <option name="seconds" hit="seconds" />
  </configurable>
</options>