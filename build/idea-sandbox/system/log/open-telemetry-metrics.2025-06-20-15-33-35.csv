# OpenTelemetry Metrics report: .csv, 4 fields: 
# <metric name>, <period start, nanoseconds>, <period end, nanoseconds>, <metric value>
# See CsvMetricsExporter for details.
# 
# NAME, PERIOD_START_NANOS, PERIOD_END_NANOS, VALUE
VFS.fileByIdCache.hits,1750390415814000000,1750390422428000000,0
VFS.invertedFileNameIndex.requests,1750390415814000000,1750390422428000000,0
VFS.fileByIdCache.misses,1750390415814000000,1750390422428000000,0
VFS.fileChildByName,1750390415814000000,1750390422428000000,13
writeAction.median.wait.ms,1750390415814000000,1750390422428000000,0
writeAction.max.wait.ms,1750390415814000000,1750390422428000000,0
writeAction.wait.ms,1750390415814000000,1750390422428000000,0
writeAction.count,1750390415814000000,1750390422428000000,1
workspaceModel.init.bridges.ms,1750390415814000000,1750390422428000000,0
workspaceModel.updates.precise.ms,1750390415814000000,1750390422428000000,0
workspaceModel.cachedValueWithParameters.from.cache.ms,1750390415814000000,1750390422428000000,0
workspaceModel.initializing.ms,1750390415814000000,1750390422428000000,0
workspaceModel.entityStorageSnapshotImpl.instances.count,1750390415814000000,1750390422428000000,3
workspaceModel.cachedValue.total.get.ms,1750390415814000000,1750390422428000000,0
workspaceModel.cachedValue.from.cache.ms,1750390415814000000,1750390422428000000,0
workspaceModel.check.recursive.update.ms,1750390415814000000,1750390422428000000,0
workspaceModel.cachedValue.clear.ms,1750390415814000000,1750390422428000000,0
workspaceModel.full.replace.project.model.ms,1750390415814000000,1750390422428000000,0
workspaceModel.replace.project.model.ms,1750390415814000000,1750390422428000000,0
workspaceModel.update.unloaded.entities.ms,1750390415814000000,1750390422428000000,0
workspaceModel.updates.ms,1750390415814000000,1750390422428000000,0
workspaceModel.loading.total.ms,1750390415814000000,1750390422428000000,30
workspaceModel.cachedValue.calculated.ms,1750390415814000000,1750390422428000000,0
workspaceModel.to.snapshot.ms,1750390415814000000,1750390422428000000,0
workspaceModel.collect.changes.ms,1750390415814000000,1750390422428000000,0
workspaceModel.cachedValueWithParameters.total.get.ms,1750390415814000000,1750390422428000000,0
workspaceModel.updates.count,1750390415814000000,1750390422428000000,0
workspaceModel.cachedValueWithParameters.calculated.ms,1750390415814000000,1750390422428000000,0
workspaceModel.loading.from.cache.ms,1750390415814000000,1750390422428000000,23
workspaceModel.cachedValueWithParameters.clear.ms,1750390415814000000,1750390422428000000,0
workspaceModel.pre.handlers.ms,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.totalPagesWriteMs,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.heapBytesInUse,1750390415814000000,1750390422428000000,0
FilePageCache.pageHits,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.totalPagesAllocated,1750390415814000000,1750390422428000000,0
FilePageCache.totalPageDisposalsUs,1750390415814000000,1750390422428000000,1060
FilePageCacheLockFree.totalPagesHandedOver,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.totalClosedStoragesReclaimed,1750390415814000000,1750390422428000000,0
StorageLockContext.competingThreads.max,1750390415814000000,1750390422428000000,0
FilePageCache.disposedBuffers,1750390415814000000,1750390422428000000,196
FilePageCacheLockFree.totalPagesReadMs,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.totalBytesRead,1750390415814000000,1750390422428000000,0
DirectByteBufferAllocator.reclaimed,1750390415814000000,1750390422428000000,43
MappedFileStorage.totalTimeSpentOnMappingUs,1750390415814000000,1750390422428000000,7807
FileChannelInterruptsRetryer.totalRetriedAttempts,1750390415814000000,1750390422428000000,0
FilePageCache.totalPageLoadsUs,1750390415814000000,1750390422428000000,62514
FilePageCache.pageFastCacheHits,1750390415814000000,1750390422428000000,3878
MappedFileStorage.totalPagesMapped,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.totalBytesRequested,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.totalPagesWritten,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.housekeeperTurnsDone,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.totalPagesRequested,1750390415814000000,1750390422428000000,0
FilePageCache.pageLoadsAboveSizeThreshold,1750390415814000000,1750390422428000000,0
FilePageCache.maxCacheSizeInBytes,1750390415814000000,1750390422428000000,202375168
StorageLockContext.competingThreads.90P,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.housekeeperTimeSpentMs,1750390415814000000,1750390422428000000,0
MappedFileStorage.storages,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.housekeeperTurnsSkipped,1750390415814000000,1750390422428000000,0
FilePageCache.pageLoads,1750390415814000000,1750390422428000000,196
DirectByteBufferAllocator.totalSizeOfBuffersAllocatedInBytes,1750390415814000000,1750390422428000000,41943040
FilePageCache.uncachedFileAccess,1750390415814000000,1750390422428000000,110
FilePageCacheLockFree.totalPageAllocationsWaited,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.totalBytesWritten,1750390415814000000,1750390422428000000,0
DirectByteBufferAllocator.disposed,1750390415814000000,1750390422428000000,153
FilePageCache.capacityInBytes,1750390415814000000,1750390422428000000,503316478
FilePageCacheLockFree.totalNativeBytesAllocated,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.totalPagesReclaimed,1750390415814000000,1750390422428000000,0
FilePageCache.maxRegisteredFiles,1750390415814000000,1750390422428000000,537
FilePageCacheLockFree.totalHeapBytesAllocated,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.nativeBytesInUse,1750390415814000000,1750390422428000000,0
DirectByteBufferAllocator.hits,1750390415814000000,1750390422428000000,3
StorageLockContext.competingThreads.avg,1750390415814000000,1750390422428000000,0.0
DirectByteBufferAllocator.misses,1750390415814000000,1750390422428000000,193
DirectByteBufferAllocator.totalSizeOfBuffersCachedInBytes,1750390415814000000,1750390422428000000,41943040
FilePageCacheLockFree.totalHeapBytesReclaimed,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.totalNativeBytesReclaimed,1750390415814000000,1750390422428000000,0
MappedFileStorage.totalBytesMapped,1750390415814000000,1750390422428000000,0
FilePageCacheLockFree.totalPagesRequestsMs,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.modify.entity.ms,1750390415814000000,1750390422428000000,0
jps.app.storage.content.reader.load.component.ms,1750390415814000000,1750390422428000000,0
workspaceModel.global.apply.state.to.project.ms,1750390415814000000,1750390422428000000,0
jps.global.get.libraries.ms,1750390415814000000,1750390422428000000,0
jps.global.initialize.library.bridges.after.loading.ms,1750390415814000000,1750390422428000000,3
workspaceModel.global.apply.state.to.project.builder.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.mutable.vfurl.index.ms,1750390415814000000,1750390422428000000,0
workspaceModel.global.updates.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.resolve.ms,1750390415814000000,1750390422428000000,0
jps.global.handle.changed.events.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.has.same.entities.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.collect.changes.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.entities.by.source.ms,1750390415814000000,1750390422428000000,0
workspaceModel.moduleManagerBridge.load.module.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.apply.changes.from.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.entities.ms,1750390415814000000,1750390422428000000,2
jps.library.entities.serializer.load.entities.ms,1750390415814000000,1750390422428000000,0
workspaceModel.moduleManagerBridge.load.all.modules.ms,1750390415814000000,1750390422428000000,0
workspaceModel.moduleManagerBridge.create.module.instance.ms,1750390415814000000,1750390422428000000,0
jps.global.handle.before.change.events.ms,1750390415814000000,1750390422428000000,0
workspaceModel.global.updates.count,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.mutable.ext.mapping.ms,1750390415814000000,1750390422428000000,0
jps.save.global.entities.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.referrers.ms,1750390415814000000,1750390422428000000,0
workspaceModel.save.cache.to.file.ms,1750390415814000000,1750390422428000000,0
workspaceModel.moduleManagerBridge.newModule.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.replace.by.source.ms,1750390415814000000,1750390422428000000,0
workspaceModel.do.save.caches.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.remove.entity.ms,1750390415814000000,1750390422428000000,0
workspaceModel.moduleManagerBridge.set.unloadedModules.ms,1750390415814000000,1750390422428000000,0
jps.global.initialize.library.bridges.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.instances.count,1750390415814000000,1750390422428000000,3
workspaceModel.load.cache.from.file.ms,1750390415814000000,1750390422428000000,0
jps.global.get.library.ms,1750390415814000000,1750390422428000000,0
jps.load.initial.state.ms,1750390415814000000,1750390422428000000,12
jps.global.get.library.by.name.ms,1750390415814000000,1750390422428000000,0
jps.library.entities.serializer.save.entities.ms,1750390415814000000,1750390422428000000,0
workspaceModel.sync.entities.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.to.snapshot.ms,1750390415814000000,1750390422428000000,0
workspaceModel.moduleManagerBridge.build.module.graph.ms,1750390415814000000,1750390422428000000,0
workspaceModel.load.cache.metadata.from.file.ms,1750390415814000000,1750390422428000000,0
workspaceModel.moduleManagerBridge.new.nonPersistent.module.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.add.entity.ms,1750390415814000000,1750390422428000000,0
workspaceModel.moduleManagerBridge.get.modules.ms,1750390415814000000,1750390422428000000,0
workspaceModel.mutableEntityStorage.put.entity.ms,1750390415814000000,1750390422428000000,0
cacheStateStorage.get.duration,1750390415814000000,1750390422428000000,0
cacheStateStorage.set.duration,1750390415814000000,1750390422428000000,0
cacheStateStorage.set.counter,1750390415814000000,1750390422428000000,0
cacheStateStorage.get.counter,1750390415814000000,1750390422428000000,10
FlushQueue.queueSizeAvg,1750390415814000000,1750390422428000000,139.9388888888889
NonBlockingReadAction.finalizedExecutionsCount,1750390415814000000,1750390422428000000,15
FlushQueue.executionTime90PNs,1750390415814000000,1750390422428000000,58367
FlushQueue.waitingTime90PNs,1750390415814000000,1750390422428000000,2768240639
AWTEventQueue.dispatchTime90PNs,1750390415814000000,1750390422428000000,30335
AWTEventQueue.dispatchTimeAvgNs,1750390415814000000,1750390422428000000,3232768.816231884
AWTEventQueue.eventsDispatched,1750390415814000000,1750390422428000000,1725
FlushQueue.executionTimeAvgNs,1750390415814000000,1750390422428000000,51624.67301587301
FlushQueue.waitingTimeAvgNs,1750390415814000000,1750390422428000000,6.826247078857143E8
ReadAction.executionsCount,1750390415814000000,1750390422428000000,50104
FlushQueue.waitingTimeMaxNs,1750390415814000000,1750390422428000000,4395630591
FlushQueue.queueSize90P,1750390415814000000,1750390422428000000,391
FlushQueue.executionTimeMaxNs,1750390415814000000,1750390422428000000,18219007
NonBlockingReadAction.finalizedExecutionTimeUs,1750390415814000000,1750390422428000000,28404
NonBlockingReadAction.failedExecutionTimeUs,1750390415814000000,1750390422428000000,0
WriteAction.executionsCount,1750390415814000000,1750390422428000000,1
NonBlockingReadAction.failedExecutionsCount,1750390415814000000,1750390422428000000,0
FlushQueue.tasksExecuted,1750390415814000000,1750390422428000000,1260
AWTEventQueue.dispatchTimeTotalNS,1750390415814000000,1750390422428000000,5563714257
AWTEventQueue.dispatchTimeMaxNs,1750390415814000000,1750390422428000000,5469372415
FlushQueue.queueSizeMax,1750390415814000000,1750390422428000000,491
