2025-06-20 15:30:51,797 [      6]   INFO - #c.i.p.i.b.AppStarter - ------------------------------------------------------ IDE STARTED ------------------------------------------------------
2025-06-20 15:30:51,817 [     26]   INFO - #c.i.i.p.PluginManager - Using broken plugins file from IDE distribution
2025-06-20 15:30:51,828 [     37]   INFO - #c.i.u.i.PageCacheUtils - File page caching params:
2025-06-20 15:30:51,828 [     37]   INFO - #c.i.u.i.PageCacheUtils - 	DEFAULT_PAGE_SIZE: 10485760
2025-06-20 15:30:51,828 [     37]   INFO - #c.i.u.i.PageCacheUtils - 	Direct memory to use, max: 2126512128
2025-06-20 15:30:51,829 [     38]   INFO - #c.i.u.i.PageCacheUtils - 	FilePageCache: regular + lock-free (LOCK_FREE_PAGE_CACHE_ENABLED:true)
2025-06-20 15:30:51,829 [     38]   INFO - #c.i.u.EnvironmentUtil - loading shell env: /bin/zsh -l -i -c '/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/bin/printenv' > '/var/folders/42/vrxsf6r14p7f46p5q2040ggw0000gn/T/ij-env-tmp-dir1489586541621764998/ij-shell-env-data.tmp'
2025-06-20 15:30:51,831 [     40]   INFO - #c.i.u.i.PageCacheUtils - 	NEW_PAGE_CACHE_MEMORY_FRACTION: 0.20000000298023224
2025-06-20 15:30:51,832 [     41]   INFO - #c.i.u.i.PageCacheUtils - 	Regular FilePageCache: 503316478 bytes
2025-06-20 15:30:51,832 [     41]   INFO - #c.i.u.i.PageCacheUtils - 	New     FilePageCache: 125829122 bytes (+ up to 10.0% overflow)
2025-06-20 15:30:51,833 [     42]   INFO - #c.i.u.i.PageCacheUtils - 	DirectByteBuffers pool: 104857600 bytes
2025-06-20 15:30:51,834 [     43]   INFO - #c.i.p.i.b.AppStarter - JNA library (64-bit) loaded in 22 ms
2025-06-20 15:30:51,836 [     45]   INFO - #c.i.p.i.b.AppStarter - IDE: IntelliJ IDEA (build #IC-241.14494.240, Thu, 28 Mar 2024 05:12:00 GMT)
2025-06-20 15:30:51,837 [     46]   INFO - #c.i.p.i.b.AppStarter - OS: Mac OS X (15.5)
2025-06-20 15:30:51,837 [     46]   INFO - #c.i.p.i.b.AppStarter - JRE: 17.0.10+8-b1207.12, aarch64 (JetBrains s.r.o.)
2025-06-20 15:30:51,837 [     46]   INFO - #c.i.p.i.b.AppStarter - JVM: 17.0.10+8-b1207.12 (OpenJDK 64-Bit Server VM)
2025-06-20 15:30:51,839 [     48]   INFO - #c.i.p.i.b.AppStarter - PID: 65447
2025-06-20 15:30:51,856 [     65]   INFO - #c.i.p.i.b.AppStarter - JVM options: [-Daether.connector.resumeDownloads=false, -Dapple.awt.application.appearance=system, -Dapple.awt.fileDialogForDirectories=true, -Dapple.laf.useScreenMenuBar=true, -Dide.show.tips.on.startup.default.value=false, -Didea.auto.reload.plugins=true, -Didea.classpath.index.enabled=false, -Didea.config.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/config, -Didea.is.internal=true, -Didea.log.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log, -Didea.paths.selector=IdeaIC2024.1, -Didea.platform.prefix=Idea, -Didea.plugin.in.sandbox.mode=true, -Didea.plugins.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/plugins, -Didea.required.plugins.id=com.wontlost.zigzag, -Didea.smooth.progress=false, -Didea.system.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system, -Didea.vendor.name=JetBrains, -Dintellij.platform.runtime.repository.path=/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/modules/module-descriptors.jar, -Djava.system.class.loader=com.intellij.util.lang.PathClassLoader, -Djbr.catch.SIGABRT=true, -Djdk.attach.allowAttachSelf=true, -Djdk.http.auth.tunneling.disabledSchemes="", -Djdk.module.illegalAccess.silent=true, -Djna.boot.library.path=/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/lib/jna/aarch64, -Djna.noclasspath=true, -Djna.nosys=true, -Dpty4j.preferred.native.folder=/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/lib/pty4j, -Dsplash=true, -Dsun.io.useCanonCaches=false, -Dsun.java2d.metal=true, -XX:+HeapDumpOnOutOfMemoryError, -XX:-OmitStackTraceInFastThrow, -XX:+IgnoreUnrecognizedVMOptions, -XX:CICompilerCount=2, -XX:ReservedCodeCacheSize=512m, -XX:CompileCommand=exclude,com/intellij/openapi/vfs/impl/FilePartNodeRoot,trieDescend, -XX:SoftRefLRUPolicyMSPerMB=50, -javaagent:/Users/<USER>/IdeaProjects/zigzag/build/tmp/initializeIntelliJPlugin/coroutines-javaagent.jar, --add-opens=java.base/java.io=ALL-UNNAMED, --add-opens=java.base/java.lang=ALL-UNNAMED, --add-opens=java.base/java.lang.ref=ALL-UNNAMED, --add-opens=java.base/java.lang.reflect=ALL-UNNAMED, --add-opens=java.base/java.net=ALL-UNNAMED, --add-opens=java.base/java.nio=ALL-UNNAMED, --add-opens=java.base/java.nio.charset=ALL-UNNAMED, --add-opens=java.base/java.text=ALL-UNNAMED, --add-opens=java.base/java.time=ALL-UNNAMED, --add-opens=java.base/java.util=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED, --add-opens=java.base/jdk.internal.vm=ALL-UNNAMED, --add-opens=java.base/sun.nio.ch=ALL-UNNAMED, --add-opens=java.base/sun.nio.fs=ALL-UNNAMED, --add-opens=java.base/sun.security.ssl=ALL-UNNAMED, --add-opens=java.base/sun.security.util=ALL-UNNAMED, --add-opens=java.base/sun.net.dns=ALL-UNNAMED, --add-opens=java.desktop/com.apple.eawt=ALL-UNNAMED, --add-opens=java.desktop/com.apple.eawt.event=ALL-UNNAMED, --add-opens=java.desktop/com.apple.laf=ALL-UNNAMED, --add-opens=java.desktop/java.awt=ALL-UNNAMED, --add-opens=java.desktop/java.awt.dnd.peer=ALL-UNNAMED, --add-opens=java.desktop/java.awt.event=ALL-UNNAMED, --add-opens=java.desktop/java.awt.image=ALL-UNNAMED, --add-opens=java.desktop/java.awt.peer=ALL-UNNAMED, --add-opens=java.desktop/java.awt.font=ALL-UNNAMED, --add-opens=java.desktop/javax.swing=ALL-UNNAMED, --add-opens=java.desktop/javax.swing.plaf.basic=ALL-UNNAMED, --add-opens=java.desktop/javax.swing.text=ALL-UNNAMED, --add-opens=java.desktop/javax.swing.text.html=ALL-UNNAMED, --add-opens=java.desktop/sun.awt.datatransfer=ALL-UNNAMED, --add-opens=java.desktop/sun.awt.image=ALL-UNNAMED, --add-opens=java.desktop/sun.awt=ALL-UNNAMED, --add-opens=java.desktop/sun.font=ALL-UNNAMED, --add-opens=java.desktop/sun.java2d=ALL-UNNAMED, --add-opens=java.desktop/sun.lwawt=ALL-UNNAMED, --add-opens=java.desktop/sun.lwawt.macosx=ALL-UNNAMED, --add-opens=java.desktop/sun.swing=ALL-UNNAMED, --add-opens=java.desktop/com.sun.java.swing=ALL-UNNAMED, --add-opens=jdk.attach/sun.tools.attach=ALL-UNNAMED, --add-opens=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED, --add-opens=jdk.internal.jvmstat/sun.jvmstat.monitor=ALL-UNNAMED, --add-opens=jdk.jdi/com.sun.tools.jdi=ALL-UNNAMED, -Xms128m, -Xmx2048m, -Dfile.encoding=UTF-8, -Duser.country=NZ, -Duser.language=en, -Duser.variant, -ea]
2025-06-20 15:30:51,857 [     66]   INFO - #c.i.p.i.b.AppStarter - args: traverseUI /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions true
2025-06-20 15:30:51,858 [     67]   INFO - #c.i.p.i.b.AppStarter - library path: /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-06-20 15:30:51,858 [     67]   INFO - #c.i.p.i.b.AppStarter - boot library path: /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains/jbre/jbr_jcef-17.0.10-osx-aarch64-b1207.12/extracted/jbr_jcef-17.0.10-osx-aarch64-b1207.12/Contents/Home/lib
2025-06-20 15:30:51,865 [     74]   INFO - #c.i.p.i.b.AppStarter - locale=en_NZ JNU=UTF-8 file.encoding=UTF-8
    idea.config.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/config
    idea.system.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system
    idea.plugins.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/plugins
    idea.log.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log
2025-06-20 15:30:51,868 [     77]   INFO - #c.i.p.i.b.AppStarter - CPU cores: 10; ForkJoinPool.commonPool: java.util.concurrent.ForkJoinPool@38d0be2d[Running, parallelism = 10, size = 0, active = 0, running = 0, steals = 0, tasks = 0, submissions = 0]; factory: com.intellij.concurrency.IdeaForkJoinWorkerThreadFactory@23b98bad
2025-06-20 15:30:51,871 [     80]   INFO - #c.i.i.p.PluginManager - Module intellij.sh.python is not enabled because dependency intellij.python.community.impl is not available
Module intellij.groovy/ant is not enabled because dependency AntSupport is not available
Module intellij.qodana.jvm.swagger is not enabled because dependency com.intellij.swagger is not available
Module intellij.qodana.python is not enabled because dependency intellij.python is not available
Module intellij.qodana.php is not enabled because dependency com.jetbrains.php is not available
Module intellij.qodana.js is not enabled because dependency JavaScript is not available
Module intellij.qodana.inspectionKts.js is not enabled because dependency JavaScript is not available
Module intellij.qodana.go is not enabled because dependency intellij.go.coverage is not available
Module intellij.cwm.plugin.elevation is not enabled because dependency intellij.execution.process.elevation is not available
Module intellij.packageChecker.php is not enabled because dependency com.jetbrains.php is not available
Module intellij.packageChecker.python is not enabled because dependency intellij.python.community.impl is not available
Module intellij.packageChecker.javascript is not enabled because dependency JavaScript is not available
Module intellij.packageChecker.go is not enabled because dependency org.jetbrains.plugins.go is not available
2025-06-20 15:30:51,885 [     94]   INFO - #c.i.i.p.PluginManager - Loaded bundled plugins: IDEA CORE (241.14494.240), TextMate Bundles (241.14494.240), Images (241.14494.240), Copyright (241.14494.240), EditorConfig (241.14494.240), IDE Services / Provisioner API (241.14494.240), Machine Learning in Search Everywhere (241.14494.240), YAML (241.14494.240), JetBrains maven model api classes (241.14494.240), JetBrains Repository Search (241.14494.240), Maven server api classes (241.14494.240), Markdown (241.14494.240), Terminal (241.14494.240), Mercurial (241.14494.240), Properties (241.14494.240), Gradle (241.14494.240), NetBeans Keymap (241.14494.240), JetBrains Marketplace Licensing (241.14494.240), com.intellij.dev (241.14494.240), Remote Execution Agent (241.14494.240), Shell Script (241.14494.240), HTML Tools (241.14494.240), Visual Studio Keymap (241.14494.240), Machine Learning Code Completion (241.14494.240), Toml (241.14494.240), WebP Support (241.14494.240), Performance Testing (241.14494.240), Grazie Lite (241.14494.240), Eclipse Keymap (241.14494.240), Perforce Helix Core (241.14494.240), GitLab (241.14494.240), IDE Features Trainer (241.14494.240), Java (241.14494.240), com.intellij.tracing.ide (241.14494.240), Java Bytecode Decompiler (241.14494.240), Java Stream Debugger (241.14494.240), Task Management (241.14494.240), GitHub (241.14494.240), JUnit (241.14494.240), Eclipse Interoperability (241.14494.240), IntelliLang (241.14494.240), Groovy (241.14494.240), JavaFX (241.14494.240), Turbo Complete (241.14494.240), Kotlin (241.14494.240-IJ), Gradle DSL API (241.14494.240), Java Internationalization (241.14494.240), UI Designer (241.14494.240), Bytecode Viewer (241.14494.240), Maven (241.14494.240), TestNG (241.14494.240), Code Coverage for Java (241.14494.240), Gradle-Java (241.14494.240), Gradle-Java-Analysis (241.14494.240), Gradle-Maven (241.14494.240), Java IDE Customization (241.14494.240), Settings Sync (241.14494.240), Git (241.14494.240), Qodana (241.14494.240), Configuration Script (241.14494.240), Shared Indexes (241.14494.240), Code With Me (241.14494.240), Subversion (241.14494.240), Package Checker (241.14494.240), Gradle Dependency Updater Implementation (241.14494.240), Async Profiler for IDE Performance Testing (241.14494.240)
2025-06-20 15:30:51,885 [     94]   INFO - #c.i.i.p.PluginManager - Loaded custom plugins: Zigzag (1.0.3)
2025-06-20 15:30:51,940 [    149]   INFO - c.i.p.i.IdeFingerprint - Calculated dependencies fingerprint in 11 ms (hash=3kp3r3cytob16, buildTime=1711602720, appVersion=IC-241.14494.240)
2025-06-20 15:30:52,003 [    212]   INFO - #c.i.a.o.PathMacrosImpl - Loaded path macros: {MAVEN_REPOSITORY=/Users/<USER>/.m2/repository}
2025-06-20 15:30:52,028 [    237]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS uses 'fast' names enumerator (over mmapped file)
2025-06-20 15:30:52,029 [    238]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS uses content storage over memory-mapped file, with compression algo: LZ4[ > 8000b ]
2025-06-20 15:30:52,029 [    238]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS uses streamlined attributes storage (over mmapped file)
2025-06-20 15:30:52,505 [    714]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS: impl (expected) version=846397, 0 file records, 0 content blobs
2025-06-20 15:30:52,515 [    724]   INFO - #c.i.o.v.n.p.FSRecordsImpl - VFS initialized: 489 ms, 0 failed attempts, 0 error(s) were recovered
2025-06-20 15:30:52,515 [    724]   INFO - #c.i.o.v.n.p.FSRecordsImpl - VFS scanned: file-by-name index was populated
2025-06-20 15:30:52,929 [   1138]   INFO - #c.i.u.EnvironmentUtil - shell environment loaded (36 vars)
2025-06-20 15:30:52,937 [   1146]   INFO - #c.i.o.v.n.p.FSRecords - VFS health-check enabled: first after 600000 ms, and each following 43200000 ms, wrap in RA: true
2025-06-20 15:30:52,942 [   1151]   INFO - #c.i.i.p.ExpiredPluginsState - Plugins to skip: []
2025-06-20 15:30:53,056 [   1265]   INFO - #o.j.i.BuiltInServerManager - built-in server started, port 63343
2025-06-20 15:30:53,067 [   1276]   INFO - #c.i.u.n.s.CertificateManager - Default SSL context initialized
2025-06-20 15:30:53,092 [   1301]   INFO - #c.i.o.v.i.l.NativeFileWatcherImpl - Native file watcher is disabled
2025-06-20 15:30:53,143 [   1352]   INFO - #c.i.i.s.p.i.SharedIndexMainZipStorage - Shared Indexes Storage is opened and empty
2025-06-20 15:30:53,146 [   1355]   WARN - #c.i.i.s.p.i.BundledSharedIndexProvider - Bundled shared index is not found at: /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/jdk-shared-indexes
2025-06-20 15:30:53,319 [   1528]   INFO - #c.i.u.i.FileBasedIndexImpl - Indices to be built:java.source.module.name(v = 4),java.binary.plus.expression(v = 4),json.file.root.values(v = 5),org.jetbrains.kotlin.idea.vfilefinder.KotlinStdlibIndex(v = 1),XmlTagNames(v = 1),org.jetbrains.kotlin.idea.vfilefinder.KotlinPartialPackageNamesIndex(v = 5),FilenameIndex(v = 258),XmlNamespaces(v = 8),Stubs(v = 46),org.jetbrains.kotlin.idea.versions.KotlinJvmMetadataVersionIndex(v = 5),html5.custom.attributes.index(v = 1),org.jetbrains.kotlin.idea.vfilefinder.KotlinShortClassNameFileIndex(v = 3),javafx.id.name(v = 3),KotlinTopLevelCallableByPackageShortNameIndex(v = 3),filetypes(v = 4096),yaml.keys.name(v = 1),xmlProperties(v = 2),TodoIndex(v = 13),bytecodeAnalysis(v = 11),fileIncludes(v = 6),java.auto.module.name(v = 6),KotlinTopLevelClassLikeDeclarationByPackageShortNameIndex(v = 3),org.jetbrains.kotlin.idea.vfilefinder.KotlinMetadataFileIndex(v = 2),HtmlTagIdIndex(v = 3),KotlinPackageSourcesMemberNamesIndex(v = 2),java.null.method.argument(v = 1),javafx.custom.component(v = 2),org.jetbrains.kotlin.idea.vfilefinder.KotlinJavaScriptMetaFileIndex(v = 4),FrameworkDetectionIndex(v = 65536),org.jetbrains.kotlin.idea.vfilefinder.KotlinMetadataFilePackageIndex(v = 2),org.jetbrains.kotlin.idea.vfilefinder.KotlinClassFileIndex(v = 3),org.jetbrains.kotlin.idea.vfilefinder.KotlinBuiltInsMetadataIndex(v = 4),KotlinBinaryRootToPackageIndex(v = 5),Trigram.Index(v = 4),com.intellij.uiDesigner.FormClassIndex(v = 0),IdIndex(v = 22),SchemaTypeInheritance(v = 2),org.jetbrains.kotlin.idea.vfilefinder.KotlinJvmModuleAnnotationsIndex(v = 1),org.jetbrains.kotlin.idea.vfilefinder.KlibMetaFileIndex(v = 4),editorconfig.index.name(v = 5),DomFileIndex(v = 0),RelaxSymbolIndex(v = 0),org.jetbrains.kotlin.idea.versions.KotlinJsMetadataVersionIndex(v = 3),java.fun.expression(v = 6),org.jetbrains.kotlin.idea.vfilefinder.KotlinModuleMappingIndex(v = 5),JavaFxControllerClassIndex(v = 1)
2025-06-20 15:30:53,321 [   1530]   INFO - #c.i.u.i.FileBasedIndexImpl - Using nice flusher for indexes
2025-06-20 15:30:53,324 [   1533]   INFO - #c.i.u.i.IndexDataInitializer - Index data initialization done: 237 ms. Initialized indexes: [FilenameIndex, RelaxSymbolIndex, DomFileIndex, XmlTagNames, Trigram.Index, filetypes, fileIncludes, FrameworkDetectionIndex, IdIndex, XmlNamespaces, html5.custom.attributes.index, TodoIndex, SchemaTypeInheritance, json.file.root.values, java.auto.module.name, editorconfig.index.name, xmlProperties, yaml.keys.name, HtmlTagIdIndex, java.fun.expression, java.null.method.argument, bytecodeAnalysis, java.binary.plus.expression, java.source.module.name, Stubs, JavaFxControllerClassIndex, javafx.id.name, javafx.custom.component, org.jetbrains.kotlin.idea.versions.KotlinJsMetadataVersionIndex, org.jetbrains.kotlin.idea.versions.KotlinJvmMetadataVersionIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinMetadataFileIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinModuleMappingIndex, KotlinPackageSourcesMemberNamesIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinClassFileIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinMetadataFilePackageIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinJavaScriptMetaFileIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinJvmModuleAnnotationsIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinPartialPackageNamesIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinStdlibIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinBuiltInsMetadataIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinShortClassNameFileIndex, org.jetbrains.kotlin.idea.vfilefinder.KlibMetaFileIndex, KotlinBinaryRootToPackageIndex, KotlinTopLevelClassLikeDeclarationByPackageShortNameIndex, KotlinTopLevelCallableByPackageShortNameIndex, com.intellij.uiDesigner.FormClassIndex].
2025-06-20 15:30:53,445 [   1654]   INFO - #c.i.p.s.StubIndexImpl - Following stub indices will be built:org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelTypeAliasFqNameIndex(v = 2),java.anonymous.baseref(v = 2),gr.annot.members(v = 5),properties.index(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelPropertyFqnNameIndex(v = 2),java.class.extlist(v = 3),org.jetbrains.kotlin.idea.stubindex.KotlinFunctionShortNameIndex(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelFunctionFqnNameIndex(v = 2),KotlinTopLevelPropertyByPackageIndex(v = 2),KotlinProbablyContractedFunctionShortNameIndex(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinFilePartClassIndex(v = 2),KotlinFileFacadeClassByPackageIndex(v = 2),gr.class.super(v = 5),KotlinProbablyNothingFunctionShortNameIndex(v = 2),java.field.name(v = 2),java.annotations(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinAnnotationsIndex(v = 3),gr.field.name(v = 5),org.jetbrains.kotlin.idea.stubindex.KotlinExactPackagesIndex(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinSuperClassIndex(v = 2),java.unnamed.class(v = 2),markdown.header.anchor(v = 2),KotlinTypeAliasByExpansionShortNameIndex(v = 2),KotlinOverridableInternalMembersShortNameIndex(v = 2),KotlinTopLevelExtensionsByReceiverTypeIndex(v = 3),org.jetbrains.kotlin.idea.stubindex.KotlinSubclassObjectNameIndex(v = 2),KotlinTopLevelTypeAliasByPackageIndex(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinPropertyShortNameIndex(v = 2),KotlinProbablyNothingPropertyShortNameIndex(v = 2),markdown.header(v = 2),KotlinExtensionsInObjectsByReceiverTypeIndex(v = 2),dom.elementClass(v = 0),org.jetbrains.kotlin.idea.stubindex.KotlinFullClassNameIndex(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinClassShortNameIndex(v = 2),gr.anonymous.class(v = 6),org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelClassByPackageIndex(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinFileFacadeShortNameIndex(v = 2),gr.class.fqn.s(v = 5),jvm.static.member.name(v = 2),dom.namespaceKey(v = 1),org.jetbrains.kotlin.idea.stubindex.KotlinInnerTypeAliasClassIdIndex(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinScriptFqnIndex(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinJvmNameAnnotationIndex(v = 2),java.module.name(v = 4),java.method.name(v = 2),kotlin.primeIndexKey(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinMultifileClassPartIndex(v = 2),gr.script.class(v = 5),gr.method.name(v = 5),jvm.static.member.type(v = 2),org.jetbrains.kotlin.idea.stubindex.KotlinTypeAliasShortNameIndex(v = 2),gr.annot.method.name(v = 5),KotlinTopLevelFunctionByPackageIndex(v = 2),java.class.fqn(v = 1),org.jetbrains.kotlin.idea.stubindex.KotlinFileFacadeFqNameIndex(v = 2),java.class.shortname(v = 4),gr.script.fqn.s(v = 5),java.method.parameter.types(v = 3)
2025-06-20 15:30:53,446 [   1655]   INFO - #c.i.u.i.IndexDataInitializer - Index data initialization done: 122 ms. Initialized stub indexes: {gr.method.name, KotlinOverridableInternalMembersShortNameIndex, gr.anonymous.class, jvm.static.member.name, markdown.header, gr.annot.method.name, KotlinTopLevelPropertyByPackageIndex, java.unnamed.class, org.jetbrains.kotlin.idea.stubindex.KotlinPropertyShortNameIndex, gr.annot.members, KotlinTopLevelExtensionsByReceiverTypeIndex, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelTypeAliasFqNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinSubclassObjectNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelFunctionFqnNameIndex, KotlinTypeAliasByExpansionShortNameIndex, dom.elementClass, gr.field.name, KotlinTopLevelFunctionByPackageIndex, org.jetbrains.kotlin.idea.stubindex.KotlinFileFacadeFqNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinInnerTypeAliasClassIdIndex, KotlinExtensionsInObjectsByReceiverTypeIndex, org.jetbrains.kotlin.idea.stubindex.KotlinAnnotationsIndex, gr.class.fqn.s, gr.class.super, properties.index, org.jetbrains.kotlin.idea.stubindex.KotlinTypeAliasShortNameIndex, dom.namespaceKey, org.jetbrains.kotlin.idea.stubindex.KotlinMultifileClassPartIndex, jvm.static.member.type, java.class.fqn, org.jetbrains.kotlin.idea.stubindex.KotlinExactPackagesIndex, org.jetbrains.kotlin.idea.stubindex.KotlinFunctionShortNameIndex, KotlinFileFacadeClassByPackageIndex, org.jetbrains.kotlin.idea.stubindex.KotlinSuperClassIndex, KotlinProbablyContractedFunctionShortNameIndex, KotlinProbablyNothingPropertyShortNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinFilePartClassIndex, KotlinTopLevelTypeAliasByPackageIndex, java.anonymous.baseref, org.jetbrains.kotlin.idea.stubindex.KotlinFullClassNameIndex, java.field.name, org.jetbrains.kotlin.idea.stubindex.KotlinJvmNameAnnotationIndex, java.class.shortname, java.module.name, java.annotations, gr.script.fqn.s, org.jetbrains.kotlin.idea.stubindex.KotlinFileFacadeShortNameIndex, java.method.name, KotlinProbablyNothingFunctionShortNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinClassShortNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinScriptFqnIndex, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelClassByPackageIndex, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelPropertyFqnNameIndex, java.class.extlist, gr.script.class, java.method.parameter.types, markdown.header.anchor, kotlin.primeIndexKey}.
2025-06-20 15:30:54,536 [   2745]   INFO - #c.i.i.p.n.PluginsGroupComponentWithProgress - Marketplace tab: loading stopped
2025-06-20 15:30:54,548 [   2757]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Staff Picks' group load started
2025-06-20 15:30:54,612 [   2821]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Staff Picks' group load finished
2025-06-20 15:30:54,612 [   2821]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'New and Updated' group load started
2025-06-20 15:30:54,668 [   2877]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'New and Updated' group load finished
2025-06-20 15:30:54,668 [   2877]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Downloads' group load started
2025-06-20 15:30:55,014 [   3223]   INFO - #c.i.w.i.i.j.s.JpsGlobalModelSynchronizerImpl - Loading global entities com.intellij.platform.workspace.jps.entities.SdkEntity from files
2025-06-20 15:30:55,016 [   3225]   INFO - #c.i.w.i.i.j.s.JpsGlobalModelSynchronizerImpl - Loading global entities com.intellij.platform.workspace.jps.entities.LibraryEntity from files
2025-06-20 15:30:55,034 [   3243]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Downloads' group load finished
2025-06-20 15:30:55,034 [   3243]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Rated' group load started
2025-06-20 15:30:55,088 [   3297]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Rated' group load finished
2025-06-20 15:30:55,682 [   3891]   INFO - #c.i.o.a.i.NonBlockingReadActionImpl - OTel monitoring for NonBlockingReadAction is enabled
2025-06-20 15:30:57,585 [   5794]   INFO - #c.i.r.OsRegistryConfigProvider - Looking for 'lobbyServerUrl' value in [/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Library/Application Support/JetBrains/CodeWithMe/config.json, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/config.json]
2025-06-20 15:30:57,585 [   5794]   INFO - #c.i.r.OsRegistryConfigProvider - OS provided value for 'lobbyServerUrl' is not found
2025-06-20 15:30:57,585 [   5794]   INFO - #c.j.r.p.c.u.CodeWithMeSystemSettings - Setting default value='https://code-with-me.jetbrains.com'
2025-06-20 15:30:57,596 [   5805]   INFO - #c.i.r.OsRegistryConfigProvider - Looking for 'lobbyServerUrl' value in [/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Library/Application Support/JetBrains/CodeWithMe/config.json, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/config.json]
2025-06-20 15:30:57,597 [   5806]   INFO - #c.i.r.OsRegistryConfigProvider - OS provided value for 'lobbyServerUrl' is not found
2025-06-20 15:30:57,597 [   5806]   INFO - #c.j.r.p.c.u.CodeWithMeSystemSettings - Setting default value='https://code-with-me.jetbrains.com'
2025-06-20 15:30:58,635 [   6844]   WARN - #c.i.u.j.JBCefApp - JCEF is manually disabled in headless env via 'ide.browser.jcef.headless.enabled=false'
2025-06-20 15:30:59,156 [   7365]   INFO - STDOUT - Found 207 configurables
2025-06-20 15:30:59,165 [   7374]   INFO - #c.i.i.p.n.PluginsGroupComponentWithProgress - Marketplace tab: loading stopped
2025-06-20 15:30:59,556 [   7765]   WARN - #c.i.o.a.ActionStub - ActionGroup should be registered using <group> tag: id="GitBranchesTreePopupFilterSeparatorWithText" class="git4idea.ui.branch.popup.GitBranchesTreePopupFilterSeparatorWithText"
2025-06-20 15:30:59,936 [   8145]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/textmate.jar/search/textmate.jar.searchableOptions.xml
2025-06-20 15:30:59,939 [   8148]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/markdown.jar/search/markdown.jar.searchableOptions.xml
2025-06-20 15:30:59,939 [   8148]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/IntelliLang.jar/search/IntelliLang.jar.searchableOptions.xml
2025-06-20 15:30:59,940 [   8149]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/eclipse.jar/search/eclipse.jar.searchableOptions.xml
2025-06-20 15:30:59,940 [   8149]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/sh.jar/search/sh.jar.searchableOptions.xml
2025-06-20 15:30:59,941 [   8150]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/tasks-core.jar/search/tasks-core.jar.searchableOptions.xml
2025-06-20 15:30:59,941 [   8150]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-coverage.jar/search/java-coverage.jar.searchableOptions.xml
2025-06-20 15:30:59,942 [   8151]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-impl.jar!/search/java-impl.jar!.searchableOptions.xml
2025-06-20 15:30:59,942 [   8151]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/javaFX.jar!/search/javaFX.jar!.searchableOptions.xml
2025-06-20 15:30:59,944 [   8153]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/kotlin-plugin-shared.jar/search/kotlin-plugin-shared.jar.searchableOptions.xml
2025-06-20 15:30:59,944 [   8153]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/searchEverywhereMl.jar/search/searchEverywhereMl.jar.searchableOptions.xml
2025-06-20 15:30:59,944 [   8153]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/idea-junit.jar!/search/idea-junit.jar!.searchableOptions.xml
2025-06-20 15:30:59,945 [   8154]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/uiDesigner.jar/search/uiDesigner.jar.searchableOptions.xml
2025-06-20 15:30:59,945 [   8154]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/javaFX.jar/search/javaFX.jar.searchableOptions.xml
2025-06-20 15:30:59,946 [   8155]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/dev.jar/search/dev.jar.searchableOptions.xml
2025-06-20 15:30:59,947 [   8156]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/cwm-plugin-terminal.jar/search/cwm-plugin-terminal.jar.searchableOptions.xml
2025-06-20 15:30:59,948 [   8157]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-svn.jar/search/vcs-svn.jar.searchableOptions.xml
2025-06-20 15:30:59,948 [   8157]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/byteCodeViewer.jar/search/byteCodeViewer.jar.searchableOptions.xml
2025-06-20 15:30:59,949 [   8158]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/testng-plugin.jar!/search/testng-plugin.jar!.searchableOptions.xml
2025-06-20 15:30:59,949 [   8158]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/marketplace.jar/search/marketplace.jar.searchableOptions.xml
2025-06-20 15:30:59,950 [   8159]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/copyright.jar/search/copyright.jar.searchableOptions.xml
2025-06-20 15:30:59,952 [   8161]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/maven.jar/search/maven.jar.searchableOptions.xml
2025-06-20 15:30:59,953 [   8162]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/maven.jar!/search/maven.jar!.searchableOptions.xml
2025-06-20 15:30:59,955 [   8164]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/Groovy.jar/search/Groovy.jar.searchableOptions.xml
2025-06-20 15:30:59,955 [   8164]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/instrumented-zigzag-1.0.3.jar/search/instrumented-zigzag-1.0.3.jar.searchableOptions.xml
2025-06-20 15:30:59,955 [   8164]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/gradle-java.jar/search/gradle-java.jar.searchableOptions.xml
2025-06-20 15:30:59,956 [   8165]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/terminal.jar/search/terminal.jar.searchableOptions.xml
2025-06-20 15:30:59,956 [   8165]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/properties.jar/search/properties.jar.searchableOptions.xml
2025-06-20 15:30:59,957 [   8166]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/performanceTesting-async.jar/search/performanceTesting-async.jar.searchableOptions.xml
2025-06-20 15:30:59,961 [   8170]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-impl.jar/search/java-impl.jar.searchableOptions.xml
2025-06-20 15:30:59,984 [   8193]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/app-client.jar/search/app-client.jar.searchableOptions.xml
2025-06-20 15:30:59,985 [   8194]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/html-tools.jar/search/html-tools.jar.searchableOptions.xml
2025-06-20 15:30:59,985 [   8194]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-debugger-streams.jar/search/java-debugger-streams.jar.searchableOptions.xml
2025-06-20 15:30:59,987 [   8196]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-perforce.jar/search/vcs-perforce.jar.searchableOptions.xml
2025-06-20 15:30:59,988 [   8197]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/gradle.jar/search/gradle.jar.searchableOptions.xml
2025-06-20 15:30:59,988 [   8197]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/testng-plugin.jar/search/testng-plugin.jar.searchableOptions.xml
2025-06-20 15:30:59,988 [   8197]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/grazie.jar/search/grazie.jar.searchableOptions.xml
2025-06-20 15:30:59,989 [   8198]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/qodana.jar/search/qodana.jar.searchableOptions.xml
2025-06-20 15:30:59,994 [   8203]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/app.jar/search/app.jar.searchableOptions.xml
2025-06-20 15:30:59,994 [   8203]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/completionMlRanking.jar/search/completionMlRanking.jar.searchableOptions.xml
2025-06-20 15:30:59,994 [   8203]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-decompiler.jar/search/java-decompiler.jar.searchableOptions.xml
2025-06-20 15:30:59,995 [   8204]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-hg.jar/search/vcs-hg.jar.searchableOptions.xml
2025-06-20 15:30:59,995 [   8204]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/performanceTesting.jar/search/performanceTesting.jar.searchableOptions.xml
2025-06-20 15:30:59,996 [   8205]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-gitlab.jar/search/vcs-gitlab.jar.searchableOptions.xml
2025-06-20 15:30:59,996 [   8205]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/cwm-common.jar/search/cwm-common.jar.searchableOptions.xml
2025-06-20 15:30:59,996 [   8205]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-i18n.jar/search/java-i18n.jar.searchableOptions.xml
2025-06-20 15:30:59,997 [   8206]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/Groovy.jar!/search/Groovy.jar!.searchableOptions.xml
2025-06-20 15:30:59,997 [   8206]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/toml.jar/search/toml.jar.searchableOptions.xml
2025-06-20 15:30:59,997 [   8206]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-ijent-impl.jar/search/platform-ijent-impl.jar.searchableOptions.xml
2025-06-20 15:30:59,997 [   8206]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/idea-junit.jar/search/idea-junit.jar.searchableOptions.xml
2025-06-20 15:30:59,999 [   8208]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/yaml.jar/search/yaml.jar.searchableOptions.xml
2025-06-20 15:30:59,999 [   8208]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/packageChecker.jar/search/packageChecker.jar.searchableOptions.xml
2025-06-20 15:30:59,999 [   8208]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-tracing-ide.jar/search/platform-tracing-ide.jar.searchableOptions.xml
2025-06-20 15:31:00,000 [   8209]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/editorconfig.jar/search/editorconfig.jar.searchableOptions.xml
2025-06-20 15:31:00,000 [   8209]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-github.jar/search/vcs-github.jar.searchableOptions.xml
2025-06-20 15:31:00,001 [   8210]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-langInjection.jar/search/platform-langInjection.jar.searchableOptions.xml
2025-06-20 15:31:00,001 [   8210]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-images.jar/search/platform-images.jar.searchableOptions.xml
2025-06-20 15:31:00,002 [   8211]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/kotlin-plugin.jar/search/kotlin-plugin.jar.searchableOptions.xml
2025-06-20 15:31:00,004 [   8213]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-git.jar/search/vcs-git.jar.searchableOptions.xml
2025-06-20 15:31:00,005 [   8214]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/modules.jar/search/modules.jar.searchableOptions.xml
2025-06-20 15:31:00,005 [   8214]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/indexing-shared.jar/search/indexing-shared.jar.searchableOptions.xml
2025-06-20 15:31:00,006 [   8215]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/featuresTrainer.jar/search/featuresTrainer.jar.searchableOptions.xml
2025-06-20 15:31:00,006 [   8215]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/kotlin-plugin.jar!/search/kotlin-plugin.jar!.searchableOptions.xml
2025-06-20 15:31:00,006 [   8215]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/cwm-host.jar/search/cwm-host.jar.searchableOptions.xml
2025-06-20 15:31:00,006 [   8215]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/gradle-java-maven.jar/search/gradle-java-maven.jar.searchableOptions.xml
2025-06-20 15:31:00,007 [   8216]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/settingsSync.jar/search/settingsSync.jar.searchableOptions.xml
2025-06-20 15:31:00,007 [   8216]   INFO - STDOUT - Searchable options index builder completed
2025-06-20 15:31:00,007 [   8216]   INFO - #c.i.u.i.FileBasedIndexImpl - Index dispose started
2025-06-20 15:31:00,027 [   8236]   INFO - #c.i.p.s.StubIndexImpl - StubIndexExtension-s were unloaded
2025-06-20 15:31:00,027 [   8236]   INFO - #c.i.p.s.SerializationManagerImpl - Start shutting down /Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/index/rep.names
2025-06-20 15:31:00,027 [   8236]   INFO - #c.i.p.s.SerializationManagerImpl - Finished shutting down /Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/index/rep.names
2025-06-20 15:31:00,028 [   8237]   INFO - #c.i.u.i.FileBasedIndexImpl - Index dispose completed in 21ms.
2025-06-20 15:31:00,031 [   8240]   INFO - #c.i.o.v.n.p.PersistentFSImpl - VFS dispose started
2025-06-20 15:31:00,032 [   8241]   INFO - #c.i.o.v.n.p.FSRecordsImpl - VFS closing
2025-06-20 15:31:00,033 [   8242]   INFO - #c.i.o.v.n.p.PersistentFSImpl - VFS dispose completed in 1 ms.
2025-06-20 15:31:00,042 [   8251]   INFO - #c.i.p.i.b.AppStarter - ------------------------------------------------------ IDE SHUTDOWN ------------------------------------------------------
2025-06-20 15:33:35,700 [      2]   INFO - #c.i.p.i.b.AppStarter - ------------------------------------------------------ IDE STARTED ------------------------------------------------------
2025-06-20 15:33:35,707 [      9]   INFO - #c.i.u.EnvironmentUtil - loading shell env is skipped: IDE has been launched from a terminal (SHLVL=1)
2025-06-20 15:33:35,708 [     10]   INFO - #c.i.i.p.PluginManager - Using broken plugins file from IDE distribution
2025-06-20 15:33:35,722 [     24]   INFO - #c.i.p.i.b.AppStarter - JNA library (64-bit) loaded in 16 ms
2025-06-20 15:33:35,722 [     24]   INFO - #c.i.u.i.PageCacheUtils - File page caching params:
2025-06-20 15:33:35,723 [     25]   INFO - #c.i.u.i.PageCacheUtils - 	DEFAULT_PAGE_SIZE: 10485760
2025-06-20 15:33:35,723 [     25]   INFO - #c.i.u.i.PageCacheUtils - 	Direct memory to use, max: 2126512128
2025-06-20 15:33:35,723 [     25]   INFO - #c.i.u.i.PageCacheUtils - 	FilePageCache: regular + lock-free (LOCK_FREE_PAGE_CACHE_ENABLED:true)
2025-06-20 15:33:35,723 [     25]   INFO - #c.i.p.i.b.AppStarter - IDE: IntelliJ IDEA (build #IC-241.14494.240, Thu, 28 Mar 2024 05:12:00 GMT)
2025-06-20 15:33:35,724 [     26]   INFO - #c.i.p.i.b.AppStarter - OS: Mac OS X (15.5)
2025-06-20 15:33:35,724 [     26]   INFO - #c.i.p.i.b.AppStarter - JRE: 17.0.10+8-b1207.12, aarch64 (JetBrains s.r.o.)
2025-06-20 15:33:35,724 [     26]   INFO - #c.i.u.i.PageCacheUtils - 	NEW_PAGE_CACHE_MEMORY_FRACTION: 0.20000000298023224
2025-06-20 15:33:35,724 [     26]   INFO - #c.i.p.i.b.AppStarter - JVM: 17.0.10+8-b1207.12 (OpenJDK 64-Bit Server VM)
2025-06-20 15:33:35,725 [     27]   INFO - #c.i.u.i.PageCacheUtils - 	Regular FilePageCache: 503316478 bytes
2025-06-20 15:33:35,725 [     27]   INFO - #c.i.u.i.PageCacheUtils - 	New     FilePageCache: 125829122 bytes (+ up to 10.0% overflow)
2025-06-20 15:33:35,725 [     27]   INFO - #c.i.u.i.PageCacheUtils - 	DirectByteBuffers pool: 104857600 bytes
2025-06-20 15:33:35,726 [     28]   INFO - #c.i.p.i.b.AppStarter - PID: 66540
2025-06-20 15:33:35,739 [     41]   INFO - #c.i.p.i.b.AppStarter - JVM options: [-Daether.connector.resumeDownloads=false, -Dapple.awt.application.appearance=system, -Dapple.awt.fileDialogForDirectories=true, -Dapple.laf.useScreenMenuBar=true, -Dide.show.tips.on.startup.default.value=false, -Didea.auto.reload.plugins=true, -Didea.classpath.index.enabled=false, -Didea.config.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/config, -Didea.is.internal=true, -Didea.log.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log, -Didea.paths.selector=IdeaIC2024.1, -Didea.platform.prefix=Idea, -Didea.plugin.in.sandbox.mode=true, -Didea.plugins.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/plugins, -Didea.required.plugins.id=com.wontlost.zigzag, -Didea.smooth.progress=false, -Didea.system.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system, -Didea.vendor.name=JetBrains, -Dintellij.platform.runtime.repository.path=/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/modules/module-descriptors.jar, -Djava.system.class.loader=com.intellij.util.lang.PathClassLoader, -Djbr.catch.SIGABRT=true, -Djdk.attach.allowAttachSelf=true, -Djdk.http.auth.tunneling.disabledSchemes="", -Djdk.module.illegalAccess.silent=true, -Djna.boot.library.path=/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/lib/jna/aarch64, -Djna.noclasspath=true, -Djna.nosys=true, -Dpty4j.preferred.native.folder=/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/lib/pty4j, -Dsplash=true, -Dsun.io.useCanonCaches=false, -Dsun.java2d.metal=true, -XX:+HeapDumpOnOutOfMemoryError, -XX:-OmitStackTraceInFastThrow, -XX:+IgnoreUnrecognizedVMOptions, -XX:CICompilerCount=2, -XX:ReservedCodeCacheSize=512m, -XX:CompileCommand=exclude,com/intellij/openapi/vfs/impl/FilePartNodeRoot,trieDescend, -XX:SoftRefLRUPolicyMSPerMB=50, -javaagent:/Users/<USER>/IdeaProjects/zigzag/build/tmp/initializeIntelliJPlugin/coroutines-javaagent.jar, --add-opens=java.base/java.io=ALL-UNNAMED, --add-opens=java.base/java.lang=ALL-UNNAMED, --add-opens=java.base/java.lang.ref=ALL-UNNAMED, --add-opens=java.base/java.lang.reflect=ALL-UNNAMED, --add-opens=java.base/java.net=ALL-UNNAMED, --add-opens=java.base/java.nio=ALL-UNNAMED, --add-opens=java.base/java.nio.charset=ALL-UNNAMED, --add-opens=java.base/java.text=ALL-UNNAMED, --add-opens=java.base/java.time=ALL-UNNAMED, --add-opens=java.base/java.util=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED, --add-opens=java.base/jdk.internal.vm=ALL-UNNAMED, --add-opens=java.base/sun.nio.ch=ALL-UNNAMED, --add-opens=java.base/sun.nio.fs=ALL-UNNAMED, --add-opens=java.base/sun.security.ssl=ALL-UNNAMED, --add-opens=java.base/sun.security.util=ALL-UNNAMED, --add-opens=java.base/sun.net.dns=ALL-UNNAMED, --add-opens=java.desktop/com.apple.eawt=ALL-UNNAMED, --add-opens=java.desktop/com.apple.eawt.event=ALL-UNNAMED, --add-opens=java.desktop/com.apple.laf=ALL-UNNAMED, --add-opens=java.desktop/java.awt=ALL-UNNAMED, --add-opens=java.desktop/java.awt.dnd.peer=ALL-UNNAMED, --add-opens=java.desktop/java.awt.event=ALL-UNNAMED, --add-opens=java.desktop/java.awt.image=ALL-UNNAMED, --add-opens=java.desktop/java.awt.peer=ALL-UNNAMED, --add-opens=java.desktop/java.awt.font=ALL-UNNAMED, --add-opens=java.desktop/javax.swing=ALL-UNNAMED, --add-opens=java.desktop/javax.swing.plaf.basic=ALL-UNNAMED, --add-opens=java.desktop/javax.swing.text=ALL-UNNAMED, --add-opens=java.desktop/javax.swing.text.html=ALL-UNNAMED, --add-opens=java.desktop/sun.awt.datatransfer=ALL-UNNAMED, --add-opens=java.desktop/sun.awt.image=ALL-UNNAMED, --add-opens=java.desktop/sun.awt=ALL-UNNAMED, --add-opens=java.desktop/sun.font=ALL-UNNAMED, --add-opens=java.desktop/sun.java2d=ALL-UNNAMED, --add-opens=java.desktop/sun.lwawt=ALL-UNNAMED, --add-opens=java.desktop/sun.lwawt.macosx=ALL-UNNAMED, --add-opens=java.desktop/sun.swing=ALL-UNNAMED, --add-opens=java.desktop/com.sun.java.swing=ALL-UNNAMED, --add-opens=jdk.attach/sun.tools.attach=ALL-UNNAMED, --add-opens=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED, --add-opens=jdk.internal.jvmstat/sun.jvmstat.monitor=ALL-UNNAMED, --add-opens=jdk.jdi/com.sun.tools.jdi=ALL-UNNAMED, -Xms128m, -Xmx2048m, -Dfile.encoding=UTF-8, -Duser.country=NZ, -Duser.language=en, -Duser.variant, -ea]
2025-06-20 15:33:35,739 [     41]   INFO - #c.i.p.i.b.AppStarter - args: traverseUI /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions true
2025-06-20 15:33:35,740 [     42]   INFO - #c.i.p.i.b.AppStarter - library path: /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-06-20 15:33:35,740 [     42]   INFO - #c.i.p.i.b.AppStarter - boot library path: /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains/jbre/jbr_jcef-17.0.10-osx-aarch64-b1207.12/extracted/jbr_jcef-17.0.10-osx-aarch64-b1207.12/Contents/Home/lib
2025-06-20 15:33:35,746 [     48]   INFO - #c.i.p.i.b.AppStarter - locale=en_NZ JNU=UTF-8 file.encoding=UTF-8
    idea.config.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/config
    idea.system.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system
    idea.plugins.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/plugins
    idea.log.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log
2025-06-20 15:33:35,750 [     52]   INFO - #c.i.p.i.b.AppStarter - CPU cores: 10; ForkJoinPool.commonPool: java.util.concurrent.ForkJoinPool@443c69d0[Running, parallelism = 10, size = 0, active = 0, running = 0, steals = 0, tasks = 0, submissions = 0]; factory: com.intellij.concurrency.IdeaForkJoinWorkerThreadFactory@41b3d8a8
2025-06-20 15:33:35,750 [     52]   INFO - #c.i.i.p.PluginManager - Module intellij.sh.python is not enabled because dependency intellij.python.community.impl is not available
Module intellij.groovy/ant is not enabled because dependency AntSupport is not available
Module intellij.qodana.jvm.swagger is not enabled because dependency com.intellij.swagger is not available
Module intellij.qodana.python is not enabled because dependency intellij.python is not available
Module intellij.qodana.php is not enabled because dependency com.jetbrains.php is not available
Module intellij.qodana.js is not enabled because dependency JavaScript is not available
Module intellij.qodana.inspectionKts.js is not enabled because dependency JavaScript is not available
Module intellij.qodana.go is not enabled because dependency intellij.go.coverage is not available
Module intellij.cwm.plugin.elevation is not enabled because dependency intellij.execution.process.elevation is not available
Module intellij.packageChecker.php is not enabled because dependency com.jetbrains.php is not available
Module intellij.packageChecker.python is not enabled because dependency intellij.python.community.impl is not available
Module intellij.packageChecker.javascript is not enabled because dependency JavaScript is not available
Module intellij.packageChecker.go is not enabled because dependency org.jetbrains.plugins.go is not available
2025-06-20 15:33:35,761 [     63]   INFO - #c.i.i.p.PluginManager - Loaded bundled plugins: IDEA CORE (241.14494.240), TextMate Bundles (241.14494.240), Images (241.14494.240), Copyright (241.14494.240), EditorConfig (241.14494.240), IDE Services / Provisioner API (241.14494.240), Machine Learning in Search Everywhere (241.14494.240), YAML (241.14494.240), JetBrains maven model api classes (241.14494.240), JetBrains Repository Search (241.14494.240), Maven server api classes (241.14494.240), Markdown (241.14494.240), Terminal (241.14494.240), Mercurial (241.14494.240), Properties (241.14494.240), Gradle (241.14494.240), NetBeans Keymap (241.14494.240), JetBrains Marketplace Licensing (241.14494.240), com.intellij.dev (241.14494.240), Remote Execution Agent (241.14494.240), Shell Script (241.14494.240), HTML Tools (241.14494.240), Visual Studio Keymap (241.14494.240), Machine Learning Code Completion (241.14494.240), Toml (241.14494.240), WebP Support (241.14494.240), Performance Testing (241.14494.240), Grazie Lite (241.14494.240), Eclipse Keymap (241.14494.240), Perforce Helix Core (241.14494.240), GitLab (241.14494.240), IDE Features Trainer (241.14494.240), Java (241.14494.240), com.intellij.tracing.ide (241.14494.240), Java Bytecode Decompiler (241.14494.240), Java Stream Debugger (241.14494.240), Task Management (241.14494.240), GitHub (241.14494.240), JUnit (241.14494.240), Eclipse Interoperability (241.14494.240), IntelliLang (241.14494.240), Groovy (241.14494.240), JavaFX (241.14494.240), Turbo Complete (241.14494.240), Kotlin (241.14494.240-IJ), Gradle DSL API (241.14494.240), Java Internationalization (241.14494.240), UI Designer (241.14494.240), Bytecode Viewer (241.14494.240), Maven (241.14494.240), TestNG (241.14494.240), Code Coverage for Java (241.14494.240), Gradle-Java (241.14494.240), Gradle-Java-Analysis (241.14494.240), Gradle-Maven (241.14494.240), Java IDE Customization (241.14494.240), Settings Sync (241.14494.240), Git (241.14494.240), Qodana (241.14494.240), Configuration Script (241.14494.240), Shared Indexes (241.14494.240), Code With Me (241.14494.240), Subversion (241.14494.240), Package Checker (241.14494.240), Gradle Dependency Updater Implementation (241.14494.240), Async Profiler for IDE Performance Testing (241.14494.240)
2025-06-20 15:33:35,761 [     63]   INFO - #c.i.i.p.PluginManager - Loaded custom plugins: Zigzag (1.0.3)
2025-06-20 15:33:35,810 [    112]   INFO - c.i.p.i.IdeFingerprint - Calculated dependencies fingerprint in 12 ms (hash=3kp3r3cytob16, buildTime=1711602720, appVersion=IC-241.14494.240)
2025-06-20 15:33:35,862 [    164]   INFO - #c.i.a.o.PathMacrosImpl - Loaded path macros: {MAVEN_REPOSITORY=/Users/<USER>/.m2/repository}
2025-06-20 15:33:35,883 [    185]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS uses 'fast' names enumerator (over mmapped file)
2025-06-20 15:33:35,884 [    186]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS uses streamlined attributes storage (over mmapped file)
2025-06-20 15:33:35,885 [    187]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS uses content storage over memory-mapped file, with compression algo: LZ4[ > 8000b ]
2025-06-20 15:33:35,907 [    209]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS: impl (expected) version=846397, 14 file records, 0 content blobs
2025-06-20 15:33:35,914 [    216]   INFO - #c.i.o.v.n.p.FSRecordsImpl - VFS initialized: 33 ms, 0 failed attempts, 0 error(s) were recovered
2025-06-20 15:33:35,914 [    216]   INFO - #c.i.o.v.n.p.FSRecordsImpl - VFS scanned: file-by-name index was populated
2025-06-20 15:33:36,066 [    368]   INFO - #c.i.o.v.n.p.FSRecords - VFS health-check enabled: first after 600000 ms, and each following 43200000 ms, wrap in RA: true
2025-06-20 15:33:36,070 [    372]   INFO - #c.i.i.p.ExpiredPluginsState - Plugins to skip: []
2025-06-20 15:33:36,175 [    477]   INFO - #o.j.i.BuiltInServerManager - built-in server started, port 63343
2025-06-20 15:33:36,191 [    493]   INFO - #c.i.u.n.s.CertificateManager - Default SSL context initialized
2025-06-20 15:33:36,205 [    507]   INFO - #c.i.o.v.i.l.NativeFileWatcherImpl - Native file watcher is disabled
2025-06-20 15:33:36,237 [    539]   INFO - #c.i.i.s.p.i.SharedIndexMainZipStorage - Shared Indexes Storage is opened and empty
2025-06-20 15:33:36,238 [    540]   WARN - #c.i.i.s.p.i.BundledSharedIndexProvider - Bundled shared index is not found at: /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/jdk-shared-indexes
2025-06-20 15:33:36,302 [    604]   INFO - #c.i.u.i.FileBasedIndexImpl - Using nice flusher for indexes
2025-06-20 15:33:36,308 [    610]   INFO - #c.i.u.i.IndexDataInitializer - Index data initialization done: 109 ms. Initialized indexes: [FilenameIndex, filetypes, XmlNamespaces, Trigram.Index, DomFileIndex, RelaxSymbolIndex, XmlTagNames, IdIndex, TodoIndex, FrameworkDetectionIndex, fileIncludes, java.auto.module.name, html5.custom.attributes.index, yaml.keys.name, SchemaTypeInheritance, HtmlTagIdIndex, java.source.module.name, json.file.root.values, editorconfig.index.name, xmlProperties, java.null.method.argument, bytecodeAnalysis, javafx.id.name, java.binary.plus.expression, org.jetbrains.kotlin.idea.versions.KotlinJvmMetadataVersionIndex, JavaFxControllerClassIndex, java.fun.expression, javafx.custom.component, org.jetbrains.kotlin.idea.versions.KotlinJsMetadataVersionIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinJavaScriptMetaFileIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinClassFileIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinPartialPackageNamesIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinMetadataFileIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinMetadataFilePackageIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinBuiltInsMetadataIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinModuleMappingIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinShortClassNameFileIndex, KotlinPackageSourcesMemberNamesIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinJvmModuleAnnotationsIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinStdlibIndex, KotlinTopLevelCallableByPackageShortNameIndex, KotlinBinaryRootToPackageIndex, KotlinTopLevelClassLikeDeclarationByPackageShortNameIndex, org.jetbrains.kotlin.idea.vfilefinder.KlibMetaFileIndex, com.intellij.uiDesigner.FormClassIndex, Stubs].
2025-06-20 15:33:36,361 [    663]   INFO - #c.i.u.i.IndexDataInitializer - Index data initialization done: 52 ms. Initialized stub indexes: {org.jetbrains.kotlin.idea.stubindex.KotlinSuperClassIndex, java.module.name, org.jetbrains.kotlin.idea.stubindex.KotlinJvmNameAnnotationIndex, java.class.shortname, java.field.name, KotlinTypeAliasByExpansionShortNameIndex, java.method.parameter.types, markdown.header, org.jetbrains.kotlin.idea.stubindex.KotlinFunctionShortNameIndex, KotlinProbablyContractedFunctionShortNameIndex, java.class.extlist, KotlinTopLevelFunctionByPackageIndex, gr.annot.members, KotlinTopLevelExtensionsByReceiverTypeIndex, org.jetbrains.kotlin.idea.stubindex.KotlinFullClassNameIndex, gr.field.name, KotlinTopLevelTypeAliasByPackageIndex, org.jetbrains.kotlin.idea.stubindex.KotlinExactPackagesIndex, org.jetbrains.kotlin.idea.stubindex.KotlinMultifileClassPartIndex, gr.method.name, KotlinProbablyNothingPropertyShortNameIndex, properties.index, java.method.name, gr.script.class, org.jetbrains.kotlin.idea.stubindex.KotlinScriptFqnIndex, org.jetbrains.kotlin.idea.stubindex.KotlinFileFacadeFqNameIndex, markdown.header.anchor, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelClassByPackageIndex, java.unnamed.class, org.jetbrains.kotlin.idea.stubindex.KotlinClassShortNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinInnerTypeAliasClassIdIndex, gr.annot.method.name, jvm.static.member.name, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelFunctionFqnNameIndex, java.class.fqn, java.anonymous.baseref, KotlinProbablyNothingFunctionShortNameIndex, jvm.static.member.type, org.jetbrains.kotlin.idea.stubindex.KotlinFilePartClassIndex, org.jetbrains.kotlin.idea.stubindex.KotlinTypeAliasShortNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinSubclassObjectNameIndex, dom.elementClass, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelPropertyFqnNameIndex, java.annotations, KotlinOverridableInternalMembersShortNameIndex, gr.class.fqn.s, org.jetbrains.kotlin.idea.stubindex.KotlinAnnotationsIndex, gr.script.fqn.s, kotlin.primeIndexKey, KotlinTopLevelPropertyByPackageIndex, dom.namespaceKey, KotlinFileFacadeClassByPackageIndex, org.jetbrains.kotlin.idea.stubindex.KotlinPropertyShortNameIndex, gr.class.super, gr.anonymous.class, KotlinExtensionsInObjectsByReceiverTypeIndex, org.jetbrains.kotlin.idea.stubindex.KotlinFileFacadeShortNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelTypeAliasFqNameIndex}.
2025-06-20 15:33:37,526 [   1828]   INFO - #c.i.i.p.n.PluginsGroupComponentWithProgress - Marketplace tab: loading stopped
2025-06-20 15:33:37,536 [   1838]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Staff Picks' group load started
2025-06-20 15:33:37,918 [   2220]   INFO - #c.i.w.i.i.j.s.JpsGlobalModelSynchronizerImpl - Loading global entities com.intellij.platform.workspace.jps.entities.SdkEntity from files
2025-06-20 15:33:37,919 [   2221]   INFO - #c.i.w.i.i.j.s.JpsGlobalModelSynchronizerImpl - Loading global entities com.intellij.platform.workspace.jps.entities.LibraryEntity from files
2025-06-20 15:33:38,441 [   2743]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Staff Picks' group load finished
2025-06-20 15:33:38,442 [   2744]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'New and Updated' group load started
2025-06-20 15:33:38,517 [   2819]   INFO - #c.i.o.a.i.NonBlockingReadActionImpl - OTel monitoring for NonBlockingReadAction is enabled
2025-06-20 15:33:39,415 [   3717]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'New and Updated' group load finished
2025-06-20 15:33:39,415 [   3717]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Downloads' group load started
2025-06-20 15:33:39,825 [   4127]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Downloads' group load finished
2025-06-20 15:33:39,826 [   4128]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Rated' group load started
2025-06-20 15:33:40,182 [   4484]   INFO - #c.i.r.OsRegistryConfigProvider - Looking for 'lobbyServerUrl' value in [/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Library/Application Support/JetBrains/CodeWithMe/config.json, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/config.json]
2025-06-20 15:33:40,183 [   4485]   INFO - #c.i.r.OsRegistryConfigProvider - OS provided value for 'lobbyServerUrl' is not found
2025-06-20 15:33:40,183 [   4485]   INFO - #c.j.r.p.c.u.CodeWithMeSystemSettings - Setting default value='https://code-with-me.jetbrains.com'
2025-06-20 15:33:40,192 [   4494]   INFO - #c.i.r.OsRegistryConfigProvider - Looking for 'lobbyServerUrl' value in [/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Library/Application Support/JetBrains/CodeWithMe/config.json, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/config.json]
2025-06-20 15:33:40,192 [   4494]   INFO - #c.i.r.OsRegistryConfigProvider - OS provided value for 'lobbyServerUrl' is not found
2025-06-20 15:33:40,192 [   4494]   INFO - #c.j.r.p.c.u.CodeWithMeSystemSettings - Setting default value='https://code-with-me.jetbrains.com'
2025-06-20 15:33:40,200 [   4502] SEVERE - #c.i.o.o.ConfigurableEP - Cannot create configurable
com.intellij.diagnostic.PluginException: Cannot load class com.wontlost.zigzag.OpenAISettingsConfigurable (
  error: com/wontlost/zigzag/OpenAISettingsConfigurable has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0,
  classLoader=PluginClassLoader(plugin=PluginDescriptor(name=Zigzag, id=com.wontlost.zigzag, descriptorPath=plugin.xml, path=~/IdeaProjects/zigzag/build/idea-sandbox/plugins/zigzag, version=1.0.3, package=null, isBundled=false), packagePrefix=null, state=active)
)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:331)
	at com.intellij.ide.plugins.cl.PluginClassLoader.tryLoadingClass(PluginClassLoader.kt:178)
	at com.intellij.serviceContainer.ComponentManagerImplKt.doLoadClass(ComponentManagerImpl.kt:1466)
	at com.intellij.serviceContainer.ComponentManagerImpl.instantiateClass(ComponentManagerImpl.kt:943)
	at com.intellij.openapi.options.ConfigurableEP$ClassProducer.createElement(ConfigurableEP.java:429)
	at com.intellij.openapi.options.ConfigurableEP.createConfigurable(ConfigurableEP.java:338)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.createConfigurable(ConfigurableWrapper.java:39)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.getConfigurable(ConfigurableWrapper.java:116)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.createComponent(ConfigurableWrapper.java:180)
	at com.intellij.ide.ui.search.SearchUtil.processConfigurables(SearchUtil.java:72)
	at com.intellij.ide.ui.search.TraverseUIStarter.lambda$startup$0(TraverseUIStarter.java:116)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:308)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:792)
	at java.desktop/java.awt.EventQueue$3.run(EventQueue.java:739)
	at java.desktop/java.awt.EventQueue$3.run(EventQueue.java:733)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:761)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:698)
	at com.intellij.ide.IdeEventQueue._dispatchEvent$lambda$12(IdeEventQueue.kt:593)
	at com.intellij.openapi.application.impl.RwLockHolder.runWithoutImplicitRead(RwLockHolder.kt:105)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:593)
	at com.intellij.ide.IdeEventQueue.access$_dispatchEvent(IdeEventQueue.kt:77)
	at com.intellij.ide.IdeEventQueue$dispatchEvent$processEventRunnable$1$1.invoke(IdeEventQueue.kt:358)
	at com.intellij.ide.IdeEventQueue$dispatchEvent$processEventRunnable$1$1.invoke(IdeEventQueue.kt:356)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$1(IdeEventQueue.kt:1021)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:106)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:1021)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$7(IdeEventQueue.kt:356)
	at com.intellij.openapi.application.impl.RwLockHolder.runIntendedWriteActionOnCurrentThread(RwLockHolder.kt:209)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:830)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:398)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
Caused by: java.lang.UnsupportedClassVersionError: com/wontlost/zigzag/OpenAISettingsConfigurable has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1108)
	at com.intellij.util.lang.UrlClassLoader.consumeClassData(UrlClassLoader.java:291)
	at com.intellij.util.lang.ZipResourceFile.findClass(ZipResourceFile.java:116)
	at com.intellij.util.lang.JarLoader.findClass(JarLoader.java:58)
	at com.intellij.util.lang.ClassPath.findClassInLoader(ClassPath.java:240)
	at com.intellij.util.lang.ClassPath.findClassWithoutCache(ClassPath.java:229)
	at com.intellij.util.lang.ClassPath.findClass(ClassPath.java:212)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:326)
	... 37 more
2025-06-20 15:33:40,202 [   4504] SEVERE - #c.i.o.o.ConfigurableEP - IntelliJ IDEA 2024.1  Build #IC-241.14494.240
2025-06-20 15:33:40,202 [   4504] SEVERE - #c.i.o.o.ConfigurableEP - JDK: 17.0.10; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-06-20 15:33:40,202 [   4504] SEVERE - #c.i.o.o.ConfigurableEP - OS: Mac OS X
2025-06-20 15:33:40,203 [   4505] SEVERE - #c.i.o.o.ConfigurableEP - Plugin to blame: Zigzag version: 1.0.3
2025-06-20 15:33:40,203 [   4505] SEVERE - #c.i.o.o.ConfigurableEP - Last Action: 
2025-06-20 15:33:40,207 [   4509] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Can't instantiate configurable for Zigzag [Plugin: com.wontlost.zigzag]
com.intellij.diagnostic.PluginException: Can't instantiate configurable for Zigzag [Plugin: com.wontlost.zigzag]
	at com.intellij.openapi.options.ex.ConfigurableWrapper.getConfigurable(ConfigurableWrapper.java:121)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.createComponent(ConfigurableWrapper.java:180)
	at com.intellij.ide.ui.search.SearchUtil.processConfigurables(SearchUtil.java:72)
	at com.intellij.ide.ui.search.TraverseUIStarter.lambda$startup$0(TraverseUIStarter.java:116)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:308)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:792)
	at java.desktop/java.awt.EventQueue$3.run(EventQueue.java:739)
	at java.desktop/java.awt.EventQueue$3.run(EventQueue.java:733)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:761)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:698)
	at com.intellij.ide.IdeEventQueue._dispatchEvent$lambda$12(IdeEventQueue.kt:593)
	at com.intellij.openapi.application.impl.RwLockHolder.runWithoutImplicitRead(RwLockHolder.kt:105)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:593)
	at com.intellij.ide.IdeEventQueue.access$_dispatchEvent(IdeEventQueue.kt:77)
	at com.intellij.ide.IdeEventQueue$dispatchEvent$processEventRunnable$1$1.invoke(IdeEventQueue.kt:358)
	at com.intellij.ide.IdeEventQueue$dispatchEvent$processEventRunnable$1$1.invoke(IdeEventQueue.kt:356)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$1(IdeEventQueue.kt:1021)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:106)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:1021)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$7(IdeEventQueue.kt:356)
	at com.intellij.openapi.application.impl.RwLockHolder.runIntendedWriteActionOnCurrentThread(RwLockHolder.kt:209)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:830)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:398)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-06-20 15:33:40,208 [   4510] SEVERE - #c.i.o.o.e.ConfigurableWrapper - IntelliJ IDEA 2024.1  Build #IC-241.14494.240
2025-06-20 15:33:40,208 [   4510] SEVERE - #c.i.o.o.e.ConfigurableWrapper - JDK: 17.0.10; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-06-20 15:33:40,208 [   4510] SEVERE - #c.i.o.o.e.ConfigurableWrapper - OS: Mac OS X
2025-06-20 15:33:40,208 [   4510] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Plugin to blame: Zigzag version: 1.0.3
2025-06-20 15:33:40,209 [   4511] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Last Action: 
2025-06-20 15:33:40,209 [   4511] SEVERE - #c.i.o.o.ConfigurableEP - Cannot create configurable
com.intellij.diagnostic.PluginException: Cannot load class com.wontlost.zigzag.OpenAISettingsConfigurable (
  error: com/wontlost/zigzag/OpenAISettingsConfigurable has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0,
  classLoader=PluginClassLoader(plugin=PluginDescriptor(name=Zigzag, id=com.wontlost.zigzag, descriptorPath=plugin.xml, path=~/IdeaProjects/zigzag/build/idea-sandbox/plugins/zigzag, version=1.0.3, package=null, isBundled=false), packagePrefix=null, state=active)
)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:331)
	at com.intellij.ide.plugins.cl.PluginClassLoader.tryLoadingClass(PluginClassLoader.kt:178)
	at com.intellij.serviceContainer.ComponentManagerImplKt.doLoadClass(ComponentManagerImpl.kt:1466)
	at com.intellij.serviceContainer.ComponentManagerImpl.instantiateClass(ComponentManagerImpl.kt:943)
	at com.intellij.openapi.options.ConfigurableEP$ClassProducer.createElement(ConfigurableEP.java:429)
	at com.intellij.openapi.options.ConfigurableEP.createConfigurable(ConfigurableEP.java:338)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.createConfigurable(ConfigurableWrapper.java:39)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.getConfigurable(ConfigurableWrapper.java:116)
	at com.intellij.ide.ui.search.SearchUtil.unwrapConfigurable(SearchUtil.java:102)
	at com.intellij.ide.ui.search.SearchUtil.processConfigurables(SearchUtil.java:73)
	at com.intellij.ide.ui.search.TraverseUIStarter.lambda$startup$0(TraverseUIStarter.java:116)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:308)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:792)
	at java.desktop/java.awt.EventQueue$3.run(EventQueue.java:739)
	at java.desktop/java.awt.EventQueue$3.run(EventQueue.java:733)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:761)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:698)
	at com.intellij.ide.IdeEventQueue._dispatchEvent$lambda$12(IdeEventQueue.kt:593)
	at com.intellij.openapi.application.impl.RwLockHolder.runWithoutImplicitRead(RwLockHolder.kt:105)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:593)
	at com.intellij.ide.IdeEventQueue.access$_dispatchEvent(IdeEventQueue.kt:77)
	at com.intellij.ide.IdeEventQueue$dispatchEvent$processEventRunnable$1$1.invoke(IdeEventQueue.kt:358)
	at com.intellij.ide.IdeEventQueue$dispatchEvent$processEventRunnable$1$1.invoke(IdeEventQueue.kt:356)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$1(IdeEventQueue.kt:1021)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:106)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:1021)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$7(IdeEventQueue.kt:356)
	at com.intellij.openapi.application.impl.RwLockHolder.runIntendedWriteActionOnCurrentThread(RwLockHolder.kt:209)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:830)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:398)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
Caused by: java.lang.UnsupportedClassVersionError: com/wontlost/zigzag/OpenAISettingsConfigurable has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1108)
	at com.intellij.util.lang.UrlClassLoader.consumeClassData(UrlClassLoader.java:291)
	at com.intellij.util.lang.ZipResourceFile.findClass(ZipResourceFile.java:116)
	at com.intellij.util.lang.JarLoader.findClass(JarLoader.java:58)
	at com.intellij.util.lang.ClassPath.findClassInLoader(ClassPath.java:240)
	at com.intellij.util.lang.ClassPath.findClass(ClassPath.java:190)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:326)
	... 37 more
2025-06-20 15:33:40,210 [   4512] SEVERE - #c.i.o.o.ConfigurableEP - IntelliJ IDEA 2024.1  Build #IC-241.14494.240
2025-06-20 15:33:40,210 [   4512] SEVERE - #c.i.o.o.ConfigurableEP - JDK: 17.0.10; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-06-20 15:33:40,210 [   4512] SEVERE - #c.i.o.o.ConfigurableEP - OS: Mac OS X
2025-06-20 15:33:40,210 [   4512] SEVERE - #c.i.o.o.ConfigurableEP - Plugin to blame: Zigzag version: 1.0.3
2025-06-20 15:33:40,211 [   4513] SEVERE - #c.i.o.o.ConfigurableEP - Last Action: 
2025-06-20 15:33:40,211 [   4513] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Can't instantiate configurable for Zigzag [Plugin: com.wontlost.zigzag]
com.intellij.diagnostic.PluginException: Can't instantiate configurable for Zigzag [Plugin: com.wontlost.zigzag]
	at com.intellij.openapi.options.ex.ConfigurableWrapper.getConfigurable(ConfigurableWrapper.java:121)
	at com.intellij.ide.ui.search.SearchUtil.unwrapConfigurable(SearchUtil.java:102)
	at com.intellij.ide.ui.search.SearchUtil.processConfigurables(SearchUtil.java:73)
	at com.intellij.ide.ui.search.TraverseUIStarter.lambda$startup$0(TraverseUIStarter.java:116)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:308)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:792)
	at java.desktop/java.awt.EventQueue$3.run(EventQueue.java:739)
	at java.desktop/java.awt.EventQueue$3.run(EventQueue.java:733)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:761)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:698)
	at com.intellij.ide.IdeEventQueue._dispatchEvent$lambda$12(IdeEventQueue.kt:593)
	at com.intellij.openapi.application.impl.RwLockHolder.runWithoutImplicitRead(RwLockHolder.kt:105)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:593)
	at com.intellij.ide.IdeEventQueue.access$_dispatchEvent(IdeEventQueue.kt:77)
	at com.intellij.ide.IdeEventQueue$dispatchEvent$processEventRunnable$1$1.invoke(IdeEventQueue.kt:358)
	at com.intellij.ide.IdeEventQueue$dispatchEvent$processEventRunnable$1$1.invoke(IdeEventQueue.kt:356)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$1(IdeEventQueue.kt:1021)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:106)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:1021)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$7(IdeEventQueue.kt:356)
	at com.intellij.openapi.application.impl.RwLockHolder.runIntendedWriteActionOnCurrentThread(RwLockHolder.kt:209)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:830)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:398)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-06-20 15:33:40,212 [   4514] SEVERE - #c.i.o.o.e.ConfigurableWrapper - IntelliJ IDEA 2024.1  Build #IC-241.14494.240
2025-06-20 15:33:40,212 [   4514] SEVERE - #c.i.o.o.e.ConfigurableWrapper - JDK: 17.0.10; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-06-20 15:33:40,212 [   4514] SEVERE - #c.i.o.o.e.ConfigurableWrapper - OS: Mac OS X
2025-06-20 15:33:40,212 [   4514] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Plugin to blame: Zigzag version: 1.0.3
2025-06-20 15:33:40,212 [   4514] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Last Action: 
2025-06-20 15:33:40,746 [   5048]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Rated' group load finished
2025-06-20 15:33:41,107 [   5409]   WARN - #c.i.u.j.JBCefApp - JCEF is manually disabled in headless env via 'ide.browser.jcef.headless.enabled=false'
2025-06-20 15:33:41,534 [   5836]   INFO - STDOUT - Found 207 configurables
2025-06-20 15:33:41,596 [   5898]   INFO - #c.i.i.p.n.PluginsGroupComponentWithProgress - Marketplace tab: loading stopped
2025-06-20 15:33:41,764 [   6066] SEVERE - #c.i.o.a.i.ActionManagerImpl - Cannot load class com.wontlost.zigzag.ZigzagAction (
  error: com/wontlost/zigzag/ZigzagAction has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0,
  classLoader=PluginClassLoader(plugin=PluginDescriptor(name=Zigzag, id=com.wontlost.zigzag, descriptorPath=plugin.xml, path=~/IdeaProjects/zigzag/build/idea-sandbox/plugins/zigzag, version=1.0.3, package=null, isBundled=false), packagePrefix=null, state=active)
)
com.intellij.diagnostic.PluginException: Cannot load class com.wontlost.zigzag.ZigzagAction (
  error: com/wontlost/zigzag/ZigzagAction has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0,
  classLoader=PluginClassLoader(plugin=PluginDescriptor(name=Zigzag, id=com.wontlost.zigzag, descriptorPath=plugin.xml, path=~/IdeaProjects/zigzag/build/idea-sandbox/plugins/zigzag, version=1.0.3, package=null, isBundled=false), packagePrefix=null, state=active)
)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:331)
	at com.intellij.ide.plugins.cl.PluginClassLoader.tryLoadingClass(PluginClassLoader.kt:178)
	at com.intellij.serviceContainer.ComponentManagerImplKt.doLoadClass(ComponentManagerImpl.kt:1466)
	at com.intellij.serviceContainer.ComponentManagerImpl.instantiateClass(ComponentManagerImpl.kt:943)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.instantiate(ActionManagerImpl.kt:1395)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.convertStub(ActionManagerImpl.kt:1635)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.getAction(ActionManagerImpl.kt:2117)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.access$getAction(ActionManagerImpl.kt:1)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImpl$actions$1.invoke(ActionManagerImpl.kt:1122)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImpl$actions$1.invoke(ActionManagerImpl.kt:1121)
	at kotlin.sequences.TransformingSequence$iterator$1.next(Sequences.kt:210)
	at kotlin.sequences.FilteringSequence$iterator$1.calcNext(Sequences.kt:170)
	at kotlin.sequences.FilteringSequence$iterator$1.hasNext(Sequences.kt:194)
	at com.intellij.ide.ui.search.TraverseUIStarter.processKeymap(TraverseUIStarter.java:293)
	at com.intellij.ide.ui.search.TraverseUIStarter.addOptions(TraverseUIStarter.java:164)
	at com.intellij.ide.ui.search.TraverseUIStarter.startup(TraverseUIStarter.java:127)
	at com.intellij.ide.ui.search.TraverseUIStarter.main(TraverseUIStarter.java:89)
	at com.intellij.platform.ide.bootstrap.ApplicationLoader.executeApplicationStarter$lambda$0(ApplicationLoader.kt:400)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1796)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: java.lang.UnsupportedClassVersionError: com/wontlost/zigzag/ZigzagAction has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1108)
	at com.intellij.util.lang.UrlClassLoader.consumeClassData(UrlClassLoader.java:291)
	at com.intellij.util.lang.ZipResourceFile.findClass(ZipResourceFile.java:116)
	at com.intellij.util.lang.JarLoader.findClass(JarLoader.java:58)
	at com.intellij.util.lang.ClassPath.findClassInLoader(ClassPath.java:240)
	at com.intellij.util.lang.ClassPath.findClass(ClassPath.java:190)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:326)
	... 24 more
2025-06-20 15:33:41,765 [   6067] SEVERE - #c.i.o.a.i.ActionManagerImpl - IntelliJ IDEA 2024.1  Build #IC-241.14494.240
2025-06-20 15:33:41,765 [   6067] SEVERE - #c.i.o.a.i.ActionManagerImpl - JDK: 17.0.10; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-06-20 15:33:41,765 [   6067] SEVERE - #c.i.o.a.i.ActionManagerImpl - OS: Mac OS X
2025-06-20 15:33:41,765 [   6067] SEVERE - #c.i.o.a.i.ActionManagerImpl - Plugin to blame: Zigzag version: 1.0.3
2025-06-20 15:33:41,765 [   6067] SEVERE - #c.i.o.a.i.ActionManagerImpl - Last Action: 
2025-06-20 15:33:41,961 [   6263]   WARN - #c.i.o.a.ActionStub - ActionGroup should be registered using <group> tag: id="GitBranchesTreePopupFilterSeparatorWithText" class="git4idea.ui.branch.popup.GitBranchesTreePopupFilterSeparatorWithText"
2025-06-20 15:33:42,140 [   6442] SEVERE - #c.i.o.a.i.ActionManagerImpl - Cannot load class com.wontlost.zigzag.CodexContextAction (
  error: com/wontlost/zigzag/CodexContextAction has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0,
  classLoader=PluginClassLoader(plugin=PluginDescriptor(name=Zigzag, id=com.wontlost.zigzag, descriptorPath=plugin.xml, path=~/IdeaProjects/zigzag/build/idea-sandbox/plugins/zigzag, version=1.0.3, package=null, isBundled=false), packagePrefix=null, state=active)
)
com.intellij.diagnostic.PluginException: Cannot load class com.wontlost.zigzag.CodexContextAction (
  error: com/wontlost/zigzag/CodexContextAction has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0,
  classLoader=PluginClassLoader(plugin=PluginDescriptor(name=Zigzag, id=com.wontlost.zigzag, descriptorPath=plugin.xml, path=~/IdeaProjects/zigzag/build/idea-sandbox/plugins/zigzag, version=1.0.3, package=null, isBundled=false), packagePrefix=null, state=active)
)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:331)
	at com.intellij.ide.plugins.cl.PluginClassLoader.tryLoadingClass(PluginClassLoader.kt:178)
	at com.intellij.serviceContainer.ComponentManagerImplKt.doLoadClass(ComponentManagerImpl.kt:1466)
	at com.intellij.serviceContainer.ComponentManagerImpl.instantiateClass(ComponentManagerImpl.kt:943)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.instantiate(ActionManagerImpl.kt:1395)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.convertStub(ActionManagerImpl.kt:1635)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.getAction(ActionManagerImpl.kt:2117)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.access$getAction(ActionManagerImpl.kt:1)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImpl$actions$1.invoke(ActionManagerImpl.kt:1122)
	at com.intellij.openapi.actionSystem.impl.ActionManagerImpl$actions$1.invoke(ActionManagerImpl.kt:1121)
	at kotlin.sequences.TransformingSequence$iterator$1.next(Sequences.kt:210)
	at kotlin.sequences.FilteringSequence$iterator$1.calcNext(Sequences.kt:170)
	at kotlin.sequences.FilteringSequence$iterator$1.hasNext(Sequences.kt:194)
	at com.intellij.ide.ui.search.TraverseUIStarter.processKeymap(TraverseUIStarter.java:293)
	at com.intellij.ide.ui.search.TraverseUIStarter.addOptions(TraverseUIStarter.java:164)
	at com.intellij.ide.ui.search.TraverseUIStarter.startup(TraverseUIStarter.java:127)
	at com.intellij.ide.ui.search.TraverseUIStarter.main(TraverseUIStarter.java:89)
	at com.intellij.platform.ide.bootstrap.ApplicationLoader.executeApplicationStarter$lambda$0(ApplicationLoader.kt:400)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1796)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: java.lang.UnsupportedClassVersionError: com/wontlost/zigzag/CodexContextAction has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1108)
	at com.intellij.util.lang.UrlClassLoader.consumeClassData(UrlClassLoader.java:291)
	at com.intellij.util.lang.ZipResourceFile.findClass(ZipResourceFile.java:116)
	at com.intellij.util.lang.JarLoader.findClass(JarLoader.java:58)
	at com.intellij.util.lang.ClassPath.findClassInLoader(ClassPath.java:240)
	at com.intellij.util.lang.ClassPath.findClass(ClassPath.java:190)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:326)
	... 24 more
2025-06-20 15:33:42,141 [   6443] SEVERE - #c.i.o.a.i.ActionManagerImpl - IntelliJ IDEA 2024.1  Build #IC-241.14494.240
2025-06-20 15:33:42,141 [   6443] SEVERE - #c.i.o.a.i.ActionManagerImpl - JDK: 17.0.10; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-06-20 15:33:42,141 [   6443] SEVERE - #c.i.o.a.i.ActionManagerImpl - OS: Mac OS X
2025-06-20 15:33:42,141 [   6443] SEVERE - #c.i.o.a.i.ActionManagerImpl - Plugin to blame: Zigzag version: 1.0.3
2025-06-20 15:33:42,141 [   6443] SEVERE - #c.i.o.a.i.ActionManagerImpl - Last Action: 
2025-06-20 15:33:42,282 [   6584] SEVERE - #c.i.o.o.ConfigurableEP - Cannot create configurable
com.intellij.diagnostic.PluginException: Cannot load class com.wontlost.zigzag.OpenAISettingsConfigurable (
  error: com/wontlost/zigzag/OpenAISettingsConfigurable has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0,
  classLoader=PluginClassLoader(plugin=PluginDescriptor(name=Zigzag, id=com.wontlost.zigzag, descriptorPath=plugin.xml, path=~/IdeaProjects/zigzag/build/idea-sandbox/plugins/zigzag, version=1.0.3, package=null, isBundled=false), packagePrefix=null, state=active)
)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:331)
	at com.intellij.ide.plugins.cl.PluginClassLoader.tryLoadingClass(PluginClassLoader.kt:178)
	at com.intellij.serviceContainer.ComponentManagerImplKt.doLoadClass(ComponentManagerImpl.kt:1466)
	at com.intellij.serviceContainer.ComponentManagerImpl.instantiateClass(ComponentManagerImpl.kt:943)
	at com.intellij.openapi.options.ConfigurableEP$ClassProducer.createElement(ConfigurableEP.java:429)
	at com.intellij.openapi.options.ConfigurableEP.createConfigurable(ConfigurableEP.java:338)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.createConfigurable(ConfigurableWrapper.java:39)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.getConfigurable(ConfigurableWrapper.java:116)
	at com.intellij.ide.ui.search.TraverseUIStarter.addOptions(TraverseUIStarter.java:158)
	at com.intellij.ide.ui.search.TraverseUIStarter.startup(TraverseUIStarter.java:127)
	at com.intellij.ide.ui.search.TraverseUIStarter.main(TraverseUIStarter.java:89)
	at com.intellij.platform.ide.bootstrap.ApplicationLoader.executeApplicationStarter$lambda$0(ApplicationLoader.kt:400)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1796)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: java.lang.UnsupportedClassVersionError: com/wontlost/zigzag/OpenAISettingsConfigurable has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1108)
	at com.intellij.util.lang.UrlClassLoader.consumeClassData(UrlClassLoader.java:291)
	at com.intellij.util.lang.ZipResourceFile.findClass(ZipResourceFile.java:116)
	at com.intellij.util.lang.JarLoader.findClass(JarLoader.java:58)
	at com.intellij.util.lang.ClassPath.findClassInLoader(ClassPath.java:240)
	at com.intellij.util.lang.ClassPath.findClass(ClassPath.java:190)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:326)
	... 18 more
2025-06-20 15:33:42,283 [   6585] SEVERE - #c.i.o.o.ConfigurableEP - IntelliJ IDEA 2024.1  Build #IC-241.14494.240
2025-06-20 15:33:42,283 [   6585] SEVERE - #c.i.o.o.ConfigurableEP - JDK: 17.0.10; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-06-20 15:33:42,283 [   6585] SEVERE - #c.i.o.o.ConfigurableEP - OS: Mac OS X
2025-06-20 15:33:42,283 [   6585] SEVERE - #c.i.o.o.ConfigurableEP - Plugin to blame: Zigzag version: 1.0.3
2025-06-20 15:33:42,283 [   6585] SEVERE - #c.i.o.o.ConfigurableEP - Last Action: 
2025-06-20 15:33:42,284 [   6586] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Can't instantiate configurable for Zigzag [Plugin: com.wontlost.zigzag]
com.intellij.diagnostic.PluginException: Can't instantiate configurable for Zigzag [Plugin: com.wontlost.zigzag]
	at com.intellij.openapi.options.ex.ConfigurableWrapper.getConfigurable(ConfigurableWrapper.java:121)
	at com.intellij.ide.ui.search.TraverseUIStarter.addOptions(TraverseUIStarter.java:158)
	at com.intellij.ide.ui.search.TraverseUIStarter.startup(TraverseUIStarter.java:127)
	at com.intellij.ide.ui.search.TraverseUIStarter.main(TraverseUIStarter.java:89)
	at com.intellij.platform.ide.bootstrap.ApplicationLoader.executeApplicationStarter$lambda$0(ApplicationLoader.kt:400)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1796)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
2025-06-20 15:33:42,285 [   6587] SEVERE - #c.i.o.o.e.ConfigurableWrapper - IntelliJ IDEA 2024.1  Build #IC-241.14494.240
2025-06-20 15:33:42,285 [   6587] SEVERE - #c.i.o.o.e.ConfigurableWrapper - JDK: 17.0.10; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-06-20 15:33:42,285 [   6587] SEVERE - #c.i.o.o.e.ConfigurableWrapper - OS: Mac OS X
2025-06-20 15:33:42,285 [   6587] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Plugin to blame: Zigzag version: 1.0.3
2025-06-20 15:33:42,285 [   6587] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Last Action: 
2025-06-20 15:33:42,285 [   6587] SEVERE - #c.i.o.o.ConfigurableEP - Cannot create configurable
com.intellij.diagnostic.PluginException: Cannot load class com.wontlost.zigzag.OpenAISettingsConfigurable (
  error: com/wontlost/zigzag/OpenAISettingsConfigurable has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0,
  classLoader=PluginClassLoader(plugin=PluginDescriptor(name=Zigzag, id=com.wontlost.zigzag, descriptorPath=plugin.xml, path=~/IdeaProjects/zigzag/build/idea-sandbox/plugins/zigzag, version=1.0.3, package=null, isBundled=false), packagePrefix=null, state=active)
)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:331)
	at com.intellij.ide.plugins.cl.PluginClassLoader.tryLoadingClass(PluginClassLoader.kt:178)
	at com.intellij.serviceContainer.ComponentManagerImplKt.doLoadClass(ComponentManagerImpl.kt:1466)
	at com.intellij.serviceContainer.ComponentManagerImpl.instantiateClass(ComponentManagerImpl.kt:943)
	at com.intellij.openapi.options.ConfigurableEP$ClassProducer.createElement(ConfigurableEP.java:429)
	at com.intellij.openapi.options.ConfigurableEP.createConfigurable(ConfigurableEP.java:338)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.createConfigurable(ConfigurableWrapper.java:39)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.getConfigurable(ConfigurableWrapper.java:116)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.getOriginalClass(ConfigurableWrapper.java:265)
	at com.intellij.ide.ui.search.TraverseUIStarter.addOptions(TraverseUIStarter.java:189)
	at com.intellij.ide.ui.search.TraverseUIStarter.startup(TraverseUIStarter.java:127)
	at com.intellij.ide.ui.search.TraverseUIStarter.main(TraverseUIStarter.java:89)
	at com.intellij.platform.ide.bootstrap.ApplicationLoader.executeApplicationStarter$lambda$0(ApplicationLoader.kt:400)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1796)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: java.lang.UnsupportedClassVersionError: com/wontlost/zigzag/OpenAISettingsConfigurable has been compiled by a more recent version of the Java Runtime (class file version 65.0), this version of the Java Runtime only recognizes class file versions up to 61.0
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1108)
	at com.intellij.util.lang.UrlClassLoader.consumeClassData(UrlClassLoader.java:291)
	at com.intellij.util.lang.ZipResourceFile.findClass(ZipResourceFile.java:116)
	at com.intellij.util.lang.JarLoader.findClass(JarLoader.java:58)
	at com.intellij.util.lang.ClassPath.findClassInLoader(ClassPath.java:240)
	at com.intellij.util.lang.ClassPath.findClass(ClassPath.java:190)
	at com.intellij.ide.plugins.cl.PluginClassLoader.loadClassInsideSelf(PluginClassLoader.kt:326)
	... 19 more
2025-06-20 15:33:42,286 [   6588] SEVERE - #c.i.o.o.ConfigurableEP - IntelliJ IDEA 2024.1  Build #IC-241.14494.240
2025-06-20 15:33:42,286 [   6588] SEVERE - #c.i.o.o.ConfigurableEP - JDK: 17.0.10; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-06-20 15:33:42,286 [   6588] SEVERE - #c.i.o.o.ConfigurableEP - OS: Mac OS X
2025-06-20 15:33:42,286 [   6588] SEVERE - #c.i.o.o.ConfigurableEP - Plugin to blame: Zigzag version: 1.0.3
2025-06-20 15:33:42,286 [   6588] SEVERE - #c.i.o.o.ConfigurableEP - Last Action: 
2025-06-20 15:33:42,287 [   6589] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Can't instantiate configurable for Zigzag [Plugin: com.wontlost.zigzag]
com.intellij.diagnostic.PluginException: Can't instantiate configurable for Zigzag [Plugin: com.wontlost.zigzag]
	at com.intellij.openapi.options.ex.ConfigurableWrapper.getConfigurable(ConfigurableWrapper.java:121)
	at com.intellij.openapi.options.ex.ConfigurableWrapper.getOriginalClass(ConfigurableWrapper.java:265)
	at com.intellij.ide.ui.search.TraverseUIStarter.addOptions(TraverseUIStarter.java:189)
	at com.intellij.ide.ui.search.TraverseUIStarter.startup(TraverseUIStarter.java:127)
	at com.intellij.ide.ui.search.TraverseUIStarter.main(TraverseUIStarter.java:89)
	at com.intellij.platform.ide.bootstrap.ApplicationLoader.executeApplicationStarter$lambda$0(ApplicationLoader.kt:400)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1796)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
2025-06-20 15:33:42,287 [   6589] SEVERE - #c.i.o.o.e.ConfigurableWrapper - IntelliJ IDEA 2024.1  Build #IC-241.14494.240
2025-06-20 15:33:42,287 [   6589] SEVERE - #c.i.o.o.e.ConfigurableWrapper - JDK: 17.0.10; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-06-20 15:33:42,287 [   6589] SEVERE - #c.i.o.o.e.ConfigurableWrapper - OS: Mac OS X
2025-06-20 15:33:42,287 [   6589] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Plugin to blame: Zigzag version: 1.0.3
2025-06-20 15:33:42,287 [   6589] SEVERE - #c.i.o.o.e.ConfigurableWrapper - Last Action: 
2025-06-20 15:33:42,335 [   6637]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/textmate.jar/search/textmate.jar.searchableOptions.xml
2025-06-20 15:33:42,338 [   6640]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/markdown.jar/search/markdown.jar.searchableOptions.xml
2025-06-20 15:33:42,339 [   6641]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/IntelliLang.jar/search/IntelliLang.jar.searchableOptions.xml
2025-06-20 15:33:42,339 [   6641]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/eclipse.jar/search/eclipse.jar.searchableOptions.xml
2025-06-20 15:33:42,340 [   6642]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/sh.jar/search/sh.jar.searchableOptions.xml
2025-06-20 15:33:42,341 [   6643]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/tasks-core.jar/search/tasks-core.jar.searchableOptions.xml
2025-06-20 15:33:42,341 [   6643]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-coverage.jar/search/java-coverage.jar.searchableOptions.xml
2025-06-20 15:33:42,341 [   6643]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-impl.jar!/search/java-impl.jar!.searchableOptions.xml
2025-06-20 15:33:42,341 [   6643]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/javaFX.jar!/search/javaFX.jar!.searchableOptions.xml
2025-06-20 15:33:42,343 [   6645]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/kotlin-plugin-shared.jar/search/kotlin-plugin-shared.jar.searchableOptions.xml
2025-06-20 15:33:42,343 [   6645]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/searchEverywhereMl.jar/search/searchEverywhereMl.jar.searchableOptions.xml
2025-06-20 15:33:42,343 [   6645]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/idea-junit.jar!/search/idea-junit.jar!.searchableOptions.xml
2025-06-20 15:33:42,344 [   6646]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/uiDesigner.jar/search/uiDesigner.jar.searchableOptions.xml
2025-06-20 15:33:42,344 [   6646]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/javaFX.jar/search/javaFX.jar.searchableOptions.xml
2025-06-20 15:33:42,345 [   6647]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/dev.jar/search/dev.jar.searchableOptions.xml
2025-06-20 15:33:42,346 [   6648]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/cwm-plugin-terminal.jar/search/cwm-plugin-terminal.jar.searchableOptions.xml
2025-06-20 15:33:42,347 [   6649]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-svn.jar/search/vcs-svn.jar.searchableOptions.xml
2025-06-20 15:33:42,347 [   6649]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/byteCodeViewer.jar/search/byteCodeViewer.jar.searchableOptions.xml
2025-06-20 15:33:42,347 [   6649]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/testng-plugin.jar!/search/testng-plugin.jar!.searchableOptions.xml
2025-06-20 15:33:42,348 [   6650]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/marketplace.jar/search/marketplace.jar.searchableOptions.xml
2025-06-20 15:33:42,349 [   6651]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/copyright.jar/search/copyright.jar.searchableOptions.xml
2025-06-20 15:33:42,351 [   6653]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/maven.jar/search/maven.jar.searchableOptions.xml
2025-06-20 15:33:42,352 [   6654]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/maven.jar!/search/maven.jar!.searchableOptions.xml
2025-06-20 15:33:42,354 [   6656]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/Groovy.jar/search/Groovy.jar.searchableOptions.xml
2025-06-20 15:33:42,354 [   6656]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/gradle-java.jar/search/gradle-java.jar.searchableOptions.xml
2025-06-20 15:33:42,355 [   6657]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/terminal.jar/search/terminal.jar.searchableOptions.xml
2025-06-20 15:33:42,355 [   6657]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/properties.jar/search/properties.jar.searchableOptions.xml
2025-06-20 15:33:42,355 [   6657]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/performanceTesting-async.jar/search/performanceTesting-async.jar.searchableOptions.xml
2025-06-20 15:33:42,361 [   6663]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-impl.jar/search/java-impl.jar.searchableOptions.xml
2025-06-20 15:33:42,384 [   6686]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/app-client.jar/search/app-client.jar.searchableOptions.xml
2025-06-20 15:33:42,384 [   6686]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/html-tools.jar/search/html-tools.jar.searchableOptions.xml
2025-06-20 15:33:42,385 [   6687]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-debugger-streams.jar/search/java-debugger-streams.jar.searchableOptions.xml
2025-06-20 15:33:42,385 [   6687]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-perforce.jar/search/vcs-perforce.jar.searchableOptions.xml
2025-06-20 15:33:42,385 [   6687]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/gradle.jar/search/gradle.jar.searchableOptions.xml
2025-06-20 15:33:42,386 [   6688]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/testng-plugin.jar/search/testng-plugin.jar.searchableOptions.xml
2025-06-20 15:33:42,386 [   6688]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/grazie.jar/search/grazie.jar.searchableOptions.xml
2025-06-20 15:33:42,386 [   6688]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/qodana.jar/search/qodana.jar.searchableOptions.xml
2025-06-20 15:33:42,391 [   6693]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/app.jar/search/app.jar.searchableOptions.xml
2025-06-20 15:33:42,393 [   6695]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/completionMlRanking.jar/search/completionMlRanking.jar.searchableOptions.xml
2025-06-20 15:33:42,393 [   6695]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-decompiler.jar/search/java-decompiler.jar.searchableOptions.xml
2025-06-20 15:33:42,393 [   6695]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-hg.jar/search/vcs-hg.jar.searchableOptions.xml
2025-06-20 15:33:42,394 [   6696]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/performanceTesting.jar/search/performanceTesting.jar.searchableOptions.xml
2025-06-20 15:33:42,394 [   6696]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-gitlab.jar/search/vcs-gitlab.jar.searchableOptions.xml
2025-06-20 15:33:42,395 [   6697]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/cwm-common.jar/search/cwm-common.jar.searchableOptions.xml
2025-06-20 15:33:42,395 [   6697]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-i18n.jar/search/java-i18n.jar.searchableOptions.xml
2025-06-20 15:33:42,395 [   6697]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/Groovy.jar!/search/Groovy.jar!.searchableOptions.xml
2025-06-20 15:33:42,395 [   6697]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/toml.jar/search/toml.jar.searchableOptions.xml
2025-06-20 15:33:42,396 [   6698]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-ijent-impl.jar/search/platform-ijent-impl.jar.searchableOptions.xml
2025-06-20 15:33:42,396 [   6698]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/idea-junit.jar/search/idea-junit.jar.searchableOptions.xml
2025-06-20 15:33:42,397 [   6699]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/yaml.jar/search/yaml.jar.searchableOptions.xml
2025-06-20 15:33:42,397 [   6699]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/packageChecker.jar/search/packageChecker.jar.searchableOptions.xml
2025-06-20 15:33:42,397 [   6699]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-tracing-ide.jar/search/platform-tracing-ide.jar.searchableOptions.xml
2025-06-20 15:33:42,398 [   6700]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/editorconfig.jar/search/editorconfig.jar.searchableOptions.xml
2025-06-20 15:33:42,398 [   6700]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-github.jar/search/vcs-github.jar.searchableOptions.xml
2025-06-20 15:33:42,398 [   6700]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-langInjection.jar/search/platform-langInjection.jar.searchableOptions.xml
2025-06-20 15:33:42,399 [   6701]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-images.jar/search/platform-images.jar.searchableOptions.xml
2025-06-20 15:33:42,400 [   6702]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/kotlin-plugin.jar/search/kotlin-plugin.jar.searchableOptions.xml
2025-06-20 15:33:42,401 [   6703]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-git.jar/search/vcs-git.jar.searchableOptions.xml
2025-06-20 15:33:42,401 [   6703]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/modules.jar/search/modules.jar.searchableOptions.xml
2025-06-20 15:33:42,401 [   6703]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/indexing-shared.jar/search/indexing-shared.jar.searchableOptions.xml
2025-06-20 15:33:42,402 [   6704]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/featuresTrainer.jar/search/featuresTrainer.jar.searchableOptions.xml
2025-06-20 15:33:42,402 [   6704]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/kotlin-plugin.jar!/search/kotlin-plugin.jar!.searchableOptions.xml
2025-06-20 15:33:42,402 [   6704]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/cwm-host.jar/search/cwm-host.jar.searchableOptions.xml
2025-06-20 15:33:42,403 [   6705]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/gradle-java-maven.jar/search/gradle-java-maven.jar.searchableOptions.xml
2025-06-20 15:33:42,403 [   6705]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/settingsSync.jar/search/settingsSync.jar.searchableOptions.xml
2025-06-20 15:33:42,403 [   6705]   INFO - STDOUT - Searchable options index builder completed
2025-06-20 15:33:42,404 [   6706]   INFO - #c.i.u.i.FileBasedIndexImpl - Index dispose started
2025-06-20 15:33:42,423 [   6725]   INFO - #c.i.p.s.StubIndexImpl - StubIndexExtension-s were unloaded
2025-06-20 15:33:42,423 [   6725]   INFO - #c.i.p.s.SerializationManagerImpl - Start shutting down /Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/index/rep.names
2025-06-20 15:33:42,423 [   6725]   INFO - #c.i.p.s.SerializationManagerImpl - Finished shutting down /Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/index/rep.names
2025-06-20 15:33:42,424 [   6726]   INFO - #c.i.u.i.FileBasedIndexImpl - Index dispose completed in 20ms.
2025-06-20 15:33:42,427 [   6729]   INFO - #c.i.o.v.n.p.PersistentFSImpl - VFS dispose started
2025-06-20 15:33:42,427 [   6729]   INFO - #c.i.o.v.n.p.FSRecordsImpl - VFS closing
2025-06-20 15:33:42,428 [   6730]   INFO - #c.i.o.v.n.p.PersistentFSImpl - VFS dispose completed in 0 ms.
2025-06-20 15:33:42,437 [   6739]   INFO - #c.i.p.i.b.AppStarter - ------------------------------------------------------ IDE SHUTDOWN ------------------------------------------------------
2025-06-20 15:34:26,422 [      3]   INFO - #c.i.p.i.b.AppStarter - ------------------------------------------------------ IDE STARTED ------------------------------------------------------
2025-06-20 15:34:26,424 [      5]   INFO - #c.i.i.p.PluginManager - Plugin PluginDescriptor(name=Groovy, id=org.intellij.groovy, descriptorPath=plugin.xml, path=~/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/plugins/Groovy, version=241.14494.240, package=org.jetbrains.plugins.groovy, isBundled=true) misses optional descriptor duplicates-groovy.xml
2025-06-20 15:34:26,429 [     10]   INFO - #c.i.i.p.PluginManager - Plugin PluginDescriptor(name=Groovy, id=org.intellij.groovy, descriptorPath=plugin.xml, path=~/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/plugins/Groovy, version=241.14494.240, package=org.jetbrains.plugins.groovy, isBundled=true) misses optional descriptor duplicates-detection-groovy.xml
2025-06-20 15:34:26,439 [     20]   INFO - #c.i.u.EnvironmentUtil - loading shell env is skipped: IDE has been launched from a terminal (SHLVL=1)
2025-06-20 15:34:26,443 [     24]   INFO - #c.i.i.p.PluginManager - Using broken plugins file from IDE distribution
2025-06-20 15:34:26,452 [     33]   INFO - #c.i.u.i.PageCacheUtils - File page caching params:
2025-06-20 15:34:26,453 [     34]   INFO - #c.i.u.i.PageCacheUtils - 	DEFAULT_PAGE_SIZE: 10485760
2025-06-20 15:34:26,453 [     34]   INFO - #c.i.u.i.PageCacheUtils - 	Direct memory to use, max: 2126512128
2025-06-20 15:34:26,454 [     35]   INFO - #c.i.u.i.PageCacheUtils - 	FilePageCache: regular + lock-free (LOCK_FREE_PAGE_CACHE_ENABLED:true)
2025-06-20 15:34:26,455 [     36]   INFO - #c.i.u.i.PageCacheUtils - 	NEW_PAGE_CACHE_MEMORY_FRACTION: 0.20000000298023224
2025-06-20 15:34:26,455 [     36]   INFO - #c.i.u.i.PageCacheUtils - 	Regular FilePageCache: 503316478 bytes
2025-06-20 15:34:26,456 [     37]   INFO - #c.i.u.i.PageCacheUtils - 	New     FilePageCache: 125829122 bytes (+ up to 10.0% overflow)
2025-06-20 15:34:26,456 [     37]   INFO - #c.i.u.i.PageCacheUtils - 	DirectByteBuffers pool: 104857600 bytes
2025-06-20 15:34:26,454 [     35]   INFO - #c.i.p.i.b.AppStarter - JNA library (64-bit) loaded in 18 ms
2025-06-20 15:34:26,459 [     40]   INFO - #c.i.p.i.b.AppStarter - IDE: IntelliJ IDEA (build #IC-241.14494.240, Thu, 28 Mar 2024 05:12:00 GMT)
2025-06-20 15:34:26,459 [     40]   INFO - #c.i.p.i.b.AppStarter - OS: Mac OS X (15.5)
2025-06-20 15:34:26,460 [     41]   INFO - #c.i.p.i.b.AppStarter - JRE: 17.0.10+8-b1207.12, aarch64 (JetBrains s.r.o.)
2025-06-20 15:34:26,460 [     41]   INFO - #c.i.p.i.b.AppStarter - JVM: 17.0.10+8-b1207.12 (OpenJDK 64-Bit Server VM)
2025-06-20 15:34:26,462 [     43]   INFO - #c.i.p.i.b.AppStarter - PID: 66756
2025-06-20 15:34:26,475 [     56]   INFO - #c.i.p.i.b.AppStarter - JVM options: [-Daether.connector.resumeDownloads=false, -Dapple.awt.application.appearance=system, -Dapple.awt.fileDialogForDirectories=true, -Dapple.laf.useScreenMenuBar=true, -Dide.show.tips.on.startup.default.value=false, -Didea.auto.reload.plugins=true, -Didea.classpath.index.enabled=false, -Didea.config.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/config, -Didea.is.internal=true, -Didea.log.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log, -Didea.paths.selector=IdeaIC2024.1, -Didea.platform.prefix=Idea, -Didea.plugin.in.sandbox.mode=true, -Didea.plugins.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/plugins, -Didea.required.plugins.id=com.wontlost.zigzag, -Didea.smooth.progress=false, -Didea.system.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system, -Didea.vendor.name=JetBrains, -Dintellij.platform.runtime.repository.path=/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/modules/module-descriptors.jar, -Djava.system.class.loader=com.intellij.util.lang.PathClassLoader, -Djbr.catch.SIGABRT=true, -Djdk.attach.allowAttachSelf=true, -Djdk.http.auth.tunneling.disabledSchemes="", -Djdk.module.illegalAccess.silent=true, -Djna.boot.library.path=/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/lib/jna/aarch64, -Djna.noclasspath=true, -Djna.nosys=true, -Dpty4j.preferred.native.folder=/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/lib/pty4j, -Dsplash=true, -Dsun.io.useCanonCaches=false, -Dsun.java2d.metal=true, -XX:+HeapDumpOnOutOfMemoryError, -XX:-OmitStackTraceInFastThrow, -XX:+IgnoreUnrecognizedVMOptions, -XX:CICompilerCount=2, -XX:ReservedCodeCacheSize=512m, -XX:CompileCommand=exclude,com/intellij/openapi/vfs/impl/FilePartNodeRoot,trieDescend, -XX:SoftRefLRUPolicyMSPerMB=50, -javaagent:/Users/<USER>/IdeaProjects/zigzag/build/tmp/initializeIntelliJPlugin/coroutines-javaagent.jar, --add-opens=java.base/java.io=ALL-UNNAMED, --add-opens=java.base/java.lang=ALL-UNNAMED, --add-opens=java.base/java.lang.ref=ALL-UNNAMED, --add-opens=java.base/java.lang.reflect=ALL-UNNAMED, --add-opens=java.base/java.net=ALL-UNNAMED, --add-opens=java.base/java.nio=ALL-UNNAMED, --add-opens=java.base/java.nio.charset=ALL-UNNAMED, --add-opens=java.base/java.text=ALL-UNNAMED, --add-opens=java.base/java.time=ALL-UNNAMED, --add-opens=java.base/java.util=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED, --add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED, --add-opens=java.base/jdk.internal.vm=ALL-UNNAMED, --add-opens=java.base/sun.nio.ch=ALL-UNNAMED, --add-opens=java.base/sun.nio.fs=ALL-UNNAMED, --add-opens=java.base/sun.security.ssl=ALL-UNNAMED, --add-opens=java.base/sun.security.util=ALL-UNNAMED, --add-opens=java.base/sun.net.dns=ALL-UNNAMED, --add-opens=java.desktop/com.apple.eawt=ALL-UNNAMED, --add-opens=java.desktop/com.apple.eawt.event=ALL-UNNAMED, --add-opens=java.desktop/com.apple.laf=ALL-UNNAMED, --add-opens=java.desktop/java.awt=ALL-UNNAMED, --add-opens=java.desktop/java.awt.dnd.peer=ALL-UNNAMED, --add-opens=java.desktop/java.awt.event=ALL-UNNAMED, --add-opens=java.desktop/java.awt.image=ALL-UNNAMED, --add-opens=java.desktop/java.awt.peer=ALL-UNNAMED, --add-opens=java.desktop/java.awt.font=ALL-UNNAMED, --add-opens=java.desktop/javax.swing=ALL-UNNAMED, --add-opens=java.desktop/javax.swing.plaf.basic=ALL-UNNAMED, --add-opens=java.desktop/javax.swing.text=ALL-UNNAMED, --add-opens=java.desktop/javax.swing.text.html=ALL-UNNAMED, --add-opens=java.desktop/sun.awt.datatransfer=ALL-UNNAMED, --add-opens=java.desktop/sun.awt.image=ALL-UNNAMED, --add-opens=java.desktop/sun.awt=ALL-UNNAMED, --add-opens=java.desktop/sun.font=ALL-UNNAMED, --add-opens=java.desktop/sun.java2d=ALL-UNNAMED, --add-opens=java.desktop/sun.lwawt=ALL-UNNAMED, --add-opens=java.desktop/sun.lwawt.macosx=ALL-UNNAMED, --add-opens=java.desktop/sun.swing=ALL-UNNAMED, --add-opens=java.desktop/com.sun.java.swing=ALL-UNNAMED, --add-opens=jdk.attach/sun.tools.attach=ALL-UNNAMED, --add-opens=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED, --add-opens=jdk.internal.jvmstat/sun.jvmstat.monitor=ALL-UNNAMED, --add-opens=jdk.jdi/com.sun.tools.jdi=ALL-UNNAMED, -Xms128m, -Xmx2048m, -Dfile.encoding=UTF-8, -Duser.country=NZ, -Duser.language=en, -Duser.variant, -ea]
2025-06-20 15:34:26,476 [     57]   INFO - #c.i.p.i.b.AppStarter - args: traverseUI /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions true
2025-06-20 15:34:26,476 [     57]   INFO - #c.i.p.i.b.AppStarter - library path: /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-06-20 15:34:26,476 [     57]   INFO - #c.i.p.i.b.AppStarter - boot library path: /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains/jbre/jbr_jcef-17.0.10-osx-aarch64-b1207.12/extracted/jbr_jcef-17.0.10-osx-aarch64-b1207.12/Contents/Home/lib
2025-06-20 15:34:26,483 [     64]   INFO - #c.i.p.i.b.AppStarter - locale=en_NZ JNU=UTF-8 file.encoding=UTF-8
    idea.config.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/config
    idea.system.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system
    idea.plugins.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/plugins
    idea.log.path=/Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/log
2025-06-20 15:34:26,487 [     68]   INFO - #c.i.p.i.b.AppStarter - CPU cores: 10; ForkJoinPool.commonPool: java.util.concurrent.ForkJoinPool@89d103c[Running, parallelism = 10, size = 0, active = 0, running = 0, steals = 0, tasks = 0, submissions = 0]; factory: com.intellij.concurrency.IdeaForkJoinWorkerThreadFactory@75c06672
2025-06-20 15:34:26,490 [     71]   INFO - #c.i.i.p.PluginManager - Module intellij.sh.python is not enabled because dependency intellij.python.community.impl is not available
Module intellij.groovy/ant is not enabled because dependency AntSupport is not available
Module intellij.qodana.jvm.swagger is not enabled because dependency com.intellij.swagger is not available
Module intellij.qodana.python is not enabled because dependency intellij.python is not available
Module intellij.qodana.php is not enabled because dependency com.jetbrains.php is not available
Module intellij.qodana.js is not enabled because dependency JavaScript is not available
Module intellij.qodana.inspectionKts.js is not enabled because dependency JavaScript is not available
Module intellij.qodana.go is not enabled because dependency intellij.go.coverage is not available
Module intellij.cwm.plugin.elevation is not enabled because dependency intellij.execution.process.elevation is not available
Module intellij.packageChecker.php is not enabled because dependency com.jetbrains.php is not available
Module intellij.packageChecker.python is not enabled because dependency intellij.python.community.impl is not available
Module intellij.packageChecker.javascript is not enabled because dependency JavaScript is not available
Module intellij.packageChecker.go is not enabled because dependency org.jetbrains.plugins.go is not available
2025-06-20 15:34:26,502 [     83]   INFO - #c.i.i.p.PluginManager - Loaded bundled plugins: IDEA CORE (241.14494.240), TextMate Bundles (241.14494.240), Images (241.14494.240), Copyright (241.14494.240), EditorConfig (241.14494.240), IDE Services / Provisioner API (241.14494.240), Machine Learning in Search Everywhere (241.14494.240), YAML (241.14494.240), JetBrains maven model api classes (241.14494.240), JetBrains Repository Search (241.14494.240), Maven server api classes (241.14494.240), Markdown (241.14494.240), Terminal (241.14494.240), Mercurial (241.14494.240), Properties (241.14494.240), Gradle (241.14494.240), NetBeans Keymap (241.14494.240), JetBrains Marketplace Licensing (241.14494.240), com.intellij.dev (241.14494.240), Remote Execution Agent (241.14494.240), Shell Script (241.14494.240), HTML Tools (241.14494.240), Visual Studio Keymap (241.14494.240), Machine Learning Code Completion (241.14494.240), Toml (241.14494.240), WebP Support (241.14494.240), Performance Testing (241.14494.240), Grazie Lite (241.14494.240), Eclipse Keymap (241.14494.240), Perforce Helix Core (241.14494.240), GitLab (241.14494.240), IDE Features Trainer (241.14494.240), Java (241.14494.240), com.intellij.tracing.ide (241.14494.240), Java Bytecode Decompiler (241.14494.240), Java Stream Debugger (241.14494.240), Task Management (241.14494.240), GitHub (241.14494.240), JUnit (241.14494.240), Eclipse Interoperability (241.14494.240), IntelliLang (241.14494.240), Groovy (241.14494.240), JavaFX (241.14494.240), Turbo Complete (241.14494.240), Kotlin (241.14494.240-IJ), Gradle DSL API (241.14494.240), Java Internationalization (241.14494.240), UI Designer (241.14494.240), Bytecode Viewer (241.14494.240), Maven (241.14494.240), TestNG (241.14494.240), Code Coverage for Java (241.14494.240), Gradle-Java (241.14494.240), Gradle-Java-Analysis (241.14494.240), Gradle-Maven (241.14494.240), Java IDE Customization (241.14494.240), Settings Sync (241.14494.240), Git (241.14494.240), Qodana (241.14494.240), Configuration Script (241.14494.240), Shared Indexes (241.14494.240), Code With Me (241.14494.240), Subversion (241.14494.240), Package Checker (241.14494.240), Gradle Dependency Updater Implementation (241.14494.240), Async Profiler for IDE Performance Testing (241.14494.240)
2025-06-20 15:34:26,502 [     83]   INFO - #c.i.i.p.PluginManager - Loaded custom plugins: Zigzag (1.0.3)
2025-06-20 15:34:26,550 [    131]   INFO - c.i.p.i.IdeFingerprint - Calculated dependencies fingerprint in 7 ms (hash=3kp3r3cytob16, buildTime=1711602720, appVersion=IC-241.14494.240)
2025-06-20 15:34:26,616 [    197]   INFO - #c.i.a.o.PathMacrosImpl - Loaded path macros: {MAVEN_REPOSITORY=/Users/<USER>/.m2/repository}
2025-06-20 15:34:26,638 [    219]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS uses 'fast' names enumerator (over mmapped file)
2025-06-20 15:34:26,640 [    221]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS uses streamlined attributes storage (over mmapped file)
2025-06-20 15:34:26,640 [    221]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS uses content storage over memory-mapped file, with compression algo: LZ4[ > 8000b ]
2025-06-20 15:34:26,663 [    244]   INFO - #c.i.o.v.n.p.PersistentFSLoader - VFS: impl (expected) version=846397, 14 file records, 0 content blobs
2025-06-20 15:34:26,669 [    250]   INFO - #c.i.o.v.n.p.FSRecordsImpl - VFS initialized: 34 ms, 0 failed attempts, 0 error(s) were recovered
2025-06-20 15:34:26,670 [    251]   INFO - #c.i.o.v.n.p.FSRecordsImpl - VFS scanned: file-by-name index was populated
2025-06-20 15:34:26,809 [    390]   INFO - #c.i.o.v.n.p.FSRecords - VFS health-check enabled: first after 600000 ms, and each following 43200000 ms, wrap in RA: true
2025-06-20 15:34:26,815 [    396]   INFO - #c.i.i.p.ExpiredPluginsState - Plugins to skip: []
2025-06-20 15:34:26,919 [    500]   INFO - #o.j.i.BuiltInServerManager - built-in server started, port 63343
2025-06-20 15:34:26,929 [    510]   INFO - #c.i.u.n.s.CertificateManager - Default SSL context initialized
2025-06-20 15:34:26,957 [    538]   INFO - #c.i.o.v.i.l.NativeFileWatcherImpl - Native file watcher is disabled
2025-06-20 15:34:26,989 [    570]   INFO - #c.i.i.s.p.i.SharedIndexMainZipStorage - Shared Indexes Storage is opened and empty
2025-06-20 15:34:26,991 [    572]   WARN - #c.i.i.s.p.i.BundledSharedIndexProvider - Bundled shared index is not found at: /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.jetbrains.intellij.idea/ideaIC/2024.1/181fa36f74690e64a81a8e06ceda9480d2a6c626/ideaIC-2024.1/jdk-shared-indexes
2025-06-20 15:34:27,051 [    632]   INFO - #c.i.u.i.FileBasedIndexImpl - Indices to be built:FilenameIndex(v = 258)
2025-06-20 15:34:27,053 [    634]   INFO - #c.i.u.i.FileBasedIndexImpl - Using nice flusher for indexes
2025-06-20 15:34:27,061 [    642]   INFO - #c.i.u.i.IndexDataInitializer - Index data initialization done: 105 ms. Initialized indexes: [FilenameIndex, filetypes, XmlTagNames, DomFileIndex, RelaxSymbolIndex, Trigram.Index, XmlNamespaces, FrameworkDetectionIndex, html5.custom.attributes.index, editorconfig.index.name, fileIncludes, java.auto.module.name, SchemaTypeInheritance, TodoIndex, IdIndex, json.file.root.values, java.source.module.name, yaml.keys.name, java.null.method.argument, HtmlTagIdIndex, java.fun.expression, xmlProperties, java.binary.plus.expression, JavaFxControllerClassIndex, javafx.custom.component, bytecodeAnalysis, org.jetbrains.kotlin.idea.vfilefinder.KotlinClassFileIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinPartialPackageNamesIndex, org.jetbrains.kotlin.idea.versions.KotlinJsMetadataVersionIndex, org.jetbrains.kotlin.idea.versions.KotlinJvmMetadataVersionIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinJavaScriptMetaFileIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinMetadataFilePackageIndex, javafx.id.name, org.jetbrains.kotlin.idea.vfilefinder.KotlinMetadataFileIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinModuleMappingIndex, KotlinPackageSourcesMemberNamesIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinBuiltInsMetadataIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinJvmModuleAnnotationsIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinStdlibIndex, KotlinBinaryRootToPackageIndex, KotlinTopLevelCallableByPackageShortNameIndex, KotlinTopLevelClassLikeDeclarationByPackageShortNameIndex, org.jetbrains.kotlin.idea.vfilefinder.KotlinShortClassNameFileIndex, org.jetbrains.kotlin.idea.vfilefinder.KlibMetaFileIndex, com.intellij.uiDesigner.FormClassIndex, Stubs].
2025-06-20 15:34:27,108 [    689]   INFO - #c.i.u.i.IndexDataInitializer - Index data initialization done: 46 ms. Initialized stub indexes: {gr.script.class, jvm.static.member.type, java.class.extlist, java.field.name, KotlinOverridableInternalMembersShortNameIndex, properties.index, KotlinTypeAliasByExpansionShortNameIndex, java.class.fqn, java.method.name, gr.method.name, kotlin.primeIndexKey, org.jetbrains.kotlin.idea.stubindex.KotlinInnerTypeAliasClassIdIndex, org.jetbrains.kotlin.idea.stubindex.KotlinAnnotationsIndex, org.jetbrains.kotlin.idea.stubindex.KotlinTypeAliasShortNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinFunctionShortNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinFileFacadeShortNameIndex, KotlinTopLevelFunctionByPackageIndex, org.jetbrains.kotlin.idea.stubindex.KotlinJvmNameAnnotationIndex, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelTypeAliasFqNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinPropertyShortNameIndex, java.class.shortname, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelPropertyFqnNameIndex, gr.field.name, gr.class.super, gr.annot.method.name, org.jetbrains.kotlin.idea.stubindex.KotlinFullClassNameIndex, KotlinProbablyNothingFunctionShortNameIndex, gr.class.fqn.s, org.jetbrains.kotlin.idea.stubindex.KotlinSuperClassIndex, org.jetbrains.kotlin.idea.stubindex.KotlinFileFacadeFqNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinScriptFqnIndex, org.jetbrains.kotlin.idea.stubindex.KotlinExactPackagesIndex, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelClassByPackageIndex, KotlinTopLevelTypeAliasByPackageIndex, java.module.name, KotlinTopLevelExtensionsByReceiverTypeIndex, KotlinTopLevelPropertyByPackageIndex, jvm.static.member.name, java.unnamed.class, org.jetbrains.kotlin.idea.stubindex.KotlinTopLevelFunctionFqnNameIndex, org.jetbrains.kotlin.idea.stubindex.KotlinClassShortNameIndex, java.method.parameter.types, markdown.header, org.jetbrains.kotlin.idea.stubindex.KotlinSubclassObjectNameIndex, KotlinProbablyContractedFunctionShortNameIndex, KotlinProbablyNothingPropertyShortNameIndex, gr.script.fqn.s, java.annotations, gr.anonymous.class, java.anonymous.baseref, org.jetbrains.kotlin.idea.stubindex.KotlinMultifileClassPartIndex, KotlinFileFacadeClassByPackageIndex, gr.annot.members, dom.elementClass, KotlinExtensionsInObjectsByReceiverTypeIndex, dom.namespaceKey, markdown.header.anchor, org.jetbrains.kotlin.idea.stubindex.KotlinFilePartClassIndex}.
2025-06-20 15:34:28,207 [   1788]   INFO - #c.i.i.p.n.PluginsGroupComponentWithProgress - Marketplace tab: loading stopped
2025-06-20 15:34:28,218 [   1799]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Staff Picks' group load started
2025-06-20 15:34:28,323 [   1904]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Staff Picks' group load finished
2025-06-20 15:34:28,323 [   1904]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'New and Updated' group load started
2025-06-20 15:34:28,348 [   1929]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'New and Updated' group load finished
2025-06-20 15:34:28,348 [   1929]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Downloads' group load started
2025-06-20 15:34:28,407 [   1988]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Downloads' group load finished
2025-06-20 15:34:28,407 [   1988]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Rated' group load started
2025-06-20 15:34:28,431 [   2012]   INFO - #c.i.i.p.PluginManagerConfigurable - Marketplace tab: 'Top Rated' group load finished
2025-06-20 15:34:28,608 [   2189]   INFO - #c.i.w.i.i.j.s.JpsGlobalModelSynchronizerImpl - Loading global entities com.intellij.platform.workspace.jps.entities.SdkEntity from files
2025-06-20 15:34:28,609 [   2190]   INFO - #c.i.w.i.i.j.s.JpsGlobalModelSynchronizerImpl - Loading global entities com.intellij.platform.workspace.jps.entities.LibraryEntity from files
2025-06-20 15:34:29,175 [   2756]   INFO - #c.i.o.a.i.NonBlockingReadActionImpl - OTel monitoring for NonBlockingReadAction is enabled
2025-06-20 15:34:30,800 [   4381]   INFO - #c.i.r.OsRegistryConfigProvider - Looking for 'lobbyServerUrl' value in [/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Library/Application Support/JetBrains/CodeWithMe/config.json, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/config.json]
2025-06-20 15:34:30,801 [   4382]   INFO - #c.i.r.OsRegistryConfigProvider - OS provided value for 'lobbyServerUrl' is not found
2025-06-20 15:34:30,801 [   4382]   INFO - #c.j.r.p.c.u.CodeWithMeSystemSettings - Setting default value='https://code-with-me.jetbrains.com'
2025-06-20 15:34:30,810 [   4391]   INFO - #c.i.r.OsRegistryConfigProvider - Looking for 'lobbyServerUrl' value in [/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Library/Application Support/JetBrains/CodeWithMe/config.json, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/lobbyServerUrl, /Users/<USER>/Library/Application Support/JetBrains/CodeWithMe/config.json]
2025-06-20 15:34:30,810 [   4391]   INFO - #c.i.r.OsRegistryConfigProvider - OS provided value for 'lobbyServerUrl' is not found
2025-06-20 15:34:30,810 [   4391]   INFO - #c.j.r.p.c.u.CodeWithMeSystemSettings - Setting default value='https://code-with-me.jetbrains.com'
2025-06-20 15:34:31,694 [   5275]   WARN - #c.i.u.j.JBCefApp - JCEF is manually disabled in headless env via 'ide.browser.jcef.headless.enabled=false'
2025-06-20 15:34:32,140 [   5721]   INFO - STDOUT - Found 207 configurables
2025-06-20 15:34:32,148 [   5729]   INFO - #c.i.i.p.n.PluginsGroupComponentWithProgress - Marketplace tab: loading stopped
2025-06-20 15:34:32,500 [   6081]   WARN - #c.i.o.a.ActionStub - ActionGroup should be registered using <group> tag: id="GitBranchesTreePopupFilterSeparatorWithText" class="git4idea.ui.branch.popup.GitBranchesTreePopupFilterSeparatorWithText"
2025-06-20 15:34:32,866 [   6447]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/textmate.jar/search/textmate.jar.searchableOptions.xml
2025-06-20 15:34:32,870 [   6451]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/markdown.jar/search/markdown.jar.searchableOptions.xml
2025-06-20 15:34:32,871 [   6452]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/IntelliLang.jar/search/IntelliLang.jar.searchableOptions.xml
2025-06-20 15:34:32,871 [   6452]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/eclipse.jar/search/eclipse.jar.searchableOptions.xml
2025-06-20 15:34:32,872 [   6453]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/sh.jar/search/sh.jar.searchableOptions.xml
2025-06-20 15:34:32,873 [   6454]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/tasks-core.jar/search/tasks-core.jar.searchableOptions.xml
2025-06-20 15:34:32,873 [   6454]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-coverage.jar/search/java-coverage.jar.searchableOptions.xml
2025-06-20 15:34:32,873 [   6454]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-impl.jar!/search/java-impl.jar!.searchableOptions.xml
2025-06-20 15:34:32,873 [   6454]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/javaFX.jar!/search/javaFX.jar!.searchableOptions.xml
2025-06-20 15:34:32,875 [   6456]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/kotlin-plugin-shared.jar/search/kotlin-plugin-shared.jar.searchableOptions.xml
2025-06-20 15:34:32,876 [   6457]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/searchEverywhereMl.jar/search/searchEverywhereMl.jar.searchableOptions.xml
2025-06-20 15:34:32,876 [   6457]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/idea-junit.jar!/search/idea-junit.jar!.searchableOptions.xml
2025-06-20 15:34:32,877 [   6458]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/uiDesigner.jar/search/uiDesigner.jar.searchableOptions.xml
2025-06-20 15:34:32,877 [   6458]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/javaFX.jar/search/javaFX.jar.searchableOptions.xml
2025-06-20 15:34:32,878 [   6459]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/dev.jar/search/dev.jar.searchableOptions.xml
2025-06-20 15:34:32,879 [   6460]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/cwm-plugin-terminal.jar/search/cwm-plugin-terminal.jar.searchableOptions.xml
2025-06-20 15:34:32,880 [   6461]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-svn.jar/search/vcs-svn.jar.searchableOptions.xml
2025-06-20 15:34:32,880 [   6461]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/byteCodeViewer.jar/search/byteCodeViewer.jar.searchableOptions.xml
2025-06-20 15:34:32,880 [   6461]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/testng-plugin.jar!/search/testng-plugin.jar!.searchableOptions.xml
2025-06-20 15:34:32,881 [   6462]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/marketplace.jar/search/marketplace.jar.searchableOptions.xml
2025-06-20 15:34:32,881 [   6462]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/copyright.jar/search/copyright.jar.searchableOptions.xml
2025-06-20 15:34:32,883 [   6464]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/maven.jar/search/maven.jar.searchableOptions.xml
2025-06-20 15:34:32,883 [   6464]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/maven.jar!/search/maven.jar!.searchableOptions.xml
2025-06-20 15:34:32,885 [   6466]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/Groovy.jar/search/Groovy.jar.searchableOptions.xml
2025-06-20 15:34:32,886 [   6467]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/instrumented-zigzag-1.0.3.jar/search/instrumented-zigzag-1.0.3.jar.searchableOptions.xml
2025-06-20 15:34:32,886 [   6467]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/gradle-java.jar/search/gradle-java.jar.searchableOptions.xml
2025-06-20 15:34:32,887 [   6468]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/terminal.jar/search/terminal.jar.searchableOptions.xml
2025-06-20 15:34:32,887 [   6468]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/properties.jar/search/properties.jar.searchableOptions.xml
2025-06-20 15:34:32,887 [   6468]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/performanceTesting-async.jar/search/performanceTesting-async.jar.searchableOptions.xml
2025-06-20 15:34:32,892 [   6473]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-impl.jar/search/java-impl.jar.searchableOptions.xml
2025-06-20 15:34:32,904 [   6485]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/app-client.jar/search/app-client.jar.searchableOptions.xml
2025-06-20 15:34:32,905 [   6486]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/html-tools.jar/search/html-tools.jar.searchableOptions.xml
2025-06-20 15:34:32,905 [   6486]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-debugger-streams.jar/search/java-debugger-streams.jar.searchableOptions.xml
2025-06-20 15:34:32,906 [   6487]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-perforce.jar/search/vcs-perforce.jar.searchableOptions.xml
2025-06-20 15:34:32,906 [   6487]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/gradle.jar/search/gradle.jar.searchableOptions.xml
2025-06-20 15:34:32,906 [   6487]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/testng-plugin.jar/search/testng-plugin.jar.searchableOptions.xml
2025-06-20 15:34:32,906 [   6487]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/grazie.jar/search/grazie.jar.searchableOptions.xml
2025-06-20 15:34:32,906 [   6487]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/qodana.jar/search/qodana.jar.searchableOptions.xml
2025-06-20 15:34:32,909 [   6490]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/app.jar/search/app.jar.searchableOptions.xml
2025-06-20 15:34:32,909 [   6490]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/completionMlRanking.jar/search/completionMlRanking.jar.searchableOptions.xml
2025-06-20 15:34:32,910 [   6491]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-decompiler.jar/search/java-decompiler.jar.searchableOptions.xml
2025-06-20 15:34:32,910 [   6491]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-hg.jar/search/vcs-hg.jar.searchableOptions.xml
2025-06-20 15:34:32,910 [   6491]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/performanceTesting.jar/search/performanceTesting.jar.searchableOptions.xml
2025-06-20 15:34:32,910 [   6491]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-gitlab.jar/search/vcs-gitlab.jar.searchableOptions.xml
2025-06-20 15:34:32,911 [   6492]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/cwm-common.jar/search/cwm-common.jar.searchableOptions.xml
2025-06-20 15:34:32,911 [   6492]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/java-i18n.jar/search/java-i18n.jar.searchableOptions.xml
2025-06-20 15:34:32,911 [   6492]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/Groovy.jar!/search/Groovy.jar!.searchableOptions.xml
2025-06-20 15:34:32,911 [   6492]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/toml.jar/search/toml.jar.searchableOptions.xml
2025-06-20 15:34:32,911 [   6492]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-ijent-impl.jar/search/platform-ijent-impl.jar.searchableOptions.xml
2025-06-20 15:34:32,911 [   6492]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/idea-junit.jar/search/idea-junit.jar.searchableOptions.xml
2025-06-20 15:34:32,913 [   6494]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/yaml.jar/search/yaml.jar.searchableOptions.xml
2025-06-20 15:34:32,913 [   6494]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/packageChecker.jar/search/packageChecker.jar.searchableOptions.xml
2025-06-20 15:34:32,913 [   6494]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-tracing-ide.jar/search/platform-tracing-ide.jar.searchableOptions.xml
2025-06-20 15:34:32,914 [   6495]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/editorconfig.jar/search/editorconfig.jar.searchableOptions.xml
2025-06-20 15:34:32,914 [   6495]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-github.jar/search/vcs-github.jar.searchableOptions.xml
2025-06-20 15:34:32,914 [   6495]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-langInjection.jar/search/platform-langInjection.jar.searchableOptions.xml
2025-06-20 15:34:32,914 [   6495]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/platform-images.jar/search/platform-images.jar.searchableOptions.xml
2025-06-20 15:34:32,915 [   6496]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/kotlin-plugin.jar/search/kotlin-plugin.jar.searchableOptions.xml
2025-06-20 15:34:32,916 [   6497]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/vcs-git.jar/search/vcs-git.jar.searchableOptions.xml
2025-06-20 15:34:32,917 [   6498]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/modules.jar/search/modules.jar.searchableOptions.xml
2025-06-20 15:34:32,917 [   6498]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/indexing-shared.jar/search/indexing-shared.jar.searchableOptions.xml
2025-06-20 15:34:32,917 [   6498]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/featuresTrainer.jar/search/featuresTrainer.jar.searchableOptions.xml
2025-06-20 15:34:32,918 [   6499]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/kotlin-plugin.jar!/search/kotlin-plugin.jar!.searchableOptions.xml
2025-06-20 15:34:32,918 [   6499]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/cwm-host.jar/search/cwm-host.jar.searchableOptions.xml
2025-06-20 15:34:32,918 [   6499]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/gradle-java-maven.jar/search/gradle-java-maven.jar.searchableOptions.xml
2025-06-20 15:34:32,918 [   6499]   INFO - STDOUT - Output written to /Users/<USER>/IdeaProjects/zigzag/build/searchableOptions/settingsSync.jar/search/settingsSync.jar.searchableOptions.xml
2025-06-20 15:34:32,918 [   6499]   INFO - STDOUT - Searchable options index builder completed
2025-06-20 15:34:32,919 [   6500]   INFO - #c.i.u.i.FileBasedIndexImpl - Index dispose started
2025-06-20 15:34:32,940 [   6521]   INFO - #c.i.p.s.StubIndexImpl - StubIndexExtension-s were unloaded
2025-06-20 15:34:32,940 [   6521]   INFO - #c.i.p.s.SerializationManagerImpl - Start shutting down /Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/index/rep.names
2025-06-20 15:34:32,940 [   6521]   INFO - #c.i.p.s.SerializationManagerImpl - Finished shutting down /Users/<USER>/IdeaProjects/zigzag/build/idea-sandbox/system/index/rep.names
2025-06-20 15:34:32,941 [   6522]   INFO - #c.i.u.i.FileBasedIndexImpl - Index dispose completed in 21ms.
2025-06-20 15:34:32,943 [   6524]   INFO - #c.i.o.v.n.p.PersistentFSImpl - VFS dispose started
2025-06-20 15:34:32,943 [   6524]   INFO - #c.i.o.v.n.p.FSRecordsImpl - VFS closing
2025-06-20 15:34:32,944 [   6525]   INFO - #c.i.o.v.n.p.PersistentFSImpl - VFS dispose completed in 1 ms.
2025-06-20 15:34:32,953 [   6534]   INFO - #c.i.p.i.b.AppStarter - ------------------------------------------------------ IDE SHUTDOWN ------------------------------------------------------
