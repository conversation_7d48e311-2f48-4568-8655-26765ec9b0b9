# OpenTelemetry Metrics report: .csv, 4 fields: 
# <metric name>, <period start, nanoseconds>, <period end, nanoseconds>, <metric value>
# See CsvMetricsExporter for details.
# 
# NAME, PERIOD_START_NANOS, PERIOD_END_NANOS, VALUE
FilePageCacheLockFree.totalPageAllocationsWaited,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.totalHeapBytesReclaimed,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.totalNativeBytesAllocated,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.heapBytesInUse,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.totalBytesRead,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.totalBytesRequested,1750390466558000000,1750390472945000000,0
MappedFileStorage.totalPagesMapped,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.totalClosedStoragesReclaimed,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.totalNativeBytesReclaimed,1750390466558000000,1750390472945000000,0
FileChannelInterruptsRetryer.totalRetriedAttempts,1750390466558000000,1750390472945000000,0
FilePageCache.capacityInBytes,1750390466558000000,1750390472945000000,503316478
FilePageCache.pageFastCacheHits,1750390466558000000,1750390472945000000,3880
FilePageCacheLockFree.totalPagesRequestsMs,1750390466558000000,1750390472945000000,0
FilePageCache.maxCacheSizeInBytes,1750390466558000000,1750390472945000000,202375168
FilePageCacheLockFree.totalPagesAllocated,1750390466558000000,1750390472945000000,0
FilePageCache.pageLoadsAboveSizeThreshold,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.totalPagesWriteMs,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.housekeeperTurnsSkipped,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.totalPagesReadMs,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.totalPagesReclaimed,1750390466558000000,1750390472945000000,0
StorageLockContext.competingThreads.avg,1750390466558000000,1750390472945000000,0.0
FilePageCache.uncachedFileAccess,1750390466558000000,1750390472945000000,110
FilePageCache.pageHits,1750390466558000000,1750390472945000000,0
FilePageCache.disposedBuffers,1750390466558000000,1750390472945000000,196
FilePageCacheLockFree.totalPagesWritten,1750390466558000000,1750390472945000000,0
DirectByteBufferAllocator.reclaimed,1750390466558000000,1750390472945000000,43
DirectByteBufferAllocator.totalSizeOfBuffersAllocatedInBytes,1750390466558000000,1750390472945000000,41943040
FilePageCache.pageLoads,1750390466558000000,1750390472945000000,196
StorageLockContext.competingThreads.90P,1750390466558000000,1750390472945000000,0
StorageLockContext.competingThreads.max,1750390466558000000,1750390472945000000,0
FilePageCache.totalPageDisposalsUs,1750390466558000000,1750390472945000000,1436
MappedFileStorage.totalTimeSpentOnMappingUs,1750390466558000000,1750390472945000000,6856
MappedFileStorage.totalBytesMapped,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.housekeeperTurnsDone,1750390466558000000,1750390472945000000,0
MappedFileStorage.storages,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.totalPagesHandedOver,1750390466558000000,1750390472945000000,0
DirectByteBufferAllocator.misses,1750390466558000000,1750390472945000000,193
FilePageCache.maxRegisteredFiles,1750390466558000000,1750390472945000000,537
FilePageCacheLockFree.totalPagesRequested,1750390466558000000,1750390472945000000,0
DirectByteBufferAllocator.hits,1750390466558000000,1750390472945000000,3
DirectByteBufferAllocator.totalSizeOfBuffersCachedInBytes,1750390466558000000,1750390472945000000,41943040
FilePageCache.totalPageLoadsUs,1750390466558000000,1750390472945000000,59350
FilePageCacheLockFree.nativeBytesInUse,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.housekeeperTimeSpentMs,1750390466558000000,1750390472945000000,0
FilePageCacheLockFree.totalHeapBytesAllocated,1750390466558000000,1750390472945000000,0
DirectByteBufferAllocator.disposed,1750390466558000000,1750390472945000000,153
FilePageCacheLockFree.totalBytesWritten,1750390466558000000,1750390472945000000,0
cacheStateStorage.set.duration,1750390466558000000,1750390472945000000,0
cacheStateStorage.set.counter,1750390466558000000,1750390472945000000,0
cacheStateStorage.get.counter,1750390466558000000,1750390472945000000,10
cacheStateStorage.get.duration,1750390466558000000,1750390472945000000,0
VFS.fileChildByName,1750390466558000000,1750390472945000000,13
VFS.invertedFileNameIndex.requests,1750390466558000000,1750390472945000000,0
VFS.fileByIdCache.hits,1750390466558000000,1750390472945000000,0
VFS.fileByIdCache.misses,1750390466558000000,1750390472945000000,0
jps.save.global.entities.ms,1750390466558000000,1750390472945000000,0
jps.library.entities.serializer.save.entities.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.entities.by.source.ms,1750390466558000000,1750390472945000000,0
workspaceModel.save.cache.to.file.ms,1750390466558000000,1750390472945000000,0
workspaceModel.moduleManagerBridge.build.module.graph.ms,1750390466558000000,1750390472945000000,0
jps.global.handle.before.change.events.ms,1750390466558000000,1750390472945000000,0
workspaceModel.moduleManagerBridge.create.module.instance.ms,1750390466558000000,1750390472945000000,0
workspaceModel.moduleManagerBridge.load.all.modules.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.put.entity.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.entities.ms,1750390466558000000,1750390472945000000,1
workspaceModel.mutableEntityStorage.remove.entity.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.add.entity.ms,1750390466558000000,1750390472945000000,0
jps.global.handle.changed.events.ms,1750390466558000000,1750390472945000000,0
jps.app.storage.content.reader.load.component.ms,1750390466558000000,1750390472945000000,0
workspaceModel.do.save.caches.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.collect.changes.ms,1750390466558000000,1750390472945000000,0
workspaceModel.sync.entities.ms,1750390466558000000,1750390472945000000,0
workspaceModel.global.apply.state.to.project.ms,1750390466558000000,1750390472945000000,0
jps.library.entities.serializer.load.entities.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.mutable.ext.mapping.ms,1750390466558000000,1750390472945000000,0
jps.global.get.library.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.apply.changes.from.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.has.same.entities.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.to.snapshot.ms,1750390466558000000,1750390472945000000,0
workspaceModel.load.cache.from.file.ms,1750390466558000000,1750390472945000000,0
workspaceModel.moduleManagerBridge.new.nonPersistent.module.ms,1750390466558000000,1750390472945000000,0
workspaceModel.global.updates.ms,1750390466558000000,1750390472945000000,0
workspaceModel.moduleManagerBridge.load.module.ms,1750390466558000000,1750390472945000000,0
jps.global.get.libraries.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.replace.by.source.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.mutable.vfurl.index.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.modify.entity.ms,1750390466558000000,1750390472945000000,0
jps.global.get.library.by.name.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.referrers.ms,1750390466558000000,1750390472945000000,0
workspaceModel.moduleManagerBridge.get.modules.ms,1750390466558000000,1750390472945000000,0
workspaceModel.global.apply.state.to.project.builder.ms,1750390466558000000,1750390472945000000,0
workspaceModel.moduleManagerBridge.newModule.ms,1750390466558000000,1750390472945000000,0
workspaceModel.global.updates.count,1750390466558000000,1750390472945000000,0
workspaceModel.moduleManagerBridge.set.unloadedModules.ms,1750390466558000000,1750390472945000000,0
jps.global.initialize.library.bridges.ms,1750390466558000000,1750390472945000000,0
jps.load.initial.state.ms,1750390466558000000,1750390472945000000,10
workspaceModel.mutableEntityStorage.instances.count,1750390466558000000,1750390472945000000,3
jps.global.initialize.library.bridges.after.loading.ms,1750390466558000000,1750390472945000000,1
workspaceModel.load.cache.metadata.from.file.ms,1750390466558000000,1750390472945000000,0
workspaceModel.mutableEntityStorage.resolve.ms,1750390466558000000,1750390472945000000,0
writeAction.max.wait.ms,1750390466558000000,1750390472945000000,0
writeAction.wait.ms,1750390466558000000,1750390472945000000,0
writeAction.count,1750390466558000000,1750390472945000000,1
writeAction.median.wait.ms,1750390466558000000,1750390472945000000,0
FlushQueue.queueSizeAvg,1750390466558000000,1750390472945000000,121.09743202416918
NonBlockingReadAction.failedExecutionTimeUs,1750390466558000000,1750390472945000000,0
AWTEventQueue.dispatchTimeMaxNs,1750390466558000000,1750390472945000000,5335154687
ReadAction.executionsCount,1750390466558000000,1750390472945000000,50085
NonBlockingReadAction.finalizedExecutionsCount,1750390466558000000,1750390472945000000,15
FlushQueue.executionTimeAvgNs,1750390466558000000,1750390472945000000,50909.7416918429
FlushQueue.tasksExecuted,1750390466558000000,1750390472945000000,1324
FlushQueue.executionTimeMaxNs,1750390466558000000,1750390472945000000,18874367
NonBlockingReadAction.finalizedExecutionTimeUs,1750390466558000000,1750390472945000000,42746
FlushQueue.queueSize90P,1750390466558000000,1750390472945000000,391
FlushQueue.waitingTimeMaxNs,1750390466558000000,1750390472945000000,4261412863
FlushQueue.waitingTimeAvgNs,1750390466558000000,1750390472945000000,6.47622258386707E8
AWTEventQueue.dispatchTimeAvgNs,1750390466558000000,1750390472945000000,2810296.767405879
NonBlockingReadAction.failedExecutionsCount,1750390466558000000,1750390472945000000,0
FlushQueue.waitingTime90PNs,1750390466558000000,1750390472945000000,2684354559
AWTEventQueue.dispatchTimeTotalNS,1750390466558000000,1750390472945000000,5433867900
AWTEventQueue.eventsDispatched,1750390466558000000,1750390472945000000,1939
FlushQueue.queueSizeMax,1750390466558000000,1750390472945000000,491
WriteAction.executionsCount,1750390466558000000,1750390472945000000,1
AWTEventQueue.dispatchTime90PNs,1750390466558000000,1750390472945000000,32255
FlushQueue.executionTime90PNs,1750390466558000000,1750390472945000000,56831
workspaceModel.check.recursive.update.ms,1750390466558000000,1750390472945000000,0
workspaceModel.cachedValue.total.get.ms,1750390466558000000,1750390472945000000,0
workspaceModel.cachedValueWithParameters.from.cache.ms,1750390466558000000,1750390472945000000,0
workspaceModel.updates.count,1750390466558000000,1750390472945000000,0
workspaceModel.to.snapshot.ms,1750390466558000000,1750390472945000000,0
workspaceModel.cachedValueWithParameters.calculated.ms,1750390466558000000,1750390472945000000,0
workspaceModel.collect.changes.ms,1750390466558000000,1750390472945000000,0
workspaceModel.cachedValue.from.cache.ms,1750390466558000000,1750390472945000000,0
workspaceModel.replace.project.model.ms,1750390466558000000,1750390472945000000,0
workspaceModel.update.unloaded.entities.ms,1750390466558000000,1750390472945000000,0
workspaceModel.loading.total.ms,1750390466558000000,1750390472945000000,29
workspaceModel.cachedValue.clear.ms,1750390466558000000,1750390472945000000,0
workspaceModel.init.bridges.ms,1750390466558000000,1750390472945000000,0
workspaceModel.pre.handlers.ms,1750390466558000000,1750390472945000000,0
workspaceModel.entityStorageSnapshotImpl.instances.count,1750390466558000000,1750390472945000000,3
workspaceModel.cachedValueWithParameters.clear.ms,1750390466558000000,1750390472945000000,0
workspaceModel.cachedValueWithParameters.total.get.ms,1750390466558000000,1750390472945000000,0
workspaceModel.loading.from.cache.ms,1750390466558000000,1750390472945000000,23
workspaceModel.cachedValue.calculated.ms,1750390466558000000,1750390472945000000,0
workspaceModel.full.replace.project.model.ms,1750390466558000000,1750390472945000000,0
workspaceModel.updates.ms,1750390466558000000,1750390472945000000,0
workspaceModel.initializing.ms,1750390466558000000,1750390472945000000,0
workspaceModel.updates.precise.ms,1750390466558000000,1750390472945000000,0
