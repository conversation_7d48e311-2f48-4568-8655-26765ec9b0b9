{"id": 546759, "xmlId": "IdeaVIM", "name": "IdeaVim", "description": "<p>Vim engine for JetBrains IDEs</p>\n      <br>\n      <p>IdeaVim supports many Vim features including normal/insert/visual modes, motion keys, deletion/changing,\n      marks, registers, some Ex commands, Vim regexps, configuration via ~/.ideavimrc, macros, Vim plugins, etc.</p>\n      <br>\n      <p>See also:</p>\n      <ul>\n        <li><a href=\"https://github.com/JetBrains/ideavim\">GitHub repository</a>: documentation and contributing</li>\n        <li><a href=\"https://youtrack.jetbrains.com/issues/VIM\">Issue tracker</a>: feature requests and bug reports</li>\n      </ul>", "organization": "JetBrains s.r.o.", "tags": ["Editor", "Keymap"], "version": "2.12.0", "notes": "<a href=\"https://youtrack.jetbrains.com/issues/VIM?q=State:%20Fixed%20Fix%20versions:%202.12.0\">Changelog</a>", "dependencies": ["com.intellij.modules.platform"], "optionalDependencies": ["com.intellij.modules.rider", "org.jetbrains.plugins.clion.radler", "com.intellij.modules.appcode", "AceJump"], "since": "233.11799.241", "size": 6459074, "url": "https://plugins.jetbrains.com/plugin/164", "vendor": "JetBrains", "sourceCodeUrl": "https://github.com/JetBrains/ideavim"}