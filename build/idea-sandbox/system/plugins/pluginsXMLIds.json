["ColourChooser", "Gold Section", "SvgViewer", "ImageView", "BSFConsole", "<PERSON>t<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JarSpyPlugin", "Log4JPlugin", "Macros Manager", "SystemProperties", "PooCookie", "PreconditionPlugin", "RegexPlugin", "RemoteSynchronizer", "SamePlugin", "SequencePlugin", "IdeProcessPriority", "ShowEncodingPlugin", "SpellCheckPlugin", "SwitchFile", "com.nicity.plugins.idea.TimePlugin", "Time Convertor Plugin", "RSSPlugin", "CompileWithoutDependencies", "Module Dependency Graph", "<PERSON><PERSON><PERSON>", "SimplePowerPack", "<PERSON><PERSON>", "Open containing folder in a File Explorer", "Native Neighbourhood", "Eclipse Workspace Importer", "HyperLink", "Native2Ascii", "XFiles", "RSS/Atom feed reader", "ProjectTitlePlugin", "DocTree", "IntelliTail", "Time Tracker Plugin", "Log<PERSON><PERSON><PERSON>", "VMOptions", "OpenWith", "Library Finder", "PrivateWriteInspection", "Inspection-JS", "JavadocWriter", "Inspection-JS for Demetra", "AutoBoxing", "CamouflagePlugin", "SimpleIntentions", "Subversion_QintSoft", "Surround", "SVN Report for IntelliJ IDEA", "ClearcaseIntegration", "CVS Report for IntelliJ IDEA", "accurev4idea", "CM Synergy Integration", "Perforce", "PerforceDirectPlugin", "MKS", "ClearcasePlugin", "BackgroundImage", "<PERSON><PERSON><PERSON>", "SkinLFPlugin", "ToolbarManager", "IdeaMouseGestures", "RemoveClipboardActions", "ConfirmExit", "CVS bar", "Describe Key", "GoToHack", "Workspaces", "API Comparator", "Context<PERSON>iewer", "CopyFQNPlugin", "Copyright", "GuiSource", "Ideascript", "StrutsComponent", "scrappy", "MetricsReloaded", "MetricsReported", "SmartIntroduce", "TestDox", "ToggleKeyword", "Type Refactoring", "KiwiScrap", "<PERSON><PERSON><PERSON>ble Plugin", "Emma Code Coverage", "Lineage", "IdeaSpring", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ts Navigator <PERSON><PERSON><PERSON>", "DeclarationsTools", "<PERSON>ruts Assistant", "Compare Directories", "Connect4", "<PERSON><PERSON>", "<PERSON>", "Sokoban", "<PERSON><PERSON><PERSON>", "DERViewer", "AntXMLGenerator", "dashboard", "<PERSON><PERSON><PERSON><PERSON>", "Hibernate Tools", "Ant Project File", "IdeaJad", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OptimizeIt 5 Plugin", "mevenide-idea", "Xp4IdeaProjectPlugin", "XPlanner plugin", "JetStyle", "yWorks Ant Explorer", "TestNG-J", "IvyDependencyImportor", "TrackLink", "Xcalia Core (Formerly LiDO-JDO)", "Retrotranslator Integration", "tddTracker", "unitTest", "JUnitGenerator", "TestNG Generator", "Raven", "FaTPlug", "Axis TCP Monitor Plugin", "TunnelliJ", "RMI Explorer", "Inca X", "FTP Bridge", "Auto-Format Text", "Code Outline", "Comment", "Hungry BackSpace", "IDEAmacs", "IdeaVIM", "JavaDoc Editor", "LineMover", "Modifier Modifier Plugin", "MoveElement", "OpenFolder", "PropertiesEditor", "PrependAppend", "<PERSON><PERSON><PERSON>", "Reformat", "RemoveEditorPopupMenuActions", "SelectWord", "Stringify", "Tabifier", "TabSwitch", "Tagify", "ToggleCharacterCase", "GenerateToString", "UpperLowerCapitalize", "GenerateSerialVersionUID", "File status bar", "<PERSON>b<PERSON><PERSON><PERSON>", "UndoCloseFile", "MiniWiki", "Lysosome", "StringEditor", "File Info", "Toggle Camel-humps", "MyCamelHumps", "<PERSON><PERSON><PERSON>", "Less Hungry Backspace", "OpenExplorer", "Locale Utility", "BrowseWordAtCaret", "EditorTree", "Character Browser", "TabStack", "Permute Plugin", "HTML Previewer", "SimpleActions", "OrdbogenSearch", "Compare Panes", "FunkySearch", "GooglePlugin", "IgnoreFilesFolders", "SyncEdit", "Resin", "StrutsLayout", "Orion", "ULC Plugin", "Cargo", "AndyTesters", "Jsp Internationalizer", "J2ME Plugin", "Code queries", "PsiViewer", "SQL Query Plugin", "PluginUploader", "<PERSON><PERSON>", "MailPlugin", "Net client", "IDETalk", "TMatePlugin", "Rob Keyboard", "XML Pack", "XPathView_obsolete", "XMLBean Generator", "org.intellij.jibx", "JAXB 2.0 XJC generator", "SDE for IntelliJ IDEA (Community Edition)", "simpleUML", "Visual Paradigm SDE for IntelliJ IDEA (Community Edition)", "DiamondSpin", "<PERSON><PERSON><PERSON>", "reportmill", "ReferenceScanner Plugin", "JProfiler", "<PERSON><PERSON>", "ZipTo(G)mail Backup", "yGuard Unscramble plugin", "<PERSON><PERSON>", "SQL script editor", "JFlex Support", "JSIntentionPowerPack", "<PERSON>ch Scripts Support", "Javascript Compressor", "JavaScript Squeezer", "JFormDesigner", "LYCAY", "PDFViewer", "JavaD<PERSON>rowser", "LJFriendListReader", "AspectWerkz plugin", "Pythonid", "<PERSON><PERSON><PERSON>", "Research", "GWT Studio", "DragNDrop", "SeleniumRC Server", "Yahoo Search", "TaskList", "<PERSON><PERSON> Manager", "OpenContainingFolder", "Problems View", "Eclipse Dependency Sync", "Color Browser", "IDEA Doc", "ANTLRWorks", "World Of Java", "ScratchPad", "Harvest Integration", "Key promoter_obsolete", "IntelliCalc", "IntelliME J2ME Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HotPlugin", "BeanShell Box", "GUIguide", "Batch Generator", "MantisConnect for Idea", "IDEAStickyPaper", "CheckStyle-IDEA", "CodeDependency", "CCMonitor", "HistoryNavigator", "IntelliLang", "fireworks", "RemoteTail", "DBHelper", "Google Code Search", "Tasks", "JumpToUsage", "AspectJ weaver", "MiscCommand", "Refactor-<PERSON> for IDEA 6.0", "Refactor-<PERSON> for IDEA 5.0", "Refactor-X for IDEA 6.0", "Refactor-X for IDEA 5.0", "EasyPluginInstaller", "PMDPlugin", "FitnesseInt", "KonaWorks PowerPack", "NaviActionPad", "Image Design Editor", "Class Hunter", "Maven 2 Integration", "FQN Improved", "IntellIRC", "AutoConstructor", "Maven1.x Repository Updater", "iSnippet", "Kiwi Generator", "Konaworks EE Inspections", "WDK View", "Puzzle", "UUID Plugin", "ThreadMon Plugin", "MidStream", "Changes Bar", "Ant Tracer Plugin", "<PERSON><PERSON>", "Code Review", "ssh", "Switch Structure", "PluginErrorReportSubmitter", "Classpath Complete", "SVNbar", "FileAttribChanger", "Identifier Highlighter", "Event Sounds", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PluginUpdateNotifier", "HandyTapestry", "JUnit 4 Synchronizer", "ToggleCase", "org.jetbrains.plugins.ruby", "QuickBuild Monitor", "Accessors Plugin", "ToggleTest", "CruiseWatcher", "Jetty Integration", "Code Search", "CodeCompletionLive", "Jalopy for IDEA 8", "org.intellij.scala", "Windows Context Menu", "Lock<PERSON>mith", "GuiceyIDEA", "AlphaTab", "C/C++", "EclipseConverter", "DeleteMultiLines", "CloseAllX", "LibraryManager", "Bindows Plugin", "Excelsior JET", "<PERSON><PERSON>", "Firefox Search", "Google Translate", "SmartCopy", "FileBrowser", "JSUnitTest plugin", "CVS - Multi-Project updater", "Alt n 8", "InspectorGeneral", "iBATIS Plugin", "XMLNavigate", "ToolWindow Manager", "SerializeMe", "Instant Calculator", "Telelogic Synergy", "Final modifier", "EncodingPlugin", "IdeaAmp", "org.intellij.groovy", "PVCS Integration", "Log4j <PERSON><PERSON><PERSON>", "WicketForge", "Berkeley DB Java Edition", "JDocs", "OfflineModule", "Fast Project Open plugin", "Winstone Integration Plugin", "IntelliJad", "Spell<PERSON><PERSON><PERSON>", "S<PERSON><PERSON><PERSON><PERSON><PERSON>-Dict-English", "ExternalCodeFormatter", "JS Debugger", "Build Feedback Plugin", "BuildFeedback.FreeTTS", "BuildFeedback.JavaxSound", "MavenProjectHelper", "Refactor-<PERSON> for IDEA 7.0", "SpellChecker-Dict-German", "Amazon EC2", "ZipChanges", "<PERSON><PERSON>", "com.intellij.struts2", "DefinitionEqualizer", "OpenInIDEA", "AAHack", "Apache Tiles 2.0 plugin", "CVS Revision Graph", "RelaxNG-Support", "SpellChecker-Dict-Russian", "Maven Archetypes", "Android Support", "DBN", "<PERSON><PERSON><PERSON><PERSON>", "org.kohsuke.idea.mbt", "CafeBabe", "Carret Inspector", "<PERSON><PERSON><PERSON>", "XSLT-Debugger", "Jetbrains TeamCity Plugin", "IntelliHeap", "Commit Log", "Spell Checker Java Dictionary", "IdeaTwitter", "Vcs Revision Graph", "Sexy Editor", "CodeGraph", "CodeLock", "IntelliPort", "IntelliBrowser", "Grok", "IntelliPROF", "Seam Maven 2 Studio for IDEA", "QuickPathManager", "Rio", "org.sylfra.idea.plugins.xstructure", "Prefontaine", "Best Web Search", "Stapler plugin for IntelliJ IDEA", "GWT ImageBundle", "JumpToCode", "Base64", "Buildr", "Snipplr API", "Search YouTube Plugin", "Message key usage plugin", "Arc", "String Manipulation", "OpenDSPInclude", "com.jalapeno.idea", "Translator", "plugin proxy", "Jindent - Source Code Formatter", "bzr4idea", "<PERSON><PERSON>", "Atlassian Connector for IntelliJ IDE", "REST Client plugin", "<PERSON>", "IdeaSabotter", "Shortcut Keys List", "IntelliJad 8", "ATG Inspections", "JarDoc", "ResourcePlugin", "org.easyb.easyb_obsolete_obsolete", "org.intellij.RegexpTester", "CheckThread", "Git4Idea_old", "org.intellij.plugins.junitgen", "SimpleHttpClient", "Infinitest", "Java<PERSON><PERSON> Helper", "EasyPatch", "Code Navigator", "Poison Inspection", "Paste Multiple", "Code Consultant", "Surround each line", "RevealInFinder", "hg4idea_old", "Equals/hashCode field inspection", "JavaDoc Sync Plugin 8", "IvyIDEA", "<PERSON>ript Monkey", "eSnippet Pro plugin_obsolete", "com.esn.idea.liquibaseejb", "salve-idea", "Check My Vars", "Code Explorer", "visualvm4idea", "Idea file rename", "HotOrNot Plugin", "Reformat++", "SCA Support", "EclipseMode", "Code-Amnesia", "SendTo", "HtmlExport", "Unvoid Methods 7", "Unvoid Methods 8", "Gismeteo Plugin", "FindBugs-IDEA", "org.sylfra.idea.plugins.revu", "GenerateBuilder", "IntelliJ IDEA Mark <PERSON>in", "Hudson Build Monitor", "bzr4<PERSON><PERSON><PERSON>", "JSON Formatter", "Class Names in Comments", "FIT-ifier", "Code snippets", "ThisInserter", "Property Sorter", "BashSupport", "Unitils plugin", "Fantom Support", "SourceViewerService Plugin", "MysticPastePlugin", "Xcordion", "<PERSON><PERSON>j <PERSON>", "com.intellij.appengine", "eSnippet Pro plugin", "Buffer It", "Bulk Load Modules", "Run Emulator plugin", "Save ChangeList <PERSON><PERSON>", "ArgoUML.Integration", "ideanginx9", "salve2-idea", "<PERSON><PERSON><PERSON>", "Ebean weaver", "File Size", "JRebelPlugin", "Key promoter", "Quick Notes", "JSTestDriver Plugin", "Dto-Wrapper-Builder Generator", "Scala Power Pack", "SQL", "Bindows Tools", "Simple Helpers", "Statistic", "IntelliGuard", "com.handyedit.AntDebugger", "com.intellij.dmserver", "BuildWatcher", "com.jetbrains.ec2manager", "IdeaServerPlugin", "Winstone Integration Plugin(CE)", "pmip", "TFS", "QAPlug", "QAPlug - Checkstyle", "QAPlug - PMD", "QAPlug - FindBugs", "iTest", "JBoss jBPM", "Network Tools", "SmarterEditor", "Groovy <PERSON><PERSON><PERSON>", "IntelliJ Open Files Copier", "IntelliJad 9", "com.intellij.aspectj", "Robotlegs Up Down Plugin", "<PERSON><PERSON><PERSON><PERSON>", "coffeescript-idea", "com.nmatveev.idea-plugin-protobuf", "simpleUMLCE", "Touch", "QAPlug - <PERSON><PERSON><PERSON>", "LogSupport", "Pomodoro-tm", "IdeaMouseGestures v9.0.1+", "OCamlSupport", "TJSCompressor", "WaspTool", "PList File Support", "JavaFx Support", "SBT", "com.jalapeno.idea.javaee", "changelist-action", "extended-code-sense", "<PERSON><PERSON><PERSON>", "Non-Dairy Soy Plugin", "ru.crazycoder.plugins.tabdir", "ro.redeul.google.go", "com.intellij.tcserver", "generate-chained-accessors", "UDDI Tools", "com.infolinks.idea.plugins.bundle", "org.bugzilla.tasks", "<PERSON><PERSON>", "CSS-X-Fire", "Re<PERSON> Groovy Console", "DataNucleusIntegration", "Copy Src", "File Listener Server", "Google Closure Soy Templates", "CMD Support", "GenerateTestCases", "PluginUpdateNotifier10(boaz)", "PluginUpdateNotifier9(boaz)", "JBehaveBddPlugin", "My Runner", "MigLayout Verifier", "SwungWeave", "IDEA Restart", "<PERSON><PERSON><PERSON>", "QuickZip", "apache-felix-plugin", "Facebook chat", "ASM Bytecode Outline", "org.sylfra.idea.plugins.linessorter", "org.coffebrew", "CodeNarc", "IntelliJCoder", "FavoriteFolders", "com.kalistick.client.idea", "RESTClient", "schemely", "AribaWeb Plugin", "<PERSON> Patch", "tea", "org.jodd.idea.props", "TabFolder", "Shell Process", "GenerateStaticBuilder", "Parser for IDEA", "<PERSON><PERSON><PERSON>in for Idea 10", "SingletonInspection", "SonOfExternalCodeFormatter", "Context Console <PERSON>", "Remote call", "Base64 for IDEA and Storm", "Install Plugin Locally", "NodeJS", "<PERSON><PERSON><PERSON>", "Javascript Uploader", "Runtime java completion", "Jenkins Control Plugin", "Javadoc Utility", "sparql4idea", "GrepCode.com Code Search", "SequencePluginReload", "Generate.Fluent.Interface", "Shifter", "Embedder", "PHPUnit code coverage", "Robotlegs", "SVN Revision Graph", "Injector", "Unicode Browser", "JsChilicat", "Signals", "Implementor", "GenerateSerializationHelpers", "Share with Pastie", "GeneratePropertyNameConstants", "CodeOutline2", "QuickJump", "org.coffeescript", "Lombook Plugin", "Identifier Highlighter Reloaded", "ideah", "Ofbiz Framework", "JavaDoc Sync Plugin 10", "BEM Support", "Dart", "pl.project13.intellij.kanbanery", "Notation Converter", "<PERSON><PERSON>", "net.jangaroo.idea.0.9", "SVN Disconnect", "EclipseCodeFormatter", "SBJava2SQL", "NUnitJS", "Easy Import", "eclipse-code-formatter-on-change-list", "IDEA DocBook", "Builder Generator", "org.jetbrains.idea.grammar", "SBJspClass", "com.jetbrains.php", "OpenJpaIntegration_obsolete", "IntelliJBehave", "com.jetbrains.php.framework", "com.intellij.phing", "R4Intellij", "EclipseMode11", "Heroku Integration", "Subversion 1.7", "OpenJpaIntegration", "NetburstJbehaveIdea", "Identifier_Highlighter", "idea-mini-ibatis", "Vaadin Support", "Assets Compressor", "FrozenIdea", "Compare Tab With Editor", "wide-task-browser", "File Name Grabber", "Shortcut Translator", "FocusTask", "com.intellij.apacheConfig", "UsefulActions", "Wicket Source", "FestSupport", "<PERSON>ly<PERSON> Plugin", "com.intellij.plugins.haxe", "Equals and HashCode Deluxe Generator", "com.dmarcotte.handlebars", "EDTOnlyInspection", "Pull up method refactoring extension", "Color", "SBT ChangeListAction", "Finder Restarter", "Pomodoro-tm-cd", "com.hp.alm.ali", "RetardedButtonsFix", "org.jetbrains.kotlin", "ConsoleUrlLink", "Equals and HashCode Apache Commons Generator", "IntelliStripes-NV", "AngularJS", "com.jetbrains.plugins.ini4idea", "com.strintec.intellij.webmaster", "GLSL", "Sql Generator", "com.intellij.plugins.html.instantEditing", "net.chilicat.felixscr", "Show As ...", "JMeter plugin", "PlantUML integration", "Java2SQL", "net.chilicat.dsViewer", "<PERSON><PERSON>", "SortSelection", "ColorIde", "ColorTree", "LESS CSS Compiler", "NEON support", "FavoritesSplitModeFalse", "Source Square Plugin", "Shortcut Trainer", "Keymap exporter", "makeitI18Nplugin", "gen-javadoc", "<PERSON><PERSON><PERSON>", "Launchpad Tasks Provider", "jetbrains.mps.core", "jetbrains.mps.java", "jetbrains.mps.vcs", "jetbrains.mps.build", "PatchIde", "<PERSON><PERSON><PERSON>", "org.jetbrains.erlang", "Pom sorter", "com.github.denofevil.TweetCode", "AceJump", "com.abelsky.idea.geekandpoke", "Magicento", "<PERSON><PERSON><PERSON>", "dal-mpe", "izi-gwt", "com.jetbrains.plugins.jade", "Org4Idea", "JSFL Support", "Plugin name here", "Pastebin-IDEA", "jd-intellij", "jetbrains.mps.baseLanguage.extensions", "RTC4Idea", "Referencer", "MoreUnit", "Google Closure Require Statement Checker", "JaggeryEditorSupport", "Create <PERSON><PERSON>", "Railways", "Aquarium", "Spock Framework Enhancements", "VisualVMLauncher", "CollabNet Tools", "net.org.selector.idea.plugins.LTFunctions", "PIT mutation testing Idea plugin", "iceScrum for Intellij", "JCompilo Java Compiler", "org.jetbrains.plugins.localization", "org.jetbrains.plugins.rest", "GrepConsole", "DeltaTabs", "com.guidewire.build.ijdevkitmvn", "org.jetbrains.plugins.slim", "net.jack-of-all-trades.idea.missing.functionality", "SourceFinder", "DeltaUtils", "org.intelizilla", "MyTranslator", "org.jetbrains.comparisonChain", "MySearch", "im.jeanfra<PERSON><PERSON>.ideaplugins.ideafullscreen", "FrameSwitcher", "Jelastic Cloud Platform Integration", "com.guidewire.gosu", "<PERSON><PERSON>in", "com.jetbrains.php.framework.structure", "net.jackofalltrades.workflow", "com.squareup.ideaplugin", "TabSwitcherExtreme", "SyncEdit 2", "com.alexeyhanin.intellij.jalopyplugin", "Gradle View", "CodeLock 1.2", "com.github.hotchemi.IdeaTweet", "com.kukido.eclipser", "gw.vark", "com.github.setial", "de.netnexus.camelcaseplugin", "emacsIDEAs", "com.rudeshko.csscomb", "de.docksnet.puml.syntaxchecker", "be.mavicon.intellij.ppimport", "de.bewalt.intellij.plugin.cvsinfo", "SvgViewer 2", "com.taobao.wuzheng", "com.webfont.ideaplugin", "WFA console", "np.intellij.epochconverter", "de.docksnet.moddep", "com.intellij.plugins.watcher", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com.intellij.lang.puppet", "BuildrPlugin", "YiiStorm", "Output Link Filter", "<PERSON><PERSON><PERSON>", "LazySpring", "fuGen", "LiveTemplatePreview", "com.vexus2.intellij.openingithub", "org.jboss.errai", "ua.com.psw.magento.database", "com.handyedit.ant.AntDebugger", "copy.on.steroids", "net.tweakers.intellij.thesaurus", "com.idamobile.android.toolbox", "com.raket.silverstripe", "<PERSON><PERSON>", "org.apache.pig.plugin.idea", "org.jetbrains.kannotator", "idea.plugin.tabsession", "com.bluetrainsoftware.groovy.idea", "cucumber-java", "cucumber-groovy", "com.linkedin.intellij.dust", "JavaREPL", "Dummy Text Generator", "de.bewalt.intellij.plugin.psl", "org.internal.easy.shell", "fr.adrienbrault.idea.symfony2plugin", "org.jetbrains.plugins.textmate", "com.eugenePetrenko.idea.dependencies", "codeReview4idea", "javaoo.idea", "de.u-mass.idea.copyConstructor", "com.machak.hippo.plugin.id", "Redline Smalltalk", "jenv-idea-plugin", "com.lexand.enclosing", "Nette framework helpers", "de.halirutan.mathematica", "com.karateca.jasmineDescriber", "com.andrewbrookins.wrap_to_column", "navigate-from-literal", "com.kodokux.github", "org.nik.distribution-cleaner", "org.mayevskiy.intellij.sonar", "com.jetbrains.php.gae", "com.jacksingleton.tabtonextsplitter", "org.intellij.tasks.navigation", "org.intellij.featureSuggester", "org.jetbrains.code-golf", "com.seanlandsman.idea.plugins.guavagenerators", "dcevm", "com.johnlindquist.OpenInDebuggex", "com.mle.idea.sbtexecutor", "com.khmelyuk.multirun", "CUBA", "com.johnlindquist.plugins.DownloadSelection", "com.ptby.dynamicreturntypeplugin", "PegdownDocletIdea", "com.jetbrains.unchain", "com.squareup.intellij.plugin.copy-as-github-path", "org.intellij.xquery", "net.eunjae.plugins.androidhelper", "com.chrisfolger.needsmoredojo", "com.bitard.concordion", "org.livescriptidea", "ro.catalin.prata.tfuploader", "jbehave-support-plugin", "com.genymotion.idea", "be.wimsymons.intellij.polopolyimport", "DeltaShareXP", "com.urswolfer.intellij.plugin.gerrit", "CodeHistoryMining", "AWS Elastic Beanstalk", "net.vektah.codeglance", "net.king2500.plugins.PhpAdvancedAutoComplete", "com.vexus2.cakestorm", "inc.chaos.mps.tool.maven.run", "LivePlugin", "com.shankh.intellij.plugin.apache.eht", "com.bulenkov.idea.Idea11IconPack", "<PERSON>rma", "com.dooapp.codesearch", "com.duncanjauncey.autosysplugin", "ru.salerman.plaintextr", "com.seventh7.plugin.mybatis", "org.editorconfig.editorconfigjetbrains", "com.jetbrains.lang.ejs", "com.bashorov.mainMenuToggler", "eu.inmite.android.plugin.selectorchapek", "GradleDependenciesHelper", "pl.projectspace.idea.plugins.php.behat", "com.github.dgt79.plugin.eclipseactions", "Camel<PERSON>", "com.jetbrains.twig", "cz.juzna.intellij.kdyby.events", "com.vexus2.intellij.marked", "com.svdb.idea.whitespace.plugin.id", "com.intellij.plugins.webcomponents", "fi.evident.dalesbred.plugin.idea", "Gitflow", "org.jetbrains.plugins.stylus", "fr.azelart.intellij.plugin.cnf", "com.arcbees.plugin.idea", "ru.trylogic.idea.gitlab.integration", "de.espend.idea.php.annotation", "PythonCore", "ua.com.web100.tools.gxt.idea.plugin", "Code Iris", "org.dylanfoundry.deft", "com.vv.adbuninstall", "com.webschik.doT", "com.crownpartners.intellivault", "com.hartmanster.compareToGenerator", "com.ramgec.geciauskas.phpstats.plugin", "com.intellij.plugins.thrift", "pl.charmas.parcelablegenerator", "org.intellij.eclipse.development.extra", "TFSTask integration", "com.fedot.idea.requirejs", "com.farawaytech.yoda.intellij", "com.siberika.idea.pascal", "com.mnw.stickyselection", "org.jetbrains.postfixCompletion", "de.sandstormmedia.flowstorm", "org.nik.presentation-assistant", "org.jetbrains.testnames", "com.hoho.conkitty", "Error-prone plugin", "com.scalaImportsOrganizer", "com.paperetto.dash", "com.jetbrains.php.drupal", "com.karateca.jstoolbox", "InnerBuilder", "SmoothScroll", "com.codename1.plugin.intellij", "org.antlr.intellij.plugin", "org.github.erikzielke.gotoproject", "com.makeapp.cocos2dx", "Rally ALM Tasks Integration", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com.amigold.auto.parcel", "zookeeper", "org.intellij.scratch", "fr.vdl.android.holocolors", "com.taobao.tmf.plugin.id", "eu.inmite.android.plugin.butterknifezelezny", "OracleCloud", "AWSCloudFormation", "com.mnr.java.intellij.idea.plugin.base64helper", "jp.funnything.offing_harbor", "org.wavescale.sourcesync", "ch.mjava.intellij.tapestry.4", "io.nlopez.androidannotations.viewbyid", "com.codexplo.intellij.android", "com.dacatech.checktests", "org.jetbrains.plugins.vagrant", "com.developerphil.adbidea", "de.suljee.dw.whenthendowhen", "org.kevoree.idea.plugin", "org.ollide.java2smali", "com.millennialmedia.intellibot", "com.junbo.idea.codenarc", "AWSElasticBeanstalkWeb", "com.floobits.unique.plugin.id", "com.jasonnz.bootstrap", "org.asciidoctor.intellij.asciidoc", "JCR Content Editor", "jp.tomorrowkey.intellij.injectlogtag", "com.mextor.intellij.plugin.search", "com.dirzys.phpstormzf1", "org.adroitlogic.idea.plugin", "org.jetbrains.idea.project.template.variables", "com.hoho.coocoo", "com.gisttemplates", "com.denimgroup.threadfix.plugins.intellij", "org.root.GoogleIt", "com.jetbrains.chronon", "com.github.mfedko.idea.plugins.filelanguage", "de.espend.idea.android", "pl.otros.intellij.JumpToCode", "org.para.plugin.openInSplittedTab", "Railways for IDEA", "Mvn<PERSON><PERSON><PERSON>", "de.espend.idea.shopware", "com.snip2code.intellij.plugin", "com.intellij.plugins.pants", "com.dpaulenk.idea.contexthelp", "org.vayafulano.relativeLineNumbers", "amailp.intellij.robot", "spy-js", "cucumber-javascript", "net.kodare.lineops", "info.darbha.plugins", "com.bsb.intellij.plugins.xmlbeans", "JavaCC Plugin", "com.ueqt.idea.plugin.GeneratePropAction", "com.wakatime.intellij.plugin", "com.github.janwaros.VcsToModule", "actiBPM", "com.jivesoftware.robot.intellij.plugin", "com.jetbrains.upsource", "de.lauerit.contextrun.plugin", "com.makeapp.cocoseditor", "com.jetbrains.php.wordPress", "com.codeOrchestra.colt.js.webStormPlugin", "com.github.masahiro<PERSON><PERSON>.PhoneGapIntelliJPlugin", "org.verily.intellij.plugin", "org.mediawiki", "org.yseasony.sqlgenerator", "fr.assoba.open.sel.plugin", "com.mycila.intellij.plugin.gmavenplus", "com.adamweigold.idea-plugin-jibx", "edu.gmu.cs.plugin.aeon", "edu.oregonstate.cope.intellij.recorder", "org.dpytel.intellij.plugin.maventest", "com.neon.intellij.plugins.gitlab", "com.ultrahob.zerolength.plugin", "Insert Final Modifier", "ua.in.dej.my<PERSON><PERSON>t", "<PERSON><PERSON>", "eu.broth.intellilab", "Ebean enhancer", "com.jantvrdik.intellij.latte", "org.bigtesting.jbehave.buddy", "ru.hyoo.jin.tree", "com.github.danielwegener.cucumber-scala", "com.aspose.intellijplugin.id", "<PERSON><PERSON><PERSON>", "de.sgalinski.typoscript.plugin.id", "com.yourcompany.classbreaskpoints.plugin", "intellij.frontendalignment", "collabs", "PairHero", "com.mistraltech.smogen", "com.zfabrik.intellij.z2plugin", "pl.holowko.tapestry.4", "socrates.tabshifter", "com.loops101.codestyle.hook", "com.kstenschke.copypastestack", "fossil4idea", "com.thesamet.intellij.ScariformFormat", "com.github.nrudenko.plugin.ormgenerator", "com.steve.plugins.mavenversion", "com.smartbear.soapui.idea.plugin", "de.espend.idea.php.drupal", "me.tatarka.androidunittest.idea", "CssAlphabeticalRearranger", "net.leppik.intellij.plugin.justifier", "com.jonathonstaff.ideaascii", "net.stateful.jetbrains.ADNCode", "siosio.FileOpenPlugin", "com.wix.eslint", "mobi.hsz.idea.gitignore", "com.jonathonstaff.androidaccessors", "uk.co.neylan.plugins.makeiteasy", "com.civ.androidStringsTools", "zielu.gittoolbox", "com.hal.phpmetrics.id", "com.alibaba.intl.urp", "org.weebly.generator", "uk.co.drache.intellij.guavaPostfixCompletion", "org.kevoree.modeling.idea.plugin", "JettyRunner-GK", "jballant.CommonJSAutoComplete", "com.nelchael.idea.projectcost", "com.christofferklang.pyxl", "org.jetbrains.plugins.phpstorm-remote-interpreter", "com.jetbrains.php.behat", "com.robotium.recorder.intellij", "<PERSON><PERSON><PERSON><PERSON>", "org.jboss.forge.plugin.idea", "com.jimulabs.mirror.plugin", "com.skiftio.intellij.lightpaper", "org.campagnelab.mps.UI", "XChart", "org.elixir_lang", "ccom.intellij.idea.plugin.hybris.impex", "assert-deluxe", "ru.spb.kupchinolabs.drozd.idea.plugin", "com.kstenschke.finderinfo", "ru.<PERSON><PERSON><PERSON><PERSON>", "com.wix.scss.lint", "com.plopiplop.leekwars", "de.espend.idea.laravel", "<PERSON><PERSON><PERSON>", "com.thoughtworks.gauge", "com.eclectide.intellij.whatthecommit", "com.jetbrains.isaev.issues.in.code", "com.arasthel.swissknife.plugin", "builder-deluxe", "com.tagmycode.intellij", "name.admitriev.jhelper", "BDVal", "org.campagnelab.background", "org.jetbrains.plugins.ruby-chef", "com.jetbrains.plugins.meteor", "anton_dev_ua.PipeTableFormatter", "org.campagnelab.Editor2PDF", "com.intellij.plugin.poe", "com.wix.jscs", "org.campagnelab.TextOutput", "org.campagnelab.UML_Diagrams", "pl.cmil.wuff.plugin", "com.zephir", "com.magnet.r2m", "com.intellij.checker-framework", "com.fishy.plugin.idea.webxlink", "org.cce", "com.github.letsdrink.intellijplugin", "org.jetbrains.settingsRepository", "com.squareup.intellij.plugins.builder", "com.idea.plugin.builder", "com.jetbrains.php.blade", "me.tatarka.holdr.intellij.plugin", "com.arasthel.groovy.override-completion", "nl.jworks.intellij.bootstrap3", "de.santiv.fastscrolling", "de.balpha.varsity", "com.steve.plugins.autoscroll", "com.github.pbetkier.intellij.synonyms", "com.axeldev.php1Up", "com.remoterapp.adbremote.plugin.id", "org.campagnelab.ClusterConfig", "org.campagnelab.GobyWeb", "org.campagnelab.Interactive", "org.campagnelab.logger", "org.campagnelab.NYoSh", "com.michael.bergens.java.playground", "de.santiv.rbesort", "pl.pamsoft.smartbuilder", "org.robovm.intellij", "com.sensiolabs.insight", "com.atsebak.ui5", "com.checkmarx.intellij", "OpenCms", "com.morcinek.android.codegenerator.plugin.intellij", "leviysoft.rainbowpony", "com.yourcompany.unique.plugin.id", "FridayMario", "ro.idea", "lsfusion.idea.plugin", "com.haskforce", "com.solveforall.clients.IdeaPlugin", "io.github.sirlantis.rubymine.rubocop", "Heroku", "luonq.ScrollFromSource", "com.oneskyapp.intellij", "Wrap in StringBuilder", "com.appstrakt.syncTranslation", "com.aspose.maven.intellijplugin.id", "ExploreTrace", "com.piegoesmoo.escape", "siosio.DomaSupport", "pro.opcode.bitrix", "de.kontext_e.idea.plugins.jqa", "com.farbluer.intellij.plugin.p4Intellij", "org.campagnelab.metaR", "com.kalessil.phpStorm.phpInspectionsEA", "repl.simple.mathematica.id", "com.classes.export.plugin.id", "com.densebrain.intellij.plugins.groovyconvert", "com.emblem.unique.plugin.idid234234", "com.westlinkin.androidLocalizationer", "net.cjkent.jodabeans.folding", "org.psliwa.idea.composer", "ru.itbasis.plugins.intellij.hashGenerator", "com.hashmem.idea", "krasa.nonProjectFilesUnlocker", "com.karateca.elementorIdea", "com.transround.nativeradmin", "com.codota.csp.intellij", "org.startica.meeniex.intellij.plugin.eclipseLikeEnterInStringLiteralHandler", "fir.im.plug.idea", "org.jetbrains.action-tracker", "com.dubreuia", "com.intellij.lang.javascript.es67", "com.champgm.intellij.plugin.preconditions", "com.konifar.material_icon_generator", "com.wix.react-templates", "com.github.MitI_7.IDEOM", "in.twbs.pure", "GsonFormat", "LimitedWIP", "com.thanosp.phpstorm.inheritdoc", "de.mprengemann.intellij.plugin.androidicons", "ro.bogdananton.testNameGenerator", "com.jeremyworboys.incDecValue", "com.manolenso.intellij.foundation5", "io.dwak.reactor.intellij.plugin", "com.robinmalfait.KopyPasta", "com.davidmis.elmplugin", "org.kitdroid.jnihelper", "com.rwteam.gscrum", "com.github.itechbear.clion-macroformatter", "com.monkeyscriptplugin", "SeleniumPlugin", "awesome.console", "pl.klamborowski.plugin.jacksongenerator", "com.igu.meteorjs", "fr.javatronic.damapping.intellij.plugin.integration", "com.eunut.android.plugin.selectors.generate", "com.sherchen.idea.plugin.injectcomment", "com.steve.plugins.recentprojects", "PerforceIC", "com.phraseapp.androidstudio", "com.renemaas.intellij.zipper", "com.eddy.generator", "net.yeahlol", "com.github.akiomik.ideaAndroidScala", "com.pulyaevskiy.phpstorm.phpdi", "com.aconex.codelitter", "com.unvired.sdk.plugin", "by.vkatz.decorate_mode_switcher", "com.github.bmsantos.idea.cola", "com.github.shyykoserhiy.gfm", "com.brainwy.pyvmmonitor", "com.ftinc.plugin.generator.sectionblock", "eu.meshuga.pudelek", "com.intellij.commander", "SourceSafe", "com.bpellint.idea", "blue.stack.j2Smali", "com.apiary.abm", "blue.stack.SerializableParcelableGenerator", "se.trifork.intellij.whitey", "pl.cmil.buildFinishedNotifier", "com.lxl.unique.plugin.id", "com.saffrontech.idea.thop", "com.fuxy.android.ide.plugin", "com.wix.coffeelint", "<PERSON>er", "com.contentful.ideagenerator", "org.macchiatow.plugin.tomato", "org.dlangplugin", "com.softdream.intellij.plugin", "makasprzak.step-builder-generator", "Explorer", "com.krylysov.nsisplugin", "ru.korgov.intellij.Generatable", "org.jetbrains.kotlin.android.dsl", "XslFoSupport", "com.boniatillo.phaserchains", "com.atsebak.raspberrypi", "com.jaumard.sails", "com.sencha.idea.IdeaPlugin", "com.armandakopian.lifecyclesorter", "org.anathan.zf2modulecreator", "com.dryabov.phpStorm.phpregexp", "cn.robin.vectorIconPack", "siani.dev.itrules", "net.servicestack.ideaplugin", "com.roomj.simpletitles", "com.easywsdl.tools.plugins.intellij", "ru.phism.intellij.unreal", "com.github.amolenaar.idea.clock", "com.ldriscoll.slf4jlogformat.intention.plugin", "pl.tomaszdziurko.codebrag.plugin.intellijidea", "net.andrevus.jetbrains.tasks.targetprocess", "root.tom.needham.imageeditor", "gta-idea-plugin", "eu.nyerel.panda.ij-plugin", "nl.mmeiboom.fitnesseplugin", "com.sjd.intellijextend", "com.kogitune.intellij.androidPostfixCompletion", "me.ele.napos.miracle.plugin.executor.ExecutorGen", "Cocos", "com.bft.control.idea", "hms.tap.idea.plugin", "com.pandawarrior.androidXMLConverter", "biz.paluch.atsoundtrack", "com.jpospisil.gototabs", "com.aopphp.go.framework", "com.ws.unique.plugin.id", "jVb_Designer", "mobi.hsz.idea.vcswatch", "YAML/Ansible support", "org.intellij.plugins.markdown", "cat.kanbanapps.gradlekiller", "one.alex.vars.inserter", "com.perl5", "com.ylt.appcan.plugin", "com.linuxgods.kreiger.intellij.idea.inspections.utility.singleton", "org.foxsly.idea.extended.generate.actions", "org.avaje.ebean.enhancer", "com.syndatis.idea.git-status", "com.syndatis.idea.showandhide", "com.busybusy.Taiga.io.Task", "idea.plugins.prado", "JsonFormat", "org.intellij.plugins.hcl", "sk.sorien.silex.plugin.id", "com.kgmyshin.ideaplugin.eventbus", "com.kgmyshin.ideaplugin.eventbus3", "org.campagnelab.ANTLR", "org.avaje.metric.enhancer", "pl.lksztmczk.openinterminal", "com.kogitune", "com.jetbrains.performancePlugin", "com.eagles13.fxmldeclarationhelper", "com.github.woru.options-completion-phpstorm-plugin", "com.insightfulminds.appcode.XcodeSchemeSelector", "intellij.buck.plugin", "nl.tudelft.watchdog", "nim.lang", "org.perfcake.pc4idea", "com.illuminatedcloud.intellij", "net.nerrd.intellij.plugin.dpicalculator", "allotria/removeusage", "com.nvinayshetty.DTOnator", "gr.jchrist.g<PERSON><PERSON><PERSON>er", "com.github.shiraji.hidetoolwindowsex", "NRGvlanghelPlugin", "com.remoterapp.adbremote.jsonviewer.id", "SpellChecker-Dict-Bulgarian", "com.techern.oxidize", "net.orekyuu.bitbucketissues", "<PERSON>ffle<PERSON>", "com.testfairy.plugin", "com.github.shiraji.gradleconfirmation", "find.me.tagged", "com.demandware.studio", "SpellChecker-Dict-Spanish", "de.espend.idea.oxid", "com.jetbrains.hackathon2015.S", "org.zkoss.zkidea", "com.layernet.plugin.adbwifi", "kodebeagleidea", "net.fhtagn.pycharm.cellmode", "ThinkStorm", "karl.gong.plugin.ptest", "net.ashald.envfile", "com.apibility.voicerecognition", "com.gluonhq.plugin.intellij", "org.rtctasks", "marcglasberg.HibernateInspectionsPlugin", "com.github.itechbear.clion.cpplint", "com.ritesh.intellij.plugin.reviewboard", "com.androhi.androiddrawableviewer", "com.jetbrains.intellij.api.watcher", "com.dmytrodanylyk.fold", "org.jraf.intellijplugin.opencurrentactivity", "org.typowriter.intellij.plugins.backgroundchibichara", "com.pojosontheweb.ttt", "com.onkiup.minedroid", "com.tcloud.android.appswift", "com.jotc.nsis.plugin.nsis", "phplint", "org.siprop.android.opencl.OpenCLCLGenerator", "io.github.francoiscambell.clionarduinoplugin", "com.github.mmin18.layoutcast.ide", "com.github.shiraji.pluginimporterexporter", "com.jadi", "org.campagnelab.NextflowWorkbench", "com.vladsch.idea.multimarkdown", "com.visprogramming.armory", "com.roomj.simpletitlesprojectonly", "com.przemyslawj.mockito.generator", "in.xiv.cs", "pl.micdev.intellij.fluentsetter", "PowerMouse", "io.codecook", "com.mulgasoft.emacsplus", "com.ihsan.bal.fjfplugin", "FitNesse", "importre.intellij.android.selector", "com.jetbrains.idear", "com.hotels.intellij.plugins.sharedviews", "org.nextras.orm.intellij", "com.github.shiraji.createintentinspection", "com.github.shiraji.newinstanceinspection", "org.sssta.androidtools", "com.fishy.plugin.idea.auto", "com.aspose.words.java.intellij.maven", "idea.k", "ua.pp.madcap.codingame.plugin", "me.guichaguri.additionaltools", "com.hasintech.intellij.angularTemplates", "Externalizer4j Plugin", "com.vuonghv2.vpmt.plugin.yumeplugin", "com.fishy.plugin.idea.ponytail", "com.lid.intellij.translateme", "com.github.platan.gradle-dependencies-formatter", "com.aspose.Cells.java.intellij.maven", "org.root.HoogleIt", "com.bulenkov.intellij.png.optimizer", "com.tapper.splittingup", "com.aspose.slides.java.intellij.maven", "com.weezlabs.intentSender", "net.pavelk.tlschema.colorer", "com.d0as8.perlrun", "net.orekyuu.java8postfix", "org.mirah.idea.plugin", "com.baidu.penny", "me.piotrbuda.intellij.pony", "com.hewy.esnippets", "convert.html.text.to.kotlin.plugin", "li.ktt", "com.eltonkola.androidsnippets", "il.co.falk.buildAndLint", "com.michaelbulava.TFSVCS", "Beaker Test Runner", "VersionOne", "com.aspose.barcode.java.intellij.maven", "de.dm.intellij.maven-archetypes-catalog-plugin", "com.directfn.unique.plugin.logmanger", "com.vpedak.testsrecorder.plugin.id", "pl.maja.nativetounicode", "claims.bold.intellij.avro", "pro.alex_zaitsev.androidstyler", "org.sonarlint.idea", "com.aspose.pdf.java.intellij.maven", "com.ppolivka.gitlabprojects", "org.turbanov.execution.cmd", "org.concordion.plugin.idea.lang", "co.leantechniques.idea-js-test-finder", "com.dmitz.intellij.plugin.websocket.client", "com.microsoft.vso.idea", "com.aspose.email.java.intellij.maven", "com.github.pedrovgs.androidwifiadb", "com.sap.hcp.WebStormPlugin", "org.evosuite.plugin.intellij", "com.raj.unique.plugin.id", "com.jetbrains.plugins.yeoman", "com.beust.kobalt.intellij", "ch.docksnet.rgraph", "pl.vltr.db4oplugin", "com.facebook.rucinskic.chromaterial", "com.spicedroid.xmltrans", "io.codearte.props2yaml", "mopedjc", "com.crashlytics.tools.androidstudio", "com.aspose.tasks.java.intellij.maven", "com.chrisrm.idea.MaterialThemeUI", "com.github.gcds.global.template.variables", "org.fregelang.plugin.idea", "com.key.testplugin.test", "com.bryansharpe.slackstorm", "com.atlassian.bitbucket.references", "com.fir.im.android.plugin.id", "com.davy307.androidassetimporter", "com.magento.idea.magento2plugin", "TemposID", "com.smilingrob.plugin.robohexar", "com.jawspeak.joined-tab-scrolling", "com.aspose.diagram.java.intellij.maven", "idea.bear.sunday", "SerialPortMonitor", "com.drinchev.projectlabel", "com.aspose.ocr.java.intellij.maven", "jbehave-support-plugin-extension", "jp.codic.plugins.intellij", "org.antlr.jetbrains.st4plugin", "com.ky-proj.spjplugin", "com.lostintimedev.java.idea.ThinCaret", "nb-mind-map-idea", "HybrisPlugin", "io.gulp.intellij", "com.aspose.imaging.java.intellij.maven", "com.emberjs", "com.coursehero.helper", "com.microsoft.tooling.msservices.intellij.azure", "net.jackofalltrades.asynch-before-run-tasks", "Vue.js", "com.wrike.plugin.intellij", "com.moxun.generetor", "com.github.mohamedkomalo.envVarsInPathVars", "ru.oldtown.idea.workflowplugin", "com.widerwille.afterglow", "io.imdone.imdone-plugin", "com.jintin.droidlane", "com.scache.createintentmethodgenerator", "35e38c06-9762-11e5-8dd3-54a050ace290", "org.eclipse.xtext.idea", "org.eclipse.xtend.idea", "org.eclipse.xtext.xtext.idea", "xyz.marcato.ideaplugin.methodscount", "com.google.gct.login", "com.google.gct.core", "com.chairbender.object_calisthenics_analyzer", "com.bobz.OpenTerminalHere", "com.github.profeg.IntelliJGettersCheckInspections", "com.fissionlabs", "cn.nekocode.plugin.parcelablegenerator", "com.neueda4j.intellij.plugin.cypher", "com.bugvm.intellij", "com.cursiveclojure.cursive", "com.afcastano.intellij.autovalue", "me.geso.assertj_postfix_plugin", "ru.sadv1r.afc.ideaPlugin", "com.intellij.lang.jsgraphql", "de.onigunn.intellij.xliff", "com.hannesdorfmann.parcelableplease.plugin", "com.bowmanb.ctttr", "com.line.plugin.auto.align", "com.moxun.plugin.s2v", "com.github.kennedyoliveira.ultimatepastebin", "com.pgyer.as.plugin", "com.coolsharp.codesection", "phpfmt", "uk.me.jeffsutton.pojogen", "uk.me.jeffsutton.restconsole", "net.masterthought.dlanguage", "org.jetbrains.plugins.node-remote-interpreter", "com.intellij.stats.completion", "com.boredream.plugin.espressocreator", "com.boredream.plugin.layoutcreator", "com.heaven7.plugin.android.databinding", "com.github.droibit.plugin.androidapilevel", "io.v.vdl", "com.bmesta.powermode", "Activity Tracker", "at.niw.EnableColorManagementPlugin", "com.fatfractal.plugins.idea.ffdl", "kos-ksp.idea-plugin", "com.ruslanpolutsygan.adderremover", "de.espend.idea.php.toolbox", "com.doglandia.GpsEmulator", "com.smilingrob.gitpair", "com.navmine.classdiagrams", "com.vladsch.PluginDevelopersToolbox", "com.widerwille.quicklook", "SnakeYAML plugin", "com.github.johnthagen.cppcheck", "be.cegeka.intellij.plugin.configurablefilename", "com.lopesdasilva.wcs.plugin", "com.breeze.plugin.reprotoc", "io.github.takuaraki.dvc", "org.sireum.intellij", "com.intellij.plugin.applescript", "com.krrrr38.idea.mockito.postfix", "com.marlboro.gitbar", "org.jetbrains.comparisonChainwootool", "com.zf.androidplugin.selectdrawable_generator", "org.broadinstitute.winstanley", "com.zf.androidplugin.shapedrawable_template", "com.bitrixsoft.lang", "com.thomas.checkMate", "com.slalom.idea.aws.avs.sutr", "com.microsoft.azure.hdinsight", "com.meredith.intellij.plugins.openinclude", "com.mbeddr.pluginmanager", "dbgsprw.apm", "com.shandiangou.plugin.csveditor", "com.rd.app.plugin.", "com.hadihariri.leanpub", "com.webstorm.symbols", "me.zheteng.idea.reverse", "com.wix.sasslint", "com.xgheaven.splitString", "com.atoum.phpstormplugin", "Samebug", "com.thoughtworks.tools.idea.templatenav", "com.digi.android.studio", "com.github.tom-power.close-tab-left-right", "org.rust.lang", "uk.co.ben-gibson.remote.repository.mapper", "com.manolenso.intellij.foundation6", "com.deepakm.plugins.intellij.misc", "OptimizeImportsForScope", "com.minutephp.framework", "com.squareup.sqldelight", "com.zenefits.hopefully", "org.toml.lang", "org.mar9000.mps.ecmascript", "com.develmagic.spoj.submitter", "com.idea.android.superplugins.plugins", "tirke.cupPlugin", "cc.redpen.intellij", "org.mule.intellij.plugin", "cz.daku.intellij.extraActions", "zjhmale.rainbow", "com.github.jk1.ytplugin", "android.platform.builder", "com.strv.linecounter", "ca.rightsomegoodgames.mayacharm", "com.footprint.asplugin.plugin.viewgenerator", "VectorDrawableImporter", "com.wannabe.smartsearch", "org.roana0229.android-xml-sorter", "zjhmale.clojure-pretty-symbol", "cz.jiripudil.intellij.nette.tester", "com.dslplatform.ideaplugin", "com.nes.intellij.plugin.id", "org.intellij.plugins.translate", "pl.j<PERSON>owski.transplug", "com.sbmpost", "com.philipleder.plugin.painpoint", "org.scalafmt.ScalaFmt", "club.guacamoledragon.plugin.twitch-chat", "com.intellij.clion-swift", "uk.ac.glasgow.scclippy", "com.github.healarconr.loggerfolding", "ru.mipt.acsl.decode.idea.plugin", "ch.uzh.ifi.batteryAwareTransformations", "edu.ua.highbay.binderplugin", "com.weebly.opus1269.smoothscroller", "com.chornsby.intellij.plugins.django.Migrations", "com.github.droibit.plugin.truth.postfix", "com.aqqxa.plugin.aligner", "de.ax.powermode", "com.aspose.Note.java.intellij.maven", "io.github.liias.monkey", "org.forgerock.cuppa", "pl.psi.jls.wuff.plugin", "cn.fishy.plugin.idea.auto.transfer", "com.jaeger.findviewbyme", "com.github.shiraji.findpullrequest", "ru.mobiledev.plugins.uigd", "com.billguard.appcodeplusplus", "camtwoTomcatRunner", "com.xcodersteam.idea.plugins.mkupscss", "zjhmale.haskell-conceal", "eu.inmite.android.plugin.preioc", "com.shanbay.plugin.color.reborn", "RunTaskAction", "com.smcplugin.leonid.menshenin.plugin.id", "online.devliving.stepbuilder.generator", "io.protostuff.protostuff-jetbrains-plugin", "org.typowriter.intellij.plugins.wallpaper", "org.slayer.testLinkIntegration", "com.souyidai.common.plugin.generators", "com.dim.plugin.adbduang", "org.buffagon.intellij.catberry", "com.infoedge.plugins.gcmtester", "com.fuzz.internal.hooks", "SequenceDiagram", "gt.tool.plugins.org.json.gen", "com.jetbrains.php.joomla", "com.github.shiraji.opencommitongithub", "io.github.maddouri.intellij.OnlineSearch", "me.drakeet.layoutformatter", "com.targetprocess.assigned.entities.plugin", "de.wieselbau.clion.clangtidy", "com.galenframework.specs.idea", "lu.uni.clion.cproject", "fr.gwallet.intellij.windowsizer", "com.philipleder.plugin.marks", "jindent.plugin.intellijidea", "com.pguardiola.androidresresizer", "com.codemind.wordcount", "fr.idapps.intellij.plugin.android.gotolayout", "com.shahab.rewatch", "com.github.leomillon.uuidgenerator", "cn.wuzhizhan.plugin.mybatis", "com.idescout.sqlite", "jp.gcreate.plugins.adbfriendly", "yan.intellij.plugin.grailsTools", "GenerateTests", "com.demonwav.minecraft-dev", "com.gm.actions.GMAction", "com.RedTeam.unique.plugin.id", "com.jiyuanime.ActivatePowerModeApplicationPlugin", "com.udari", "GitflowExtended", "io.github.jhsx.GoJetPlugin", "com.msiddeek.webpstinks", "no.tornado.tornadofx.idea", "com.fueled.mvp", "dk.erikzielke.gitblit", "com.github.Deadleg.idea-openresty-lua-support", "org.zalando.intellij.swagger", "com.github.shiraji.permissionsdispatcherplugin", "com.github.shiraji.clearignorethisupdate", "com.jmg.codecomment", "com.corochann.plugin.clion.singlefileexecutionplugin", "com.softbank.robot.sdkplugin", "org.campagnelab.WebPublisher", "Force Shortcuts", "com.sked.yorklogcat", "com.sked.soft.crater", "com.flow.intellij", "com.juanpany.es6.intentions", "com.android.newclassdialog", "UDC", "com.csky.manifestmodify", "com.iTimeTrack.intellij.plugin", "com.github.denofevil.anyBarIdea", "com.sjtu.chenzhongpu.sparkexamplexmvn", "ru.hungrymole.kotlin", "de.mobilej.adc.plugin.id", "com.pyango.gmbh", "com.lichfaker.plugin.id", "org.intellij.gitosc", "com.mglaman.drupal_run_tests", "swingexplorer-plugin-v2", "com.pengli", "jss.JumpSourceSpec", "org.jspresso.plugin.ijds", "net.codestats.plugin.atom.intellij", "com.jakutenshi.projects.umlplugin", "com.denis.zaichenko.angular.2.ws.live.templates", "com.github.shiraji.breakpointsmanager", "io.halik.intellij-plugin", "org.jetbrains.corrector", "haxe.checkstyle", "HtmlEntity", "software.amazon.ion.intellj", "com.etienne.toggleinclude", "com.universityandroid.tutorial", "com.sqide.test", "com.samsonova.scala.sd.id", "com.octogog.idea.bazel-build-formatter", "org.jetbrains.gsearch", "com.rbf.unique.plugin.id", "marekbruchaty.TPCMG", "com.github.shiraji.kreateintentinspection", "com.codewaves.intellij.plugin.iconcreator", "ClassicIcon", "com.happysoftware.quickcompare.plugin", "com.mummu.gcm.send", "com.eddyyuan.plugin.android.AndroidResourceGenerated", "com.github.shiraji.kewinstanceinspection", "com.u3coding.removebutterknife", "main.kotlin.com.jimschubert.intellij.swaggercodegen", "GsonOrXmlFormat", "com.thasneem.draganddrop", "A prevent pinned tabs from closing plugin - by momomo.com", "A move tab left and right using the keyboard plugin - by momomo.com", "com.dslfoundry.plaintextgen", "com.intellij.translation", "com.github.holgerbrandl.pasteimages/", "com.droidtestlab.espressoblackbox.id", "com.oroplatform.idea.oroplatform", "com.github.shiraji.ipgwizard", "org.cce.appcode", "com.peterae86.copy", "org.github.feiwongreed", "org.mule.tooling.intellij.dataweave", "org.mule.tooling.intellij.raml", "com.weex.darin", "com.github.pshirshov.bytecodeeditor", "org.siani.javafmi", "LogSupportLite", "io.coati.idea", "com.writeoncereadmany.semantichighlighting", "com.boohee.plugin.translation", "com.sum.qiu", "ru.vkurdin.idea.php.lambdafolding", "Royll.ID", "com.ykfs.plugin.generate", "com.getsensibill.stylecreator", "com.gutils.android.autocode.plugin.intellij", "io.ringle.ij.cmaked<PERSON>s", "cn.magicwindow.sdk", "de.lukweb.hasteit", "pav.sprykerFileCreator.plugin", "com.backkoms", "bigfootindie.log_generator", "io.edgeg.gtm.intellij", "org.elgg.ps", "com.umu.langtip", "co.notime.intellijPlugin.backgroundImagePlus", "com.github.alvaromarco.CleanArchitecturePlugin", "cn.enilu", "com.wingsofts.plugin.MVPHelper", "com.gmail.blueboxware.libgdxplugin", "k-infinity", "org.majki.intellij.ldapbrowser", "com.jetbrains.annotations.preloader", "com.ivoryartwork.plugin.folivora", "com.thbs.materialdesign", "com.javils.ThemeSwitcher", "com.ssanjun.intellij.datagrip.jsonDataViewer", "net.kotek.multidpi", "com.tencent.bugly.plugin.idea", "org.avaje.ebean8.enhancer", "BunyanConsole", "google-java-format", "com.github.aayvazyan", "ru.yole.jitwatch-intellij", "com.farwolf.androidannotion", "com.necisstudio.adbwifiandroid", "net.hexar.json2pojo", "com.thangiee.metadroid", "com.necisstudio.logisys", "com.dynatrace.integration.idea", "org.jetbrains.memory.view", "com.zone.unique.plugin.singleton", "com.zone.unique.plugin.space", "at.dotti.intellij.plugins.xmpp", "CloudInteractive", "nl.capaxit.tooltiprunner", "com.hpe.nga.ide.intellij-plugin", "com.olegych.hungry.backspace.reloaded", "com.q-ide", "com.symbol.emdk.wizard", "tw.yalan.android.plugin.findviewbyidtobindview", "com.konifar.stringssearch", "fi.helsinki.cs.tmc", "com.gogh.android.plugin.translation", "training", "blast.browser", "com.gogh.android.plugin.easytranslation", "org.jetbrains.plugins.sample.ToolWindow", "org.moe", "com.davidgjm.idea.plugins", "com.ludditelabs.autodocintellij.plugin", "org.dan.idea.charremap.plugin", "org.moe.designer.ios", "org.jetbrains.spek.spek-idea-plugin", "net.coding.git", "AureliaStorm", "com.shang.android.layoutformat", "com.ryanair.id", "com.outofmemory.entertainment.dissemination", "de.tfr.idea.plugins.i18n-formatter", "<PERSON><PERSON><PERSON><PERSON>", "some.awesome", "info.novatec.testit.livingdoc.intellij", "org.dan.idea.postlaunchhook", "org.intellij.plugins.postcss", "cn.yiiguxing.plugin.translate", "krasa.CpuUsageIndicator", "com.juanfelippo.translator", "com.pdehaan.idea-plugin-flatbuffers", "com.github.shiraji.colormanager", "me.pkhope.plugin.library_manager", "de.caluga.intellij.plugin.gpe", "me.xfcy.idea.CopyThePath", "io.github.antlabs.code-generator", "org.igu.plugins.nativescript", "com.dvd.intellijdea.materialcolorpalette", "com.roysom.closeallprocesses", "com.wannabe.graven", "com.basisjs", "com.growingio.sdkHelper", "org.jetbrains.plugins.phpstorm-docker", "com.spotify.scio-idea", "com.alexanderpa.flyway.migration.creator", "me.artspb.idea.eval.plugin", "cn.pinmix.shaohui.ACNPlugin", "com.exynap.plugin", "com.ryan.MvpClassesGenerator", "net.petitviolet.idea.scala.minimal-cake-pattern-generator", "com.likfe.ideaplugin.eventbus3", "com.thangiee.freasy-monad", "com.thea.plugin.imagecompression", "AtlasTool", "nix-idea", "com.google.idea.bazel.ijwb", "cn.leeii.plugin.annotation", "cn.mycommons.tools.plugin.databindingconvert", "uk.co.reecedunn.intellij.plugin.xquery", "co.lujun.plugin.betranslate", "gradle_cleaner_intellij_plugin", "com.apkfuns.plugin.freeline", "moe.xing.databindingformatter", "merger4idea", "com.ioncodes.pasta", "com.undo_software.clion.reverse", "com.vaadin.designer.intellij", "com.ankama.intellij.uuidmaker", "com.netcracker.rss.portlet.structure.generator", "com.github.shchurov.prefseditor", "org.intellij.plugins.ceylon.ide", "GenerateSVG", "com.service-now.plugin.idea", "merger4ideaPlugin", "ai.deckard.intellij.plugin", "io.nativetap.plugin", "com.robohorse.robopojogenerator", "com.instant.android.plugin.snorlax", "org.jetbrains.idea.clojure", "at.kienmeier.plugins.creational.stepbuilder", "de.kontext_e.idea.plugins.autofill", "IntelliJ-Shortcuts-For-AEM", "com.ccnode.codegenerator.mybatis.generator", "com.rybkin.jbehave.example-table-formatter", "com.yellow5a5", "com.lypeer.matchmaker", "net.orekyuu.semicolonless", "tu.wenbo.easyliteral", "de.tomtec.idea.plugin.gradle.run", "com.stylint", "g<PERSON>kin", "cz.jiripudil.intellij.nette.factoryGenerator", "com.thomas.needham.neurophidea", "me.fingerart.idea.openuploader", "com.github.shiraji.emoji", "com.appcodeplugins.swiftlint", "com.funivan.phpstorm.refactoring", "co.lujun.plugin.colorpanelhelper", "com.google.idea.bazel.aswb", "org.argus.cit.intellij", "com.github.shchurov.gradlestop", "com.taobao.weex.lang", "com.alibaba.baichuan", "OdpsStudio", "me.aheadlcx.dimenhelper", "com.drakklord.gradle.metric.core", "com.drakklord.gradle.metric.checkstyle", "com.drakklord.gradle.metric.pmd", "me.fingerart.idea.apidebugger", ">me.drakeet.plugin.multitype", "codes.ai.java.intellij", "adb.wifi.woaiwhz", "com.dropbox.djinni.ideaplugin", "me.aleph0.androidexporter", "io.flutter", "de.achimonline.changelistorganizer", "com.xdandroid.scaterknife", "com.intellij.resharper.unity", "org.hexu.getcode", "fr.tolc.jahia.intellij.plugin", "com.intellij.resharper.HeapAllocationsViewer", "com.othoslabs.InspectionKeyBinder", "com.cnfol.mrz", "com.xiyuan.PropertiesToClass", "TRTIdea", "com.seventh7.widget.iedis", "restore.sql", "ru.basecode.ide.rest.plugin", "com.adamliesko.gotests.plugin", "de.achimonline.tempconfigcleaner", "teeschke.geocode", "com.itheima.sz.plugin.commoncode", "com.jedide.apex", "org.openmastery.ideaflow", "com.siimkinks.sqlitemagic", "eu.sim642.idea.zalgofy", "com.github.openComments", "de.domjos.ideaMantis", "com.phonegap.tools.plugin", "jclasslib", "ru.limydesign.plugins.yandex.translate", "com.e2dit.e2tml", "jhunovis.idea.plugin.umlauts", "com.e16din.incl", "com.rd.filtertranslate", "com.vladsch.MissingInActions", "<PERSON><PERSON><PERSON>", "com.jamhub.appaie.plugin.dbot", "de.monticore.lang.montisecarc.MontiSecArcLanguagePlugin", "io.hansel.androidstudioplugin", "com.znshadow.viewbinder", "com.github.jpmossin", "loopeer", "co.nums.intellij.aem", "info.bem.bemmet", "com.github.shiraji.databindinglayout", "me.chunsheng.plugin.shortcut", "com.wangzai.plugin.findViewById", "com.intellij.react.css.modules", "com.jokerzoid.intellij.plugin.stylelint", "lx", "com.upsolver.PreCommitHook", "com.bpleslie.idea.BradsTheme", "com.jetbrains.malenkov.color.blindness.support", "com.kraft.idea162IconPack", "common.plugin.extension", "io.alef.vypa", "com.gilecode.langlocker", "com.darkyen.darkyenustimetracker", "org.avallach.daedalus.ide", "net.jangaroo.idea.4", "com.jetbrains.php.phpspec", "ru.investflow.mql", "com.fireline.plugin.id", "org.limitium.mathfolding", "pw.mcclure.djaneiro.pycharm", "tiny.unique.plugin.id", "name.gudong.singletongenerate", "com.eflabs.plugin.efcommon", "com.xingren.fc.rangers-logging-intellij-plugin", "com.rodziu.plugins.JFramework", "com.yuyh.reactnative.tools", "cn.mycommons.autovalueconvert", "com.intellij.bigdecimal-folding", "com.stfalcon.mvvmgenerator", "Royll.SimpleXmlFormat.ID", "fr.inria.lille.spirals.repair", "net.jangaroo.idea.migration", "com.codealike.client.intellij.plugin", "org.avaje.ebean10.enhancer", "name.kropp.intellij.makefile", "com.huangmb.idea", "com.dachmx.android.checkcite.file", "com.xujiaji.plugin.MVPManager", "moe.studio.formatter", "org.exbin.deltahex.intellij", "com.zx.mvphelper.unique.plugin.id", "in.thekalinga.snippet.intellij", "com.ob.androidstudio", "com.ferrius.phpStorm.phpattention", "org.santoslab.compilers", "org.a8sport.translate.unique.a8translate", "com.lany.plugin", "com.hr.dimenify", "com.dtforce.resman.plugin", "TypingFreezeAnalyzer", "com.github.tinselspoon.intellij.kubernetes", "com.github.michalszynkiewicz.analyze-multiple-stacktraces", "kim.uno.templates", "ThreadDumpVisualizer", "com.thibaulthelsmoortel.pastebin", "com.bruce.intellijplugin.generatesetter", "com.observly.recorderplugin", "vette.neos", "com.linroid.plugin.bigo.proto", "MHCommitMessageTemplate", "com.github.jeysal.intellij.plugin.livetemplate.scriptenginemacro", "life.iuni.android.plugin.butterknife", "com.liu.lang", "com.github.nukc.plugin.apkmultichannel", "org.apache.camel", "com.xu", "org.sanchouss.idea.plugins.instantpatch", "org.jaguar.dart", "net.scrumplex.fxmlhelper", "ABACUS Plugin", "com.lozi.android.plugin.search_resource", "Samebug for Android Studio", "me.leefeng.mvphelper", "xyz.belvi.kingsmentor.resourceType.plugin.organiser", "com.NASMSupport", "org.sth.grafter-macro-support", "com.yii2support", "com.cedarsoft.serialization.generator.intellij-plugin", "net.lambcode.enforceBracketStyle", "com.netease.regular", "com.pytenlabs.instapk", "com.worldline.vbu.testrecorder.intellij", "com.ironman.me", "com.aemtools", "com.activemesa.hdline", "com.kalessil.phpStorm.yii2inspections", "com.sanyinchen", "com.nevaryyy.fvg", "net.javaru.idea.frc", "com.github.bjansen.intellij.pebble", "lasca.intellij.plugin", "send2terminal", "navigate-to-url", "uk.co.itmoore.intellisubsteps", "wangyi.plugin.singleton", "LogSupport2", "com.stylismo.nullability-annotations-inspection", "il.ac.technion.yearlyproject2016.team8", "com.naughtyserver", "ImportPlugin", "edu.berkeley.cs61b.plugin", "com.github.s4nchez.okeydoke.idea", "org.logtalk", "pl.edu.pwr.judy.intellij", "de.uniorg.ui5helper", "com.teamscale.ide.intellij", "Sweep", "io.harply.plugin", "CopyRestUrl", "io.cloudslang.intellij.plugin", "com.scireum.rythmengine.detector", "reasonml", "org.jetbrains.plugins.vue", "pro.kondratev.wicketlambdafold", "com.ccnode.codegenerator.MyBatisCodeHelper", "org.turbanov.run.configuration.as.action", "eu.osimowicz.plugins.intellij.CodeBlocksSorter", "com.andyken.ormlite.rawsqlgenerator", "com.github.droibit.plugin.rxjava.postfix", "core.plugin.monkey", "de.qrdn.coco_idea", "com.bmesta.mvntoolkit", "com.github.me10zyl.converter", "be.maartenballiauw.rider.reversepackagesearch", "com.crowdin.crowdin-idea", "TemplateBuilder", "yet-another-builder", "org.ferdavs", "net.olegg.bodylookin", "de.sgalinski.fluid.plugin.demo.id", "com.weirddev.testme", "nl.rubensten.texifyidea", "me.serce.solidity", "com.reverie.revlocalization", "com.wjs.shortcut", "com.xiaohansong.codemaker", "com.qburst.plugins.android", "in.oneton.contrib.plugin.angular.material", "com.github.b3er.idea.plugins.arc.browser", "com.poratu.idea.plugins.tomcat", "com.appcloud.test", "com.cainiao.scriptGenerator.plugin.20170220", "com.cedricziel.idea.typo3", "fixdroid", "com.cainiao.fktools", "com.loren.generater", "com.netease.NEJ", "com.vimtools.ideaexactionbar", "Show REST Services", "com.axter.plugin.IDsCreate", "zh-translate-en", "com.yugai.mvp", "com.demonwav.autosync", "idea.plugins.thirdparty.filecompletion", "MultiHighlight", "com.miui.gallery.dao.generator", "com.bruce.intellijpluin.stackoverflow", "com.jetbrains.php.codeception", "com.appdynamics.androidstudioplugin", "com.chakki_works.watchme", "com.shenjiajun.TinyPic", "com.vaadin.designer2.intellij", "io.ballerina", "com.hxsmart.zhangbh.JNIVAR2C.id", "ru.adelf.idea.dotenv", "com.your.ELICE.eliceplugin", "name.kropp.intellij.qml", "io.github.mengyu-dev", "be.waines.idea.undo-scroll", "cyberhck.github.idea.plugin.phalcon", "com.baijiahulian.tianxiao.action", "com.coursehero.hodor", "com.hpe.adm.octane.ideplugins.intellij", "ms.konovalov.intellij.hidpi-profiles", "com.talkingdata.orm.tool", "com.wang.okhttpparamsget", "com.ledongli.GGTemplate.TemplateCreator", "io.intino.plugin", "com.dianping.unique.plugin.id", "com.hotswap.agent.plugin", "com.ayvytr.easyimportandroidproject", "com.google.idea.bazel.clwb", "com.alibaba.autonavi.qa.testng", "org.turbanov.commits.message.checker", "com.netease.hearttouch.as_nei_plugin", "com.tobszarny.activetabhighlighter.plugin.id", "com.headwire.aem.tooling.intellij", "com.github.beansoftapp.reatnative.idea", "wuhaowen.mvpkillerID", "com.viartemev.requestmapper", "org.jetbrains.plugins.go", "com.sc.tool", "com.senthil.codesearch", "me.hehaiyang.codegen", "jbehave-syntax-support", "uk.ac.glasgow.microissues", "io.t28.json2java", "com.perfectomobile.intellijidea.androidstudio", "it.droidcon.androidstudioplugin", "com.canoo.dolphinplattform.intellij", "cn.isaac.codelines", "com.agido.idea.settings", "com.keshasosiska.easymock-expect-builder", "com.dragon.mvphelp", "com.puke.GitGetter", "com.banyaibalazs.createmoxplugin", "2017031807130000", "be.jeroendruwe.auto-exclude", "ru.fix.completable.reactor.plugin.idea", "net.omikron.equalsandhashcodereloaded", "codes.seanhenry.mockgenerator", "de.ifahrentholz.snippets.polymer", "com.elpassion.mainframerplugin", "com.canking.coderhealth", "com.maihaoche.mazda", "com.gelonggld.android.fastmvp", "jetbrains.mps.idea.java", "jetbrains.mps.idea.vcs", "ru.neofusion.ExcludeSymlinks", "jp.co.future.ideausqlfmt", "IdeaVimExtension", "com.bubiu.plugin.uniqueres", "ModuleLibraries", "com.andreibacalu.plugin.stop_build", "io.sourcetrail.idea", "org.jetbrains.plugin.lightbulbtoggle", "com.gamache.intellij.ErrorItemLineMarker", "org.dnltsk.mapfileplugin", "org.diydyq.miapp", "com.vmax.android.ads.plugin", "com.perfectomobile.intellijidea", "org.ice1000.a8translate", "com.joker.find.view", "org.psliwa.idea.givwenzen", "com.cegin.PUTVT", "com.aurimasniekis.phpclasstemplates", "com.aurimasniekis.phppsr4namespacedetector", "io.github.vcuswimlab.stackintheflow", "br.com.br.com.breakpoint.breakpoint.toogleAll", "com.example.intellijidea.plugins.findduplicates", "com.github.lwfwind.automation.plugin.aping", "cz.k2.eshop", "quokka.js", "at.dotti.intellij.plugins.team.mattermost", "com.meizu.flyme.mall.XmlToJson", "JGivenPlugin", "cyningxu.findviewbyx", "de.espend.idea.php.phpunit", "io.wisetime.plugins.window.branch", "com.kota65535.intellij.plugin.keymap.exporter", "com.potterhsu.jsonviewer", "com.mabdurrahman.intellij.customtitle", "com.sourcegraph.jetbrains", "de.espend.idea.php.behat", "com.apksecure.plug1234.plugin.id", "com.bigmy", "com.dafa.plugin.easyperfs", "com.android.component.explorer", "org.pine.plugin.PineSpecPlugin", "org.intellij.plugin.jojoldu.translator", "com.jin.translate", "io.allandequeiroz.random.image.background", "com.zxk.plugin.buildjson", "com.hd123.plugin.id", "com.automation.mtop.plugin.mtop-ng", "org.jetbrains.debugger.streams", "com.kesselring.valuegenerator", "org.jetbrains.fortran", "com.appcodeplugins.swiftify", "com.zen.idea.plugin.adt", "com.toan2.yapf", "com.alayouni.ansiHighlight", "ch.dasoft.iconviewer", "AutoSwitchIm", "org.metaborg.spt.testrunner.intellij", "dk.appdictive.adbwificonnect", "ru.scratty.generatetomap", "com.gheorman.radu.connascence.detection", "yp.tools.plugin.my_activator", "com.maihaoche.cx5", "com.j92.current-date-generator", "yp.tools.plugin.swords", "com.pedalbuild.intellij.plugins.buildstats", "com.github.syuchan1005.emojiprefix", "com.vdoc.intellij", "com.packer.as.plugin", "net.rentalhost.idea.laravelInsight", "org.hackathon.perf-guard", "train.brain", "org.jetbrains.plugins.dired", "pl.jakubchmura.snmp.mib", "org.intellij.flashcards", "net.kenro.ji.jin.intellij.purescript", "org.yaoqiang.bpmn.editor.plugin", "io.github.donkirkby.livepycharm", "com.jinbolx.findviewforkotlin", "awesome.aurora.borealis.zupersearch.plugin.intellij", "com.intellij.ideolog", "jetbrains.mps.testing", "com.adaptavist.idea", "org.intellij.xquery.marklogic", "com.nasmlanguage", "com.stfalcon.mvpgenerator", "com.sunary.options-completion-phpstorm-plugin", "edu.jetbrains.plugin.lt", "me.aristotll.python.typing.adder", "com.tang", "codes.seanhenry.teardown", "fr.ensimag.deca.intellijplugin.id", "org.tinygroup.studio.service", "org.tinygroup.studio.template", "br.com.matheusfm.jsonutils", "ru.itis.androidplugin", "intellij-micropython", "brown.bat", "org.teavm.idea", "com.fuscent.idea.mybatis-xml-validator", "cn.wjdghd.unique.plugin.id", "QueryBuilderRefactor", "com.leeiidesu.gen.GenerateMVP", "com.taobao.atlas", "net.avenwu.tools.biu", "com.testvagrant.optimus.elements", "domagoj.korman", "Key Promoter X", "com.sercapnp", "com.smartbit8.laravelStorm.intellij", "cn.windfantasy.plugin.parcelablegenerator", "com.qiaoshouliang", "com.lmax.intellijLint", "me.ihxq.acejump.lite", "com.leon.generate.all.filed.setter", "mr.intellij.plugin.autofactory", "cn.kotliner.dataclass", "com.leon.databinding.gettersetter", "org.yxdroid.plugin.yxdroidtools", "cn.ieclipse.smartqq.intellij", "com.leon.databinding.generate.all.filed.setter", "com.morphling.builder.plugin", "com.aandk.plugin.log", "com.github.syuchan1005.MCPluginDebugger", "org.jeto.taskfocus", "me.danwi.miniapp", "com.ztf.talk", "com.suhininalex.cloneDetection.plugin.id", "com.endoidou.copy_current_activity", "com.github.novotnyr.jwt-intellij-plugin", "com.alibaba.uxcore", "coop.rchain.lang", "org.jfrog.idea", "com.me.speachme.intelliSpeach", "com.fwdekker.randomness", "com.ccnode.codegenerator.MyBatisCodeHelperPro", "com.djk.yyy.kotterknife", "ch.raffael.mddoclet.integrations.idea.MarkdownDocletIdea", "com.google.bamboo.id", "com.nd.sdp.common.CustomUIToolWindow", "com.beame.io.insta.ssl", "org.lff.plugin.dupfinder", "com.hotels.intellij.plugins.network", "su.nlq.idea.ExternalizableGenerator", "com.intellij.rider.nancy", "org.leblanc.CodeNarcNG", "ru.hiq.butterknifesharper", "ijkl-shortcuts", "me.tatarka.nyandroid.NyandroidRestorer", "name.kropp.intellij.proselint", "com.test.jinesh.json.to.pojo", "tv.twelvetone.gradle.plugin.navigation", "com.marijnzwemmer.rider.inject", "org.intellij.plugins.tickscript", "CodeWithMe plugin", "commit-template-idea-plugin", "de.endrullis.idea.postfixtemplates", "de.victorsaar.intellij.aem", "ren.helloworld.mvp.plugin", "com.qiaoshouliang.mvpPlugin", "com.maihaoche.mclaren.mybatisgenerator", "io.github.sskorol", "com.glagol", "cn.ieclipse.aorm.as", "com.github.mmm444.ijphab", "com.jk.kangdi.libbutterknife", "com.retryu", "com.wishtack.pysynthetic.intellij", "uk.co.reecedunn.intellij.plugin.marklogic", "de.juserv.intellij-propertiessort", "com.niorgai", "io.github.satr.idea.plugin.connector.la", "com.qiaoshouliang.CreateAdapter", "com.bmapleaf.mvvm", "com.inflectra.SpiraTeam", "org.bitvault.plugin.dpkplugin", "com.flageolett.roc", "org.tsxuehu.gen-api-doc", "com.asif.gsonpojogenerator", "longevity.idea-plugin", "top.jsmzr.cipher.component.CipherTools", "com.wkp.utils", "jetbrains.mps.ide.migration.idea", "me.minidigger.miniplugin", "com.zcolin.zplug", "me.aristotll.ruby.rdoc.adder", "de.misi.idea.plugins.junit5helper", "com.harmonycloud.mars.unique.plugin.id", "com.liyijiang.androidpx2dp", "com.talkingdata.jira.integration", "oorg.bvic23.intellij.plugin.storybook.Storybook", "de.markiewb.idea.gwtnavigator", "ru.makkarpov.ucl", "com.javaccy.plugins.LookupInfo", "com.khande.idea.plugin.SmartFindViewById", "be.maartenballiauw.rider.globaljson", "lv.midiana.misc.phpstorm-plugins.deep-keys", "Appetizer.io", "com.jetbrains.typofixer", "nl.capaxit.idea.plugins.usagevisualizer", "com.applariat.idea.plugin.id", "cn.yzl.activityintentcreater", "com.drvector", "fortify.fod", "com.ydzy.test", "com.ydzy.tangchaoSqlToJava", "com.ydzy.tangchaoGui", "com.dingxiang.plugin.StringEncryptor", "org.jbooster.jbooster-logging-intellij-plugin", "VisibleForTesting", "org.lejos.intellij.plugin", "com.luciozhang.plugin.id", "com.github.nuclearg.smartgs", "de.dieploegers.develop.idea.shellfilter", "com.shanbay.template", "wu.seal.tool.json<PERSON><PERSON><PERSON>", "com.yang.dialog", "com.khande.idea.plugin.TabNumberIndicator", "com.sburlyaev.terminal.plugin", "ImportCost", "net.offbeatpioneer.intellij.plugins.gravsupport", "com.samsung.srr.dex.adb_tool", "com.stoneplugin.unique.plugin.id", "com.hadihariri.pathtitle", "MyPlugin.CreateDialog", "com.talkingdata.smart.tool", "com.github.kamikx.adbdatetime", "name.ekt.jetbrains.plugins.flatten", "org.bitvault.plugin.bvkplugin", "com.bolex.recitewords", "io.github.francoiscambell.clionarduinoplugin2017", "Check-In Policies for TFS (Team Foundation Server)", "gw.in.ij", "ModuleColorPlugin", "com.nerdscorner.mvp.builder", "bundled-pycharm-help", "bundled-pycharm-edu-help", "krasa.translatorGenerator", "com.deadlock.scsyntax", "com.musical.ly.plugin.muse-mvp-plugin", "cn.itgrocery.plugin.markdownip", "itaka.intellij.plugin.json.navigator", "edu.umd.cs.cpm", "be.maartenballiauw.rider.xdt", "com.daveme.intellij.chocolateCakePHP", "com.its.Plugin.AS", "DuplicateLines", "com.ctrip.wireless.android.build", "com.cxp.plugin", "com.treenear.treeshape", "com.github.typ0520.plugin.fastdex", "com.josesamuel.logviewer", "com.peng.myproguard", "de.cusp.cu.curNotes", "si.deno.converter.cyrillic", "zhushou", "de.escalon.idea.plugin.svelte", "com.laomao.space.id", "intellij.zeppelin.", "org.jetbrains.plugins.wsl", "com.panshen.me.QuickSearch", "com.veracode.greenlight.intellij.plugin.id", "org.reneYang.texas.encrypt", "com.yuyashuai.plugin.dictionary", "com.ppdai.booster.ppdai-logging-intellij-plugin", "nsu.<PERSON>.<PERSON><PERSON>", "cn.bestwu.gdph", "com.buczkowski.gherkintsrunner", "org.mapstruct.intellij", "net.seesharpsoft.intellij.plugins.csv", "com.fooock.experimental.and.beta.icons", "com.ls.translate", "org.cwlplugin", "org.squarecell.wow.addon_support", "com.aconex.test.namer", "com.mallowigi", "com.cursive-ide.emacsplus", "com.alibaba.p3c.smartfox", "com.qianniu.plugin", "io.g13.ideaplugins.time", "be.maartenballiauw.rider.svcutil", "com.github.tomasmilata.intelliroutes", "com.mighty16.json.kotlin.class", "name.gudong.plugin.wb-build-config", "net.askarin.mpbots.idea.mpbotsideplugin", "manifold.ij", "com.duke.screenmatch", "com.ppolivka.plugin.gitcleaner", "io.github.xusida.idea.plugins.formatyaml", "com.zhaoyanjun.AndroidTranslate", "MyBatisLogPlugin", "StringToolsPlugin", "com.zhuomeng.unique.plugin.id", "com.amintado.bootstrap", "com.asta.mobi.generate.ca.code", "com.senthil", "izhangzhihao.rainbow.brackets", "com.jetbrains.edu", "org.avaje.ebean11.enhancer", "<PERSON><PERSON>", "com.mikyou.plugin.which_activity", "com.github.nokia.pyvenv", "com.mingyuanyun.appcloud.jetbrains.plugin.buildtool", "pl.com.andriejsoft.CloneArtifact", "com.huangmb.idea.fineI18N", "artsiomch.cmake", "com.xrath.plugin.fold", "com.gisinfo.idea.plugin.android", "org.jetbrains", "com.shamcode.intellij.embersnippets", "JBClearcasePlugin", "de.citec.json", "com.duseev.intellij.preservelayout", "me.lotabout.codegenerator", "git-commit-message-plugin", "me.scana.ok<PERSON>le", "com.kaygisiz.dependencygraph", "com.huangyuanlove.transform.VariableNameStyleTransfer", "com.maff.codingcounter", "com.fashare.smallhelper", "com.yx.plugin", "com.simon.plus.NotesTranslation.Translation", "com.jinsihou.react.snippets", "xyz.elmot.clion.armsupport.prj", "imp.ffs.mde.helper", "com.mnw.navigateidentifier", "com.intellij.resharper.ConfigurationSense", "com.baomidou.plugin.idea.mybatisx", "linyuzai.plugin.xml", "com.xiuyukeji.plugin.translation", "be.sourcedbvba.gradle.plugin.conference-snippet-plugin", "com.zq.plugin", "com.guohe.selector", "com.oliverlockwood.plugins.jenkinsfile", "FlutterI18n", "com.zhw.tgnet.android.AndroidProGuard", "com.mnw.tabmover", "com.gelonggld.plugin.dbcompare", "com.mnw.permute", "rocks.blackcat.vcl", "cat.helm.clean", "de.yannik_sc.java.intelli_j.plugins.unitize", "easy.plugin.translation", "com.bianxiaoyan.selector", "com.fanhl.intellij.postfix", "com.werfad", "com.pokkt.management.tool", "XmlDocInspections", "ImplicitNullability", "net.codeweavers.rider.autoattach", "com.suming.react.PropTypes", "org.tboox.xmake", "com.yinjin.intellij.translation", "com.kaygisiz.gradlehelper", "maven-wrapper-plugin", "codes.rudolph.idea.cfg", "ExceptionCatcher", "com.yyx.kotlinmvp", "tushar.checkForPull", "com.github.bpazy", "com.agilezhu.bundledatabuilder", "intellij-scheme", "es.n10.clean.plugin.id", "com.jincnashen.sky.plugin.id", "idrabenia.solidity-solhint", "com.winwin.idea.plugin.gotodeclaration", "eu.gillissen.rider.usersecrets", "com.rhtech.stser", "com.tencent.qgame.lintsample", "felix.plugins.pojo.builder.generator", "com.apkfuns.androidsourceviewer", "com.ntilde.androidinput", "com.cedricziel.idea.twig.postfix", "com.htz.plugin.export", "coral.plugins", "com.pythondce", "com.shellcheck", "com.jlh.plugin.idea.mybatis.generator", "com.gopkgm.plugin", "xyz.belvi.droid_snippet", "com.nomtek.plugins.loco", "com.yangsimin.android.lifecycle.plugin.id", "me.javaroad.plugins.code-generator", "switch_project", "com.cedricziel.idea.xliff", "com.schibsted.protein", "com.nodesagency.plugin.nstack_translation_folding", "de.imgruntw.plugin.datetimeconverter", "com.github.gtache.lsp", "com.github.ArtsiomCh.NestedBracketsColorer", "cc.yihy.dto", "org.wso2.siddhi", "net.kenro.ji.jin.intellij.crystal", "com.abby.plugin.gradledependencyhelper", "com.kalessil.phpStorm.phpInspectionsUltimate", "com.br.quicksloth", "com.tsunderebug.discordintellij", "com.yangsm.android.lifecycle.plugin.id", "java.com.lz.autogs", "io.github.kongeor.p4n", "com.zzy.plugin.MvpFileCreator", "org.jetbrains.ruby-runtime-stats", "com.uddernetworks.codehelp", "in.1ton.idea.spring.assistant.plugin", "com.fqxyi.plugin.butter.knife.code", "com.almightyalpaca.intellij.plugins.discord", "AsyncConverter.AsyncConverter", "com.nengjun.geek", "Roflcopter", "documentum-plugin", "com.pytest", "com.semihunaldi.intellij.ideacurrency.plugin", "color.scheme.Google Developer Tibau", "lt.martynassateika.fuelcms", "com.github.zxj5470.fcc", "lv.midiana.misc.idea-plugins.deep-dict-completion", "com.intellij.plugin.adernov.powershell", "<EMAIL>", "be.programaths.idea.plugins.editors.tss", "com.illuminatedcloud2.intellij", "ASM Bytecode Outline 2017", "com.hikesoft.idea.plugin.microservice.matrix", "com.deparse.dpframe.plugin", "com.chinaedu.aedu.plugin", "bundled-intellij-idea-help", "com.xin.update.bmob", "com.yinhu.app.plugin.mvp_create", "com.lanshiqin.plugin.hello-plugin", "com.linecorp.games.cs.quaty.helper", "com.daylerees.rainglow", "com.yangding", "com.funivan.idea.editUsages", "hunspell", "com.atomdu.plugin", "me.xiongjinteng.plugin.xbatis", "nosi.xmlpojo_plugin", "org.nik.project-necromancer", "bundled-datagrip-help", "bundled-phpstorm-help", "de.arktis.breeze-connector", "org.pcsoft.plugins.intellij.iss", "com.liangxiaoqiao.plugin.timetransfer", "edu.umontreal.hatchery", "com.zhaow.restful.toolkit", "eu.andret.intelliachievements.v1", "com.remove.recursion.inspection", "org.spilth.rubymine.migrations", "com.github.syuchan1005.npmscriptrunner", "cn.yzl.kotlin.ex.click", "com.github.wxisme.unique.plugin.id", "org.jetbrains.debugger.streams.kotlin", "ASM Bytecode Viewer", "com.devwook.copy_path_helper", "com.github.kornilovaL.flamegraphProfiler", "io.github.lizhangqu.intellij.plugin.android.bundle", "com.sgaop.idea.codeinsight.plugin", "org.plugin.dot.id", "<PERSON><PERSON><PERSON>", "io.github.biezhi.plugins.gitmoji", "org.unirail.SlimEnum", "com.github.syuchan1005.katakatataaaaaaan", "org.dimazay.stepgenerator", "org.lice.lang", "com.arcticicestudio.nord.jetbrains", "com.ag.ui5.process", "com.wongel.MVPGenerator", "com.strixsoftware.intellij.format", "org.covscript", "org.ngs.adapter.plugin", "com.github.bassaer.kotlin_variable_completion_plugin", "vip.guco.stylusassist", "com.linsir", "Properties Compare", "suphuyquansu.plugin.translation.GoogleTranslation", "com.ss.jme.plugin", "cz.malevic.plugins.characterposition", "com.contrastsecurity.contrast-intellij-plugin", "com.linsage", "com.erstens.gaosi.unique.plugin.id", "tech.central.ai.awstail", "com.sonnk.iterm", "com.chainstaysoftware.assertions2assertj", "com.gmail.blueboxware.extsee", "com.icapps.niddler", "ua.in.dej.groupper", "com.huawei.ide", "redEyeGuy.rsunder10.plugin", "com.ng-zorro.live-templates.plugin", "org.sireum.intellij.injector", "com.lomoye.plugin.codeGenerator", "com.hpe.mc.androidhelper", "org.mule.tooling.intellij.dataweave.v2", "com.ibm.cxa.plugin", "com.github.fartherp.plugin", "com.sunsharing.idea.plugin.zeus", "org.mvnsearch.rest-editor-client-contrib", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ThinkTank", "zato", "com.lsjwzh.plugin.tinypng", "org.lr.tool", "com.wkp.android.mvp", "com.flageolett.nodeconfig", "com.gem.gdis.disl-run", "com.deflatedpickle.nim", "tv.twelvetone.intellij.plugins.intellitidy", "DSLTrans", "mobi.hsz.idea.nodesecurity", "org.pmesmeur.sketchit", "com.github.kassak.geo-support", "siosio.kodkod", "CyclomaticComplexity", "io.solo.squash.intellij", "com.frost_fox.goto", "brown.bat.redis<PERSON>li", "org.lr.helper", "com.haku.less.Babylon", "com.intellij.lang.graql", "com.aswitcher", "io.intheloup.reactored.generator", "com.squaretest.Squaretest", "io.scheinecker.intellij.coco", "com.taobao.idlefish.alleria", "ArchitectureReloaded", "pl.elka.spark", "org.ice1000.julia", "com.fitltd.lifefit.lifeedit", "cn.yan.android.devkit", "io.github.intellij.dub", "com.vivalab.create.module", "fr.aviscogl.header", "com.fabio", "il.co.galex.namethatcolor", "com.github.keraton.jumblej", "com.shenhua.idea.plugin.quoit", "com.zhishan.java.zhishanMvc", "qemu", "org.stellar.intellij.plugin", "cn.kevin.mybatis.hepler", "com.ctrip.zt.gaok.plugin.generateModelPlugin", "com.kengajs.winnie.actions", "com.axellience.vuegwt", "fr.poud<PERSON><PERSON><PERSON>poud<PERSON>aplugin", "com.alivanov.intellij.plugins.liquigen", "com.kevinlinxp.sublimeSnippetsSupport", "com.ripstech.intellij", "main.com.duxtinto.intellij.plugin.github.codereviews", "io.pivotal.jasmine", "com.gradoservice.plugin.forge", "com.codeGenerator.wangtj", "de.cusp.cu.curNotesMDs", "org.jetbrains.kotlin.native.clion", "com.jetbrains.plugin.idea.nonsource.comments", "intellij.prettierJS", "com.github.joaotfernandes", "com.github.mendess2526.javaclonegenerator", "ch.ricardo.plugins.intellij.mockery", "com.jetbrains.TabFormat", "com.baislsl.ideaplugin.encryptor", "io.github.qeesung.component.HighlightBracketPair", "com.gmail.robmadeyou.compost", "carbon-now-sh", "pl.kpiska.jsondiff", "me.kingtux.motivation", "com.mrz.showversion", "org.intellij.plugins.touchbar_support", "com.neo.test.plugin.Translate", "com.ctrip.zt.gaok.plugin.easyJavaBeanPlugins", "com.jetbrains.embeddedProjectJdk", "org.jetbrains.plugins.hocon", "darthorimar.cbt", "com.boredream.plugin.androidcodegenerator", "com.intellij.kubernetes", "com.bulletjet.StartupTimer", "bundled-appcode-help", "bundled-clion-help", "bundled-goland-help", "bundled-rubymine-help", "bundled-webstorm-help", "mw.unitv", "de.symcon.idea.plugin.library", "plugintest", "org.masamotod.idea.Drupal8NamespaceDetector", "com.tp.globalegrow", "io.github.nzlong.generator.generate-builder", "com.person.controlFlowGraph", "com.github.anonfunc.vcidea", "Pip requirements info bubble", "com.bettercloud.intellij.plugin.kafkatool", "com.ctfin.idea.mybatis-generator", "com.xzdz.codereview", "com.github.novotnyr.consul-intellij-plugin", "myFolders plugin for IntelliJ IDEA", "com.github.youwi.pasteimages", "Pojo Builder", "com.kondratevpavel.plugins.goland.gomethodgenerator", "com.savion.hello.plugin", "bundled-mps-help", "com.codota.intellij.assistant", "com.shen.plugin.element", "JBLJavaToWeb", "net.lagerwey.cucumber-kotlin", "color.scheme.Eclipse - Dark", "com.meicloud.plugin.setting", "com.vladsch.git-file-case-fixer", "WindowPlugin", "com.djordjem.idea.plugin.cirilizator", "com.madfish.ide.readhub", "com.twitter.scala-repl", "com.thinkwide.plugin.wizard", "mohammad.src", "com.sollyu.java.idea.touchbar.support", "ru.yandex.tracker", "com.euphoricity.gitignore", "us.egek.searchstackoverflow", "com.tukeping.dt.vars.plugin", "com.cinnober.paramMocker", "cn.levey.binding_layout", "com.github.zxj5470.javaxhint", "org.talend.sdk.component.intellij.plugin", "color.scheme.GapStyle", "org.ziglang", "com.twotalltotems.eru.intellij", "org.masamotod.idea.EscapeFromSearchEverywhere", "com.pablogsal.black", "com.zw.builder", "com.github.mi<PERSON><PERSON><PERSON><PERSON>", "com.guyazhou.tools.plugin.reviewboard", "ir.ali.plugin.comparisonChainGenerator", "org.muchu.mybatis-support", "com.github.sckm.constant-string-placeholder", "MisWangMvp.MvpCreateAction", "li.niwato.motivational.gif", "com.redefiningproductions.com.api-key-watcher", "org.ice1000.braceSucks", "ris58h.lacopom", "org.jetbrains.plugins.go-template", "com.github.brunobastosg.gerador-cpf-cnpj-intellij-idea", "com.cinnober.intellij.plugins.argument-generator", "pl.dmazuruk.reformat-plugin", "patrick.kelleter.angular-cli-quick-switch", "com.guiwang.plugin.AssemblyPlugin", "com.huawei.iot.code-analysis-idea-plugin", "color.scheme.Dark Blue Theme", "io.ghostbuster91.nspek-intellij-plugin", "com.legendmohe.plugin.pargmamark", "kz.kolesa", "com.apptorium.TeaCode-IJP-Helper", "me.codeboy.plugin.reverse-lines", "jenkis.build.with.retry", "com.amadeus.SearchBySegmentfault", "com.amadeus.SearchByBaidu", "com.intellij.powerstation", "com.nerdscorner.mvp", "com.lift.intellij-lift", "com.wlh.beangenerator", "dk.lost_world.intellij-touch", "org.gdlplugin", "net.ruixin.rx-plugin", "com.wbars.php.folding", "com.verilang", "dongfg.coolhub", "com.sqlandroid.airplane", "org.hapjs.tools.plugin.idea.hap-plugin", "org.jetbrains.kotlin.native.appcode", "org.p4.p4plugin", "Gitflow feature mame plugin", "project_manager", "gelonggld.com.db2bKG", "sk.stuba.fiit.mock_manager", "com.xiaopo.flying.LayoutMaster", "com.garage.works.xpathValidator", "de.hszemi.rmsplugin", "gerdes.mobtimer", "com.ebay.sd.jbehave", "hongSir.plugin", "com.blackbuild.intellij.maven-wrapper-support", "Quick Find Plugin", "org.dudariev.converter.generator", "org.amdatu.idea", "color.scheme.Tempo-theme", "com.elmottaki", "org.jetbrains.plugins.iasemenov.idea-git-extensions", "InternalWiki", "com.godwin.json.parser", "com.pingfangx.plugin.RenameFileX", "co.jeshan.code.typeformation", "com.itz.translation", "cc.duduhuo.git-conflict", "sk.zlatohlavek.iway.project_switcher", "br.com.diegomelodev", "com.burkett.builderberg", "io.cana.intellij.pojogenerator", "com.wuhao.code.check", "com.LineJumper", "ria-intellij-plugin", "com.jlupin.plugin.nextserver.intellij", "com.swift.lin.wings", "org.jetbrains.plugins.ruby.motion", "me.maiyuan.cleancode", "org.argus.intellij", "itca.mediakeys-plugin", "com.mibexx.mbx-mocoapp", "color.scheme.Defcula", "viz.idea.plugin.ii-plugin", "com.softwareco.intellij.plugin", "com.parser.cp.mind-sport", "com.dslfoundry.langvis", "com.palani.clion.cuda-run-patcher", "com.github.youwi.robotlauncher", "studio.edaphic.sv", "google-sceneform-tools", "net.itca.xkcdotd", "com.millennialmedia.intellibot@SeleniumLibrary Patched", "com.github.youwi.runrobot.plugin", "com.jetbrains.tlint-plugin", "com.huang.tra", "in.sdqali.intellij.freebuilder-plugin", "com.zhkeen.flyrise.fe.translate", "com.ifengxue.plugin.jpa-support", "com.apicloud.plugin", "DocSearch", "DeFacto.DeMark", "com.wookii.gomvp_plugin", "ignaciotcrespo.github.com.android-gradle-switches", "GsonFormat4DataBinding", "to.etc.domui.intellij.plugin", "rebase-guru", "com.ruin.lsp", "de.veihelmann.closureplugin", "tanvd.bayou.plugin", "com.mistamek.drawablepreview.drawable-preview", "com.wlh.builder.generator", "com.jcgroup.android.SourceCodeAttach", "com.liferay.ide.intellij.plugin", "YwqFastMVP.ID", "ignaciotcrespo.github.com.vector-drawable-thumbnails", "align-carets", "color.scheme.Cobalt 2", "CVS", "com.cosium.vet.vet-intellij-plugin", "ru.leoklo.stackuml", "io.loli.browserfx", "com.dhc.fmvp", "SmartModule.ID", "org.jetbrains.plugins.vgo-support", "com.zsc.mvpHelper", "com.github.CodeAssistPlugin", "hu.rb.cs.generator", "JVM Wrapper Support", "detekt", "com.puyixiaowo.cameltoggle", "com.bhc.plugin.mybatis", "com.godwin.db.debugger", "io.samspel.angular.schematic.generator", "com.wkl.idea.plugin.intdef.gen", "com.wustor.nulltodie", "io.ams340.idea-plugin-tddo", "org.intellij.plugin.jin5u.papagotranslator", "krasa.IdeaIconPack", "com.yhuang.code-smell-detector", "com.cn.oddcn.plugin.gobuildergenerator", "com.eastwood.tools.idea.micromodule", "com.opticdev.ideplugins.jetbrains", "dead-links", "com.godream.unique.plugin.id", "IntellijIRC", "com.dhc.plugin.component", "ca.rightsomegoodgames.maxpy", "com.jlh.enumgenerator", "GitCheckoutFilesFromOtherBranch", "info.woody.api.intellij.plugin.csct.CodeStyleChecker", "com.volitic.plugin.ngdartfolding", "com.github.mprops.idea", "io.acari.DDLCTheme", "com.imuxuan.core.builder", "com.pubudu.wizard", "com.nazir.plugin1", "com.codemr.analyzer", "net.nearwood.v1", "com.xiyuan.TypeScriptExecutor", "com.mario.WSAnything.plugin", "dev.mpp.android-data-tools", "com.fourtek", "me.mikheev.levsha-idea", "org.mule.tooling.intellij.muleruntime.v2", "io.ktor.intellij.plugin", "com.youzan.framework.easy-plugin.EnumGenerator", "au.com.reece.intellij.plugin.BambooMenu", "xyz.ihac.derrick", "com.github.ArtsiomCh.JDCleanRead", "XianYu Co,.Ltd", "slides-presenter", "com.karlpu.trackingcode", "QQ963577663", "ru.meanmail.plugin.requirements", "za.co.sfy.angular.imports-sorter", "com.vivo.internet_soft_1_2.plugin_base", "io.plugin.tsnode", "ru.spb.se.contexthelper", "org.gw.swagger", "com.nemesis.platform.nemesis-idea-plugin", "Up2Dep", "com.mahesh.flashwizard", "org.igu.plugins.bettercomments", "com.databricks", "cn.wizzer.code", "com.Ajie.IntelJ.HelloAction", "design.patterns.plugin", "launch-url-from-string", "jp.roundrop.sqlformatter", "com.syl.zombie.enumgenerator.v666", "fr.dco.kotlin.vcs-kotlin-converter", "com.jetbrains.colorful.toolwindow.icons", "cn.deemons.tinypng.plugin", "plugin.achdjian.it.ESP2866", "my.example.my-plugin", "ru.scarlett", "cn.yiiguxing.plugin.md.palette", "com.williamlee.plugin.swagger.generate", "com.github.awesomelemon.deep-api-plugin", "me.artspb.hackathon.git.bisect.run", "com.shanbay.plugins.image.slimming", "com.eastwood.tools.idea.repo", "com.github.kassak.ij-advertiser", "com.github.kassak.dg-exposer", "testing.tddplugin", "com.ibm.appscan.intellij.plugin", "org.jetbrains.cbs.idea-plugin", "com.zcx.mvp.unique.plugin.id", "com.fox..plugin.codedesigner", "kotless", "com.jetbrains.intellij.file-manager", "testdoxon.TestDoxon", "com.jetbrains.tuna-plugin", "com.gizmo", "org.deniskusakin.aem.aem-groovyconsole-plugin", "org.kdb.studio", "com.albert-patterson.notes-plugin", "biz.elfuego.idea.issues.gitea", "EditorGroups", "com.josedacruz.apps.intelliHashs", "com.google.developers.certification.android", "com.zxy.idea.plugin.external-plugin-support", "com.intellij.plugin.copy.options", "BrewJava", "https://github.com/yclnycl", "com.sidneibjunior.react", "com.longforus.mvpautocodeplus", "liu-platform-code", "cn.sourcespro", "com.funkygeneral.customviewmaker", "com.dwilden.crondescriptor", "com.linmilin.plugin.PreviewImage", "org.spekframework", "com.github.alexmojaki.birdseye.pycharm", "Tolltech.Tollrech", "com.github.mustfun.plugin.mybatis", "com.pytest_support", "TaskManagerPlugin.TaskManagerAction", "com.chriscarini.jetbrains.intellij-sample-notification", "com.intellij.database", "idea.k3", "org.jspresso.plugin.i18n", "me.majiajie.ideaplugin.findviewforandroid", "com.ken.savebackup", "com.kcng.test", "com.sjhy.plugin", "me.xdrop.night-owl", "zhengkuan.yzk.auto.transform.encoding", "log-improvement", "com.jlupin.plugin.platform.intellij", "MatlabSupport", "com.suusan2go.kotlin-fill-class", "com.evansalter.extract-json-tags", "com.ajayadkar.sqlite.androidstudio", "com.bruce.intellijplugin.dubboPlugin", "com.evolitist.ev3c", "jms-messenger", "atg-toolkit", "hu.hawser.co-author-plugin", "JOL", "com.sjhy.plugin.easycode", "com.blankj.AndroidStudioPluginTemplate", "com.cedricziel.idea.fluid", "me.chaerim.yapf", "com.kedacom.legoarch.plugin", "com.damoguyansi.all-format", "com.petyonwu.base64.id", "com.kefan.generate-protobuf", "mm.netmock-tools", "com.markotanic.deleteVCSMessage", "com.unique.plugin.CamelCasePascalCaseConvert", "cappuccino.intellij.plugin", "com.yinhai.ta3.plugin.id", "io.snyk.snyk-intellij-plugin", "io.binx.cfnlint.plugin", "IntelliGuard 2", "com.github.essquilo.eventsounds", "live.codeshark.plugin.codesharkjetbrains", "com.smzdm.my-plugin", "io.rang<PERSON><PERSON>i", "cn.deemons.plugin.Globalization", "com.devnagri.androidstudio", "edu.cmu.cs.alabaster", "me.laria.code.idea_caseconv", "org.igu.plugins.consolelog", "com.fusion", "color.scheme.Bigface", "com.ysnows.wxapp", "cargowatch", "me.vukas.remote-debug", "io.paleocrafter.chronomorph", "com.chriscarini.jetbrains.environment-variable-settings-summary", "com.anecdote.WebSequenceDiagrams", "com.plugin101.demo.vipergenerator", "com.hand.hec.aurora.ide.plugin.idea", "ICU", "kemoke.net.cssmodule", "com.jucham", "machine.specifications.runner.rider", "com.markbolo.stepbuilder", "n1.ru.env.integration", "sc.plugin.cjkconv", "com.hdyg.lidroid.litchi.plugin.LitchiPlugin", "org.muzi.open.helper", "be.jeroendruwe.plugins.translator", "search-constant-strings", "me.baluwii.easy.snippet", "com.gitlab.lae.intellij.actions", "cn.kt.better.mybatis.generator.id", "com.stfalcon.ktmvvmgenerator", "com.alvincezy.TinyPic2", "color.scheme.Pretty Dark", "org.jetbrains.settingsRepositoryPlus", "com.zero9178", "com.devlomi.sharedprefsmanager", "org.objectweb.asm.idea", "com.staszkox.test.navigator", "com.symbol.emdk.wizardex", "no.tillerapps.monosplitter", "ch.raffael.idea.plugins.runpopup", "com.famiover", "com.sskorupski.intellij.plugin.builder.mock.fluent", "com.alfredxl.templatefile", "me.lvxuan.SpaceToUnderlinePlugin.StunAction", "com.noober.plugin.tiny", "home.shz.lombok.help", "color.scheme.Eclipse Dark Theme", "NaoUnit", "lermitage.intellij.extra.icons", "pv.kwick.actions", "TextGenGen", "com.compilerexplorer.compilerexplorer", "com.intellij.plugin.nebularsnippets", "com.actigence.p2y", "net.sjrx.intellij.plugins.systemdunitfiles", "de.fhluebeck.opemjmlplugin", "com.qmui.Svg2Vector", "ca.ubc.ece.cpenn221.plugin", "com.gitlab.lae.intellij.actions.tree", "com.aweme.as.plugin", "com.qccr.mvp.id", "br.com.fabioluis.usegists", "Inspection.Compare.Plugin", "org.yyx.plugin.start_time", "com.leinardi.pycharm.pylint", "wiki.crowd.cloudutils", "com.leinardi.pycharm.mypy", "com.pinkfloyded.idea-file-path-autocomplete.idea-file-path-autocomplete", "com.xinv.release.json2go", "com.zhishui.skydragon.snippet", "me.xiba.plugin.SesamePlugin", "com.jet.my-plugin", "com.devendortech.intellij.plugin.decompile", "com.centurylink.mdw.studio", "com.mallowigi.imageicon", "com.szzc.rcar", "SGsonFormat", "russellcloud.cli", "com.winter.plugin", "darthorimar.scalaToKotlin", "com.ivanovych666.intellij.plugin.jsonsorter", "fuchsia.developer.plugin.fidl", "ch.namlin.plugin.bundlesort", "net.denysloshkarev.plugins.jetbrains-breadcrumb-plugin", "com.jared.plugin.id", "pers.fw.tplugin", "com.intellij.testGuiFramework", "org.zalando.intellij.swagger.examples.extensions.zalando", "me.brokenearthdev.has<PERSON><PERSON>ner", "com.github.blaugold.intellij.datanucleus", "com.mxloft.magicTime", "tk.miemie.underlinecameltogglecase", "vzt.kapien.js-i18n", "org.maxal.dialogResizer", "net.showmeaway.testplug", "tezos", "com.blueline.idea.plugin.packagejar", "TabSwitch2", "ChinaUnicom.unique.plugin.id", "com.khande.ideaPlugin.CopyAndroidReference", "com.leesz.fast-db", "com.qbutton.pinetreeoptimizer", "com.natpryce.pottery.pottery-idea-plugin", "com.buyaoshushu.intellMPD", "com.jetbrains.php.dql", "com.zplugins.plugins.mayihelpu", "com.manzo.android.sluggard.plugin", "plugin.achdjian.it.ESP8266", "org.pmesmeur.vdesk", "color.scheme.Evangelion", "com.rickykurt.plugin.vant", "com.github.novotnyr.gitlab-quickmr-idea-plugin", "me.shiki.DartJsonFormat", "de.sgalinski.fluid.plugin.id", "com.jetbrains.gitalso", "com.alipay.antcloud.ide", "cz.ackee.localizer", "php.plugin.pretty", "org.arend.lang", "com.longforus.kotlincodesorter", "de.cawolf.quick-mock", "be.ugent.piedcler.dodona", "man-page-viewer", "com.intellij.jira", "com.github.syuchan1005.ImageComment", "com.github.zxj5470.clpstfx", "net.afpro.idea.aophelper", "com.github.zxj5470.exchange-intellij", "com.github.airsaid.androidlocalize", "com.brain.intelligent.SerializationCoder", "color.scheme.Wombat", "BorgCSbxml", "pl.koziolekweb.enumerathor", "com.xovis", "com.brobert.step-builder-plugin", "ru.tinkoff.jira-backlog", "akunevich.pullrequest", "com.wrd.plugin.b64-to-pdf", "com.sunq.action.WebStormPX2REMTools", "com.chriscarini.jetbrains.logshipper", "com.andreaepifani.toggle-case", "com.javampire.idea-openscad", "wiki.crowdwiki.wiki", "com.star.intellij.console.link", "com.github.kassak.multi-bnf", "com.zxy.idea.plugin.findViewById-support", "com.github.rmatafonov.cucumber-scenarios-indexer", "lt.martynassateika.idea.codeigniter", "io.github.takc923.isearch", "com.netcetera.trema.trema-intellij", "labs.vex.intellij.plugin.phantom", "com.josebigio", "lt.auciunas.tadas.testCaseCreator", "git-extended-update-index", "codereview.idevcod.com", "com.intellij.resharper.azure", "com.nicklyu.comments-translator", "xyz.paphonb.plugin.clion.singlefileexecutionplugin", "org.bigtows.PinNote", "cn.mycommons.serializednamegen", "adinar.FstringMigrator", "com.talkingdata.my.favorite.code", "com.jsdelivr.pluginintellij.JsDelivrPackageSearch", "com.pinger.translate", "net.kenro.ji.jin.intellij.crystal-2", "ros-integrate", "org.jvmlet.intellij.reveal-dependency-plugin", "com.zitiger.plugin.xitk", "com.yuricfurusho", "com.intellij.patch", "com.zjuqsc.impler-go", "de.sgalinski.typoscript.enterprise.plugin.id", "color.scheme.EvangelionU02", "com.clazz.sonar", "com.notime.intellijPlugin.backgroundImagePlus", "com.itkacher.okhttpprofiler", "xyz.morecraft.dev.jetbrains.intellij.plugin.lang.yscript", "com.madrapps.handlebars-support", "intellij-clock", "com.imuxuan.core.flag", "pw.unbear.ijplug", "name.tachenov.intellij.maven-dependency-updater", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "io.xnc.plugins.androidActLauncher", "Demofinal1.Demofinal1", "me.destro.intellij.plugins.maven-macros-toolbox", "cn.joylau.code.who-did-it", "SQLFormat", "org.tonyyan.plugin.ddlcreator", "com.funivan.idea.phpClean", "com.sairic.intellij.prependstorynumber", "org.intellij.griffon", "io.github.mthli.nongfu", "com.sqlandroid", "esp32.embedded.clion.armsupport.prj", "org.jraf.intellijplugin.nyantray", "wiki.crowd.doc.python", "com.sannsyn.opennlpmodelstrainer", "com.guowenlong.dimens", "ExportPlugin", "org.xiaoweige.intellij.plugins", "le.cloud.rock", "io.terminus.common.p-doc", "com.objectriver.cloudcompiler.plugin.idea", "org.frawa.elmtest", "com.huawei.ide.oversea", "com.skritskiy.api.proposals", "com.guidewire.encoder", "com.vladsch.clionarduinoplugin", "synergix.plugin.intellj", "org.jspare.tools.avro4idea", "com.interfaced.cutejs.plugin.support", "fr.alban.petiot", "com.bianlifeng.wormpex.plugin.coder", "com.github.sbouclier.intellij.plugin.designpatterns", "club.datamigration.console-fold", "org.yalang.plugin", "com.jeno.bracket-selection", "com.AvansTI.Boebot", "com.tencent.cloud.TCAPM", "org.campagnelab.Util", "com.tencent.warningwang", "br.com.viasoft", "com.imuxuan.core.search", "cn.mofada.axml.plugin", "com.company.dontcare.ansible.vault.crypter", "com.ecarx.plugin.findview", "com.perl5.lang.tt2", "com.perl5.lang.mojo", "com.perl5.lang.mason.framework", "com.perl5.lang.mason.mason2", "com.perl5.lang.mason.htmlmason", "com.perl5.lang.embedded", "de.manuelhuber.quick-merge", "com.ahope.appshields", "org.jf.fusionIdea", "org.jfrog", "com.github.amibiz.ergokeys", "aws.toolkit", "org.liujing.generator", "com.imuxuan.core.toggle", "org.openntf.xsp.xpagessdk-intellij", "com.zmei-framework.generator.plugin", "AwesomeKotlin", "man<PERSON>.graph<PERSON>al", "fr.mary.olivier.aw-watcher", "com.intellij.largeFilesEditor", "ProtostuffAnnotator", "com.weathfold.SideOnlyChecker", "com.davidprihoda.dvc", "org.javapro.idea", "kendal.plugin", "io.github.takc923.better-sticky-selection", "com.alged.bfe", "com.qbb6.RmUnusedMethod", "CodeNarcPlugin.V1", "com.williamwen", "com.deploytools.plugin", "com.rspn", "com.tp.xml", "com.alibabacloud.intellij.toolkit-intellij", "com.github.me10zyl.enhancedfilesearch", "com.zool.plugins.translate", "sonnenhut.liveedit.patch", "org.kubicz.mavenexecutor.plugin.id", "com.muhammed.thasneem.moreinfo.plugin.android", "com.arangodb.intellij.aql", "cn.lid.mybatis.generator", "com.violinday", "com.tt.str.clean.StrResClean", "io.nichijou.notranslation", "live.angular.switch.extension", "com.xrj.plugin", "io.github.takc923.recenter-top-bottom", "com.dherre3.wasm-text-parser", "com.cuongloveit.androidsorter", "com.github.alexandrelombard.org.eclipse.xtend.idea", "com.github.alexandrelombard.org.eclipse.xtext.idea", "com.ruiyu.ruiyu", "io.github.helloworlde.soar", "de.kostue.svn-helper", "com.jay.plugin.safesoft", "achdjian.plugin.ros", "intellij.plugin.ping", "com.protobuf.free.gen.plugin.GenProtobuf", "sk.infinit.testmon", "org.kai.protogen", "com.kenshoo.logzio-intellij-plugin", "arouter-roadsign", "com.joker.intellij.plugin", "TraceMetrics", "com.github.oowekyala.javacc", "com.mhdthasneem.apk.mover", "com.ifillbrito.immutable", "ru.andrey.search-plugin", "org.yanhuang.plugins.intellij.exportjar", "com.github.recognized.screencast.recorder", "com.newtoncodes.spellchecker", "com.github.recognized.screencast.editor", "com.faebeee.vuecomponentcreator", "idea.goyacc", "com.datarankings.plugins.PowerRefactorPlugin", "com.iambedant.commentTranslator", "com.idleFish.flutter.fishSerializable", "org.jetbrains.plugins.sass", "info.kwarc.mmt.intellij.MMTPlugin", "org.jetbrains.plugins.less", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "org.opencypher.graphddl.intellij.plugin", "com.zoutairan.OutputJumper", "org.dimdev.knit", "com.tencent.falco.falco-sdk-plugin", "com.azoft.json2dart", "com.hbt.tabs.placement", "com.mhdthasneemp.shahasher.plugin", "com.sevendark.ai.plugin.AiCoder", "com.hand.ide.plugin.aurora-support", "com.ytw88.weappsupport", "com.bytedance.keypton", "com.xgimi.commit.template", "de.fsyo.uremn.transfer-plugin", "com.erstens.plugins.scaffold", "com.magicbytes.flares", "lv.midiana.misc.idea-plugins.deep-js-completion", "org.qupla.language", "liwey.Json2PojoWithLombok", "linlan.tech.copy-to-oss", "com.ddlab.tornado.gitpusher.id", "com.credowolf.DepE", "kg.akoikelov.intellij.rms", "org.intellij.gitee", "GapsHelper", "com.pglessframework.idea.plugins", "me.texy.databindinghunter", "org.jetbrains.execsound", "com.tencent.jceplugin", "codeAppend", "cn.manchesterlee.plugin.translator", "cs.uchicago.hyperloop.panorama-view", "iytplugin", "com.github.smaugfm.emptyconfiguration.EmptyConfiguration", "com.illuzor.afo", "codes.geekhub", "woaini.liquibase.plugin", "com.dmitryzhelnin.intellij.plugin.gitextensions", "com.aegamesi.java_visualizer.plugin", "com.nguyencse.mvpcreator", "com.synopsys.sig.desktop.idea", "com.puke.timer.TriggerAction", "com.laisontech.unique.plugin.id", "com.github.scrat98.jwt.debugger.intellij.plugin", "org.summer.export2Local", "com.zxy.idea.plugin.gradle-plugin-support", "IdeaLogViewer", "com.intellij.resharper.machine.specifications", "com.luojilab.dedao.DDProjectWatcher", "com.alipay.idea.plugin.UpdateDemoId", "arrow-kt.io.keep87", "W3Validators", "hdzi.idea-editstarters-plugin", "com.intellij.tasks", "com.intellij.tasks.timeTracking", "net.tlatlik.intellij.ihdl", "com.zll.format", "Subversion", "com.planetexpress.go_struct_tag_generator", "com.github.leecho.idea.plugin.mybatis.generator", "com.bisguard.java.antidecompiler.idea", "com.malukenho.saleforceid", "python-flake8", "com.danizz.TranslatorPlugin", "com.magicbytes.blaze", "23.by<PERSON>ode-disassembler", "com.i18ns.intellij.robot", "tk.birneee.jbehave-table-formatter-intellij-plugin", "com.nvlad.tinypng-optimizer", "cn.kqg.kysh_quick_generation", "com.sgaop.codegenerat.nutzfw", "org.linkki-framework.tooling.linkkit-intellij", "myBitbucket", "it.karger.keymap-switch", "net.supachat.xanathar", "com.suiyiwen.plugin.idea.apidoc", "com.idlesign.qrcoder", "kotlintest-plugin-intellij", "com.juanlondono.androidXMLExport", "com.cttrip.viewgenerate", "com.rilixtech.android.butterknifegenerator", "com.binarysushi.sfcc-studio", "com.wahdan.com.wahdan.spockAdb", "com.wkl.idea.plugin.json2entity", "com.intellij.properties", "net.berla.intellij.bookmarkactions", "Ttr", "com.jack.plugin.autojson", "com.github.rougsig.duck-progress-bar", "com.newsenselab.todogfilter", "com.jincnashen.sk.plugin.id", "com.bloidonia.intellij-rest-linkomatic", "cn.mycommons.auto", "com.supermuscleman.record2gstter", "cn.proem.framework", "com.shanbay.plugins.dsl.listener.builder", "com.kevinshu.retrofit2.plugins", "CyIdeaTools", "com.lyle.plugin.flutter", "com.interfaced.brs-plugin", "cn.renyuzhuo.plugin.easygradle", "com.cody.banks.sort-by-editor-section", "com.kiexujiaqi.func-comment", "itbuddy.Annotator.SBC", "com.adacore.Ada-IntelliJ", "me.apon.swdp", "<PERSON><PERSON><PERSON><PERSON>", "com.fernando88to.superconsolelog.SuperConsoleLog", "com.helloyuyu.plugin.arouternavigatefunctiongenerator", "com.markskelton.one-dark-theme", "org.zoolytic", "com.github.camork.fileExpander", "com.chriscarini.jetbrains.jetbrains-auto-power-saver", "pl.szymonprz.idea-cheatsh-plugin", "python-smart-execute", "org.ermadan.kafkalytic", "SnakeCharm", "com.d0tplist.swingplayground", "cretlabs.room.builder", "com.your.company.unique.plugin.translateId", "com.andreycizov.partialnav", "com.dell.easydebug", "io.stanwood.android", "app.teamhub", "com.jfrog.conan.clion", "de.thomasrosenau.diffplugin", "com.petre-s.mock-gen-intellij", "HOCON Converter", "me.wbean.plugin.bean.converter", "fistache.framework.id", "com.netease.p3c.smartfox", "cc.ssnoodles.db2j-plugin", "org.ahren.android.native.debug", "com.wangbailin.plugin.underscore_case_toggle_to_camel_case", "com.xuan.plugin.iconfontpreview", "hu.meruem.meruem-generator-intellij", "com.chimerapps.proguard-retrace", "branchmanager", "com.xh.p3c.codeStylePreview", "com.asing1elife.reformat.plus", "com.kplugin.codegen", "com.zty.plugin", "me.wbean.plugin.dubbo.invoker", "de.cvguy.kotlin.koreander.intellij", "io.github.api.swapi", "com.github.antipovandrey.generator", "de.achimonline.kickassembler-acbg", "com.haskkor.webstorm.plugin", "com.github.ArtsiomCh.KDocCleanRead", "com.guazi.android.chespand.plugin.switch", "com.fkw.plugin.jnetkit.rpc-jumpto", "com.flyhand.ideaplugin.gittool.branchcompare", "io.owen.plugin.easycomment", "com.lhxia.kotmvp.generator", "BER-TLV support extension", "org.jetbrains.navigation", "com.hzm.act", "com.uso.plugin.pom", "com.baseten.pl.baseTen", "cn.yiiguxing.plugin.figlet", "com.daynight.plugin", "com.github.ArtsiomCh.CommentFolder", "com.github.rougsig.file-template-loader", "com.xp.bjsdm.link", "com.github.parfoismeng.plugin.butterknife", "io.nots.intellij", "tv.twelvetone.intellij.plugins.intellivue", "color.scheme.Github farshid Light v2 (rainglow)", "org.liamjwang.yamlconfig-idea.yamlconfig-idea", "ru.meanmail.plugins.prettify-python", "com.hardy.JsonTool", "pl.gmat.screengenerator", "cognitivecomplexity-rider", "io.acari.normandy.progress.bar", "chieftain.mybatis.mybatis-gen-menu", "me.gaigeshen.idea.ecmybatis", "com.atlassian.idea.bob", "com.sourceplusplus.plugin.intellij", "com.sofy.ai.ideaplugin", "ru.meanmail.plugin.pyannotations", "com.abeade.plugin.figma.import", "com.yiycf.plugins.mavenDependencyHelper", "com.github.novotnyr.idea.git.quickbranches", "com.vladsch.plugins.touchTypistsCompletionCaddy", "ht", "itbuddy.ta", "rs.pest", "org.aion4j.avm", "com.github.lzy.lets-hotfix-idea-plugin", "com.footprint.footprint-plugin", "cn.codetector.lc3.lc3intellij", "com.jetbrains.rider.android", "as.alittleide.com", "com.jiangyy.auto-gradle", "com.intellij.plugins.vscodekeymap", "cwi.nl.devUDF", "io.microconfig.idea-plugin", "CheckStyle-IDEA-FLINT", "ink.organics.pojo2json", "com.cmcm.hanwen.AutoGenerateInfocAction", "com.area-b.scal<PERSON><PERSON>-template-plugin", "com.jetbrains.kaggle", "com.pv.kwick.activity", "de.longnguyen.plugin.clion.new.entry.point.plugin", "com.schuetz", "com.wacc_04.waccPlugin", "com.badoo.ribs.intellij-plugin", "com.android.wills.wills", "dk.lost_world.Dictionary", "cn.lvji", "com.demisto.demisto-plugin", "www.fclassroom.com.ifelse", "org.hyhe.string_creator", "org.twinnation.intellij-fluent-setter-generator", "color.scheme.GeekNight", "com.jetbrains.darkPurpleTheme", "com.jetbrains.CyanTheme", "com.jetbrains.grayTheme", "com.abeade.plugin.fcm.push", "com.github.kropp.intellij.theme.ubuntu", "dk.demus.idea.CodeNarc", "com.ilscipio.scipio.ce.scipio-editor", "stlint", "io.github.takc923.universal-argument", "com.tylerthrailkill.intellij.solarized", "ion", "cn.hikyson.godeye.ideaplugin", "io.solidloop.jetbrains.ide.serverlessframeworkgui", "jp.storm.rulerz.rulerz", "color.scheme.Smooth_Minimal", "com.hiberbee.intellij.hiberbee-theme", "me.catcoder.themeswitcher", "com.khahn.angular-extract-component", "com.pqixing.modularization.idea", "com.github.mturlo.intellij.theme.spacegray", "com.mallowigi.idea", "com.knowledge.mnlin.KViewBind", "org.bdshadow.json.serialization.generator", "com.adrian.dima.theme.monkai.pro", "com.bloc.intellij_generator_plugin", "mhashim6.idea.plugin.drumroll", "hu.aradipatrik.onedarknative", "leetcode-editor", "com.ld.fanyi", "com.xinkun.theme.md", "zielu.cleansheet", "com.bakerj.flutter.fishreduxtemplate.id", "co.simpleq.qlaunch", "and<PERSON><PERSON>s", "com.alipay.idea.plugin.idea-csv-editor", "com.github.syuchan1005.revealinprojectview", "DirectoryMarker", "com.jooas.themes", "beeender.ComradeNeovim", "com.voestalpine.FxmlSync", "com.github.kisstkondoros.codemetrics", "com.berzanmikaili.intellij.monokai_pro", "Translation Yan<PERSON><PERSON>", "dark.flat.theme", "cn.mafengwo.hubo", "light.flat.theme", "edu.utsa.cs.sefm.privacypolicyplugin", "illuin.bot-conversation-preview-plugin", "ClearCachePlugin", "com.jetbrains.intellij.datalore", "tanvd.grazi", "org.ice1000.tt", "github.cweijan.test-theme", "com.hhh.plugin.auto.tinypic", "ua.com.arturmamedov.MAGist", "ru.softmg.workers", "com.jakecoffman.theme", "com.pandora.plugins.kotlin_converter", "ru.vsu.apakhomov.experimental.plugin.Experimental", "com.zitiger.plugin.converter", "ru.serafimarts.roboticket", "info.dong4j.idea.plugin.markdown.image.kit", "dev.v<PERSON><PERSON><PERSON><PERSON>.stateart", "com.wingcheongyip.angular-console-plugin", "de.platon42.cajon", "com.jglo-team.JGlo", "org.limechain", "com.jfboily.darkjeff", "com.zw.beanConverter", "com.jgentil.theme.darcula_blacker", "com.danieliwaniec.php.return_types", "cn.fantasticmao.ycy.intellij.plugin", "io.data2viz.kotlinx.htmlplugin", "com.codestream.jetbrains-codestream", "com.trade.plugin.id", "com.github.today-.theme.spaceday", "pl.tndsoft.constructorplugin", "com.itangcent.idea.plugin.easy-api", "vosk.ruta.ruta-idea-plugin", "com.him.core.generator", "OksiBlackCS", "DeepBugsJavaScript", "com.dy.plugin.uploadmv", "com.herokuapp.vuesion", "com.godwin.android_debugger", "au.com.clearboxsystems.plugin.jetbrains.darkerla", "com.sinitek.sirm.plugin.sirmcode", "com.mqx.codeReview", "com.geoffgranum.plugin.BuilderGen", "m2mobi.m2plugin", "com.ns.ide.Builder", "spring.web.initializr.plugin", "com.cybrosis.catdea", "Mongo 2.0", "hebehan.easychangefontsize", "com.chenenyu.intellij.widgetgenerator", "bobril-runner", "color.scheme.Pandrkula", "com.github.phntom.anton", "color.scheme.Liborized", "com.hand.ide.plugin.hap-support", "com.samdark.intellij-visual-studio-code-dark-plus", "com.rmondjone.commit_plugin", "com.tylerthrailkill.intellij.pastellicious", "color.scheme.Colorful - By Hulk", "com.adrian.dima.theme.dark.orange", "FastDebug", "com.jensteichert.themes.nightowl", "com.pvsstudio", "lermitage.jetbrains.darcula.sombre", "com.ljb.mvp.plugin", "com.fynn.intellij.plugin.localPropertyTools", "org.idea.SimpleCodeReview", "com.ss.android.ugc.conan.trace", "color.scheme.GitHub 3", "com.dtstack.idea.translator", "net.nazarsit", "com.vermouthx.idea", "YapiUpload", "com.ezio.gen.turbo", "com.bearcast", "com.fernandojimenez.mattecarbon.theme", "uk.co.hadoopathome.intellij.avro.intellijavroviewer", "com.starxg.browserfx", "jp.i<PERSON><PERSON><PERSON><PERSON>", "de.timon.schelling.interesting.theme", "color.scheme.litydity", "immortal.me.dart_json_convert", "com.platform.gen", "com.netease.cc.plugin.NECCPlugin", "fr.jukien.intellij.plugins.pojo-generator", "com.wan", "cn.com.autohome.tuan.plugins", "io.github.leeeory.theme.plaid", "com.marry.intelli.plugin.namespace.sqlId", "call-graph", "com.wzc.sw.plugin", "com.funk.sage.theme", "com.vincentp.gruvbox-theme", "color.scheme.Atom One Dark", "com.github.hayoi.haystack", "com.suiyiwen.plugin.idea.servicedoc", "com.area-b.schema2json-intelliJ-plugin", "com.martinagabrielli.themes", "rasarts.espresso.light.theme", "J2ME", "CodeReviews", "com.github.davidjgonzalez.intellij.aemscriptconsole", "lermitage.intellij.battery.status", "com.android.aas", "color.scheme.Tibau2", "jetbrains.mps.LangDoc", "io.eightpigs.m2m", "dark_candy", "silkworm", "Reykjavik", "com.thvardhan.gradianto", "com.github.masooh.intellij.plugin.groovyfier", "foggy-night", "com.joseph.stackexchange", "soft-charcoal", "white-sand-ui", "madrid", "MetalHeart", "warm-night", "Thursday", "com.ronformat", "com.herbert.george.flutter-snippets", "com.martinagabrielli.themes.machupicchu", "com.martinagabrielli.themes.kyoto", "com.capsulode.intellij.plugin.javadoc.ExposeJavadoc", "color.scheme.farshid Light v2.6 (rainglow)", "com.ellersoft.jetbrains.git-user", "com.github.inikolaev.intellij.maven", "color.scheme.Sunset Vibes Theme", "org.py.translate.translate", "com.hqjl", "net.p35.common-enum-values", "com.coderpillr.folding", "com.zeke.wong.neck-protect", "com.raindrop", "com.github.redfoos.logstash-intellij-plugin", "com.nerdscorner.dimension.tools", "dev.blachut.svelte.lang", "com.jseppa.intellij.amibroker", "com.github.patou.gitmoji", "com.liuzhihang.toolkit.toolkit", "ir.intellij.idea.plugin", "kimzo.BacklogPlugin", "com.plungermen.plugin.encourage", "com.jetbrains.fast.mouse.scroll", "oodmi.log-idea-plugin", "com.egoshard.intellij.k8s-runtime-config-plugin", "com.storedobject.idea.SOIdea", "com.xpfirst.plugin.javacoderules", "com.github.novotnyr.maven-version-idea-plugin", "pl.pszklarska.pubversionchecker", "color.scheme.Ember", "com.yuanshang.PhpStorm", "org.jetbrains.snippme-plugin", "com.monocode.angularjstoolbox", "rsdeditor", "com.justint.usd-idea", "com.bisguard.android.antidecompiler.idea", "com.apkfuns.swan.plugin", "com.bluewaitor.tsReact", "io.intheloup.flutter-bloc-generator", "com.yck.devlopment.plugin", "com.github.zouyq.cmdTools", "com.bytedance.jedi.templatemanager", "com.zxwl.statistics", "com.coderpillr", "color.scheme.Gruvbox", "net.objecthunter.idea.BuilderGenerator", "com.bytedance.android.tool.console", "io.eightpigs.intellij.darktheme", "color.scheme.Molokai", "saros", "andrasferenczi.dart-data-plugin", "com.jingtuo.plugin.jetbrains.flutter.yaml", "com.fernandojimenez.obsidian.theme", "com.zenuml.jetbrains", "spek-test-template-plugin", "com.github.mars05.crud-plugin", "com.murong.plugin.id", "xbee-micropython", "cn.sugarapp.plugins.yaml2props", "com.chuntung.plugin.mybatisbuilder", "io.gitlab.zlamalp.arc-theme-idea", "li.joker.tools.odli", "hadouken.progress.bar", "com.wingman.mvi-plugin", "net.wukl.ruleoffour", "com.itangcent.idea.plugin.easy-yapi", "wuhulala-typora", "com.github.rougsig.actionsdispatcher.ideaplugin", "SplitHistory", "com.wildma.plugin.showdialog", "com.dgtis.code.gen.plugin.code-gen-plugin", "me.gaigeshen.mybatis.helper", "code-smell-detector", "org.jetbrains.intellij.scripting-clojure", "it.achdjian.plugin.ESP322", "org.jetbrains.intellij.scripting-python", "tools.jla.overwatch-workshop-tools", "com.mmyh.plugin.easyapp", "XPathView", "me.hiten.completion", "com.riven.unique.plugin.id", "com.cotify.plugins.cotify-jetbrains", "com.knowledge.mnlin.JsonToDartBean", "color.scheme.Monokai Pro Spectrum", "com.xiaoju.automarket.mtn.test.mtn-test-jetbrain-plugin", "io.github.QCute.CodeCase", "com.zhang.ideaOpener", "com.intellij.bigdatatools", "eu.welovegaming.theme", "com.ypwang.plugin.go-linter", "com.coxier.TinyPNG", "cn.alanhe.plugin.bookmarkx4idea", "jebe.liao.JBLSpringBootApp", "com.feiduan.poetry", "com.hdivsecurity.hdiv-intellij-plugin", "com.dgtis.doclever.plugin.doclever-plugin", "com.jetbrains.packagesearch.intellij-plugin", "sncf.oui.scriptshortcut", "com.lauvinson.open.assistant", "com.dataiku.dss.intellij", "com.github.shiraji.yaemoji", "allure-ee-idea", "de.welcz.datev.scc.ui.theme", "com.github.gilday.darkmode", "com.github.jokerddj.dart_json_serialize", "com.xiaojinzi.routergo", "com.bugparty.dartjson", "org.py.theme.cutetheme", "org.py.plugin.tolower", "com.android.tools.apk", "com.google.appindexing", "color.scheme.Muse Light", "com.forrily.en2zh.plugin.id", "hotreloadplugin-rider", "com.github.humazed.DartAutoSerialization", "com.github.zxj5470.wxapp", "wenhe.online.ddd.dmvp", "org.jboss.tools.intellij.analytics", "color.scheme.Muse Dark", "com.noskcaj19.robotpy-pycharm", "com.lol1pop.intellij-visual-studio-code-dark-plus", "com.wibowo.intellij.mdd", "org.jetbrains.intellij.scripting-javascript", "org.jetbrains.intellij.scripting-ruby", "com.intellij.plugins.sublimetextkeymap", "com.carbonblack.intellij.rpmspec", "com.github.igrmk", "com.pwc.accelerator.market", "com.intellij.plugins.netbeanskeymap", "com.intellij.plugins.eclipsekeymap", "com.troyanovsky.idea.CodeHelper", "com.ankit.mahadik.json.dart.class", "com.android.tool.sizereduction.plugin", "io.github.likeanowl.topias", "com.envy.plugin", "com.jumpnotzerosoftware.protohighlight-jetbrains-plugin", "color.scheme.GoSense", "color.scheme.Monokai Materialized", "com.github.novotnyr.idea.rabbitmq", "com.hql.quick.import.id", "com.plugin.maven-dependencies-searcher", "color.scheme.Monokai", "shay.daniel.plugin.pysystemrunner", "io.heidou.codesearch", "javabean.jtv.com.cn", "vip.okfood.idea.plugin.FlutterImgSync", "gen_template", "scorpion.progress.bar", "PHPCodeGenerator+", "idea-yapi", "GitCommitHelper", "com.wzportal.idea.tool", "com.ldx.PatcherDialog", "github.zgqq.intellij-enhance", "com.dongqing.plugin.findviews", "signal9.icon-java-plugin", "org.jetbrains.bunch.tool.idea.plugin", "com.denghb.eorm.plugin", "JFormDesigner-Marketplace-Edition", "com.taff.plugin.orchide", "cn.focus.sohu.kill8080", "com.bytedance.jedi.jdt", "com.camelot.idea.plugins.ask", "sourcery.pycharm-plugin", "com.seventh7.widget.iedis2", "com.vultix.intellij-ron", "com.tencent.wegamex.Community", "com.rookout.intellij-plugin", "ccom.intellij.idea.plugin.commerce.integration", "com.yoheimuta.intellij.plugin.protolint", "com.esehl.ann.format", "com.wzportal.idea.tool.propertiesfilesorter", "cn.enilu.webflash.generator", "org.yidan.idea.plugin.jasmine", "com.felhr.airadb.air-adb", "com.hsiaosiyuan.idea.ont", "com.plugin.image-icon-viewer", "com.suning.dal_support", "com.flipkart.dsp", "com.flipkart.dsp.dark.pheonix", "Abc.MoqComplete.Rider", "com.gilecode.xmx.smx-idea", "com.gitlab.neshkeev.huskygiraffe.huskygiraffe", "com.gitlab.lae.intellij.jump", "jebe.liao.JBLExploadedRefresh", "rest.bef", "com.intellij.kotlinNative.platformDeps", "com.lexastudio", "com.google.services.firebase", "jebe.liao.JBLLoveShortCut", "com.baomidou.plugin.idea.mybatisPlus", "csense-idea.kotlin-checked-exceptions", "org.broadinstitute.wdl.devtools", "streamline.refactoring.plugin", "app.phompang.android-resource-deprecater", "io.github.maddouri.intellij.OnlineSearch2", "com.michael.bergens.java.playground2", "jr-mp-ide-idea", "sg.bigo.mobile.android.srouter", "cogurets", "saigonwithlove.ivy.intellij", "net.aquadc.mike.plugin", "de.ehmkah.projects.imgdiff", "com.vecheslav.darculaDarkerTheme", "de.marius<PERSON>fler.flutter_enhancement_suite", "me.ely.codegen", "<PERSON><PERSON>", "com.jetbrains.lightThemePreview", "J2EECfgFile", "SSMCodeGen", "DafnyPlugin4Intellij.DafnyPlugin4Intellij", "com.zhenai.lib.ZhenaiLintPlugin", "com.idisfkj.databinding.autorun", "org.nxt.plugin.kitfox", "com.alibaba.astore.plugin", "com.darkyen.wemi.intellij", "com.liuqi.tool.idea.plugins", "co.droidmesa.green", "com.sashashpota.twitter-plugin", "org.martica.wombat_calculator", "org.illyasviel.amethyst", "RedsoftYapiUpload", "pers.fw.doc", "com.ultrahob.zerolength.plugin2", "com.github.typ0520.jsontodart", "com.lzw.demo", "io.cacher.intellij", "cn.luojunhui.touchfish", "info.makarov.s3-plugin", "riot.js", "com.johnlee.redblack.redblack_theme", "com.upcwangying.plugins.data-auth-idea-plugin", "de.espend.idea.php.generics", "org.liujing.plugin.json_serializable_format", "com.hernanhrm.horizon", "com.tan.jutils.plugin.id", "cc.moky.intellij.plugin.svga-support", "MadJenvPlugin", "org.exbin.utils.guipopup", "org.tonyyan.plugin.documentassistant", "com.netease.newsreader.plugin", "eu.sndr.wrap-lines-plugin.wrap_lines_plugin", "org.jgrasp", "cn.liuqi.tools", "io.github.QCute.RecordCompletion", "com.zhan.mvp.KtArmor-MVP", "design.verybeautiful.onedarkmonokai", "fr.ythollet.header", "com.intellij.nativeDebug", "io.ruudboon.phpstorm-phalcon-4-autocomplete", "net.seesharpsoft.intellij.plugins.file-preview", "color.scheme.Python Sublime", "me.ely.kodegen", "site.forgus.plugins.api-generator", "org.moe.community", "me.fornever.commandlink", "com.github.sblundy.elvish-lang-plugin", "com.deepexi.plugin.mapper-scan", "com.github.nreg.SearchWithBing", "com.github.nreg.SearchWithBaidu", "im.plmnt.plugin.greek", "woainikk.kotlinizing-statistics", "color.scheme.Derwish Theme13", "com.wayj.mvphelper", "com.tabnine.TabNine", "com.lucky.plugin.code.generator", "tremasov", "com.github.xwsg", "tech.lnkd.LNKD-tech-Editor", "de.lukweb.discordbeam", "com.gentrio.runscriptplugin", "Smart Centaur Runner", "tk.cofedream.plugin.mybatis", "com.gomitu.android.dimensionconverter", "com.youzan.debug.tools", "cn.ivhik.wyb.intellij.plugin", "xao-rider", "com.dev.tools.kit.easycoding", "com.ppismerov.ksvu", "com.bruce.intellijplugin.RedisPlugin", "com.adebesin.spacemacs", "com.phpinnacle.redoc", "com.bashu.soek", "com.antoshk.jsf-el-extension-plugin", "com.orleviad.python-import-fixer", "com.swissas.tala.swissas", "com.intellij.resharper.StructuredLogging", "com.sunny.plugin.MockitoGenPlugin", "com.qchery.idea.plugin.FastBuilder", "com.jetbrains.ChooseRuntime", "com.jmonkeystore.newgametemplate", "com.github.tth05.minecraft-nbt-intellij-plugin", "com.debut.corda", "com.hanclouds.assistant", "luogu-intellij", "de.thl.intelli<PERSON>", "color.scheme.Tomorrow Dark", "com.heytea.plugin.ccg", "com.ctrip.unique.plugin.id", "csense-idea.kotlin-assistance", "csense-idea.java-assistance", "com.madrapps.eventbus-plugin", "com.orleviad.python-import", "com.github.florent37.plugin.jsonserializable", "com.koxudaxi.pydantic", "com.cainiao.digit.cndigit-plugin", "com.sogou.adstream.code-sync", "com.intellij.idea.plugin.sap.commerce", "artsiomch.cmake.plus", "com.zh.zhsWizard", "jgsogo.conan.clion", "com.youzan.cloud", "com.shumyk", "cn.xydzjnq.generateview", "ro.florinpatan.gopher", "com.microfocus.intellij.plugin.gitclient", "ca.rightsomegoodgames.HythonPlugin", "coderpillr.theme", "com.uncle2000.generator.stringxml", "com.fhtiger.plugins.humpTransfer", "to<PERSON><PERSON><PERSON><PERSON>", "com.fan.plugin.SimpleCamelCase", "framework-router-roadsign", "com.lzx.multplugin", "org.semonte.intellij.swagger", "com.sander.installapk", "tv.codely.codelytv-theme", "color.scheme.One Monokai", "com.pjialin.green-lite-theme", "com.jmonkeystore.intellij-integration", "com.clutcher.comments_highlighter", "com.andrey4623.rainbowcsv", "com.longshihan.learnEN", "com.yyh.filter", "com.template.flutter.code.plugin", "com.jphoebe.plugin.swagger", "com.sjhy.plugin.CodeFactory", "com.yagaan.yagaan-intellij-plugin", "com.gitlab.lae.intellij.actions.java", "com.bdpqchen.android.plugin.alog", "com.uncle2000", "com.ipleac.layout-opener", "poxu.plugin.dot-mvn-idea-plugin", "mps<PERSON><PERSON><PERSON><PERSON>", "com.zbk.plugin.TranslateAction", "com.sossy.cpachecker.plugin.CpacheckerPlugin", "<PERSON><PERSON><PERSON>ode-rider", "io.coala.jetbrains", "mpsDart", "csense-idea.kotlin-test", "mpsBloc", "com.tencent.mtt.logsdk.plugin.logsdk-plugin", "de.cronn.validation-files-diff", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com.wuba.financials.snippet", "com.zihuan1.zhtranslation", "color.scheme.Custom Theme", "com.github.kassak.dg-test-helper", "me.akainth.ambient", "com.grouter.idea.plugin", "com.airsaid.InspireWriting", "com.fhtiger.plugins.humptransfern", "com.pandocker", "com.mrarm.coffeebean.ui.theme", "kaboyi.ideaplugins.wordcount", "com.codcut", "net.firebits.odoo", "com.xinyuan.plugin.code", "com.jetbrains.python.two.end", "JUnit4-<PERSON>llel-Runner", "com.bdpqchen.android.plugin.tagalog", "com.vicodes.Plugins.Tomcat.v1", "com.xingin.KDocGenerator", "com.yuriikachmar.wsclient", "com.sonphil.imageview-scaletype-visual-guide.imageview-scaletype-visual-guide", "com.vectioneer.clion.plugin.motorcortex", "com.closeli.plugin.switchoem", "com.star.easydoc.easy-javadoc", "com.justcode.employer.custom", "com.eny.i18n", "com.plugin.agentzero.my_agentzero_plugin", "com.mishin870.gkf", "com.qbitlogic.idea.iceberg", "com.maoqis.KotlinFindVIewById.id", "se.clau.intellij-lux", "csense-idea.java-test-assistance", "git-commit-template", "yaoxuanzhi.todoList", "com.linkkou.plugin.intellij.assistant", "com.villains.intelij.plugin.trashpandatheme", "ai.blindspot.intellij.ScalariformFormatter", "GitIntegrationProject.MGGitIntegration.GitIntegrationProject", "edu.illinois.cs.cs125.intellijlogger", "12909", "io.naraplatform.studio.drama-plugin", "com.cheng.plugin.coding.easier", "com.ke.link", "be.3factr.t4processor", "dk.lost_world.phpinsights", "com.layoutmanager", "de.cmm.side_nodes", "com.zhaopin.platform.zpd-assistant", "io.github.alexengrig.lambdax.intellij.plugin", "color.scheme.Spring Light", "com.github.timo_reymann.composer_dump_autoload_phpstorm_plugin", "io.ajab.timestamp-generator-plugin", "codeseekpluginid", "org.jetbrains.plugins.iceterm", "com.intellij.php.markdown", "com.mfwebstudio.atomonedark.plugin.id", "com.intellij.plugins.visualstudiokeymap", "co.simpleq.mvn-dependency-updater", "com.virtuslab.contextbuddy", "com.bruce.intellijplugin.custom-generatesetter", "io.github.xiaopihai7256", "com.neebal.flutility", "com.microfocus.mobilecenter.intellj-plugin", "TppIdeaPlugin", "com.jetbrains.plugins.code-notes-plugin", "com.jetbrains.hackathon.indices.viewer", "com.lun.learn.plugin.activity.tools", "cn.cxzheng.methodtraceman.ideaplugin", "io.github.newhoo.quick-dev", "herobrine1010.idea.plugin.drumroll", "sk.tuke.kpi.arena.intellij", "com.adebesin.emerald", "me.elabee.idea.plugin.swoole", "com.zalesskyi.csv_translator", "lk.chathurabuddi.jasper-report-supportn", "com.qdqtrj.dart", "MIFCMNotification.MIFCMNotification", "com.github.luobiao320.uml.adapter", "oracle.nosql.intellij.plugin", "be.jbeckers.compare_tab_with_editor2", "com.javaccy.plugins.idea.CodeGen", "com.greengrowapps.remoteopenfile", "com.lfrobeen.intellij-datalog", "com.nndi-tech.oss.intellij-gensett", "com.your.zywulian.plugin.usecase", "com.cherep_corp.plugins.request_converter", "ws.logv.mps.generator.editors", "com.wuhaowen.mvpkiller.kt", "cn.vove7.ide.plugin.rest", "com.uama", "de.ur.mi.code-timeline", "mi.wuguagnliang.plugin.dimens", "dev.feedforward.ShiftOpen", "com.dengzii.plugin.findview", "cn.reker.intellij.injectlogtag", "at.wtioit.intellij.plugins.odoo", "com.boufnichel.intellij.scc-code-guard", "Generate Document", "com.mikejhill.intellij.movetab", "org.xujin.halo.idea.plugin", "com.intellij.plugins.emacskeymap", "com.intellij.plugins.gnomekeymap", "com.intellij.plugins.kdekeymap", "com.intellij.plugins.resharperkeymap", "com.intellij.plugins.xcodekeymap", "com.intellij.plugins.xwinkeymap", "MILiveTemplate.MILiveTemplate", "org.github.plugin.redis-simple", "shirotelinintellij.yasukotelin.com.github", "net.antelle.intellij-xcode-dark-theme", "gdscript", "xyz.stupidwolf.plugins.idea.start-cost-time", "WxbYapiUpload", "com.jetbrains.FocusTuner", "com.intellij.gradle", "com.intellij.copyright", "org.jetbrains.plugins.github", "org.jetbrains.plugins.haml", "hg4idea", "HtmlTools", "Refactor-X", "com.jetbrains.restClient", "com.jetbrains.sh", "org.jetbrains.plugins.terminal", "com.jetbrains.changeReminder", "com.jetbrains.plugins.webDeployment", "org.jetbrains.plugins.yaml", "com.intellij.php.phpt", "com.jetbrains.php.workshop", "com.lucky.caige.ibatis", "com.malbul.idea.theme.breath", "de.fraunhofer.iem.secucheck.analysis-integration", "com.technia.Technia_plugin", "com.sankuai.meituan", "com.ly.maven.versions.plugin", "com.nd.sdp.indexed.bundle.handler", "cn.vove7.ide.plugin.eventbus_navigator", "ru.coding4fun.painless-transact-sql", "io.github.jiarus.HowManyCode", "com.fish.bin.extract.text", "com.one.plugin", "com.intellij.applet", "com.bymarcin.packages4apex", "net.officefloor.intellij.OfficeFloorPlugin", "com.intellij.jsp", "colourizeit", "pl.dominikw.Windchill-Intellij-Plugin", "com.benett.GenerateThriftField", "com.dengzii.plugin.adb", "io.mateu.lombok-plugin", "com.yulrizka.wordfreq", "com.blai30.lotus", "com.qiao.plugin.MyPlugin", "com.liucr.plugin.commitwithtask", "com.epam.sha.intellij.locatorupdater", "com.hrzntsudio.spatialos.intellij", "Git4Idea", "com.cschar.power.mode3.zeranthium", "palantir-java-format", "com.zhuzichu.plugin.codeinspection", "com.zhuzichu.plugin.autocode", "com.reallyliri.plugins.interfacepairing", "CPAchecker", "com.xmqiu.realm", "com.reallyliri.plugins.no-namespace-providers", "com.ymt360.tool.check", "io.github.newhoo.mysql-explain", "com.antkorwin.unicoder", "org.dandoh.favacts", "com.saint.lnkScreen", "se.fortnox.jbehavesinglescenario.jbehavesinglescenario", "com.intellij.vaadin", "com.aska.fed.gitbooks-summary-generator", "de.fraunhofer.iem.swan_assist", "io.github.newhoo.apollo-conf", "io.github.newhoo.bean-invoker", "io.github.newhoo.jvm-parameter", "com.zxwl.HDF5Visualizer.plugin.id", "com.littlepudding.company.unique.plugin.id", "edu.vub.somns.somns-intellij", "com.1v0.dev.hugo-intellij-plugin", "Antmove", "de.tum.www1.orion", "com.amber.lib.plugin.androidx_migrate", "com.huawei.deveco.hms", "com.namespace.sort.namespace-sort-plugin", "id.mustofa.breeze-dark", "jones.foldcallblocks.fold-call-blocks", "com.nmeylan.powermode", "tag-editor", "com.xingin.xhs.intellij_plugin", "de.itemis.mps.selection", "intellij.music", "ToolWindowsActions", "machiry.test.sample", "com.redhat.devtools.intellij.quarkus", "de.slisson.mps.hacks", "rider-cake-plugin", "de.itemis.mps.blutil", "de.itemis.mps.celllayout", "de.itemis.mps.editor.diagram", "de.itemis.mps.editor.widgets", "de.itemis.mps.grammarcells", "de.itemis.mps.nativelibs", "de.itemis.mps.nativelibs.loader", "de.itemis.mps.tooltips", "de.itemis.mps.utils", "de.q60.shadowmodels", "de.q60.shadowmodels.examples", "de.slisson.mps.conditionalEditor", "org.apache.commons", "de.itemis.mps.editor.math", "de.slisson.mps.editor.multiline", "de.slisson.mps.richtext", "de.slisson.mps.tables", "com.instana.idea.php", "com.intellij.plugins.macoskeymap", "org.kelemenistvan.swagger-rearranger", "dev.fervento.assert-by-debug", "com.markacz.lineAppend", "com.attachme", "com.pycrunch.intellijconnector", "com.github.stevejagodzinski.akka-log-inspection-plugin", "org.strangeway.qa.lithium", "com.materkey.codepoints", "artsiomch.meson.syntax", "tecbot.tecbotid", "dev.qvint.intellij.devhelp", "com.lecheng.hello.idextxt", "site.wangxing.plugin.format.field.pyramid", "ru.nobird.arch.templates", "com.sipesistemas.CopyDjangoModelFields", "co.bybardo.julianfalcionelli.android-etymon", "php.codeGolf.training", "inegration.testrail", "com.naixiaoxin.idea.hyperf", "com.chx.plugin.memo", "com.wjp.AndroidLocalizePlugin", "com.foryou.plugin.mvvm", "idimaster.org.golden-rules-idea-plugin", "com.codyi96.android.get.emoji", "com.zhoug.plugin.android.findviews", "com.github.lppedd.idea-return-highlighter", "com.github.czy211.licensetemplate", "com.github.nhat-phan.codecleaner", "io.github.fatihbozik.intellij-license-generator", "indent-rainbow.indent-rainbow", "com.mallowigi.colorHighlighter", "com._4paradigm.csp.plugin.i18n", "com.winning.pub", "org.vjapps.rsp.parser", "com.yuxuan66.fastTransform", "com.birbit.artifactfinder", "test-rail-plugin", "com.rookout.pycharm-plugin", "julis.wang.no<PERSON><PERSON>pe", "ten.branch_creator", "com.him.softSolutions.AndroidResToolLaunchPlugin", "com.vectioneer.clion.plugin.motorcortex.toolchain", "moe.knox.factorio.autocompletion", "<PERSON><PERSON>le<PERSON><PERSON>", "Gitflow-semantic-version", "startrek-progressbar", "zcchenk", "de.beyondco.tinkerwell.tinkerwell", "com.dropbox.plugins.mypy_plugin", "jebe.liao.JBLHtmlToThymeleaf", "com.dongwuamon.androidstringremover", "com.fcbox.test.lotus-swagger", "color.scheme.Yicru", "SpotProfiler", "be.ac.ucl.info.inginous_submit", "com.fimaworks.jetbrains-10bis-plugin", "juventus-progressbar", "wang.zhanwei.clangformat", "org.jetbrains.IdeaVim-EasyMotion", "com.jetbrains.space", "com.cmcm.maidian.plugin", "me.y<PERSON><PERSON><PERSON>.AnnieTools-Translate", "com.nerdscorner.android.plugin.github", "com.yiwowang.plugin.config", "com.dengzii.plugin.converter", "org.jetbrains.plugins.cidr-intelliLang", "org.intellij.intelliLang", "com.github.czy211.wowtoc", "com.myoutdesk.tailwind_formatter", "com.bauerbao.srchardcodeutil", "com.icthh.xm.configuration", "org.designite.intellij.plugin", "zd.zero.waifu-motivator-plugin", "com.xobotun.idea.VerticalAlign", "csense-idea.csense-kotlin-annotations", "com.github.turansky.yfiles", "org.microshed.intellij-plugin", "com.ifetch.cq.mapper.generator.plugin", "com.github.lppedd.idea-conventional-commit", "com.wada811.kotlinizepr.kotlinize-pr-plugin", "io.vlinx.p_protector4j", "org.eclipse.scout.sdk.s2i", "com.zxy.ijplugin.wechat-miniprogram", "com.ld.automatic.translation", "io.scriptcloud.plugin", "StubsGenerator", "CsStubsGenerator", "CsBaseLanguage", "CsStdLibrary", "Eclipse theme", "com.github.lppedd.idea-conventional-commit-angular2", "com.beaverkilla.generateeverythingplugin", "com.intellij.marketplace", "org.xiaogang.unit.test", "net.starlighthosting.StarlightDark", "dev.lazyts.vue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cn.idealismxxm.graphene-helper", "com.mageberry.SetterGetterGenerator", "com.ichong.pulgin.git", "com.digital-sector.bx-component-setup", "com.reedelk.plugin", "com.heyangli.www.translation", "org.jetbrains.plugins.phpstorm-wsl", "cn.intellizhi.templatesupport", "Cloak", "com.ming.ccg", "org.arxing.dart_fast_import", "ru.aic.template_generator", "org.lso.logit", "com.zielsmart.eya-dev-tool", "com.baidu.intellij.plugin.id", "com.youzan.mobile.enjoyplugin", "com.teamnova.stickode.plugin", "com.github.szymonbaranczyk", "com.laravel_idea.plugin", "it.unisa.plugin.adoctor", "hu.aradipatrik.nightowlnative", "com.toefel18.kotlin-fill-class-2019.3", "AutoYApi", "com.joncpl.jDark", "vignes.vecta_map2", "com.ucharm.YapiUpload", "com.mobiledi.flutter_plugins", "com.angel.easy.model.generator", "com.SoulButton.unique.plugin.id", "com.microfocus.sv.intellij-idea-plugin", "com.github.GifBackground", "com.github.RESTMan", "com.virtuslab.graphbuddy", "de.tyxar.clean_architecture_plugin", "com.github.ingTools", "ru.coding4fun.intellij.database", "com.intechsoft.plugins.modelhandler", "org.shonminh.helper.GenerateModelAction", "commit-message-helper-idea-plugin", "com.autohome.lemon.dbcheck", "com.ctrip.basebiz.autoinsertshark", "red.stu.plugin.idea.coder.querycheck", "com.alibaba.rdc.jvm-inject", "org.aber.plugins.poorcheck", "com.yilnz.intellij.highlighter.LocalVariableHighlighter", "idv.freddie.plugin.decompiler", "com.dncomponents.plugin", "base_bloc_generator.base_bloc_generator", "xgimi-commit-template-idea-plugin", "dev.jeka.ide.intellij", "com.dragon.idea.plugins.generator.code.generator", "com.devdo.intellij", "com.netease.music", "org.example.untitled", "com.youngfeng.ideaplugin.generateserialversionuid", "pers.fw.doc_en", "dev.ngocta.pycharm-odoo", "com.shuqi.tools.translate-properties", "io.lapli.listEnv", "color.scheme.RadeevHarmony", "com.tongji.CoIntelliJIdea", "com.primer.efp_plugin", "color.scheme.Neo Fire", "com.hyphoon.IDEA-Swagger-Knife4j-Plugin", "com.yxf.plugin.fix.sgc", "dev.icerock.moko.intellij-plugin", "com.github.timmyovo.CopyToFinder", "com.moore.plugin.releaseCheck", "com.xjke.GenerateRepository", "com.alipay.mobile.slicebuilder", "SpringBootGen", "yijun.sun.gitcommittemplate", "cn.agilecode", "com.herbert.george.dart.extensions", "com.reneodlt.corda-plugin", "SpringCloudGen", "net.phoneportal.idea.fuelphp", "com.netease.yanxuan.hardcodeopt", "com.github.ausmarton.cucumber-scala", "com.majera.intellij.codereview.bitbucket", "com.josiassena.GitHubGists", "com.uber.motif", "com.kaishwarya.json.Editor", "com.zugazagoitia.spanish-spell-checker", "de.wlsc.junit.converter.plugin", "dev.arunkumar.scabbard.idea-plugin", "com.nerdscorner.android.view.utils.plugin", "cn.xunyard.coding-helper", "io.uniflow.uniflow", "com.jetbrains.theme.core", "summer-scene-plugin", "org.arxing.chinese_converter", "com.self.plugins.protobuf", "com.guet.flexbox.handshake", "sog.p3c.quick.development", "sog.p3c.smartfox", "com.predlab.jet", "com.epam.drill.intellij-plugin", "gen-plugin", "com.kibro.recipe-lang", "me.mbolotov.onlychecker", "com.nnthink.aixcoder", "com.microfocus.plugin.hw", "com.xyz.caofancpu.D8gerAutoCode", "etp.gpb.marketplace.uxg.plugin.id", "com.shuqi.tools.cmp-helper", "cz.vookimedlo.intellij.plugin.subtle-hacker-theme", "com.github.wangji92.arthas.plugin", "org.jenkin.intellij.plugin.setterfieldhelper", "com.hassan.intellij.plugins.mob-programming-timer", "com.dengzii.plugin.template", "com.chuntung.plugin.gistsnippet", "com.swazerlab.create-react-process.create-react-process", "com.jsonjuri.phpSyntaxColorHighlighter", "com.jsonjuri.cakePhpSyntaxColorHighlighter", "pl.suzuyo.code-generator", "gpu-monitor", "com.ptg.quickcodegeneration.plugin", "io.github.jshanet.extractor-intellij-plugin", "dcevm-plugin", "com.liuzhihang.toolkit.copyasjson", "net.ntworld.nhat-phan.merge-request-integration-ce", "com.pyphanon.py_phanon", "org.tonybaloney.security.pycharm-security", "com.leyongleshi.idea.plugin.pasteimageintomarkdown", "com.hiy.kae", "net.dstribe.custom_word_separators", "net.ntworld.nhat-phan.merge-request-integration-ee", "com.jsonts.kotlin", "com.siyueren.codehelper", "com.sannniou.multiitem.plugin.templates", "dev.johnwatts.CertificatePlugin", "internalsvisibleto-rider", "com.tom.fonter", "com.shenyong.flutter.refgenerator", "in.fantastic.intelliJeditortools", "io.github.luke-biel.IonType", "color.scheme.Mellow Contrast (rainglow)", "fi.aalto.cs.intellij-plugin", "com.jiaqi.converter", "org.example.idea-plugin-lombok-alt-enter", "monokai-pro", "com.jeppeman.locallydynamic", "org.aeternity", "com.github.aamnony.idev", "org.ishvetsov.wlcengine", "melabsinthiatum.kotlin-mpp-shared-items-browser", "com.dxy.platform.plugin.chd.api.search", "cn.jitash.plugin.commit", "it.unisa.casper", "com.nguyennk.run-typescript-plugin", "com.andrey4623.intellij.plugins.effectiveinnerbuilder", "com.localizely.flutter-intl", "cn.j-tools.jenkins-plugin", "io.artage.sfcc.salesforce-commerce-cloud", "com.hbmy.bageyang.Protoc-Plus", "org.example.ThesisPlugin", "com.him.softSolutions.AndroidResViewerPlugin", "de.espend.idea.localization", "com.yazan98.intellij.theme.thentrom", "org.droidfort.gitignore", "top.imyzt.plugins.PojoToFormData", "com.vivo.it.plugin.vlibrary.mvp", "com.maksimnovikov.moxystrategy", "com.somewater.jsync", "org.martica.equality", "srgdev.com.PasteExternal", "com.mars.genAllSetter.genAllSetter", "pl.beone.promena.intellij.plugin.Promena", "com.circleci.intellij", "com.intellij.selenium", "com.puyixiaowo.workbook", "oh-my-idea", "to.bri.intellij.clubhouse", "com.tgcity.plugin.id.mvp", "sh.spinlock.idea.hackernews", "com.developerartemmotuzniy.LocalBranchMergedCleaner", "com.rohit.patil.long.date", "kz.kuba.LazyAliass", "com.jetbrains.plugins.compass", "at.dotti.intellij.plugins.jazz", "com.piekill.transmitter", "some.awesme", "com.intellij.zh", "com.intellij.ko", "com.codesmagic.idea.minbatis", "de.msg.mf.intellij-plugins", "com.miguel.idea.plugin-json", "moe.lemonneko.fabricdev.fabricdev", "com.jayk.plugins.idea.pullrequest", "gorepogen.intellij.plugin.gorepogen-intellij-plugin", "com.kaygisiz.pojoToJsonSchema", "io.reactjava.reactjava-lang", "com.decentralized.internet.id", "com.secure.refactor", "com.kablemonck.idea.plugins.ChangeListPopup", "com.intuit.intellij.makefile", "mcd.dbviewer", "com.buckstabue.stickynotes", "dev.booij.shoot_plugin", "cn.alanhe.plugin.viewPackageVersions", "com.github.sbouclier.intellij.plugin.intellij-plugin-java-builder-pattern", "com.fnovellon.buildonmusic", "com.b1f6c1c4.gradle_run_with_arguments", "top.shenluw.intellij.dubbo-plugin", "org.example.myplugin", "dev.dohpaz.php-extras", "com.master.studio.plugin.adb_input", "com.goodrequest.goodformatter", "de.nodroid.serviceportal-builder", "org.endpointdeps.EndpointDependencies", "ru.mekh.idea.plugin", "com.nguyencse.arm", "com.badoo.liveprof.plugins", "cc.ssnoodles.db2j-plugin-ce", "com.github.mutcianm.external-laf-switch", "com.github.rxyor.plugin.pom.assistant", "com.dcasadevall.jira_todo_tasks", "arithmetic_plugin", "net.kaiba.source", "com.demoJian.RunAs", "com.GEN.LastEdited.v1", "org.learn.tdd.red-green-refactor", "guynir.pypath", "com.giancarlocode.built-value-snippets", "com.danielg.duda.commonprop", "com.fly-mix.flr", "color.scheme.Tomi", "com.yemreak.DarkCode-Theme", "cn.umisoft.umi-code", "DclareForMPS", "de.sist.gitlab", "com.zxy.ijplugin.javaClassToTypescript", "fastify_decorators.plugin", "com.github.mdk.plugin.persiantranslator", "com.navigatetomodule", "com.github.chencn.yamlandprops", "org.whitesource.wss-intellij-plugin", "com.lomoye.plugin.goJavaApi", "hu.rb.commentparser-intellij", "com.evolveum.midpoint.studio", "de.jensklingenberg.ads", "org.ijplugin.javaClassToDart", "com.google.developers.certification.tensorflow", "cn.alanhe.plugin.openWithApplication", "org.elasticsoftware.elasticactors", "org.devisions.design.intellij-vipassana-theme", "fr.ft.aviscogl", "com.github.lppedd.idea-backtick", "me.mbolotov.cypress", "zio.intellij.zio-intellij", "com.zxy.ijplugin.javaClassConverterCore", "com.shizhuang.duapp.ideaplugin", "ddd.diagram.tool", "com.freer.naysayer88", "com.vpoverennov.pegen", "ru.meanmail.plugin.django_command_runner", "no.eirikb.avatest", "cn.mrdear.intellij.related-file-intellij", "org.github.ontanikotani.mathfolding", "com.sharedvocabs.plugins.rdfandsparql", "org.eclipse.codewind.intellij", "pro.bashsupport", "com.eeui.plugin.eeui", "com.xjke.GenerateJPA", "com.bruce.easycode.mybatiscodehelper", "com.github.raniejade.godot-kotlin", "com.intellij.STAintellij.intellij_plugin", "<PERSON><PERSON><PERSON><PERSON>", "createadapter.createadapter", "CloudFoundry", "io.thundra.debugger", "info.rayden.voldeskine.dependencies", "org.bitstrings.idea.plugins.TestInsanity", "com.bingo.mvvm.MvvmAssistant", "com.ts.pdt.pdt-intellij-plugin", "io.devbench.uibuilder.idea", "de.whisp.android", "net.roseboy.cnchar", "at.knittl.intellij.plugins.jmhack", "color.scheme.My theme", "engineer.echo.plugin.mvvm", "com.mroche.JsonHelper", "ua.com.glebk.paste_git_branch", "com.ly.fn.idea.plugin.gitlab.merge-request-ut-coverage-check", "com.jarporter.jarporter", "com.intellij.plugins.mactootherskeymap", "com.intellij.rider.godot", "Rider UI Theme Pack", "com.example.plugin.jdeste3", "fr.radequin.Copy-as-SQL", "site.pegasis.smart_return", "color.scheme.Ayu Dark", "org.intellij.plugins.dhall", "org.hibnet.intellij.theme", "com.seboba.MockKTestGenerator", "com.hz.tech.jjh.property.name", "com.viewrouter.helper.plugin", "com.sheepapps.vivabloc.vivabloc", "me.profiluefter.moodlePlugin", "com.github.ttyniwa.intellij.plugin.align", "MyBatisLog", "com.dongukkim.javamemorykiller", "name.tachenov.intellij.copy-with-line-numbers", "com.mfw.app.dep_plugin", "dev.niels.SQLServerBackupAndRestore", "cn.mrdear.intellij.class-decompile-intellij", "com.xenoamess.x8l_idea_plugin", "com.ekino.oss.jcv-idea-plugin", "shoobah.pink.as.heck", "com.sunlands.apollox.script", "com.vecheslav.darculaSolidTheme", "com.aos.android.multilan", "intellij.clion.embedded.platformio", "gras", "com.arkoisystems.ArkoiLang", "com.dxf.SuperMVP", "cn.jxzhang.plugin.json-formatter", "org.example.myPlugin18", "color.scheme.nye", "com.lizongying.nicesql", "com.knziha.vectorpathtweaker", "com.schalar.mapper", "de.nordgedanken.auto_hotkey", "ru.<PERSON><PERSON>ov", "dev.pnbarx.idea.treecolor", "com.intellij.plugins.riderkeymap", "tech.phosphorus.intellij-prolog", "Git Commit Template For PingAn", "com.furstenheim.zpl-plugin", "org.b333vv.metricstree", "net.mamoe.mirai-console-intellij", "phoneixTool", "com.ganshenml.unique.plugin.id", "quickturnstudio.sdk.language.qss.QtStyleSheetHighlighter", "com.intellij.ja", "com.faebeee.reactcomponentcreator", "com.gmwframework.YapiUpload", "org.xujin.halo.toolkit-intellij", "com.samvtran.plugins.macosforallkeymap", "com.optimizely.intellij.sdk.action", "com.chivas.plugins.bean", "com.ixigua.flutter_asset_iteral", "com.mesmers.dimentools", "www.haisen.quickSnippets", "com.nassim.theme.superdark", "com.github.lppedd.kotlin-additional-highlighting", "com.linka.tianxiatech.wordFormat", "CopyGithubUrl.CopyGithubUrl", "com.wajahatkarim3.sidemirror", "fr.socolin.application-insights-debug-log-viewer", "cn.olange.pins", "me.mbolotov.cypress.pro", "com.me", "com.souche.android.xdep", "com.vv.plugin.id", "org.example.modularization", "lermitage.intellij.iconviewer", "com.tokky.cout", "com.futuremarket.p3c.smartfox", "color.scheme.oblivion", "net.n2oapp.n2o.idea.plugin", "com.sinar.ActivatePowerModeXApplicationPlugin", "net.kraschitzer.intellij.plugin.time_tracker.intellij-time-tracker", "idea.plugin.protoeditor", "com.aladdin.debugger.idea.plugin", "com.yeepay.yop.devtools.idea", "org.jetbrains.plugins.spotbugs", "com.sensei.solutions", "IntelliJDeodorant", "code naming", "com.ponshine.svnlabel", "com.oasis.dScriptChecker", "org.example.bfi-language-plugin", "top.shenluw.intellij.stock-watch", "cn.mofei.util.directory.comparator", "com.ervinracz.MetaModelHintProviderPlugin", "com.notime.intellijPlugin.backgroundImageByURL", "dev.falsehonesty.intellij-preprocessor", "org.quantumquacks.plugins.multiuser-git", "online.zero2hero.flyme", "com.yuzd.codegen.ktorm", "github.com.zh1614933.Paste-Images-as-Base64Str", "ca.ulaval.glo.sensei", "com.jdpay.android.plugin", "com.dk.mavenversion", "org.nagarro.coderefactoring", "com.github.lppedd.idea-conventional-commit-commitlint", "io.github.QCute.ErlangHotLoader", "SmartJump", "com.intellij.plugins.visualassistkeymap", "com.atlassian.confluence.hermes", "com.github.xiaolyuh.mrtf-git-flow-4idea", "com.ucarinc.plugin.mustacheUtil", "dev.patrick<PERSON><PERSON>.darculaPitchBlackTheme", "net.dreamlu.event", "org.xstudio.plugin.idea", "org.yuzd.codegen.protocodegen", "jp.kitabatakep.intellij.plugins.CodeReadingNote", "lekanich.magic-advisor", "org.generator.code-generator", "com.worldctrl.tsp", "com.github.chencn.intellij-high-contrast-theme", "com.github.weisj.darkmode", "ch.raffael.meldioc", "kotest-plugin-intellij", "TunnelliJ-Pro", "com.panda.spring.ioc.analyze.plugin", "com.brownian.plugins.intellij.complexity", "com.github.shiraji.ccch", "Align Environment Property Files", "org.generator.go-lang-code-generator", "org.generator.ruby-code-generator", "com.github.houkunlin.database.generator", "com.vargant.dfc", "dart.helper.exgou.cn", "com.ADMARIl.halcyon-jetbrains", "com.github.wenzewoo.jetbrains.plugin.jetbrains-markdown-image-support", "com.asherbernardi.jsgfplugin", "com.curry.study.first.plugin.id", "world.gregs.intellij.plugins.deobfuscation", "org.softeng.Design-Pattern-Doc", "Commit Template", "com.cvte.mobile.plugin.PokemonMavenGenerator", "com.whir.plugin.webservice", "com.flutter_create_stateful_widget", "com.alibaba.p3c.xenoamess", "com.failfast", "com.horse.react-class-style-to-css", "com.singler.godson.pryer", "org.timelogger.timelogger-intellij-plugin", "net.mamansoft.markowl", "com.newpurr.plugin.gsonformatphp", "DependencyInjector", "jones.restarteslintaction.restart-eslint-action", "com.r3bl.plugins.shorty", "ru.drom", "com.cn.xida.plugin.godesignpattern", "com.jiajun.flutter_add_image", "spawnia.intellij-keymap", "gw.gosu.ij", "com.pwhxbdk.plugin.swagger.tool", "org.idlesign.pythonz", "com.rhyme.flutter.plugin.jumptoassets", "com.idea.plugin.meetimebuilder", "com.brownian.intellij.plugins.pomchecker.PomChecker", "com.pkx.plugins.PKXCodeGen", "lekanich.HeadsOrTails", "com.xjke.composerSearch", "ru.dspt.black", "com.maaxgr.intellij.jsonviewer", "com.github.hoangld93", "saker.build.ide.intellij", "com.wadday.vinc-theme", "cn.eli.dimens", "icu.jogeen.markbook.id", "Tolltech.Tollrider", "com.heger.ScriptClassificationPlugin.ScriptClassificationPlugin", "dev.arunvelsriram.desccron", "it.unisa.vitrum", "color.scheme.Oceanic Next", "cn.olange.rule", "org.cosee.itm_viewer", "com.spy.unique.plugin.noButterKnife", "com.github.mdk.jetbrains.plugin.persiantranslator", "org.generator.javascript-code-generator", "org.generator.php-code-generator", "com.github.developerutils.kotlin-function-arguments-helper", "com.intellij.antbuild", "com.github.dhaval2404.material_icon_generator", "com.teamtrackerhq", "dev.bachelder.opencensus_php_extension_support", "jiongzhi.intellij.auto-formatter", "io.nebula.idea.plugin.cpnt-build", "com.epam.healenium.hlm-idea", "org.jetbrains.fortea", "com.jetbrains.rider.fsharp", "color.scheme.Night Owl", "snake-eater", "org.easysoc.verilog", "org.ice1000.bililive.danmaku", "com.reportmill.SnapIdea", "ua.zt.mezon.android.opencl.OpenCLCLGenerator", "monokai-pro-forked", "de.cvguy.kotlin.koreander", "com.youssef.tool.idea.plugins", "com.y4kstudios.pytestimp", "com.levses", "RadoBart.gopher", "aishwarya18102000.starlight", "com.jasoncool.android.resources.change", "org.chiga17.MarkerPlugin", "com.madrapps.dagger-plugin", "com.xjke.core.apiAll", "com.valord577.mybatis-navigator", "color.scheme.GoDark theme", "org.ggz.PrettyJSON", "top.crossoverjie.opensource.sqlalchemy-transfer", "com.east.ioc", "com.virtuslab.git-machete", "net.sutanto.go_field_tag", "me.vnagy.intellijplugins.argo", "com.genuitec.codetogether", "com.cyl.idea.plugin.ConfigurationStopHook", "org.intellij.exposure.DslPreview", "com.joer.TalkBackPlugin", "org.eightnineplugins.XMLExtractor", "my_Plugin.LibraryCompare", "com.husker.weblaf.plugin", "commit-prefix-plugin", "com.xstudio.com.xstudio.plugin.idea", "fr.gab<PERSON><PERSON>lson.prisma.prisma-intellij-plugin", "org.arkivanov.mvikotlin.plugin.idea.timetravel", "io.conceptive.quarkus-intellij-plugin", "tarn.gradle.dependencies", "com.github.rogerhowell.JavaCodeBrowser", "net.makerbox.rgt", "dev.somethingspecific.SVGReactPlugin", "com.intellij.javafx", "com.wuyr.google_library_query", "com.vladislav.jsontopojo", "dev.ahmed<PERSON><PERSON>.nocopy.idea-plugin", "com.github.Xlopec.elm.time.travel", "com.xiaoyu.Json2Dart", "org.hedhman.pony.idea.pony-idea-plugin", "com.saturn.intellijPlugin.backgroundImageFromBing", "com.fromLab.idea_plugin", "com.xiaxiayige.fluter_assets_sync", "org.twdata.minimaltitles", "com.nowsprinting.intellij-mob", "io.galagutskiy.symbol-idea-plugin", "pl.consdata.cd-commons-sass-sorter", "maven-versions-updater-plugin", "com.wrq.rearranger", "org.rezane.IntelliJ-IDEA-test-run-plugin", "com.bfu.just-mvp-plugin", "com.touhidapps.column.align", "me.profiluefter.netbeansExporterPlugin", "com.legendmohe.plugin.pragmamark", "com.lizongying.abbreviation", "ru.sadv1r.ansible-vault-editor-idea-plugin", "org.gap.ijplugins.spring.idea-spring-tools", "cn.cloud.auto.restful.tool", "io.techtrails.intellij.prisma", "org.example.actinidia", "com.joaovanzuita.converge-theme", "com.mrcd.xrouter.navigator", "in.obvious.mobius.creator", "org.cyberline.openhab2", "io.arrow-kt.arrow", "com.biyusheng.plugins", "PbCompile.PbCompile", "com.wxibm333.ConvertToJsonParam", "io.github.cdgeass.mybatis", "it.corradodellorusso.devsearch", "com.brasstacks.angular_dart_generator", "com.learn.plugins", "i-views", "com.hupu.HupuComponentPlugins", "cn.robin.mgs.v5.theme", "com.oppo.plugins.JavaToBytecode", "com.koxudaxi.poetry", "org.jetbrains.search-everywhere-docs", "org.nemwiz.jira-commit-message", "org.jetbrains.visual.programming.ide", "info.loric.run", "com.pyfips.intellij.custominspections", "flowable-bpmn-visualizer", "me.lensvol.blackconnect", "io.unthrottled.themed-components", "today.mingyue.fnb", "io.github.stefansjs.flatbuffersplugin", "com.midnightgamer.reactnative.snippts", "net.zero9178.mbed-support", "com.protobuf.free.gen.plugin.ProtobufGenerator", "cn.xhuww.ADBHelper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cms.rendner.intellij.py-prefix-fstring-plugin", "org.yapal.world.commandlinelauncher", "com.bordercloud.sparql-plugin-intellij", "com.gitee.threefish.sqltoy.plugin.id", "io.jmix.studio", "com.maxifom.pyenv-integration", "com.github.dbDoc", "color.scheme.Relentless-Colors", "es.spockdatatable.idea", "de.chr33z.pycharm-vred-py", "com.github.timo_reymann.ansible_vault_integration", "com.jtschwartz.codeswap", "xndlnk.monokai", "com.wxibm333.easy-http-client", "io.github.whimthen.websocket", "top.berialsloth.lldbremote", "org.jige.FindRESTUrl", "riderblockjumper-rider", "com.brewingcoder.IntelliJMCPlugin", "com.blai30.lotus.theme", "com.evil.inc", "pl.sudo.imageviewer.python", "sututest01", "be.<PERSON>", "com.elliotwaite.godot_theme", "cn.sugarapp.plugin.yaml-sort-plugin", "com.jetbrains.lets-plot-intellij-plugin", "com.qingci.lazy", "deno", "com.againfly.plugin.timestamp.MainAction", "com.obroom.plugin.toolset", "com.dharmadeveloper.EyesSaver", "dev.harivignesh.dsl", "org.example.FormattedOutput", "com.intellij.lang.liquid", "me.artspb.idea.build.number.plugin", "com.uriyyo.pytest.pytest-plugin", "com.rmondjone.jsontotypescript", "com.intellij.swagger", "com.service.char.format", "com.github.gfjalar.intellij-wordspec-plugin", "com.base16.shiromm.shiromm.monokai", "com.xdl", "com.github.odinggg.parsemybatissql.ParseMybatisSql", "com.yz.flutter.code.check", "org.websync", "me.peterfaria.values", "com.jtschwartz.smartsort", "to-go-struct", "dev.boldizsar.zsolt.android-form-factor-toggler", "color.scheme.KorneiDontsov", "com.spidev.pubassist", "color.scheme.thermo", "chunlong.plugin.DubboInvokerPlugin", "color.scheme.Day Off", "color.scheme.Candykai", "viz.plugin.AndroidCommonPlugin", "wave.Go2ProtoPlugin", "wily.wily-pycharm", "com.carlosdurazo.pacman", "com.yz.android.injectview", "com.yamanyar.PegaRuleTools", "quickturnstudio.language.qml.QmlEditor", "intellij.indexing.shared", "com.voyager.plugin.se", "com.malvery.yaruDarkTheme", "com.mobidevelop.robovm.intellij", "cn.neday.excavator", "com.fz.strings.plugin.id", "com.jetbrains.plugins.gjslint", "com.jetbrains.plugins.jscs", "com.jetbrains.plugins.jslint", "com.ajaaibu.pitch.dark", "com.pandora.plugins.build_variant_selector", "sof-syncer", "dev.bookmarks.intellij.plugin", "com.khch.restful.tool", "com.gafner.giv", "StringUtils", "io.aesy.regex101", "cn.acooly.acooly-coder-plugin", "com.software.architecture.plugin", "me.d<PERSON>.go<PERSON>ps", "com.facebook.litho.intellij", "com.pluralsight.activity-insights-jetbrains", "ru.derekbrown.ideaavr", "com.dtyunxi.plugin.idea.x-maker", "org.jetbrains.markojs", "com.uriyyo.evaluate_async_code", "com.deflatedpickle.intellij.concurnas", "zdc.ideaPlugin.MavenManager", "me.kapien.vue-router-helper", "com.pvsstudio.dotnet", "com.blackfireweb.stryker", "color.scheme.GitHub Primer Dark", "com.alipay.android.plugin.mpaas.boost", "com.translate.mybatisLog", "com.github.jef.forest-night", "com.wiser.plugin.format", "hwang.gg.gitLazyEye", "com.firsttimeinforever.intellij.pdf.viewer.intellij-pdf-viewer", "per.wbh.TimeAutoFormat", "com.MyPlugin", "com.intellij.flex", "com.tnc.test.plugin.id3", "org.elasticsearch4idea", "com.tenxcloud.tenxcloud-toolkit", "io.notestash.notestash", "com.tiepy.nimatron", "org.jusecase.jte-intellij", "com.ccnode.codegenerator.MyBatisCodeHelperProMarketPlace", "pt.up.fe.specs.intellij.psiweaver", "com.github.serg-delft.hyperion", "com.gitee.threefish.idea.code.tool.plugin", "siosio.EmojiCommitLogViewer", "com.thtfpc.mybatis-log", "org.flosum.Flosum", "com.glensun.plugin.pluginTest4", "kancy.plugin.MybatisplusCodeGenerator.id", "com.reshiftsecurity.plugins.intellij", "com.bluewhale.mybatis.log.query.formatter", "com.github.adedayo.intellij.checkmate-plugin", "izhangzhihao.rainbow.fart", "com.furstenheim.more-actions", "io.notestash.notestashbasic", "com.vagrantini.ocd", "com.llt.mybatis.pro.max.plus", "dev.thull.polar", "org.ojplugins", "com.cyl.idea.plugin.OpenConsoleHtmlLinkByWebBrowser", "com.kongming.parent.snippet-doc", "edu.princeton.cs.lift.intellij", "com.zin4uk.query", "com.github.beansoftapp.visualgc.plugin", "com.wondernect.plugin.code-generator", "com.ctrip.market.plugin.ut-plugin", "com.sire.plugin.apk.search", "plugin.ymp.restful", "com.zanpf.crud.intellij-plugin", "com.codertainment.scrcpy", "jetbrains.team.auth", "com.deepspace.stash_notifications_plugin", "com.tfc.ulht.Plugin Drop Project", "com.kirito.plugintest.id", "com.qub1.sortpp", "com.llt.rainbow.fart", "br.com.zup.beagle-intellij-plugin", "org.exam.mysql-to-h2", "com.phrase.intellij", "com.jetbrains.plugins.asp", "com.intellij.play", "com.intellij.seam.pageflow", "com.intellij.seam.pages", "CFML Support", "com.intellij.seam", "com.intellij.jbpm", "com.intellij.tapestry", "com.intellij.gwt", "com.qalens.corr", "org.generator.python-code-generator-copy", "com.intellij.resin", "info.galliano.idea.pionPlugin.IdeaPionPlugin", "com.huq.dubbo-invoke", "com.windea.stellaris", "com.hubberspot.tools.json-java-convertor", "manjaro.epb", "color.scheme.Monokai Sublime Text 3", "pl.tigersoft.intellij.faker", "com.yangchenwen.sqlparamssetter", "manjaro.ptpb", "io.github.biezhi.plugins.gitmoji-unicode", "com.niclas-van-eyk.laravel-make-integration", "com.obroom.plugin.smartsearch", "com.tonyma.idea.plugins.id", "com.baojieearth.javadoc", "com.fomin.plugin", "com.tcnp.plugin", "MyBatisLogSqlPlugin", "com.dekonoplyov.KeymapNationalizer", "top.zhoukunqiang.sql.parser", "re.bizar.celestial", "engineering.b67.intellij-reek-plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com.liys.plugin.onclick", "com.shuntian.plugin.DiffToolSetPro", "com.pestphp.pest-intellij", "com.github.jadepeng.rainbowfart", "com.github.kindermax.intellijlets", "com.openfileplugin", "org.tbm98.flutter-riverpod-snippets", "org.jetbrains.plugins.template", "cn.codemao.middleware.idea", "com.cdk8s.plugin.antv-helper", "org.pydemia.theme.cobalt9", "tsystems.zpssbb.plugin.vaSegment", "myplugin.untitled", "io.github.markusmo3.BetterMnemonics", "com.github.artsiomch.JReader", "com.zzj.chineseCompletion", "com.jeppeman.globallydynamic", "gr.aueb.reactiveness", "com.badahori.creatures.plugins.intellij.agenteering", "com.sukaiyi.gitee-idea-plugin", "org.jetbrains.javafx-demo", "lekanich.eye-health", "justbucket.excludeComponentN", "com.caryguan.firstplugin.id", "com.zone.sweet.upload_here", "com.dimabdc.idea.php.phpunit", "com.cloudbees.idea.plugin", "com.github.odinggg.NewYapiUpload", "org.dusg.plugins.fixProjectModule", "org.testshift.testcube", "com.stardon.androiddrawable", "com.github.marktlzhai.mideaiotandroid", "com.codemr.professional", "com.github.hinaser.gfm-advanced", "generator-java-from-json", "com.telesoftas.ijplugin.gitconfigcommittemplate", "com.kalessil.idea.jsInspectionsUltimate", "org.github.pojo2proto", "com.sonatype.nexus-iq", "com.cyl.idea.plugin.RemoveNextLineNeedlessWhitespaces", "org.asciidoctor.intellij.asciidoclet", "cn.feelcode.jpa-sql-log", "au.com.glassechidna.luanalysis", "jenkins-debug-address-plugin", "com.liuyibo.plugin.classfinder", "ru.panteleev.bitrix-idea", "org.jetbrains.research.refactorinsight", "com.google.idea.gn", "clueqva.Decorate.me", "com.zzh.plugin.devicemonitor", "manjaro.mpb", "com.rahul.gqlformat", "com.bungabear.AndroidRunOnMultipleDevices", "com.embold.intellij", "com.github.marbor.shortcutsstats", "com.rain.I18nTool", "com.huage2580.leeks", "com.vnea.intellij.plugin.quickly-change-editor-font-size-preference", "com.github.beansoftapp.flutter.storm", "org.move.lang", "com.chesterccw.excelreader", "io.github.newhoo.restkit", "com.github.nthykier.debpkg", "manjaro.spb", "manjaro.zpb", "com.github.vljubovic.javaFxHelper", "com.vnea.intellij.plugin.move-cursor-to-class", "com.github.brpaz.jetbrains.plugin.vscodesnippets", "org.company.textblocktostring", "com.ddlab.tornado.codepublisher", "com.github.malpp.withers", "com.chylex.intellij.coloredicons", "com.shetline.ligatures-limited", "com.navercorp.openwhisk.intellij", "com.therdnotes.aws-lambda-deployer", "com.widlofapps.theme.spectre", "net.bialon.acmeTheme", "com.algo", "com.intellij.grazie.completion", "org.racket.lang", "com.linda.plugin.translate", "com.kylo.xml2kt", "com.xiaobing1024.laravel", "com.rspn.git", "io.undo.liverecorder.replay4j", "color.scheme.ASJ Dark", "com.github.shuntakeuch1.kotlinmybatisentitygenerator", "inigo.objectmothercreator", "com.mohak1712.code-snapshot", "icu.gxnb.Aaawesome", "com.godwin.kdocer", "aid.dev.jscp", "org.ktzhang.quip-plugin", "commit-cn-message-template", "com.common.collect.plugin.idea.box", "sonique.intellij.plugin", "com.github.shan-96.gitcustomcommit", "com.dethlex.numberconverter", "generatedsoft.mps.dbfp", "io.github.pnancke.gitTag4Intellij", "plugin.codedoc", "com.github.lanahra.modifierutils", "atom-one", "org.freeone.2ts4j", "me.bytebeats.mns", "com.yz.android.codecompletion", "me.ii.minimal-intellij", "com.github.andrewaylett.intellijlua", "moe.leer.moduledg", "my.rs.injection-assist", "io.github.will7200.plugins.casbin", "daehyun.kim.camelBar", "com.github.yikunz.tortoisegitcaller", "com.vk.kphpstorm", "com.zt.json-editor", "net.mamoe.kotlin-jvm-blocking-bridge", "com.genovich.idea.idegram", "miniApp", "jp.kitabatakep.intellij.plugins.PlantUMLStudio", "commit-template-check-plugin", "org.jetbrains.completion.full.line", "lafite", "generatedsoft.mps.xmlph", "de.fgalinski.typo3.extension.manager.id", "org.example.AccessibilityPlugin", "dev.aid.<PERSON><PERSON><PERSON>", "net.labymod.intellij.singlehotswap", "com.truqu.intellijtqformat", "com.github.affishaikh.kotlinbuildergenerator", "com.yineng.code.review", "com.crunch42.openapi", "com.github.tuchg.nonasciicodecompletionhelper", "avalonia-rider", "com.github.odinmillion.gdbessentials", "it.czerwinski.intellij.wavefront", "com.github.lurunze.mybatis.sql", "com.chesterccw.ExcelDiffer", "spring-generation-tools", "io.github.heldev.elasticindents", "com.jd.com.ins.qsm.EasyCamelQSM", "cn.com.autohome.tuan.plugin.intellij", "cz.bios.idea", "open-liberty.intellij", "Performance Toolbox", "com.deadsilly.blackbird.theme", "ASM Bytecode Viewer Support Kotlin", "cn.uniondrug.jx-doc", "org.example.kplug", "org.openpolicyagent.opa-idea-plugin", "dependency-updater-plugin", "com.wt.plugin.jenkins.apk_signer", "pthk.srthk.json-2-firebase", "com.intellij.gitlab", "com.blacknebula.TestCherry", "cn.vove7.plugins.internationalizationtranslation", "sam.tigrr.plugins.nomoreindex", "org.jetbrains.jumpToLine", "org.chienho.PondPlugin", "negationhighlighter-rider", "com.321zou.j2s", "pw.itr0.plugin.idea.idea-conventional-commit-kaba", "cn.youngthing.idea.plugin", "com.linuxauthority.plugin.darcula.black", "com.proteo.intellij.burnt", "com.roy.common.plugin.typescript", "ir.msdehghan.plugins.ansible", "com.pablotj.galician-spell-checker", "com.jetbrains.codeWithMe", "org.jonahhenriksson.ron", "com.cn.ric.plugin.generators", "com.felixzz.maven-project-version-plugin", "com.lgzarturo.the.lone.unique.coder.dark.theme", "com.github.goodforgod.dummymapper", "com.facebook.ktfmt_idea_plugin", "br.com.devsrsouza.kotlinbukkitapi.tooling", "com.your.ltc.flatocean.id", "com.qiyu.dbdoc.generator.id", "com.github.maketubo", "org.yona.intellij.plugin", "org.dblanovschi.DiscordPanel", "com.github.gitduckhq.intellij-plugin", "solarized", "com.lake.json2dart", "com.github.lonre.bucp", "com.wind4869.snippets", "com.dinhhuy258.tabnumber.tab-number", "com.muyun.intellij.plugin.background.image.rotation", "tp-keys", "xamlstyler.rider", "com.tencent.android.asyncstacktrace", "com.jetbrains.kmm", "ai.serenade.intellij", "io.github.huzunrong.git-open", "com.intellij.rider.mnemonics", "com.totwhy.fast.dp", "com.github.halflife3.table2java", "mdx.js", "com.zhixinhuixue.noteplugin", "com.diffblue.intellij.cover.plugin", "caster.io.My<PERSON>in", "com.enjoyable.testing", "GsonFormatPlus", "com.freeview.company.unique.plugin.ReadmeID", "com.dt.theme.vs", "com.simonalong.neo.coder", "color.scheme.Toy Box Colors", "nl.deschepers.laraveltinker", "com.yanoer.mybatis_log_analysis", "Code MiniMap", "com.zhixinhuixue.mark", "v4fire", "yuanqi.plugin.codetemplate", "com.gmike.visual-studio-2019-dark", "com.chenrl.plugin.todo", "com.github.xeonkryptos.eclipseprojectcreator", "Execution-History-Recorder", "dev.polek.adbwifi", "me.harol<PERSON><PERSON><PERSON>.Kotlin Data Class to SharedPreferences", "es.datastructur.61bsnaps", "com.cyl.idea.plugin.HighlightsConsoleAbsolutePath", "Breakpoint-Exporter-Importer", "com.cjamcu.getx-snippets", "org.altostra.designer", "com.netease.ms.intellij", "com.jetbrains.webstorm.web-assembly-plugin", "com.tikalk.chavrusa", "com.tyh.generateBeanBySql", "com.coding.as.zpush", "huan.huynh.provider_widget_generator", "com.intellij.grazie.gec", "com.github.cpdncristiano.getx_snippets", "com.github.beansoft.devkit.helper", "com.starcor.codecommit.template", "com.alexey-anufriev.scopes-manager-intellij-plugin", "org.leo.goanno", "unreal-link", "com.starzec.piotr", "com.puvn.GooooogleItPlugin", "com.kwai.dynamic.aar", "com.programtong.firstplugin.id", "net.fischa.hasteupload", "net.accelf.intellij.gmusic", "<PERSON>.te<PERSON><PERSON>", "uk.org.oliveira.virtual_folder_folding", "com.fxiaoke.lizc8407", "com.github.etkachev.nxwebstorm", "com.kalvan.gen", "com.cyl.idea.plugin.ExcludeFileAction", "org.example.idea-todo", "tk.labyrinth.apparatus.one", "com.github.btnewton.breakreminder", "com.programtong.plugins.programtong-DependenciesDownload-plugin", "io.rita-dsl.rita-lang", "Variable-History-Viewer", "com.github.yasukotelin.notelightjetbrains", "com.github.beansoft.reatnative.idea.free", "com.service.common.mlc", "person.jianglong.plugins.cs2java", "ru.paracells.cell.theme", "com.zongwu233.plugin.springboot-domain-helper", "com.burton.plugin.plugin-demo3", "com.pig4cloud.plugin.easycodex", "com.fundtool.plugin.id", "org.yilnz.intellij-runthismethod", "gorm-plugin", "com.stiltsoft.intellij.toolchain", "me.bytebeats.polyglot", "intellij.webp", "co.kodevincere.assetsmanager", "com.github.huifer.plugin.gorm-gen", "Git Message Util", "com.github.felixhaller.issuebranchcreator", "org.example.CompareToGenerator", "com.ymm.AddJvmParams", "com.panda.p3c.smartfox", "com.nbadal.ktlint", "pfe.esi.G<PERSON>al-Hammache.RAndroid", "francisco.ist.IntelliComment", "com.ikresimir.stacksearch", "Unit-Test-Coverage-Viewer", "com.cral.JSONKeyFinder", "org.aya.plugins.checksql", "com.broadcom.vsemanager4idea", "com.jetbrains.idea.safepush", "com.gionchat", "com.fdd.FddMvvmGenerator", "com.daveychu.mockk-generator", "com.haulmont.jpab", "com.yilnz.intellij.searchExamples", "com.piean.idea-plugin.coding-wizard", "com.felixzz.maven-dependency-properties-plugin", "org.jetbrains.macOsKeymap", "com.kablemonck.idea.plugins.GitTagFromCommitDialog", "com.kagof.pokeprogress", "com.github.braisdom.object-sql-intellij", "net.mamoe.mirai-console", "com.github.wu191287278.springmvc2swagger", "com.ibeetl.intellij.support", "ca.cammisuli.theme", "cn.xiaoheiban.go-zero", "com.github.iguissouma.nxconsole", "com.margush.ConstructionPaper", "com.google.ide-perf", "com.fun90.idea.patcher", "com.intellij.tide", "com.intellij.plugins.visualstudioformackeymap", "dev.hashnode.bas.the.inspector", "fish.payara.intellij", "com.procyk.maciej.tcgenerator", "com.cerensoftware.structure", "com.phodal.charj", "Momo Code Sec Inspector (Java)", "com.jdpay.android.JPLint", "org.xuxiake.ideaToolCollection", "com.caldonazzi.alessandro.uml", "com.github.milankinen.ideaicebergtheme", "com.github.filipwtf.filesize", "com.jetbrains.nim", "com.wang.vcs", "com.geekmake.plugin.id", "happy-commit", "com.zhaosq.book", "li.barlog.oceanic-primal", "ru.itrack.bitrix.templates", "com.youzan.mobile.savitar", "me.schoditsch.UltimateStackoverflow", "org.simple.enc", "in.bulma.snippet.intellij", "bazelbuild-rider", "com.github.asarco.huejetbrains", "com.github.intheclouddan.intellijpluginld", "com.boringcactus.fairyfloss", "com.bwdvolde.idea.propertiestranslation", "jddd", "reqid", "keipa.plug", "com.diffblue.intellij.cover.commercial", "edu.caltech.cms.intelliviz", "fastXstools", "com.github.max.hll.api-navigator", "com.jetbrains.inin", "io.gitlab.zlamalp.arc-theme-idea-dark", "de.markiewb.idea.externalrunconfiguration", "org.awesome.JsonBeanGenerator", "com.github.pavelsemenov.swaggerschemagenerator", "com.github.beansoft.spring.mybatis", "com.intellij.php.psalm", "com.intellij.php.tools.quality.phpstan", "de.greenblood.alsatian.webstorm.plugin.alsatian-webstorm-plugin", "com.github.ushiosan23.darktheme", "color.scheme.Pure (Dark)", "co.anbora.labs.firebase-syntax-highlighting", "com.dsazup.scanmode", "dev.simplix.tooling", "com.baijia.plugin.plugin-gradle-demo", "ru.avdim.unicorn", "ru.taptima.phalyfusion", "cn.com.lasong.plugin.idea", "com.person.develop.plugin", "tech.kinori.epsilon.grammars", "com.specificlanguages.buildnotifier", "csdn.tools", "me.khol.intellij.lowlighting", "com.kenshoo.pl-intellij-plugin", "com.zju3dv.python_alias_import", "fun.bugfix.fish.book-read", "com.strawhats.kenerator", "com.github.apaunov.kcoroutine", "me.bytebeats.jsonmstr", "b.braune<PERSON>.<PERSON><PERSON>-<PERSON>-<PERSON><PERSON><PERSON>-Theme", "ir.alizeyn.sonofman", "activiti-bpmn-visualizer", "com.ithema.firstplug.id", "com.ithema.taunt.plugin.id", "com.notebook.plugin.id", "com.github.grishberg.android.android-layout-inspector-plugin", "tech.alotof.catbreak", "com.mdrobnak.intellij-lalrpop", "com.codetime.intellij.plugin", "com.laravel_idea.tailwind", "org.example.sample", "com.hacknife.databindingobserable", "org.example.StreamExamples", "co.prodly.appops.release.devtools", "com.github.fengyuchenglun", "com.zdj.intellijplugin.generateconvert", "figma_overlays", "com.obroom.plugin.jpasql", "dev.nybroe.collector", "com.ashlikun.plugin.mvvmcreate.id", "1259", "com.github.inxilpro.intellijalpine", "com.yexiong.rainbow.rainbow-code", "com.imbanban.plugin.lan", "com.bitfly.platform.laoyoutiao", "edu.rit.cs.plcc.jetbrainsPlugin", "com.github.jsbeckr.tailwindidea", "net.postsharp.bitwise-rider", "com.intellj.plugin.SpringBootBannerPlugin", "com.smyachenkov.jsonpatchplugin", "org.example.anti-indulged", "com.cmgapps.intellij.proguard-retrace-unscambler", "org.intellij.sdk.forchecker", "com.github.googee.laravelgenerator", "cn.yh.sdk.plugin", "om.wuba.bsb", "com.taobao.intellij.plugin", "CodeTester-IDEA", "systems.fehn.intellijdirenv", "com.github.valich", "be.filip.git-prefix-plugin", "com.github.bentito.conbu", "ai.expert.studio", "ru.itrack.bitrix", "pronskiy.elephpant", "com.misset.OMT", "color.scheme.VisualStudio2019Dark", "org.example.IDE_Plugin", "pholanda.github.io", "com.github.pirocraft.challengingtimer", "color.scheme.Material Solarized Dark copy", "com.bloxbean.algodea", "cn.com.pism.batslog", "Aviator<PERSON>", "com.liuzhihang.doc-view", "com.equocredite.generateCompareTo", "com.github.frimtec.idea-import-control-plugin", "color.scheme.MakixScheme", "com.refactorings.ruby.RubyRefactorings", "com.sharedaka.idea.swagger-helper", "cn.point.cat.middleware.middleware.idea", "plus.ldl.plugin.first.id", "com.github.evgenyrsk.propertytyperevealer", "io.joy.umlDesigner", "com.intellij.tailwindcss", "com.xiyt.devhelper", "org.ressec.idea.plugin.I18nResourceBundleTranslator", "org.intellij.sdk.toolWindow", "com.github.godmoonlight.moonstyle", "com.ieasyclick.devtools", "com.0full.grilledFish", "com.zyhang.startup.core-intellij-plugin", "com.github.yungyu16.intellij.sqllogjoiner", "me.schlaubi.gradleupdater", "com.juliuscanute.mps.python", "com.github.sleepingraven.idea-setting-explorer", "com.qianxunc.hzero-ui", "com.github.cross-language-cpp.djinni-intellij-plugin", "io.github.mishkun.ideavimsneak", "io.aesy.yam<PERSON>t", "ru.poidem.intellij.plugins.poidem-generator", "com.github.comtihon.catcherintellijplugin", "com.jfarrelly.intellij.plugin.method.parameter.order.code.inspection", "com.nerd.vision", "com.deflatedpickle.smoothscroller", "com.retail.sorter", "Go To Implementation Plugin", "com.zl.gdg.plugin", "io.himcs.idea.thinkphp", "com.chimerapps.moorinspector", "com.laniax.intellijtribotplugin", "com.bchan84.intellij.devdocs", "ru.deeplink.plugin", "net.postsharp.split-sdks-rider", "com.kititeam.WifiDebug", "Execution-God-Recorder", "com.tencent.android.tpns.plugin", "org.fever.pypendency", "com.m1lk4fr3553r.intellij.vcscopy", "com.netease.cloudmusic.plugin.timefree", "color.scheme.Xcode-Dark", "Twitch Dark Theme", "cc.yyf.MarkDownNoteYYF", "fun_work.tools.bean_to_table.BeanToTable", "vip.okfood.liam.plugin.i18n", "com.solvesall.ledgerplugin", "com.sourceplusplus.sourcemarker", "com.ishow.plugin.mvvm", "org.example.AndroidTestPlugin", "eu.bolt.toolkit.intellij-plugin", "PMD-IDEA", "vip.okfood.liam.plugin.json", "com.github.danielramosacosta.rustier", "com.github.wiximo.intellij.plugin.intellij-plugin-java-builder-pattern", "com.oboom.plugin.camelcase", "club.nutsoft.Github3Theme", "dev.bas.bas-tools", "eu.bradan.purebasic.PureBasic", "com.github.fe3dback.intellijgoarchlint", "site.forgus.plugins.api-generator-plus", "com.crzsc.FlutterAssetsGenerator", "ru.meanmail.plugin.requirements-pro", "com.codebay.intellij-plugin", "terra.support", "com.github.davidenkoim.idnamessuggestingplugin", "com.obroom.plugin.regextool", "com.github.urm8.isortconnect", "com.github.llanc.CodeDictionary", "com.github.kaustubhpatange.composecolor", "com.top.mapperResolving", "com.thief.idea", "com.vermouthx.intellij-investor-dashboard", "io.github.codejunk1e.thrusters", "fish.payara.micro.intellij", "io.procker.codeshare", "new.eu.inmite.android.plugin.butterknifezelezny", "com.github.fsancheztemprano.typescriptfolding", "com.cookpad.astemplates", "com.pixelperfect.android-package-helper", "com.pixelperfect.gitlab-ci", "com.pixelperfect.circle-ci", "com.yang.babybus.plugin.UpdateSourceJar", "com.easylokal.stringResourceHelper", "dev.meanmail.plugin.nginx-intellij-plugin", "color.scheme.Cobalt Beanlee", "intellij-plugin.hitokoto.cn", "com.tal.json", "net.ralphpina.intellij.openapilinter", "com.joaovanzuita", "io.github.oldborn.atspot", "wxb.Global2Lua", "com.skynet.hujian", "robertpranjic.swetz", "com.everspring.plugin.swagger-tool", "com.jetbrains.intellij-structural-search-for-kotlin", "com.tyro.oss.pairing", "com.github.googee.tools", "org.example.realease-demo", "com.youzan.mobile.enjoy-as-plugin", "com.honvin", "com.github.zjb-it", "com.emberjs.experimental", "com.ydzs.mvpTool.plugin", "com.promyze.promyze_plugin", "in.imperialb", "com.imooc.firstplugin.id", "com.github.si9ma.codetimejetbrains", "com.github.xneffarion.intellijmodulesourcejoinplugin", "MinionsProgressBar", "ThundercatsProgressBar", "TeletubbiesProgressBar", "FluffyProgressBar", "dev.lanky<PERSON>.palenightintellijtheme", "com.strongmore.notebook.id", "pers.tommas.emacsflavor", "io.github.pelletier197.fixkture", "com.xiaxiayige.okflutter", "com.lx.plugin.database", "com.cobular.transprideprogressbar", "plantuml-parser", "com.intellij.CloudConfig", "https://github.com/codedrinker/console-chat", "org.gsdk.GsdkPlugin", "com.junli.android.snap", "com.pixelperfect.heroku", "com.caigua.mybatis_generator_pluing", "com.logicalclocks.hops-intellij", "org.anilmisirlioglu.keystroke.idea-keystroke-counter", "ru.neofusion.ExternalToolsTextReplace", "com.gustave.intellij.plugin.gustave-tools", "com.ztjy.plugin", "com.daiwenzh5.mybatis-sql-print", "org.webcat.IntellijPlugin", "com.github.exidcuter.dockerregistryexplorer", "com.google.mad-scorecard", "sg.bigo.mobile.android.incrementalbuildhook", "com.kobiton.plugin", "com.looper.plugin.id", "com.ray.file", "org.dmfs.intellij.unclutter", "dev.bogny.phpstorm.inheritdoc", "br.com.dynamiclight.android-master-tools", "net.vektah.codeglance2", "com.elliotwaite.cyberpunk_theme", "com.star.easy_generate", "com.github.cedricziel.ideaconcourse", "de.idetools.filename_template", "com.pr1st0n.cflint", "com.github.zhiiw.codinghelper", "com.github.alexander_topilskii.gradleplugin", "com.aaron.com.aaron.plugin.AaronTranslation", "spring-assistant-@valueToYml", "com.github.mateusz512.crxcleaner", "icu.jogeen.fishbook.id", "org.ressec.idea.plugin.resource-bundle-translator", "com.khashiar.sunrise", "com.netease.cloudmusic.asplugin.lint", "com.github.glennlefevere.stenciljswebcomponents", "com.github.elyspio.swaggercodegen", "com.github.janneri.innerbuildergeneratorintellijplugin", "io.adev.summer-plugin", "com.lovoio.investment.pervasive", "CakeConfigureCompletion", "ArkApiUpload", "io.github.ygun.dotbg", "com.biyusheng.github-plugin", "net.mrava.plugin.sdk.http", "io.github.isenninha.fd", "com.mengzz.FluentFormatter", "com.nilsenlabs.flavormatrix", "ws.logv.<PERSON><PERSON>", "org.misaka.plugins.webstorm-keymap", "de.docs_as_co.intellij.plugin.diagramsnet", "anaribeiro.ist.EcoAndroid", "com.bqt.test.plugin.BqtPlugin", "dev.meanmail.plugin.python-bytecode", "com.coasns.pax", "com.xiaocx.power", "org.intellij.intellij-protobuf-erlang", "com.isuwang.plugin", "cn.rino.plugin.createpatch", "com.ryd.idea.plugin.SingleTonX", "com.tsf.plugin.tsf-idea-plugin", "org.intellij.intellij-protobuf-id", "color.scheme.Tokyo Night", "com.eginnovations.android.studio.plugin.egAndroidStudioPlugin", "com.yanglx", "com.dengzii.plugin.rbk", "com.speacode.video", "com.asyncapi.plugin.idea", "cloud.agileframework.agile-generator-idea", "com.github.saurabh702.jenkinsfile.validator", "com.moilioncircle.intellij.any2dto", "cn.layne666.mypa<PERSON>er", "com.liuzhihang.toolkit.mybatis-jump", "com.jrmplugin.jrmplugin", "net.banterly.builderguidedcompletionplugin", "Background Image", "com.github.mikesafonov.jenkins-linter-idea-plugin", "io.joy.theme.asiimov", "net.sjrx.intellij.plugins.ansiblesupport", "io.terminus.I18nHelper", "com.mxn.terrier", "ru.curs.celesta.intellij", "com.github.aloxc.plugin.restplus", "org.cacticouncil.amphibian", "com.qbutton.Flashlight", "com.luneo7.junit4.intellij", "com.github.rokospa.native-query-polisher-plugin", "com.david.androidstring", "com.hjf.redis.manager.redis-manager", "color.scheme.Visual Studio Light", "color.scheme.Visual Studio Dark", "com.vermouthx.xcode-theme", "net.sf.opk.avro-schema-support", "net.cakebuild.cakerider", "org.whitesource.wss-pycharm-plugin", "org.whitesource.wss-webstorm-plugin", "org.norbye.tor.kdocformatter", "com.xander.plugin.jsontodart100", "com.xander.plugin.jsontodart1_1_0", "GsonFormat-Plus", "icu.jogeen.StopCoding.id", "com.dmitryshamin.blade", "wallaby.js", "com.github.aaa-aa.plugin-demo", "ch.repolevedavaj.projectenv.intellijplugin", "slinky.core.intellij", "com.kings.serializable java", "header", "com.clu.idea.utils.MyPojoToJson", "org.pps.development.githelper", "com.github.luomingxu.idea", "com.github.volkov.znavi", "com.noctumsempra.themes.espresso.lightgram", "ge.od<PERSON>i", "io.github.tbm98.flutter_generator_snippets", "color.scheme.CyberpunkUI", "com.github.aaaaa.notebook.id", "com.noctumsempra.themes.nescafe_spresso", "net.isyundong.YunDongTools-Capsules", "com.jd.dpl-idea-plugin", "github.mengzz.intellij-fluent-tool", "com.github.bisgardo.intellij.closebrace", "cn.bigcoder.plugin.objecthelper", "<PERSON><PERSON><PERSON><PERSON>", "site.kason.intellij-kalang", "dev.totallynotrobots.reflow.tools", "com.zhengrenzhe.github-drak-theme", "top.jdap.jdap-plugin", "ee.developest.gtm", "RenameRelatedTests-rider", "AutocompleteTestStrings-rider", "nsubstitutecomplete-rider", "com.dancheng.markbook.id", "com.liang.dva-hepler", "com.github.ooftf.androidmvvmgenerator", "cn.pfinal.club.pflayui.plugin", "InnerBuilder Continued", "DataBinding SetGat", "com.yonghui.httprunner.plugin.HttpRunnerEditor", "com.mayreh.jsr310-intellij-plugin", "org.oberdiah.classplane", "io.testaxis.intellijplugin", "com.smartapplications.stormsections", "com.github.bjansen.mintellij", "org.intellij.sdk.KunpengCompilerPlugin", "Kunpeng Foundation", "Kunpeng Library", "Kunpeng Porting Advisor", "bao.li.jun", "fr.readthedocs.rtb", "io.unthrottled.amii", "com.github.pflingstring.push_me_not", "arthas-hotswap", "gileli121.glasside.windows", "fr.nouvelle-techno.share-on-sharemycode-io", "com.liao.hello_book.plugin.id", "fasttest-intellij-plugin", "com.github.kkkiio.hub-coder", "org.invoker.jrebel_log_notify", "com.github.krjakbrjak.bazel", "com.jetbrains.fuzzysearch.fuzzysearch", "com.noctumsempra.themes.rainglow.remastered.megapack", "com.github.mutl3y.ansiblehelper", "com.github.madwareru.intellijronremix", "color.scheme.Xcode Midnight", "ch.ristin.icontract_hypothesis_pycharm.icontract-hypothesis-pycharm", "net.chakmidlot.jetbrains.bigquery.plugin", "cn.anseon.plugin.id", "GsonFormat-Support4.1", "org.antlr.jetbrains.sample", "at.ltw.hochkoenig.uf.uf-useraction-plugin", "pl.cmil.datasource-by-link", "it.stefan.babelnet-search-plugin", "com.yuanshuai.tool.idea.showdoc", "com.mocaris.plugin.myTools", "color.scheme.Material Palenight", "com.github.halvra.opencell", "com.bartoszadamczyk.glowing-darcula", "com.lsl.plugin", "com.tao.getx", "cn.techflower.editor", "com.redhat.devtools.intellij.kubernetes", "io.github.ricardormdev.clockifyplugin", "edu.postech.csed332.team3.markdowndoc", "com.luyao.beta", "com.aixuexi.plugin.download", "fluentvalidation-rider", "net.ntworld.nhat-phan.sentry-integration", "net.ntworld.nhat-phan.sentry-integration-community", "com.along.FindComponent", "com.michaelpauldev.elvis", "com.dancheng.crawl.process.id", "com.daie.jrelax.id", "net.corda.cdl.plugins.intellij", "com.oracle.coherence.coherence-idea", "com.github.motui.meican", "com.tramp.idea.plugin", "com.magento2utilities", "com.healingtjx.cold", "io.github.lmikoto.copy-dubbo-invoke", "eu.theblob42.idea.whichkey", "top.breezes.javabean2ddl.java-bean-2-ddl-idea-plugin", "com.snail.plugin.resupdate", "color.scheme.Galizur", "com.garawaa.unique.plugin.id", "cn.nucun.CodeChat", "com.daie.game.2048.id", "com.kn.diagrams.generator.generator", "es.chatcod", "com.seliote.MyBatis4II", "mr.pasta", "color.scheme.Immersive Dark", "intellij.ktor", "thief-pdf-idea", "com.we2cat.plugin", "com.redbastie.tailwind", "org.jetbrains.projector-plugin", "org.bilalkilic.kediatrhelper", "cn.sumile.sumilePlugin", "com.gitee.hperfect.idea-plugin", "org.example.ScriptToolPlugin", "com.jd.com.ins.qsm.StopCoding Lite", "*************", "io.github.lmikoto.dubbo-client", "cms.rendner.intellij.py-styled-dataframe-viewer", "color.scheme.Colorful Darcula", "com.github.aborn.webx", "com.jetbrains.evaluation.scratcher", "io.nimbly.json.Any2Json", "no.invarians.intellij.scaffolding", "com.mirkoalicastro.mavenversionrefactor", "dev.nocalhost.nocalhost-intellij-plugin", "com.github.hexffff0.plugin.egg", "snippets", "com.gloomyghost.crazycommit", "com.mesmer.plugin", "com.github.plaskowski.embeddedbrowserintellijplugin", "pl.iterators.kebs-intellij", "de.caluga.intellij.plugin.propertyEnums", "com.redhat.devtools.intellij.rsp", "robocorp.lsp.intellij", "com.code-scan.intellij", "tech.settler.bitrix_plugin", "ch.wellernet.intellij.plugins.xraycucumber", "optionals-intellij", "me.foreverigor.streamtips", "info.dogechain.doge_control_center", "com.github.davidsteinsland.postgresvault", "com.anatawa12.autoVisitor", "io.unthrottled.theme.randomizer", "dmit<PERSON>y.molchanov.commit-prefix-plugin", "Symlink Excluder", "com.cap.plugin.elasticsearch.client", "com.stackin.dev.intellij.plugin", "com.github.jonathanlocke.intellij.easymap.macos", "kr.co.finda.androidtemplate", "cn.com.servyou.ServyouTransform", "com.github.adamwojszczyk.sepiaTheme", "app.shaders.intellij.toy", "dev.monogon.cuelang", "io.github.aveenstra.run-anything", "color.scheme.Visualstudio Light Simple", "de.ohmesoftware.parcelablegenerator", "com.github.jtmelton.semgrepideaplugin", "pl.kamilpek.Capitalize", "com.github.plaskowski.findimmutablesusagesplugin", "com.intellij.grazie.pro", "org.intellij.sdk.MobTime", "com.baidu.chenminrui.plugin", "com.github.aleksandrsl.intellijbrowserslist", "com.xy.yaml", "io.k6.ide.plugin", "com.brandiico.jetbrains-nightfall-theme", "com.alf.plugin.QuickOpenFile", "com.pqixing.aex", "com.sp.idea.plugin.generator", "org.zhangwenqing.wdio.idea-run-wdio", "com.mg.idea.plugin.generator", "com.yu.plugin.convert.plugin", "com.yatoufang.hse.Xdoc", "org.jetbrains.android.jpsBuild", "com.oldhiccup.plugins.renpy", "com.wordpress.ltb.browse-tags-plugin", "com.github.ohadshiffer.plugintemplate", "ent.brainstorm.galizur.theme", "com.github.mwsmith3.adbtools", "org.intellij.sdk.codeInspection", "dev.lankydan.tranquil", "Redis", "com.wtyt.plugintest", "net.welights.jetbrainsplugin.cttm", "com.autohome.auto-gist", "dev.alis.os.aip-lint-plugin", "com.primeholding.rxbloc_generator_plugin", "com.noorts.toggler", "com.github.grishberg.android.yamp", "io.telereso.plugin", "com.MrJHao.plugin.dictionary", "dev.rasul.wrapcompose", "com.prolog.develop.plugin", "com.pepej.papi-core-plugin", "com.github.lppedd.idea-debugger-enhancer", "com.toidicode.php-cs-fixer", "org.elbcoding.genjutest", "se.piksel.intellij.themes.prpl", "daidodo.format-imports", "com.github.taraktikos.cucumberclojure", "ru.jf17.mql-plugin", "com.cap.plugin.rocketmq.client", "org.exportfile.Export File", "com.jianwudao.JavaBean2JSON", "com.redhat.devtools.intellij.telemetry", "me.shed<PERSON>el.architectury", "com.fctorial.api-linter-intellij", "org.easy.ideaplugin", "com.dengzii.plugin.fund", "com.jetbrains.restClient.legacy", "com.fuzy.find.in.path", "io.nais.nais-starter-intellij", "com.pixelperfect.bitrise-ci", "io.terminus.dev.erda.ErdaMySQLMigrationLint", "fluentassertions-rider", "com.darshan.ezylogin", "com.zzq.plugin.smart.comment", "com.github.zeke8402.intellijserenade", "com.github.kkkiio.protobuf.support", "Unit-Test-<PERSON><PERSON><PERSON>-Runner", "com.austin.professor.theme", "com.mohn93.advanced.json2dart.plugin", "com.github.MappingGeneratorIntellijPlugin", "net.bjonnh.intellij.filepermissionsplugin", "com.chivas.wipe", "com.zoulejiu.mybatis.smart.plugin", "com.autohome.idea.plugin.codereviewer", "io.github.srizzo.rspector", "com.github.kimdohun0104.kindaplugin", "io.github.srizzo.codebuddy.code-buddy-plugin", "com.github.vsay01.mvpsetup", "org.easysoc.diagrammer", "pt.ipleiria.OOPTable", "com.ruowen.EasyTools", "me.enaumov.plugins.salmon", "org.hack4impact.recommendations", "com.github.ice45571.piccompress", "git-commit-guide-idea-plugin", "ch.mristin.crosshair-pycharm", "com.stilesyu.www", "com.icoder0.groom", "com.intellij.resharper.Exceptional", "com.magic.plugin.getxgenerate", "YapiUploadCRC", "tamas_g_barna.whalelint", "com.autohome.idea.plugin.container", "com.neal.plugin.generator", "io.nimbly.tzatziki", "com.delfi.xmobile", "cn.hacker-tools.esatong", "com.github.grahamsmith.darttest", "org.example.Testspector", "com.github.danieldeng2.waccplugin", "org.jddj.karthus", "xyz.zono.plugin.InputSequenceAction", "com.github.pyltsin.sniffer", "color.scheme.Entropic", "com.github.zcg.love-your-eye-theme", "dev.ekvedaras.laravelquery", "com.github.r00bertos1.zzpjplugin", "org.vinhlt16.clean-archt-data-model", "com.github.kamildike.bigointellijplugin", "com.appiumFindByReplacer", "de.marhali.easyi18n", "Ara_ide", "com.js.JimUtils.failedDown.Cleaner", "org.example.MethodTracelong", "org.jetbrains.plugins.kotlin.jupyter", "com.ankitkaneri.gomock.GoMock", "com.mahee.butterknife", "com.mesdocteurs", "com.lianjia.ide", "com.xiaohe.codeplugin.unique.plugin.id", "panda.bar", "eu.ibagroup.formainframe", "net.berryh.pipe-table-formatter-intellij", "com.syntax.visualizer.plugin", "color.scheme.Hard Candy", "color.scheme.Harvest", "0537", "com.github.chocovon.debug-variable-extractor", "com.github.chengpohi", "io.edap.plugin.intellij.support", "Quick Fix", "com.hotdog.plugin", "com.cn.u2takey.plugin.go_mode", "com.fofund.jgt.jiyu-test", "com.outbina.pogo", "com.latskap", "io.github.az1a.reminder", "rocket_bunny-studio-plugin", "com.github.jnhyperion.hyperrobotframeworkplugin", "otp.lucky.OTPCodeGenerate", "com.jw.netpresenter.plugin.id", "com.shike.mvp_helper", "com.github.jensim.megamanipulator", "plugin.linearizability.violat", "com.bukalapak.sherlock", "com.github.zeke8402.intellijembark", "com.ivianuu.injekt", "com.github.surpsg.diffcoverage", "cn.gmlee.plugin.JasCode", "quickturnstudio.sdk.language.qss.QtStyleSheetsEditor", "cn.acy<PERSON>.MyTools", "com.jetbrains.rider.ezargs", "cn.wj.plugin.vcs", "com.v", "com.github.cmourglia.ijblockjumper", "com.c5inco.modifiers", "de.timo_reymann.intellij-mjml-support", "com.daimler.doc.auto-doc", "com.itunic.idea.mybatis-toolkit", "org.example.SelenidePlugin", "io.kanro.idea.plugin.protobuf", "com.github.rey5137.robot-runner-plugin", "org.lucien.health", "com.github.rafaelldi.tyeplugin", "com.kimen.plugin", "org.albertzeyer.IdeaResolveSymlinks", "com.github.zuminX.easy-swagger", "ArthasHotSwap", "hu.innovitech.javafitter", "org.example.TestGradle", "org.example.shields-snippets-intellij", "firsttimeinforever.gradlerunguttericons", "com.github.zweihui.vivacuttemplates", "org.kooni_boop.movefaster", "com.github.vilinfield.dust", "Earthsong", "com.github.duxa174.bitrix-stubs", "org.example.TestPlugin", "com.vk.idea", "pl.kamilpek.GenerateConstants", "ASM Bytecode Viewer Updated", "com.tsbe.ocamlj", "com.github.cwilper.debug-observables", "io.github.pelletier197.mockkator", "org.murtukov.css-to-jss", "PyShua", "com.lightrun.idea.plugin.saas.LightrunPlugin", "com.codesync", "org.mooselab.logging-observer", "dev.anvith.alphabet", "BinaryGen", "io.altra.tools.aem-intellij-plugin", "com.okki.gendartfile", "space_rider_nuget_credentialprovider", "by.msq.me", "io.github.seggan.slimefunaddonplugin.slimefun-addon-plugin", "com.github.menwhorust.tomorrownighttheme", "com.github.artemmotuznyi.resourcecompletion", "dev.lanky<PERSON>.fairyfloss", "org.outsparql.outSPARQL", "com.utopia-rise.godotjvmideaplugin", "varme.pw.oceanic-dark-theme", "com.joshdavies.fptheme", "com.github.jonathanlocke.intellij.easymapmac", "rocks.strawberry", "org.sourcejump", "com.github.chocovon.debug-variable-sl", "com.pawfa.bitbucket.integration.plugin", "com.bytedance.lynx", "io.unthrottled.amii.rider", "org.laim.plugin.generate.plant.uml", "com.anonymous.mvpgo.plugin", "org.example.CodeCompanion", "com.ytanikin.datasetnavigator", "com.github.sulir.runtimesearch", "kvision.project.wizard", "nl.jusx.pycharm.lineprofiler", "com.lz.LzGenerate", "org.jetbrains.compose.desktop.ide", "com.logmark.cron.tools.id", "chapter_reader", "lermitage.intellij.nightandday", "F9FrameWorkSupport", "com.github.ileasile.intellij", "com.github.tulv.templateplugin", "com.jdbclog.plugin.id", "net.allape.XFTP", "com.samjakob.askama-template-support", "com.stasmarkin.kineticscroll", "com.cedricziel.idea.typoscript", "com.vtradex.plugin.configurator", "info.fluffos.idea_bison", "com.LineJumperRedux", "cn.com.xylose.xylose", "com.pine.fast.plugin", "tech.stonks.flow", "com.github.mzdm.embedded_dartpad", "com.openbankproject.createconnector", "lermitage.intellij.extratci", "com.github.l0drex.intellijkdebreezetheme", "com.cheer", "TW-commit-message-helper-idea-plugin", "com.bj58.ideaPlugin", "com.coolplugintest.********************************", "com.github.jaqat.test-it-idea-plugin", "com.biyusheng.es", "ru.uchmag.PhpstormTools", "com.yuxuan66.super-log", "com.zongwu233.plugin.springboot-domain-helper-2019", "io.joy.kacha", "com.sdadas.jasypt", "com.aaronhu.plugins.java2json", "com.imiyou.firstplugin.id", "se.clau.yang", "ru.verkhovin.nonlatinlayoutshortcuts", "br.com.zup.beagle-template-intellij-plugin", "com.faendir.kotlin.autodsl.inspections", "se.ohol.driver", "org.example.valakasbar", "cn.cym.codetoolkit", "dev.fishr.just<PERSON><PERSON><PERSON><PERSON><PERSON>", "com.wang.bilibili.danmaku", "org.tera.plugins.Livy", "com.github.selcukguvel.viewasjson", "com.danielstudio.idea.plugin.btcprice", "color.scheme.POLY Oceanic 2021.2", "com.kun.FormatJSONForEntity", "com.autohome.buildapk", "com.bstera.gobatis", "com.fakerandroid.as.plugin", "AndCodeHelper", "com.bosszhip.template", "com.japplis.appletrunner.intellij", "com.xkcoding.idea.plugins", "xyz.dsvshx.upload-plugin", "org.brightify.hyperdrive.plugin", "com.ymm.ymm-git-commit-template", "com.danichvolk.plugins.advancedscreenshoter", "color.scheme.Darcula Red", "dev.cristianofromagio.wrap_html_attributes", "color.scheme.Tactual Studio", "LineShare-intellij-plugin", "com.github.eaghayi.clusterusages", "appland.appmap", "il.co.sysbind.intellij.moodledev", "org.example.lama-plugin", "ru.mse.lama-plugin", "com.github.qlonik.intellij.peggy", "com.zcy.plugins.CommonTools", "damiao.hr.<PERSON><PERSON>", "com.mirkoalicastro.gotogit", "gherkin-overview", "com.github.matquant14.cddarktheme", "net.bounceme.monkee.monarcula", "com.github.openmindculture.intellijcutepinklighttheme", "io.vacuum", "ch.abacus.importandbuild", "StackTraceWebSearch", "com.github.curious-odd-man.tee-output", "com.villains.intelij.plugin.newoceantheme", "com.aivin.havefun.firappupload", "eu.adrijaned.intellijvhdlplugin", "com.senjoeson.plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com.samtech.plugin.jpaentitygenerator.id", "com.google.assistant.plugin", "com.hungry.panda.template", "org.cirruslabs.intellij.plugins.starlark", "com.admiral.uniset-plugin", "org.kdb.inside.brains.KdbInsideBrains", "com.github.myibu.plugins.leetcode", "com.germaniumhq", "ussplugin", "com.github.zhizhulp.templateqdedu", "com.github.lkqm.mongodoc-gen", "org.mocaris.plugin.FlutterTools", "com.veezean.idea.plugin.codereviewer", "com.github.pvoid.androidbp", "cn.varsa.idea.eclipse.pde.partial", "cn.te0.flutter-getx-starter", "com.bobrusik.plugin.android_libraries_agent", "net.coderazzi.codeartifact_maven", "sexualreptilians.dragonprogress", "dev.mayankmkh.intellij.linear", "com.coasns.leetcode", "red.hxc.code-review-plugin", "dev.bmac.indexes-shared-perforce", "org.benish.importfixer", "net.coderazzi.aws_codeartifact_maven", "com.github.plugin.protoc", "com.github.beansoft.theme.battlefield", "org.xuxiake.RestfulTool_SpringBoot", "org.aj.cds", "io.foojay.discoidea", "me.jinghong.restful.toolkit", "com.your.zth.first.plugin.id", "com.github.xuchengen.leo.plug", "my.unique.company.with.unique.id.hexagonal.arch", "cookiecode-stepbuilder-plugin", "eu.bolt.boltservertools", "com.lukasbach.intellij.snippets.typescriptreact", "com.aravindparappil.reorderlinesbylength", "net.howyi.intellij-chicken-theme", "org.ca65", "dev.turingcomplete.intellij-gradle-utilities-plugin", "io.unthrottled.amii.android", "deno-dx", "org.xuxiake.backgroundImagePlus", "theme-oldirony-dark", "eu.udemx.BuildFinishNotification", "com.ichaoge", "fantom.codeowners", "com.github.tiste.responsiveintellijplugin", "io.intellij-sdk-thread-access", "net.thoughtmachine.please.plugin", "tabs.id", "com.agrotzsc", "org.brex.plugins.codeowners", "com.tao.provider", "com.ypsx.plugin.protox", "com.zhuvar.plugin.TBlock translator", "icu.windea.pls", "com.sdc.vault", "com.winning.tmts.plugin.winning_wxp_boot", "DataFormats", "com.arch.temp", "net.ossrs.ideasrs", "it.krzeminski.ycp.plugin.intellij", "com.boohee.plugin.translation2", "com.emonadeo.tanne", "ch.lightspots.it.intellij.plugin.generate.builder", "com.github.jisoo0817.hongbogtemplate", "no.hvl.tk.visualDebugger", "com.github.xeonkryptos.integration", "com.antlersoft.bbqforidea", "com.yyh.reverse.streverse", "gudqs7.github.io.doc-savior", "com.zhengfei.databasehelper", "plugin.test", "thkoeln.archilab.divekit", "com.madhavth.flutter_cubit_plugin", "com.github.borispristupa.onlynewwarningsplugin", "com.intellij.drools", "com.intellij.jboss.arquillian", "com.intellij.datagen", "com.intellij.helidon", "com.intellij.spring.webflow", "com.intellij.guice", "com.intellij.spring.osgi", "org.lame.tardis-extract", "com.github.ravaelamanov.intellijhttpclient", "org.squareup.cash.hermit.idea-plugin", "com.github.konevdmitry.VaDima", "architecture.guard.solution.code.review", "com.yyh.string.operator", "com.intellij.grpc", "com.intellij.microservices.ui", "com.vizor.mbuild.mbuild-intellij-plugin", "JSR45Plugin", "org.jcorbett.more-black", "com.virtuslab.codetale.intellij-plugin", "nl.makertim.jetbrains.casetoggler", "com.rxliuli.vite-jetbrains-plugin", "yxg.<PERSON>", "com.nanovms.ops", "org.example.githubpang.rossynt", "io.cronenbergworld.brainfuck", "vivid.money.elmslie.plugin", "org.onapsis.tabsInPresentation", "com.github.javaica.springer", "me.xfcy.idea.CopyThePathLegacy", "org.jetbrains.plugins.tinygo", "marklogic-plugin", "overshade-astra-theme", "com.goods.myhappy.boy", "com.alibaba.oneapi2.generator", "com.pvsstudio.clion", "com.smartbear.swaggerhub.intellij.plugin", "se.clau.intellij_msc", "com.github.seranth.restplugin", "com.github.hx.rubocopcompletion", "io.tempo.jetbrains-time-tracking", "io.codelingo.plugins.jetbrains", "com.kalessil.phpStorm.phpInspectionsUltimate-mp", "com.github.aqinn.ktxhelper", "org.intellij.qodana", "com.intellij.javaee.ejb", "intellij-pomodoro", "https://github.com/DUNNIK", "de.interaapps.pastefy.intellij", "com.github.umbreon22.inlayenumordinals", "com.tao.bloc", "enseidler", "com.github.mounthuaguo.monkeyking", "com.mrliuxia.doraemon", "com.xsonline.top", "com.code.p4ij", "com.wwtbnbw.comused", "color.scheme.Atom One Dark  Plus", "color.scheme.My One Dark", "com.hello2morrow.sonargraph.integrations.intellij", "dev.turingcomplete.intellijbytecodeplugin", "DartJsonGenerator", "pw.muffet.things.derevo", "com.fcbox.mockwebserver.plugin", "com.itcodebox.notebooks.id", "com.immomo.wink", "me.harold<PERSON>in.intellijbuildwebhooknotifier", "org.apache.openwhisk.intellij", "com.github.jiangxch.cocollect", "io.github.kings1990.FastRequest", "com.github.cleric.intellijkeymaphotreloaderplugin", "dev.jiby.intellij.svelte", "com.itcodebox.leewyatt.notebooks.id", "com.github.al-assad.intellij-plugin-open-in-os", "com.github.imorate.persiandigits", "com.xiaxiayige.plugin.methodsort", "com.neeeraj.eclipsetheme", "org.example.IntelliJ Rekoder Plugin", "com.github.project.block", "github.mengzz.annotation-tool", "io.openapiprocessor.intellij", "io.turntabl.profiler", "astra.toolkit", "com.kinglozzer.silverstripe", "com.efun.dev", "org.xuxiake.ideaToolCollection.castration", "cn.haojiyou.CodeGlance3", "com.github.minasploit.migrationfacilitator", "com.sachin.hotkeys", "com.translate.auto", "org.tmm.data-mapper", "com.boppy.jellybeans", "de.famst.DICOMVisualizerPlugin", "com.intellij.properties.bundle.editor", "intellij-awk", "ren.idea.thinkphp6helper", "org.whitesource.analysis.plugins", "com.bredogen.projectenv", "com.jetbrains.rider.testPlugin.testPlugin", "com.automation.iosccevaluator", "com.github.ufo22940268.openpr", "com.github.huntervang.remla", "com.jamesfchen.manager.modulemanager", "com.dreamteam.translator.translate-on-the-go", "org.vk.nocolor", "com.httpriestess.base16-tomorrow-dark", "com.axproject.axcookie", "lechuck.intellij-plugin.task", "com.sw.FastLayoutInspector", "org.jetbrains.plugins.outdated-deps", "com.github.pushpavel.autocp", "nl.bryanderidder.regexrenamefiles", "com.aiunng.prj.idea.plugin.date.convert", "com.testknight", "com.evgenysobko.diploma", "com.aspose.total.java.intellij.maven", "com.github.shuaiouke.redlibcommandfile", "com.github.bric3.excalidraw", "drhd.<PERSON><PERSON><PERSON><PERSON>", "com.intellij.appcode.kmm", "one.goranson.logboot", "org.example.CustomWordTranslator", "color.scheme.Eink", "com.mistj.codein", "com.logmark.mapper-struct.generator.id", "com.bloxbean.intelliada", "com.github.actualkwarter.yoshisislandprogressbar", "com.github.yusufugurozbek.testcontainers.port.updater", "cn.liaoxy.plugins", "commit-template-idea-plugin-personal", "<PERSON>", "com.easycr", "com.mbeddr.mpsutil.editor.querylist", "com.mbeddr.mpsutil.intentions", "com.mbeddr.mpsutil.modellisteners", "org.mtspark.MTJobSubmission", "com.mbeddr.mpsutil.projectview", "com.mbeddr.mpsutil.treenotations", "com.mbeddr.mpsutil.projectview.vcs", "de.q60.mps.libs", "de.itemis.mps.extensions.build", "de.itemis.mps.extensions.jackson", "org.modelix.model.api", "org.wx.web_publisher", "org.jetbrains.plugins.ninefix", "org.zig", "com.rebecca.flutter", "org.tiandongbo.duowan.LayoutInspectorPr", "cuke-entity-helper", "com.inject.plugin.InjectHelper", "org.example.heshen-git", "se.linerotech.linerotech", "de.q60.mps.collections.libs", "com.github.gillesmoris.intellifold", "uk.org.oliveira.vg", "cn.jgayb.fenix-plugin", "org.veppev.plugins.template", "com.huawei.kunpeng.devkit", "com.autonavi.tools.timetransform", "leetcode-editor-pro", "fun.mike.intellij-plugin", "coderead.IdeaPlugins.maven", "com.Mushroom.TouchSprite", "color.scheme.Iceberg / One Dark combo", "vivid.money.elmslie.plugin.codegenerator", "org.jetbrains.kotlin-js-inspection-pack-plugin", "by.mrvanish97.kbnsext.plugin", "com.plugin.frege", "com.zhmok.zhmok-plugin", "cn.jlynet.json2java", "com.samkortekaas.norminette", "org.hacybeyker.HackFast", "com.xhz.ImmersiveStyle", "org.whize.plugins.idea", "org.neodapps.plugin", "color.scheme.Visual Studio Default Light", "org.fairy.intellij", "com.aff.idea.plugin.chapter0-0-9", "org.aogavrilov.PastebinSender", "com.ixiongyu.Toolbox", "Scala-Macro-Tools Plugin", "com.bukowiecki.regdebug", "org.example.athens-api", "com.github.ssm_template", "cn.martina.tools.id", "dev.kotx", "com.yunke.middleman", "org.i18nally.jetbrainsideplugin", "com.github.soullesswu.myintellijplatformplugin", "com.xiaomi.dsg.dimenify", "com.huawei.hdn.apihi", "org.lukasj.idea.torquescript", "color.scheme.Visual Studio 2013 - Dark", "com.xxz.plugin.AutoAssistJson", "be.biggerbytes.exceptional.exceptional-intellij-plugin", "com.github.norbert515.detectiveintellij", "com.mallowigi.keymap", "org.czechgrouptranslation", "com.hd123.jetbrains.jira.workinghours", "com.zx.plugin", "org.misas.codesec.idea", "com.ibexa.dxp.plugin", "verify-rider", "com.kun.mybatis log convert", "grepper", "com.shuzijun.markdown-editor", "cn.eziolin.zhiwei4idea", "com.github.itsbp.textutils", "co.solidworx.idea.php.phake", "com.aiunng.prj.idea.plugin.sql.generate", "com.yaniv.formatter", "org.0x00b.plugins.gocomment", "de.platon42.m68k", "ru.vasilev.CucumberUnusedStepsPlugin", "com.sothis-baka.IDontHaveAPersonalPage.plugin.1", "com.jetbrains.naming-is-hard", "GenerateClassFile20210721", "com.biyusheng.xorm.plugins", "com.github.cetonek.tenebris", "com.sunsharing.plugin.idea.amoy", "com.github.madhoma.androidblank", "com.wtyt.lucky.idea.global-jvm-param", "com.github.lunakoly.quicklink", "ski.chrzanow.foldableprojectview", "com.github.atishikawa.emacsmacoskeymap", "com.mzl0101.packageBuild", "io.github.intellijnews", "servicestack-rider", "GenerateClassFile20210727", "SidePanelSwitcher", "com.daniel-espinoza.inline-error", "com.github.alexisgardin.intellijyamlsorter", "CleverTap", "org.774N.AutoScrollTheEditor", "com.github.pppurple.locate-opened-file", "by.overpass.TwineAndroidPlugin", "com.testvagrant.ekam-plugin", "com.github.aliasliao.codeurl", "icu.kyakya.open-in-apps", "HighlightBracketPair", "pibosak.SmartSearch", "io.buildman.Buildman", "app.pieces.plugins.jetbrains", "com.github.caijh.plugin.intelij.background.image", "ai.leinao.bitahub-idea-plugin", "com.leon.plugin.arouter", "cn.juhe.silk.plugin.id", "com.air.nc5dev.tool.plugin.nc5devtool", "org.bitnum.utils.code-generator", "intellij-coretex-plugin", "com.eslgaming.engineering.intellij-pants-plugin", "com.palantir.witchcraft.api.logging.idea", "com.haojiyou.CharAutoReplace", "com.dongfang.plugin.caf.helper", "com.pgyer.uploadapk.plugins.id", "com.lc.plugin", "com.github.passerr.idea.plugins", "org.bytecamp.program_repair.astor_plugin", "com.wangjin.Generate.EolinkerDoc", "org.jetbrains.toolbox-enterprise-client", "com.k.pmpstudy.OpenedFilesDiffShortcut", "net.earthcomputer.classfileindexer", "com.lc.note.mb.id", "com.wangfengxi1.jd.easynote.id", "com.github.cnfn.refactor-negation-boolean", "app.netlify.joaovanzuita/", "com.github.knothhe.intellijviewas", "com.developtools.api-doc", "com.paulmethfessel.intellij-keyword-info-kotlin", "com.zach.plugin.spark.sql", "org.vepanimas.uml.javascript", "com.github.antonsimola.translocowebstormplugin", "com.rahulrav.baseline-profiles", "com.github.yinabameguru.gradlecleansnapshotcache", "com.cqucc.lijing.demo", "org.jboss.tools.intellij.mta", "com.jtschwartz.proxytoggle", "com.justai.jaicf.plugin", "com.github.meimingle.tsvnpwdintellij", "com.bukowiecki.breakpoint.manager", "dev.feedforward.tldrintellij", "color.scheme.Visual Studio Code Default Color Scheme", "com.eversql.plugins.jetbrains", "com.github.goldsubmarine.restfulhelper", "io.xmake", "cc.gmem.darkubuntutheme", "com.github.dogsunny.bpehelper", "CleverTap Plugin", "com.intellij.php.codeigniter3", "com.github.patrickmilnes.freeze", "com.github.brandtjo.releasescripthelper", "foundation", "com.skl.java.code.generate.plugin.id", "io.tarantool", "com.millennialmedia.intellibot@lte2000", "yapix", "de.iserv.iserv-intellij-plugin", "TimeStamp", "work.ezura.intellij-plugin-translator", "com.hjx.maven.plugin", "com.napalmpapalam.napalmpapalam.theme", "io.github.danthe1st.ij2gdocs", "edu.msu.cbowen.cse335intellijplugin", "nz.hailwood.inertiajs", "com.github.xanclry.swaggerui", "com.hz.ypzj.djy.easy.fbi", "org.sourcelab.intellij.plugin.PHPDataObjectGenerator", "com.joker.serial_number", "np.com.susanthapa.ModuleLoader", "com.github.jrd77.codecheck.id", "com.zp.idea-redis-client", "com.bytedance.tools.codelocator", "com.github.nicsilver.jumpertest", "com.github.enzdev.ideangxtranslateautocomplete", "tanvd.paddle", "org.ice1000.kala", "com.k.pmpstudy.RenameFilesRefactorBatch", "org.mallowigi.idea.MaterialThemeUI.HighContrast", "com.madou.bg.boom", "Keep-Unit-Test-Running", "GrpcWithGraphql", "color.scheme.EclipseDarkLike", "com.cadeeper.plugins.mqtt-client-plugin", "com.haulmont.rcb", "cn.ifget.plugin.landlords", "com.jetbrains.rider.plugins.statiq", "com.cornflower.plugin.superspeed", "org.example.spearmint_theme", "com.plugin.uniapp", "com.samkortekaas.codam.header", "de.DuckAndPlatypus.ErrorPrinterPlugin", "org.parchmentmc.scribe", "net.brendamour.changelog-generator-intellij", "com.jogeen.pulgin.boringball.id", "net.twilightcity.flow", "com.gredicer.FindActivity", "eu.andret.ats.companion.idea", "RocB", "com.wuyr.intellijmediaplayer", "com.github.kcrebound.smenafluttersnippets", "com.ecosystem.intellij.plugin", "com.cap.plugin.basic.nosql", "com.github.beansoft.iscratch", "com.tang.ideaplugin", "com.your.company.unique.plugin.AttackflowSCA", "com.zhu.intellij.plugin.dubbo_test_code", "com.zhu.intellij.plugin.dubbo_test", "com.github.evgenys91.machinet", "com.bukowiecki.weevil", "com.godfather1103.intellijPlugin", "com.itcodebox.fxtools.id", "io.github.iisimpler.helper.id", "com.jetbrains.php.architecture", "org.sparib.ftcConvenience", "deaglegross.coachsharp", "com.github.enokiy.go-struct-to-json-intellij-plugin", "com.qiniu.phoenix.plugin", "com.hand.plugin.hzero-tools", "plugin.mx.restful", "org.jetbrains.uncrustify", "com.github.frezamirul.loadtopico", "dikt-idea-plugin", "net.doublegsoft.appbase.intellij", "commit-SFtemplate-idea-plugin", "org.HSA.Code Annotation Tool", "com.yujunyang.intellij.plugin.sonar", "com.hand.sctc.cux-easy-code-id", "com.jetbrains.rider.plugins.argumentslinecomplete", "ro.mingle.mingletoolkit", "me.uyt.build.variant.selector", "org.xparser.xparser_plugin", "com.github.intellij.ginkgo", "JavaScriptDebugger", "tslint", "io.moderne", "io.github.mishkun.ataman-plugin", "commit.comment.plugin.zh_CN", "com.reactpreview.intellij.plugin", "com.lindychan.pleasestandup.id", "com.github.balintrudas.avro-random-generator", "intellij-crystal-lang", "com.marcherdiego.events.navigator", "com.lx.xml2excel.conversion.action.Xml2ExcelBatchConversionTool", "Lombook Plugin-uxlog", "org.maxuhui.maxuhui-plugin", "com.yandex.appmetrica.intellij", "com.fred.shen.allbymyself", "com.ningenqi", "com.mzyupc.a-redis", "fr.decam.percenttohexcolor", "com.ritesh.intellij.plugin.reviewboard.wy", "su.mingchang137.commander-of-leek", "com.farm.coding.memo", "com.oplus.ocs.plugin", "me.mbolotov.json.schema.generator", "com.github.bahamondev.errorlens", "com.yanncebron.intellipikchr", "com.tko.androidsearchit", "io.github.freeApplications.intellij-plugins", "io.github.huzunrong.project-switcher", "de.shyim.shopware6", "io.github.jebeaudet", "edu.vub.soft.at", "Yamato-Daiwa ES-Extensions", "com.lemley.idea", "ZsmpDoc", "edu.vanderbilt.grader.tools", "com.duartbreedt.movementprogressbars", "com.github.spy.sea.sea-intellij-plugin", "com.by-zxy.plugins.appFileConverter", "com.weiyi.translation.plugin.id", "com.intellij.java.rareRefactorings", "org.jetbrains.accessibilityplugin", "org.crev", "com.ying.cmic.HttpStringInspection", "sf.dev.plugin.SoftFanDevelopmentPlugin", "ide.writers", "com.intellij.apiCompatibilityRepairer", "com.jetbrains.flora", "color.scheme.Gruvbox Material", "com.checkmarx.checkmarx-ast-jetbrains-plugin", "com.github.tomsfernandez.jsonld", "Yamato-Daiwa Frontend", "com.dtyunxi.xtoolkit", "com.github.ma349432587.jdosautodeploy", "dev.khbd.lens4j-intellij-plugin", "com.ynet.android.plugin.id", "com.jetbrains.rider.plugins.xpand", "com.raysono.intellij.magnolia", "com.cap.plugin.basic", "com.cap.plugin.elasticsearch", "com.lansoft.custom_mybatis_plugin", "com.znb.sqsdkplugin", "com.linden.lumio-theme", "com.agenthun.intellij-astock-plugin", "cn.huoxian.dongtai.plugin", "com.lijianxin.layoutinspectorv2", "com.voc.ide.plugin.tools", "com.rubenbermejoromero.sonicprogressbar", "cn.boz.jb.plugin.spd-editor", "com.lucas.LangTransExcel", "ru.skb.lab.intellij.plugins.skb-lab-tools", "com.yanncebron.m68kplugin", "com.stenalpjolly.tfcv", "cn.crec.codetool", "com.github.copilot", "argument.twins.com.kDocAdvancedGenerator", "com.github.ilyakolomin.statidea", "com.github.stengerh.intellij.foobar2000.titleformat", "com.github.arm092.apricodemonokai", "com.volcengine.MarsPlugin", "commitlint", "org.swim.recon", "dev.flikas.idea.spring.boot.assistant.plugin", "org.01pawn.rainbow-variable", "io.cygert.theme", "com.github.travistx.accessorize", "cloud.skadi.gist", "orntitled", "dev.hashnode.bas.insomniac", "com.github.kawamataryo.copygithublink", "YapiUploadPlugin", "com.jetbrains.rider.plugins.localizationshow", "color.scheme.TestClub VCS Dark", "cadence-language.cadence-for-intellij-platform", "d-avko.npm-dependencies-bump", "me.awildb<PERSON><PERSON>.theme-roulette", "org.cafeboy.idea.plugin.codeit", "com.cap.plugin.redis", "it.auties.reified-plugin", "com.yazantarifi.Viper", "com.sofast.cloud.plugin.feignSdkGen", "org.gjs", "com.tiger.tiger-doc", "com.akamai.edgeworkers-intellij", "io.github.facilityapi", "com.github.wenzewoo.jetbrains.plugin.jetbrains-code-remark", "com.alibabacloud.intellij.cosy", "com.github.nthily", "com.github.bozsadam.tabasclass", "io.github.homeant.guava-event-bus-idea", "ask.me.again.shortcut.additions", "simd", "lermitage.intellij.worldclock", "cn.codekong.Video2GifPlugin", "WJHarry.TortoiseGitEasy", "com.huawei.kunpeng.hyper.tuner", "net.techminded.npmania-plugin", "kotlin.fir.ide.development", "EDN-JSON Converter", "co.giammar.kyle.shadesmear", "com.github.aider.angularjsfixdependency", "com.github.th7mo.themeo", "com.alibaba.dc.deliverysupport.park", "com.capital.cloud.liufuqiang", "camunda-bpmn-visualizer", "com.yapi.generator.qiyun.YApiImporter", "com.alibaba.dc.deliverysupport.park.202", "org.elementaryteam.elementarypluginintellij", "org.intellij.askjarvis", "org.corium.CoriumIntelliJPlugin", "com.github.beansoft.jadx.gui.idea", "com.netflix.graphql.dgs.intellijplugin", "com.github.mholzer85.generateobjectid", "com.xiaxiayige.android.parcelablegetset", "com.aiunng.prj.idea.plugin.string.convert", "com.bx.DataExportX", "com.hiyunhong.gradlekiller", "Endpoints Explorer", "com.javiersc.intellij.theme", "TheBlind.privateNotes", "color.scheme.<PERSON>cy <PERSON>", "com.martin.plugin.Synthetic2ViewBinding", "io.tripled.idea.editor-sync", "color.scheme.<PERSON><PERSON> dark", "com.github.dogsunny.beetlsqlidea", "dev.hitools.plugin.android.template", "com.lwi.SqlLogsPlugin", "com.starxg.mybatis-log-plugin-free", "com.blueguagua.idea.first.plugin.id", "com.moyu.moyu", "com.Sensorjang.plugin.Code2QRcode.id", "color.scheme.Paedda_dark", "org.byted.easytest", "com.ddd.spec.com.check.ddd-spec-check", "color.scheme.Material Darker", "org.example.Showpop", "com.titicaca.idea.spring.assistant", "org.typedb.typeql.plugin.jetbrains", "com.ast.devmate.intellij", "nl.swis.Dr<PERSON>al-IDE-Plugin", "com.github.stykalin.ToggleOracleUUID", "sql-json-to-go-struct", "com.github.selcukguvel.fastimport", "com.github.pberdnik.dependenciesanalyzerplugin", "com.fasten.legado.read", "com.shopee.repo.listeners.BuildVariantShowListener", "org.cpanmac.mybatis-log-format", "com.easternenterprise.decor", "tech.pantheon.yanginator", "com.github.smashedtoatoms.zenburn", "com.github.bashspbu.intellijjavacodealigner", "com.dynamic.qqzj", "fr.godox.FxIdClipboarder", "com.jetbrains.rider.plugins.converttoxelement", "AsyncA<PERSON>.AsyncApostle", "com.tradetested.quarkus-intellij-plugin", "com.java256.typingCat", "org.jetbrains.advocates.rider.plugins.dotnetwatch", "com.alibaba.dc.deliverysupport.park.2019", "uk.co.droidinactu.ArcTestDocPlugin", "com.wsy.smartndkstack", "io.github.newhoo.restkit.ext.jax-rs", "com.javidasgarov.unicodifier", "com.github.qianmi", "com.gigadevice.srm.ide.plugin.gd-support", "com.rangaofei.UrlTool", "org.hq.data_plugins", "ir.mmd.intellijDev.Actionable", "nl.jrdie.idea.springql", "flutter.bloc.generator.flutter_bloc_generator", "com.mayreh.tlaplus-intellij-plugin", "dev.<PERSON><PERSON>", "com.github.shoothzj.visible-test-verify-plugin", "net.lihui.app.plugin.thoughtworkscodereviewtools", "io.github.mishkun.unsafe-wilhelm-scream", "com.dongbao.dhgAuthorityPlugin", "org.seve.seve-plugin", "fr.phpierre.axelordevtools", "co.anbora.labs.todo.export", "com.twl.lib.kubit.plugin", "com.tw.plugin.clover", "com.github.cong1223.intellijpluginyapi2ts", "AMP Helper", "intellij-extract-css", "cn.zzh.mongodb-schema-autogeneration", "com.intellij.clion-gradle", "com.hayapenguin.42-header", "com.github.zhangchaojiong.RocketXPlugin.ide", "cloud.shentao.mybatis-plus-extend", "com.nickescobedo.view-on-packagist.View on Packagist", "com.cnsky1103.A-SOUL-<PERSON><PERSON>er", "com.cnf271.issue.id.in.commit.verifier", "com.jetbrains.rider.plugins.notautomapper", "org.zimuwse.idea.converter", "JpaExtraX Plugin", "co.coscreen.coscreen-intellij-integration", "com.javidasgarov.controllerFinder", "com.github.thencuber.intellijlinterplugin", "com.github.shinichy.integrant", "com.trianguloy.collapseclosingtags", "com.github.tobi812.sprykerplugin", "com.coolplugintest.3cnba987fdgi98se37dabd71c361c8", "com.mininglamp.km.nebula-generator", "com.wyz.wyz.idea.plugin.mybatislogtosql", "net.earthcomputer.quiltflowerintellij", "com.github.hanlin19900610.lnandroidmvvmgenerator", "com.github.pratclot.syntheticstoviewbinding", "com.softtech.capera.plugin", "com.speacode.video.organization", "com.github.dmi3dmi3.controlflowhighlighter", "JavaApiDocs Plugin", "com.samuraism.plugins.kasumi", "Test", "team.CodownBook", "com.meowool.sweekt.ide", "color.scheme.CyberpunkColorScheme", "top.walterInKitchen.gitDiffShort", "org.ziglang.jb", "com.kingron.coderobot", "com.goswivt.clean_architecture_plugin", "ai.vespa", "icu.windea.starboundText", "io.github.mudongjing.InjectClass.plugin.id", "com.codingmates.ghidra", "com.github.victorrentea.slf4jplugin", "yy-dev-tools", "com.github.danbai225.pwlchat", "little-sweet-sweet-plugins", "com.lizhi.translation-ios-flutter-plugin", "org.metersphere.Metersphere", "LuaLegacy", "ru.elementaryteam.elementarypluginintellij", "com.github.mbolotov.playwrightintellij", "lclang.plugin", "com.leinardi.pycharm.mypy.experimental", "com.poplzp.plugin.clion.singlefileexecutionplugin", "vn.com.extremevn.evpg", "tomoki1207", "com.github.brcosta.cljstuffplugin", "wl-mybatis-generator", "WGSL", "test.my.first.testplugin", "nikolay2022.idea.plugin.notification", "mockitools", "jiux.net.plugin.restful.toolkit", "org.darkrabbit.supermarioscroll", "com.bambi.sqlmanager", "me.imaskeleton.jetblack", "com.chriscarini.jetbrains.sample-intellij-plugin", "com.pyfox.intellij.aonji", "dev.khbd.interp4j-intellij-plugin", "com.star.truffle.plugin", "plugin.mx.tools", "AMP Workbench", "com.github.soarex16.doccommentrepl", "cothema.react-quick-switch", "com.chriscarini.jetbrains.iris-jetbrains-plugin", "web-facet-importer", "eu.oakroot.go-imports-tidy", "com.github.jmorjsm.rosepineintellij", "top.xinsin.timer", "com.caiqichang.browser", "com.github.pshirshov.bytecodeeditor.xenoamess", "color.scheme.Eclipse Classic Dark", "color.scheme.Eclipse Classic Light", "me.seclerp.rider.plugins.efcore", "com.andy.wang", "com.pojul.androidstudio.library.manager", "com-cs-org", "com.zanon.android.adb.adb-plugin", "org.example.facto-plugin", "com.ast.testing.SignTest", "xyz.nature.code-auto-gen", "com.xbb", "com.devemks.android_source_viewer", "com.github.barfurth.jb.plastic", "com.dhiwise.pxtodp", "com.billy.dubbo-box", "com.vivo.mobile.vsm.id", "com.huifu.dougong", "com.dubreuia.pih", "dev.xframe.protoc4j", "TabSwitchPlus", "nl.underkoen.jetbrains.adventofcode", "com.alibaba.compileflow.designer", "com.coolplugintest.3cb83784f7dabd71c4f57b364c8", "dev.xframe.foldercompact", "me.rochblondiaux.headers", "com.dprint.intellij.plugin", "com.drgos.greenly", "org.mvnsearch.jetbrains.plugins.rsocket-jetbrains-plugin", "com.pluto.pagent", "com.vivo.mobile.vsm.test.id", "com.billy.dubbo-box.shop", "elasticsearch-admin", "yondervision.wish.tcnp", "com.turbinekreuzberg.extendInPyz", "org.example.flutterImageTools", "me.him188.kotlin-dynamic-delegation", "vedro", "person.ronie.plugins.quickRun", "brq.intellij.plugins.confrunner", "com.chimerapps.tools.storage-inspector-plugin", "com.github.valbendan.dtp", "color.scheme.OneDark Reborn", "com.github.mreram.bazaartemplateplugin", "com.jetbrains.rider.plugins.mvvminspections", "org.metaisbeta.plugins.asniffer", "com.starfish.jsonformat", "com.intellij.csharpier", "cn.xmirror.sca.xcheck", "me.harol<PERSON><PERSON><PERSON>.intellijkotlinbulkaddnameparams", "com.libra.plugin.JavaGenerate", "com.github.quodai.jb-plugin", "com.github.binarybeing.idea.markdown.linker", "io.karma.kromatic", "dev.jbang.intellij.JBangPlugin", "com.github.fawaz.flutter-freezed-snippets", "com.leeyom.app.searcher", "com.intellij.microservices.debugger", "de.jensklingenberg.htmltocfw", "cn.bugstack.idea.plugin.vo2dto", "YapiUploadPlus", "org.rookie.plugins.BeanMappingKey", "org.zhou.plugins", "com.nojaf.rider.plugins.fantomas", "com.github.jonatha1983.queryflag", "unicorn.bar", "com.dbtool.id", "com.murphysec.intellij.Plugin", "com.angelscript-intellij", "com.smzdm.core.git-cluster-plugin", "org.vitalii.vorobii.st-js-helper", "com.shz.anonymous-progress-bar", "dev.meanmail.plugin.nginx-intellij-plugin-pro", "com.chip.c2u", "dev.xframe.HierarchyAdapter", "co.anbora.labs.kse", "com.github.fisherman08.Idea-WebSocket", "alipay.xunjian.cloud", "co.bito.bito-intellij", "com.linuxgods.kreiger.idea.statusbar.filename", "StringManipulationLocalization", "com.zhangyangyi.plugin.LogStatementGenerator", "su.gov.<PERSON>er", "com.meetinclass.dslincolor", "org.example.angular-combined-view-plugin", "com.sitischu.kuro", "com.zhiya.idl.plugin.idl_generation", "com.github.yue7872.webstormplugin", "org.webapp.plugins-layerfile", "com.specktro.darktheme", "ca.nosuchcompany.rider.plugins.mediatr", "co.com.bancolombia", "com.zhangyangyi.plugin.CodeGenerator", "com.xzq.ormCodeGenerator", "com.liys.plugin.bindingClick", "com.ljqc.fosseye-intellij-plugin", "Gitflow-Fix", "com.github.ragurney.spotless", "com.cnoke.changefile.name", "com.thelumiereguy.compose_helper", "xyz.kwin.yapi", "org.example.ReminderPlugin", "com.github.lvlifeng.githelper", "com.rightpoint.MagentoFactory", "nl.mranderson.ns-progress", "uwu.nothingbutyou.idea-cfr-plugin", "com.hmetao.simple-select-search", "com.issac.plugins.qa.tools", "cn.rino.plugin.patchtool", "com.study.plugin.translate", "org.jetbrains.security.package-checker", "com.github.serverfrog.bitburnerplugin", "com.wxh.isoftstone.markbook.id", "com.devesis.romeo-theme", "zookeeper-admin", "kk-android-template-plugin", "cn.mama.pregnant", "de.joachimsohn.CDS-Language-Support", "de.eudaemon.ideaswag", "org.jetbrains.corda", "com.idea-aedi.plugin.fast-coding", "com.showyourwork.show_your_work", "com.showyourworkdev.show_your_work_dev", "com.cadeeper.plugins.api-runner", "color.scheme.Kaj", "org.strangeway.msa", "info.com.hochet.k8sDebugger", "com.github.jsmzr.cryptotool", "com.github.voml.neo_theme", "com.intellij.zh-tw", "com.itao.linux-helper", "com.quickref.intellij-plugin", "com.alibaba.lizihou.generate.convertor", "org.example.FCompGenerator", "com.github.voml.jss_intellij", "com.study.plugin.sedentaryreminder", "dev.ithurts.idea-integration", "com.fersoft.apigee-proxy-plugin", "io.github.rxf113", "com.previewjs.intellij.plugin", "com.mesour.intellij.neon", "ru.reksoft.sql-sorter", "com.mybatis.smart.pro.plugin", "color.scheme.Sense", "ai.coderefactor", "com.sy.code.MybatisEntityMapping.id", "com.ciii.bob.plugin.android.adbwifi", "com.gabriel.lopez", "fortify.securityAssistant.security-assistant-intellij", "com.wasdjkl.pushfile", "androidx.compose.plugins.idea", "com.github.intellij.plugins.mt4ij", "com.gabriel.lopez.barprogress", "com.github.arissa34.jnigenplugin", "me.seclerp.rider.plugins.monogame", "FluentLanguage", "brq.intellij.plugins.commit-checklist", "net.plpgsql.ideadebugger", "github.com.cq1228.JCode5", "com.idconfict.idconfict", "com.lazyaudio.plugin", "io.creams.creams-feign-switch", "com.fluxparticle.xsdvisualizer", "com.gabriel.lopez.motosport", "com.nils-degroot.srcery-theme", "com.dong.plugin.id", "com.hi.template", "com.intellij.queryComplexity", "net.coderline.rider.plugins.xamlregions", "lu.ewen.minijvm.language", "io.gitpod.jetbrains.gateway", "org.jetbrains.compose.intellij.platform", "AWSL", "com.intellij.plugins.defaultforgnomekeymap", "com.intellij.plugins.defaultforkdekeymap", "com.intellij.plugins.defaultforxwinkeymap", "com.intellij.plugins.netbeans6.5keymap", "com.intellij.plugins.visualstudio2022keymap", "org.intellij.sdk.action", "com.xiaomi.smarthome.ddd.assistant", "com.test.plugin", "com.xtu.plugins.flutter", "com.github.misterboe.typo3livetemplates", "co.anbora.labs.pdn-viewer", "com.pzxm.mysql-log", "com.github.valbendan.ideascripts", "de.quantumrange.betterCollapse", "sputnik", "com.lugeek.chain", "djblue.portal", "com.unger1984.npmdependencychecker", "OpenGL-Plugin", "cn.wuzhizhan.plugin.mybatis.cloud", "superluye", "org.testable.idea.testable-idea", "com.christopherosthues.starwarsprogressbar", "com.seeyon.seeyonplugin.seeyonplugin", "com.botjs", "Time Tracker", "com.github.sudhans.searchwithstackoverflow", "com.sixtynorth.demonstrable-pycharm-plugin", "me.bytebeats.asp.analyzer", "com.intellij.javaee.batch", "org.intellij.grails", "com.intellij.spring.batch", "com.intellij.spring.ws", "com.shouxin.feikong", "org.reloadium", "com.sumologic.intellij-wordspec-plugin", "com.warley.crud-ninja", "o2e.plugins.tools", "com.wenchui.plugins.study.pluginstudy", "org.lengxing.plugins.language.lengxingplgin", "color.scheme.Solarized Junlong", "o2e.frida", "com.beem.plugin.GitCommitPlugin", "org.dochub.idea.arch", "dev.jstanger.corn-intellij", "com.schwarzit.spectral-intellij-plugin", "com.premiumminds.datagripvaultplugin", "ch.newinstance.plugin.mavendependencychecker", "com.brennanpowers.plugins.intellij.StaticCopyMethodGenerator", "com.github.suck1n.keystrokesound", "org.videobug.videobug", "wave.h2o.ai", "intellij-ocaml", "com.vk.admstorm", "sk.mlynek.NotifyOnExit", "be.uliege.gboml-intellij", "ru.crabs.odc_plugin", "com.github.andreypfau.intellij-ton", "fun.gengzi.imagetools", "color.scheme.Cream Soda", "csense.idea.kotlin-not-highlighter", "com.nekofar.milad.intellij.hardhat", "io.github.linwancen.show-comment", "com.github.huoguangjin.sexymove", "com.limin.plugin.codecount", "com.mairwunnx.ThePremiumDark", "net.dyadix.themes.neva", "silkdotnet-rider", "com.nekofar.milad.intellij.truffle", "berezhkoe.cognitivecomplexity", "me.harol<PERSON><PERSON><PERSON>.replacestringformatwithtemplate", "com.bennycode.linessorterplus", "com.spicelang.intellij-spice", "org.mvnsearch.jetbrains.plugins.dubbo-jetbrains-plugin", "com.kyle.lin.unique.plugin.id", "com.github.thibaultsoulabaille.marianatheme", "sc.plugin.com.intellij.zh-tw", "net.auoeke.uncheck", "org.jebtains.lama", "com.huawei.wenchui.plugins.hdn-org-plugin01", "io.github.tgeng.latexsymbol.LatexSymbol", "com.chansos.dubbo-go-generator", "com.intellij.jsf", "com.jetbrains.jax.ws", "com.jetbrains.appcode.android", "com.github.dhananjay12", "cn.hexinfo.devops.jetbrains-rdm", "com.ecarx.t2bg.commit", "com.nekofar.milad.intellij.nuxtjs", "com.github.valbendan.ansible", "com.nekofar.milad.intellij.nextjs", "com.mintlify.document", "xyz.tanxiao.moyu", "com.bee.plugin.killGradle", "com.github.t-kameyama.db-to-kotlin-class", "de.femtopedia.diffplugin", "cn.tannn.killport.id", "com.github.haydenjc.convertyamltoproperties", "com.tianlei.plugin.mybatis", "com.netease.cc.build.plugin.id", "by.overpass.svg-to-compose-intellij", "org.example.Fcscode-Theme", "dev.eltonsandre.intellij.spring.assistant.plugin", "ice.explosive.orchesty", "com.tyq.company.yq", "io.axoniq.ide.intellij", "color.scheme.VS Mac Dark Monokai", "mnix.timestamp", "com.gy.ut.template", "com.huawei.wenchui.java2smali", "com.github.jrd77.data-tool.id", "jetbrains.mps.kotlin.typesystem", "com.github.muromirikka.folderaliasjb", "jetbrains.mps.coderules", "jetbrains.mps.core.types", "kr.kennysoft.idea260828korean", "com.github.dlzhangteng.baselibrarytemplateplugin", "com.autohome.dealer.app.gitmind", "com.github.evgenys91.machinetpaid", "library.lbwb.dev", "org.mvnsearch.plugins.justPlugin", "net.rhsrobotics.OnBotStudio", "org.ideadebugplugin.Intellij-Idea-Simple-Plugin-Copy-Debug-Variable", "com.plugin.sql", "com.immomo.mln.hotreload.MLNHotReload", "com.github.obiscr.exceleditor", "com.wellzhi.smart-work-tool.id", "cn.edu.whu.zoolina.plugin", "KeOnesWorkbench", "com.mrdev.multilanguages", "com.github.mbinic.intellijtgit", "com.github.heyvito.goenv", "com.mrdev.plugin.localization", "easy-code", "com.leesin", "com.wangcaoming.JJPlugin", "org.example.IdeaViewBindingExtPlugin", "com.github.dosh93.akitahelper", "com.github.catppuccin.jetbrains", "com.softwareco.intellij.ops.plugin", "net.zentao.intellij_zentao", "com.github.nakedsnake26.nierautomatatheme", "com.aquasecurity.plugins.intellij-tfsec", "org.zowe.explorer", "com.majera.intellij.codereview.gitlab", "com.aquasecurity.plugins.intellij-Trivy", "Tidal Cycles", "com.roc.code-generator", "dev.tigr.melody-intellij-plugin", "com.zzx.maven.nexus.upload.id", "com.github.furubarug.intellij.external.binary.viewer", "kn.uni.dbis.oberon.lang", "org.yml.plugin.hotchpotch", "cn.wuzhizhan.idea.mybatis.free-idea-mybatis", "com.codelogic.itellicape", "com.copperleaf.ballast.Ballast", "com.wangcaoming.jj_develop_tools", "cn.har01d.plugin.kitty", "com.litarvan.tiger-idea", "ug.template.plugin", "com.github.scaventz.comparator", "org.linyimin.mybatis-sql-viewer", "com.malek.nightscript_language_plugin", "net.trelent.document", "software.amazon.smithy.intellij", "com.dynamic.enums.fill", "com.jd.wl.tool", "com.mitc.intellij.openapi.utils", "<PERSON>y", "Weblogic", "WebSphere", "com.github.shubham076.darkthemes", "com.joutvhu.intellij.dart-scripts", "com.github.adamwojs.platformsh", "org.ferhatozcelik.adbcontroller.wifi", "tel.panfilov.intellij.plugin.springboot", "org.jiangys.tool.idea.toolset", "com.github.jackielii.lspserver", "com.github.nilsbaumgartner1994.refactordataclump", "band.effective.progressbar", "com.nekofar.milad.intellij.nestjs", "com.backbase.bst", "com.jam.enovia", "com.github.olivernybroe.globalrayintellij", "es.upm.dit.gsi.intellijbootstrapplugin", "com.nekofar.milad.intellij.slim", "com.github.zzehring.intellijjsonnet", "com.nekofar.milad.intellij.remix", "com.jin.beanStructure", "org.pcha.intellij-bloblang-support", "com.github.vladtura.yamltemplatesintellij", "de.femtopedia.openocd", "imiatselski.idea_find_in_files_ignoring_whitespaces_plugin", "org.toitlang.intellij-toit", "za.co.lalit.ray.so", "com.oppo.camera.AutoPhone", "com.github.andronikusgametech.ijreplaymod", "com.HUAWEI.CBU.EI.MA", "my.plugin", "com.itao.camel-case", "org.hg.VoidTheme", "org.example.UltimateReactSnippet", "com.javidasgarov.commit_checker", "Fix Protocol Message Parser", "MapStructMappingTool", "EnhancedUnrealEngineDocumentation", "com.intellij.plugins.cobalt.colorscheme", "com.intellij.plugins.warmneon.colorscheme", "com.intellij.plugins.vibrantink.colorscheme", "com.intellij.plugins.darcula2018.2.colorscheme", "com.intellij.plugins.github.colorscheme", "com.intellij.plugins.espresso.colorscheme", "com.intellij.plugins.twilight.colorscheme", "com.intellij.plugins.rails_casts.colorscheme", "com.intellij.plugins.blackboard.colorscheme", "com.intellij.plugins.dawm.colorscheme", "com.intellij.plugins.xcode.colorscheme", "com.intellij.plugins.all_hallows_eve.colorscheme", "com.intellij.plugins.qtcreatorkeymap", "com.tjhelmuth.postgres-explain-visualizer", "com.shxmxn.theme", "org.mvnsearch.jetbrains.plugins.httpx-jetbrains-plugin", "<PERSON><PERSON><PERSON><PERSON>", "com.dooray.project.intellij.dooray-project-intellij", "io.github.xxpain.id", "de.php_perfect.intellij.ddev", "com.kingsoft.shiyou.omnisdk.build.IDEPlugin", "org.swiftformat.plugin", "com.huawei.hdn.toolkitPlatform", "com.tony.markbook.id", "com.alex.tokyonight", "com.nasller.CodeGlancePro", "org.StaticTestVariableNameImporter", "io.github.newhoo.restkit.ext.dubbo", "com.ready-os.core.idea.plugin.library", "com.vengo.ddd.plugins", "com.wcd.mo.pdf", "color.scheme.Meppz!-Dark", "com.qqviaja.plugins.golden-radio-intellij-plugin", "color.scheme.Poly Oceanic", "eu.aylett.pest", "net.twistedbytes.rider.plugins.unreal_genproj", "ftblag.height-saver", "com.guohanlin.yapiquicktype", "com.blocklatency.raspberrypi", "me.aguo.plugin.oldyoungradio", "com.soloCoding.SoloCoding", "maratische.plugin.stacktraceviewer", "com.alibaba.bizworks.ide.plugin", "StandardName.id", "com.github.codinai", "com.kingkoxy.augenkrebs", "com.ess.terminal-pro", "color.scheme.one-dark-neko<PERSON>chan", "ru.b<PERSON>ovsky.commentlint", "wiremocha", "color.scheme.PixelsDE Theme 5.7", "com.github.blendthink.flutter-snippets", "io.github.newhoo.restkit.ext.redis", "com.intellij.plugins.textmate.keymap", "javaDocPlus", "cn.mtjsoft.tinypng.plugin", "dev.antonius.annoyingsemicolon", "com.dimens.values.tools", "com.item.simpleRead", "GinSkeleton", "com.prismicio.snippets", "com.bamboo", "org.example.flat-theme", "br.com.pandras.colorNames", "com.github.pierrejeambrun.intellijxcodedarkertheme", "com.simple.plugin-orm", "org.nelzkie.labs.crossover", "io.github.askmeagain.macromagic.MacroMagic", "org.dynamodb4idea", "cn.codepod.tool.markdown.image", "com.lis.intellij.plugin.aemforvault", "org.example.Spock-Count", "com.github.ptoffy.intellijcivicxcodetheme", "super.generator", "org.casc.lang", "com.jetbrains.gerryPurpleTheme", "commitMessageCheck", "com.github.teinnsei.compiler_explorer", "net.aybat.x509.plugin", "com.aht.plugins.flutter.flavor", "com.github.vladogol.keymapxmlexporter", "Module Colors", "salted.fish.tools", "org.tzraeq.BeanCombiner", "io.raftt", "com.github.open-template-hub.theme", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "io.digiline.secretideplugin", "intellij-stimulus", "org.example.TestProject2034", "org.franciscode.moonlight-jetbrains-theme", "com.symflower.symflower", "com.jetbrains.rider.plugins.clickableconsole", "dev.denwav.gradle-libs-error-suppressor", "com.github.jyoo980.reachhover", "com.github.outofculture.refactordeprecate", "com.github.mohamead.spiderlog", "com.zhikan.agile.liga_plugin_intellij", "org.evosuite.plugin.intellij.xenoamess", "com.sf.plugin", "communism.heyafei.CompareWithOtherDirFile", "io.terminus.trantor.plugin", "com.wafer", "cn.nirlin.MyBatisAssist", "com.github.antoniohbmonteiro.testingplugintemplate", "FileSync", "com.btjava.maven.pomver", "com.lilittlecat.generate-all-getter-and-setter", "com.obiscr.fastshell", "com.github.ceclin.bdlh", "today.movatech.plugins.jsonnet", "com.bash.cmd", "Invalid service monitoring", "pastordougdev.dartbarrelfile", "com.profoundinventions.houdini", "com.github.p0las.dividers", "color.scheme.Capitola", "com.xh.flutter.assets.plugin", "shop.itbug.FlutterCheckVersionX", "com.ld.rookie", "com.frozenkub.yinyang-theme", "com.hui.plugin_plus", "cn.pace.touchfish-plus", "fr.toshi.autocomposepreview", "com.marvin", "com.alexoro.tcg", "ar.com.tucho235.intellijtestledslights", "com.github.captaingopher.colorblindtheme", "org.example.Ceethru", "wtf.lars.breakpoint-notify", "ua.in.hft.StructureImproved", "cat.dcat.jbplug.mactabbing", "xdd-idea-plugin-plus-helper", "vin.plugins.pom", "com.boboram.laravelenvswitch", "com.github.alexeyitaliano.string2sql", "com.github.leeteng2001.betterglsljetbrain", "com.oxygenxml.xsdtojsonschema", "me.danwi.sqlex", "dev.el<PERSON><PERSON>re.intellij.quarkus-assistant", "com.intellij.plugins.colorful.darcula.colorscheme", "com.intellij.plugins.monokai.colorscheme", "run.gleam", "com.levinzonr.arch.jetpackcompose.plugin", "lumigo-intellij-plugin", "com.grinner.tool.chess.finder", "com.vyperplugin.vyper-plugin", "color.scheme.Davs Neon theme", "com.abrandt.plugins.ColorAdditions", "com.chylex.intellij.disabletaskcontexts.DisableTaskContexts", "com.dysh.unreal-simple", "com.pyz.markdown.doc.tool", "at.knusperleicht.quasar", "com.jetbrains.rider.plugins.timberbornriderplugin", "com.github.mksby", "vin.howe.themeGotham", "idea-plugin-mybatis-generator", "org.robin.UtilsGen", "com.dhiwise.dhiwise", "com.intellij.plugins.mnemonicKeymap", "com.github.sherviiin.copywriter", "io.github.toxa2033.saved.state.plugin.idea", "dev.warrengates.codecreator", "com.github.madneal.secdog", "com.github.yusukekuro.yamlbase64decoder", "dk.cego.gitlab_ci_local_plugin", "pers.kearon.yii2-url", "com.zhuyelong.mylogin.id", "br.com.devsrsouza.intellij.dropboxfocus", "com.github.tsvetilian.ty.android-permissions-tester", "dev.jan0660.TheAboveTheme", "com.chriscarini.jetbrains.loc-change-count-detector-jetbrains-plugin", "com.samuraism.plugins.mito", "org.example.test", "cn.blingsec.tools.intellij", "com.github.quairix.mappingreplacement", "com.yleanlink.template", "com.lm.AndroidArchitect", "com.yk.company.table.md.id", "com.mayreh.literate-intellij-plugin", "org.harelang", "org.intellij.sdk.regexp", "com.github.tiste.nanoleafintellijplugin", "com.intelligentcomments", "moe.sndy.abyss", "com.kthem.com.plugin.rememberMe", "com.xtu.plugins.reviewer", "org.lj92458.plugins.reformatMine", "com.github.rafaelldi.diagnosticsclientplugin", "com.caretdev.plugins.idea", "top.xystudio.plugin.idea.LiteFlowX", "com.by122006.zircon.ijplugin", "com.github.bufbuild.intellij", "com.github.alaanor.candid", "ru.mideev.mitheme", "com.lvlifeng.jenkinshelper", "ch.example.test.SmallUselessPluginToIgnore", "kfang.agent.lombok-pql-plugins", "com.db.database.tool", "com.github.dankinsoid.multicursor", "io.ayfri.kordex-plugin", "dev.testify", "color.scheme.Pansy", "com.lishihe.hosts", "dev.itssho.module", "color.scheme.One Dark Pro", "com.github.dinbtechit.vscodetheme", "com.ssk.plugin.background", "SyuyaMurakami.windget", "com.cap.plugin.basic.bigdata", "com.cap.plugin.hadoop", "com.api.plugin", "com.intellij.jakarta.nosql", "youngstead.relative-line-numbers", "GoStructToThriftIDL/Protobuf", "com.qing.bai.ui.automation.test", "org.bruce.IBatisCodeHelper", "me.code4me.plugin", "com.rezaghz.angry-live-templates", "com.gitlabci.plugin", "de.arrobait.antlers", "com.presley.mp-code", "co.anbora.labs.ngrok", "ru.ilimurzin.bitrixexcluder", "macokai-pro", "com.ysw.generate-proto", "com.github.spirits.lotusmint", "ir.mmd.intellijDev.EasySolution", "vitest.runner", "com.ikats.chigoose.codegen.IkatsCodeGenPlugins", "com.xwh.jsonToDart", "GoFuncLitFolder", "org.uom.lefterisxris.codetour", "io.karatelabs.intellij", "com.github.DerekBum.composeSlidesPresenter", "color.scheme.Automne", "com.github.fffguo.console.beautiful.json", "com.intellij.graalvm", "com.odinsam.tabmenus", "com.hxz.mpxjs", "sg.bigo.plugin.FastLayoutInspector", "org.mallowigi.idea.MaterialThemeUI.Extras", "gudqs7.github.io.search.everywhere.api", "org.example.JetbrainsAwilixPlugin", "com.sanath.tools.jsp.converter", "org.antlr.jetbrains.eo", "net.stefanfuchs.jslt.intellij.language.jslt-intellij-plugin", "com.king.java.field.plugin", "com.xtu.plugins.game.center", "color.scheme.<PERSON><PERSON>", "testingassistant-rider", "com.com.pdkj.hx-util", "com.github.nizienko.autokey", "com.qing.bai.case-unify", "com.wdf.api", "org.sendo.CleanArchitectureGenForStation", "org.sendo.CleanArchitectureGenerator", "com.github.Joehaivo.icon-font-viewer", "com.fapiko.jetbrains.plugins.better_direnv", "Intellij_GitHub_Dark_Theme", "com.ashelkov.hoon.plugin", "com.lyrieek", "com.guohanlin.JsonToAnyLanguage", "ir.mmd.intellijDev.cps", "com.yuanzhixiang.star", "org.smark.devtools.DevTools", "com.lawmillenium.narutoprogress", "com.roscopeco.jasm.intellij", "com.linuxgods.kreiger.swedish-personal-identity-numbers", "org.intelliLangHub.langClient", "org.mallowigi.idea.MaterialThemeUI.CustomTheme", "org.mallowigi.idea.MaterialThemeUI.LangAdditions", "org.mallowigi.idea.MaterialThemeUI.ProjectFrame", "com.jetbrains.idear-osr", "com.github.zorbn.gourdjetbrains", "AndCodeGen", "com.gitee.threefish.idea.plugins.rancher", "leetcode-helper", "com.amusing.plugin", "com.gerry.redis", "gudqs7.github.io.getter-setter-postfix", "com.xu.jsonmodule.plugin.id", "com.vampire.haskell.language", "com.github.imsingle.commongenerator", "com.kingsrook.intellij-commentator-plugin", "CopilotTheme", "com.compilemind.intellij-jcef-plugin", "com.glodonedu.glodonedu-codegen", "com.csereoka.reactions", "com.dlts.code.tools", "com.chylex.intellij.rider.vcsgroupbyproject.VcsGroupByProject", "jk-yapix", "com.github.dankinsoid.tuistappcode", "com.hippo.HippoTheme", "fi.tampere.catto.plugin", "another-to-go-struct", "com.dsoftware.ghtoolbar", "com.ahmed3elshaer.true-black-theme", "org.vhdl", "io.github.xiaozhuai.jetbrains-qoi", "color.scheme.dr0i-xndlnk-monokai-darker", "com.jetbrains.rider.plugins.debugnotes", "com.weibo.open.plugin.breeze", "color.scheme.Twilight", "dev.el<PERSON><PERSON><PERSON>.redis-mananger", "io.github.cdgeass.disable-maven-default-http-blocker", "color.scheme.thunderbirds", "cn.shanghai.oyb.onnx", "com.zchz.contract-plugin", "uk.co.mmtdigital.cfnnag", "ca.rightsomegoodgames.GoldenRatio", "com.gavinzh.idea.FolderSort", "com.lcl100.", "com.jetbrains.rider.plugins.newfileshortcut", "io.github.agimaulana.flutter_test_file_creator", "com.marksrv.MarkCustomPlugin", "com.github.nizienko.SpaceInvaders", "me.alexjs.theme.darcula-liquid", "com.lcl100.convert.request.header", "com.mukatalab.scopeActions", "org.mukatalab.jumpy", "com.alisli.intelligenthistory", "com.saumyaroy.gowireutil", "com.github.lucianoratamero.hoshitheme", "com.gaowb.auto.createdSql.v1.0.0.plugin.id", "com.junhai.oversea.sdk.plugin", "com.lcl100.upload.file", "color.scheme.Panda Syntax", "top.geekcloud.dartpad", "org.mel.Adb commander", "com.github.wszpwsren.completionwithrepohelper", "com.huaweicloud.DevSpore.plugin.idea", "fi.testaustime.plugin_intellij", "cn.dxy.app.DxyJsonToDart", "com.github.blarc.gitlab-template-lint-plugin", "slowgenius-tools", "com.joshestein.ideavim-quickscope", "io.snowmate.pycharm-plugin", "com.korioz", "automate", "com.rappi.ms-api", "com.berserk-112.nowcoder-editor", "com.ctrip.triptool", "com.ruben.codespector", "cz.sigler.remotelogconsole", "tech.lin2j.simple-deployment", "dev.ghostzero.nda", "com.zerofinance.ZeroGitDeployToolkit", "com.amber.android.plugin.language_io", "com.service.spockkit", "color.scheme.ayu-dark-allround", "com.y4kstudios.pycharmtypingimp", "com.github.dankinsoid.appcodeassets", "org.utbot.intellij.plugin.id", "com.huawei.h3dstudio", "com.reminderfish.toolx-plugin", "club.gclmit.plugin.jetbrains.gitfox", "com.github.lzj960515.codegenerator", "org.xiaochangbai.idea-tools", "com.pingfangx.plugin.legacylayoutinspector", "Valkyrie", "com.valantic.intellij.plugin.mutation", "com.donghanx.RearrangeTab", "com.plugins.pullrequest", "color.scheme.Darkness", "org.eolang", "org.serenityos.jakt", "com.github.nowtilous.projectcolor", "dev.turingcomplete.intellijjvmsmanagerplugin", "com.vk.noverify", "com.voxelbuster.DiffRename", "com.mright.first.plugin.id", "com.mikedg.StickID", "com.dominik.gruvbox.material", "org.digma.intellij", "com.github.vitalibo.bitbake-intellij-plugin", "caseine.moodle.plugin", "com.github.cooker.cjava", "cn.bluetron.nb.autoparts.process-plugin", "tk.ogorod98.DualScreenCustomizer", "net.ddns.rkdawenterprises.brief4ijidea", "com.manymobi.intellij.esdsl", "com.mkprogs.FlutterHelper", "com.github.dragonhatcher.natexlangplugin", "com.datadog.intellij", "com.wabbi.intellij-plugin", "vitest", "com.github.bucherfa.accessibilitylinter", "com.ramusthastudio.plugin.unixtimestamp", "alxmag.lorem", "org.asterisk.commit-message-standard-plugin", "com.vesync.template", "com.chriscarini.jetbrains.git-push-reminder-jetbrains-plugin", "com.bshpanchuk.taskopener", "cn.androidkt.idea.plugin.colorhelper", "com.rappi.yopay", "net.rentalhost.plugins.php.hammer", "com.dingtalk.xiuchen.components", "com.pingfangx.plugin.templatex", "io.tesler.tesler-helper", "com.jetbrains.Rider.DevKit", "com.jetbrains.rider.plugins.ilruntimedebugger", "com.fortify.remediation", "com.chylex.intellij.keeppopupswhiledebugging", "com.github.zimablue1995.everything", "-", "CopilotDarkTheme", "space.whitememory.python-inlay-params", "io.github.frykher.jetbrains-kanagawa-theme", "com.jxudp.code", "com.chriscarini.jetbrains.automatic-github-issue-navigation-configuration-jetbrains-plugin", "com.caijy.plugin", "com.kikimanjaro", "com.github.joehaivo.RemoveButterKnife", "com.github.eig114.darkburn", "me.<PERSON><PERSON>.odoo", "com.kikimanjaro.screencodepro", "com.emcpheron", "org.example.tool", "org.freeone.javabean.tsinterface", "net.eratiem.zenscriptsupport", "daddyfrosty.intellij.vfx.support", "ai.hypergraph.tidyparse", "com.github.dankinsoid.ideaswiftformat", "com.brownian.testify", "com.github.boheastill.pd2", "io.volantis.better.coding", "com.flreey.codekits", "Bean Converter", "com.github.codespaces.jetbrains.ide", "fr.toshi.composeiconviewer", "cn.xlorpaste.jetbrains", "org.monkeyhelper.plugins", "com.sk.ng", "com.tarantoollua", "com.immuthex.redis", "com.furqat.intellij.plugin", "com.wilinz.globalization.translator", "com.gitlab.msciachero.intellij-gitlab-plugin", "cn.mrdear.setter.<PERSON><PERSON><PERSON><PERSON>", "org.ideplugins.vale-cli-plugin", "com.zjb.repeat-previous-step", "color.scheme.MakeMeGlow", "com.coder.gateway", "me.bors.slack-share", "com.ratel", "tech.dentest", "com.byte_stefan.collect_util", "com.yuanzhy.dogcoder.ide.intellij", "com.github.bobi.osgiannotationprocessor", "com.github.bobi.aemgroovyconsoleplugin.aem-groovyconsole-plugin", "com.sandronimus.intellij.plugin.toggl", "com.github.nopothegamer.mcresourceplugin", "com.marcherdiego.json.visualizer", "com.rrawat.gitlabmrview", "mit.brambasiel.background.intellij-background-image", "cn.com.mustache.mybatis.mybatis-helper", "ais-components.id", "com.prient.intellijpushover", "com.github.reallyliri.limonitprogressbarplugin", "com.iwmedien.commit-template-idea-plugin-typo3", "com.pawsql.PawAdvisor", "com.mesour.intellij.latte", "org.jetbrains.idea.maven.maven2-support", "network.radicle.jetbrains", "com.lilittlecat.plugin.intellij-pangu", "com.github.chanmufeng.markdownindex", "com.jetbrains.gerryThemesPro", "com.easydatabaseexport", "com.toocol.plugin.anisEscapeTooltip", "tech.velocity.jb-env-plugin", "com.github.linrenen.uniappsupport", "com.chylex.intellij.inspectionlens", "color.scheme.Everforest", "com.jcduhdt.sharp.codePosition", "eink-theme", "com.github.thomasbagnolati.diamondthemeintellij", "com.jaredrobertson.plugins.angularFileSwitcher", "com.github.mushan0x0.tinypnggopluginidea", "xyz.janek.simplates", "org.strangeway.jdl", "de.shyim.idea1password", "catappult.uploader.plugin", "com.stackspot.intellij.plugin", "lin.wang.plugin.lintest", "dev.zbinski.html-attribute-folder", "com.jetbrains.php.rector", "com.bugull.androidinitplugin", "com.kikee.tools.zookeeper.idea", "SequenceOutline", "org.iduxfe.coder", "dev.willebrands.intellij.sloppyfocus", "org.openrndr.plugin.intellij", "codeflections.typengo", "com.hfc123.android.HFCXmlFormat", "com.anas.intellij.plugins.ayah", "com.lowgular.intellij", "net.morimori0317.katy<PERSON>-broken", "org.example.my-first-plugin", "io.github.askmeagain.bookmarkkeeper", "org.yh.statistics.plugin", "com.jd.ark.plugin.ProjectScaffold", "com.github.bin.bugktdoc", "com.maxsavteam.ciconiahelper", "com.zettle.android.feature.template", "cn.pxx.plugin.codetime.CodeTimeRecord", "dev.bsara.oscuro.jetbrains", "com.github.vineetver.torva", "org.chromie.chromie", "com.github.reedoverflow.stage1streader", "im.mrx.leolanguage", "org.thermoweb.intellij.yaml", "com.metalbear.mirrord", "com.github.wjlroe.idea-json-detect", "app.pieces.plugins.codeplusplusij", "wzq.jcstress.plugin", "tech.settler.react-generator", "com.sdc.butterfly-toolkit", "com.sunmi.com.sunmi.ideplugin.FinancialPlugin", "me.egeinanc.dbzprogressbar", "de.shyim.ideaphpstantoolbox", "askcodi", "com.github.gitofleonardo.simplesqlitebrowser", "com.github.camork.fix.fileExpander", "com.github.songxiyuan.dilogfind", "com.github.maxzbs.redthemesidea", "com.github.atternatt.powercommit", "org.example.OpenFile", "com.immomo.mua", "com.github.jiuzhuan.browser-tab-manager", "com.github.andrewi.cmlplugin", "org.jiang.JsonAndXmlToJavaBean", "com.github.thermoweb.encryptstringplugin", "cn.ilovejj.dukpt-plugin", "org.dush.idea.plugin.k8.kubernetesExplorer", "com.kodaris.devkit.intellij", "com.github.ekvedaras.classfactoryphpstorm", "com.rohdeschwarz.ic", "com.example.tools", "love.nuoyan.component_bus.plugin", "com.github.linyuzai.inherit.plugin.intellij", "com.dguner.lombok-builder-helper", "io.thundra.merloc", "firsttimeinforever.focusmode", "com.triple.tools.plugins.dbmerger", "intellij.nextjs", "com.matyrobbrt.enhancedgroovy", "dev.buijs.klutter.jetbrains", "it.thoson.flutter.page", "it.aliut.kotlincollectionsizes", ">com.ben.df", "de.martin3398.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cn.ilovejj.BitmapTool", "org.zhihom.gormgenplugin", "com.github.jcraane.fasttravel", "com.woowa.kotest-boilerplate", "com.petriuk.sops-intellij-plugin", "com.github.BlueDriver.BeanUtils-copyProperties", "com.usenimbus.jetbrains", "com.tuqi.Byr", "n.v.k.Qt6<PERSON><PERSON><PERSON>", "com.giathuan.kotlinter", "com.ld.browser", "ch.inss.openapi.crudwizard", "com.aleohq.aleo-developer", "com.github.gabotechs.graphqxlideaplugin", "com.lazzycoderr.flutter-mobx-live-template", "HeaderCommentsGenerator-v1.0", "com.lzw.adbwifiutils", "git-commit-template-simple-cn", "com.google.cloud.workstations.ide.jetbrains.connector", "mi<PERSON>la", "com.sfeir.yaml2env", "com.github.livegoplayer.goparser", "color.scheme.PixelsDE Theme 6.1", "thrift-syntax-fork", "com.denigma.intellij", "com.bennyhuo.kotlin.deepcopy", "com.google.home.ide", "com.github.tsonglew.etcdhelper", "io.github.askmeagain.mapstructor", "com.luminucx.alleon", "com.ld.Translate", "com.github.youopensource.youidesearch", "com.cainiao.lowcode.idea-plugin", "com.zlf.java-code-helper.plugin", "com.dengzii.plugin.kt_ext_indexer", "com.rick.ormplugin.guide-idea-plugin-orm", "com.wcx.plugin", "com.hi.dhl.SyncKit", "color.scheme.<PERSON>", "the.amazing.spiderman-progress-bar", "com.github.skyloft7.nextsketch", "catappult.billing.integration", "cn.sunist.project.bililive-danmaku", "io.github.fstaudt.helm", "color.scheme.SeeSharp Theme", "org.ideplugins.gitlab-yaml-pipeline-lint", "com.boss.android.lite.plugin", "com.pixelperfect.netlify-dashboard", "com.aleohq.leo", "io.codelink.plugins.jetbrains", "com.pricefx.studio", "com.hsofttec.intellij.querytester", "io.cygert.forceprojecttabs", "com.intellij.javaee.webSocket", "com.intellij.remoteControl", "com.intellij.spring.shell", "com.intellij.spring.websocket", "ZKM", "com.github.martinale14.stackedsnippetsintellij", "com.github.samge0.json2cs", "com.gg.plugins.mongo", "com.lukaspiatkowski.pycharm.pylint", "com.github.mokkapps.codesnapintellijextension", "dev.encore.intellij", "intellij.vitejs", "intellij.webpack", "com.gg.plugins.json", "com.ld.SQL-trouble-shooting", "com.github.judalabs.intellijjsonmvc", "com.sk.gn", "com.guiohm.intellijmonorepocommitprefix", "com.github.maxstepanovski.projecttreeplugin", "com.anritsu.intellij.plugin.dtl", "com.feakin.intellij", "home.saied.composesamples.ideaplugin", "com.chenyilei.mysql2h2-plus", "com.github.texousliu.opengitmoji", "com.guxingke.intellij.one", "site.duqian.DusanPlugin", "com.kakao.git-new-tag-extended", "cn.therouter", "com.jetbrains.gerryCyberpunk", "de.beyondco.tldr", "com.github.takemikami.intellij-plugin-pytest-parametrize-inlay-hint", "com.cjian.timestamp", "co.anbora.labs.brew-bundle", "org.pathcheck.intellij.cql", "com.github.googee.laravelbuilder", "darcula-night", "ru.sadv1r.marketplace-repository-headers-idea-plugin", "org.strangeway.tsr", "com.alex.idea.plugin.codereviewer", "com.jetbrains.gerrySpace", "com.jetbrains.resharper.quirky", "com.chiyou.messy.code.recovery", "com.nisus.idea-plugin-refactoring-sheet", "io.theme.toggler.theme-toggler", "com.codeassist.plugin", "com.code.generation.tool", "phalcon.autocomplete", "com.jiaxintech.gboot-idea-plugin", "de.nilsa.infersharp", "com.gionchat.json.bean", "com.example.code-reactions", "com.example.RIdiom", "com.kanpredict.jetbrains.plugin", "com.autocode.convert", "zzccctv.Ka<PERSON>de", "com.yallatech.yallachat.Hermes", "nightshift", "com.github.kikimanjaro.stickyscroll", "cn.ouyubin.godependency4goland", "io.github.binarybeing.hotcat.plugin", "com.github.suhli.ideagokratosplugin", "io.snappify", "ice.explosive.gdscript", "jungehaie.gitlike", "dev.kdl", "com.obelisk.onedarkdarker", "me.tianshili.matcha", "com.pixelperfect.github", "com.intellij.mermaid", "com.nisus.my-classic-eclipse", "dev.ruipereira.idea.carbonnowsh", "com.nkrasko.vcs-log-links", "top.fallenangel.jimmer-generator", "com.jetbrains.writerside", "com.meterian.heidi-plugin-intellij", "intellij-theme-relax-your-eyes-green", "com.single.ton.json_comparator", "com.github.aslbarnett.lapisjetbrains", "com.nttdata.dedalowcoding.plugin", "com.intellij.aqua", "com.jetbrains.gerryDarculaButtonOutline", "com.cs.codegraph", "com.eny.i18nVue", "lermitage.intellij.ilovedevtoys", "com.intellij.spring.data", "com.intellij.spring.mvc", "com.intellij.reactivestreams", "com.intellij.javaee.jpa", "com.intellij.javaee.app.servers.integration", "com.intellij.cdi", "com.intellij.spring.integration", "com.intellij.spring.cloud", "com.intellij.javaee", "com.intellij.javaee.el", "Tomcat", "com.intellij.javaee.extensions", "com.intellij.spring.boot", "com.intellij.spring.boot.initializr", "com.intellij.beanValidation", "com.intellij.hibernate", "com.intellij.spring.security", "com.intellij.javaee.web", "com.intellij.thymeleaf", "com.jetbrains.restWebServices", "<PERSON><PERSON><PERSON>", "GlassFish", "com.intellij.spring", "com.intellij.spring.messaging", "fi.jyu.rider.comtest", "com.intellij.dsm", "org.jetbrains.plugins.javaFX", "io.github.palexdev.SessionManager", "com.github.izhangzhihao.intellijgooglesearch", "com.naboo", "org.btik.platformio-plus", "com.jetbrains.gerryNature", "com.intellij.aqua.selenium", "jetbrains.extension.vscode.beansoft", "com.github.kreuzerk.skol", "io.github.huacnlee.autocorrect", "com.github.asm0dey.typewriterplugin", "com.jetbrains.gerryCherry", "com.jetbrains.gerryCoffee", "com.oppo.as.dev.toolkit", "com.posidev.applicationInspector", "dev.magic.extension", "com.ar.company.AR-Structure", "com.github.twobiers.copynice", "org.jacksun.idea.MVCHelper", "com.similarimage", "com.example.demo", "com.github.traineratwot.noasciihighlighter", "tech.pdai.idea-plugin-for-all", "cn.yitulin.HttpInvokerIdeaPlugin", "yeamy.restlite.i18n", "com.sailorj.itools.action.MyPoJo2JsonAction", "com.github.boatrainlsz.wslpath", "com.ticketmaster.vax_macro", "com.github.miagilepner.pgservice", "dev.priporov.idea.notes", "com.jizhi.jizhi-tool", "dev.paulshields.assistantview", "com.hutf.me.ovft_mybatis_generate", "org.vlang", "co.anbora.labs.fantom-lang", "io.sandboxapp.plugin", "com.github.softsense.flutterintellijpreview", "org.sourcelab.intellij.plugin.KafkaConnectPlugin", "com.brucege.DatabaseHelper", "com.oxygenxml.json.converter", "win.doyto.query.plugin.enhancement", "org.skellig.plugin.language", "com.you", "com.intellij.micronaut", "com.intellij.quarkus", "com.github.zooombiee.mvvmplatform", "com.mrootx.idea", "com.fobgochod", "com.github.crobati.shishoutheme", "com.jetbrains.gerryAurora", "com.zhuohc.solarizeddarkflat", "com.wuba.FairTemplatePlugin", "com.faendir.intellij.gradle-version-catalogs-plugin", "io.github.rezeros.redit", "org.jetbrains.bsp", "lekan<PERSON>.Race", "com.francma.intellij.everforest", "com.yuye.java-tools", "com.ld.gitlab", "thomas.gian.<PERSON>", "com.doslin.BuildBootPro", "com.ilumer.textpastry.Jetbrains-Text-Pastry", "com.instabug.ide-plugin", "brig.concord.intellij", "com.github.openmindculture.intellijcutepinkdarktheme", "com.nisus.my-classic-eclipse-light", "com.zx.unique.plugin.id", "color.scheme.One Dark Ruby", "com.lijiahaosummer.nccdev", "com.hamed.PostmanToRetrofit2v2", "SolarizedChandrian", "com.feature.architecture", "com.freetime5.plugins.HotfixPlugin", "io.github.lgp547.any-door-plugin", "dev.earthly.earthly-intellij-plugin", "org.byted.dycloud", "org.ideajsontools.IdeaJSONTools", "com.huxiaobo.generate", "com.donotdisturb.do_not_disturb", "com.yoxio.dev-tool", "com.peon.WuPo", "com.larboy1991.idea.plugin.JKDoc", "me.harol<PERSON><PERSON>in.intellijpluginevaluateselectedexpression", "com.aiwan.gorm", "org.alibaba.plugins.kotlin", "com.wavesplatform.rideplugin", "com.albertoventurini.jetbrains.graphdbplugin", "com.github.iktsuarpokluo.oneliner", "ru.nosovni.json-test-generator-plugin", "com.matrixboot.Kael", "<PERSON><PERSON><PERSON>", "idea.FastRequest", "com.leiga.agile.leiga_plugin_intellij", "com.quixxi", "com.mth.CodeSnippet", "de.lulonaut.autofetch", "com.vk.modulite", "net.auoeke.structured-properties", "com.epiheader.epiheader", "com.testPlugin.Test", "com.emperador.cleancodetdd", "dev.huyaro.gen.x", "com.github.elad12390.webstormhygen", "io.github.md2conf.idea-markdown-copy-as-plugin", "com.star.easydoc.easy-kdoc", "com.azhon.flutter.module.bridge", "co.anbora.labs.sqlfluff", "elegant-theme", "com.fast.hot.swap", "community.flock.wirespec.lsp.intellij_plugin", "com.chm.plugin.idea.ForestX", "org.wen.github.SpecialCopyPlugin", "com.plugin.zh", "com.kiven.json2dart", "Excel to Entity", "com.icode.codenote.id", "com.focustech.mic.magdge", "com.getyourguide.paparazzi", "gruvbox-anhoder", "io.github.gdutxiaoxu.gitreview.fx", "prabin.timsina.api.bank", "mps.tweaks.stringediting", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com.aiwan.io-game", "dev.lonami.uniffi-dl", "com.github.sorinflorea.runconfigextras", "com.anguomob.anguo", "color.scheme.Jewels", "co.anbora.labs.sqlfluff.ultimate", "com.foldright.auto-pipeline.auto-pipeline-idea-plugin", "com.github.pyvenvmanage.pyvenv", "org.strangeway.vaadin", "com.taotao.cloud.idea.plugin", "com.codeium.intellij", "com.jetbrains.ComposeDemoPlugin", "com.cliff.plugin", "yij.ie.idea-comment-queries", "com.github.patrickfv.horizonintellij", "com.github.hixon10.openfileinazuredevops", "com.apifox.uploader", "com.github.abuballan.adbcommanderplugin", "com.lagseeing.git-commit-template-idea-plugin", "com.atwix.magento", "com.github.linwancen.plugin.author", "com.github.lonre.gruvbox-intellij-theme", "com.example.yyx", "com.weylan.ProtostuffTagGenerator", "jetbrains.mps.Woshitanyue.extensions", "com.llyke.plugin.tools", "kr.sparkweb.tailwindtools", "pitch-black", "wiesmak.rnbwdshpb", "com.mavenapps.CleanFrameWorkFlutter", "io.github.bloeckchengrafik.cloudflared", "com.koxudaxi.ruff", "com.xxxtai.coding.SmartInputSourceIntelliJ", "com.github.michaellazar.gemini_plugin", "com.hand.ikunProgress", "cn.ryoii.git-extend-untracked", "com.apshenkin.codeceptjs", "com.example.MyPlugin", "com.xiaopeng.huangxz.demo", "ai.codegeex.plugin", "com.github.hugohomesquita.htmxjetbrains", "io.github.t45k.bitbucket_coverage", "com.cfa.api.sdk.intellij", "com.cyx.UploadToPgyPlugin", "Calyx", "com.alipay.tsingyan", "Yggdrasil", "com.github.bartjaskulski.wordpresstextdomain", "dracula-vscode", "ChatGPT", "com.github.texhnolyzze.jira-worklog-plugin", "nl.dirkgroot.structurizr-dsl-plugin", "com.tencent.ams.MosaicTemplateEditPlugin", "com.microej.tools.intellij-idea-plugin", "co.com.devlinx9.k8s-cxts-selector-stbar", "setbug.com.PHP8_Chinese_Document", "com.github.mustfun.mofar", "fr.rom14700.jacocorapportfinder", "io.samjingwen.code-formatter", "com.github.kikimanjaro.intellify", "com.khch.scheduler", "com.dvd.intellij.d2", "org.cjc.XmlToCode", "io.unthrottled.doki.icons", "com.as", "ZivKap.GitHubTheme", "com.fast.hot.swapper", "com.github.kings1990.chatgpt", "com.apollographql.ijplugin", "io.github.askmeagain.pullrequest", "com.smallcloud.codify", "com.github.j1v37u2k3y.pastemarkdownimage", "de.tweis.flowdebug", "com.abodx.stringer.Stringer_X", "com.gjing.plugins.plugin-demo", "com.k1ngjulien.ng-gruvbox-theme", "io.autofill.kotlin.kotlin-auto-fill", "com.enhe.enhe-endpoint", "com.cfp.raptorProgessBar", "com.tinyweb.dm-mdx-editor-idea-plugin", "com.bruce.easycode.community", "com.example.demo1", "com.github.houweiandroid.vesyncmvvmtemplate", "com.hxw.newmapperext", "com.github.blackhole1.ideaspellcheck", "com.leo.gors.intellij", "com.hitd.HelloWorldPlugin", "com.huawei.hdn.deploy", "com.pixelperfect.travisci", "com.example.ToBrowse", "com.kiires.plugin.clion.translator", "org.intellij.prisma", "Gruvbox43", "com.gjj.cool.Farmland.id", "com.github.BlueDriver.idea.plugin.CheckingBracketPair", "com.broxus.t-sol", "net.zdechov.hajdam.midiswing", "com.dll.tools.plugin.reviewboard", "night-blossom", "ma.leet.ft_header", "com.zzk.JsonSchema", "de.achimonline.github_markdown_emojis", "dev.fastball.fastball-IntelliJ-plugin", "izhangzhihao.rainbow.brackets.lite", "com.jxselab.AlxTyper", "com.taylorrayhoward.gruvbox-theme", "io.swimm.intellij", "com.lucas.stag", "com.github.inf166.pluginphpstormtypo3committemplate", "fr.devcafeine.implement_interface_dart", "ma.leet.ft_norminette", "com.github.simiacryptus.intellijopenaicodeassist", "itmo.logo", "dev.sbp", "com.github.janbiasi.gotthardtheme", "com.example.xml-escaping-helper", "ru.protei.xml-escaping-helper", "dev.resolvt.idea-integration", "com.uncomment.uncomment", "dev.kotx.gptcoder", "com.mamiksik.parrot", "com.cnis.CodeNotes", "com.zequal.versiontool", "org.utbot.rider.plugin.id", "com.vlnabatov.alabaster", "com.danilogoodies.danilogoodies", "com.plugin.chatgpt", "com.Delixus-IntelliJ-Plugin", "com.dss.git.git-auto-pull", "com.github.kkyeer.debugger.to.uml", "me.small.vision", "com.github.kikimanjaro.hexagonjetbrainsplugin", "pageqiu.replace.special.symbols", "com.sgota.plugin.idea.javadoc2", "com.github.btbrq.simpleeditorplugin", "com.ooneex.theme", "de.parrot.mongoexpert", "com.ooneex.icons", "com.github.radgospodinov.plugin.reminder", "me.x150.intellij-code-screenshots", "icu.windea.bbcode", "com.murphysec-enterprise.intellij.Plugin", "com.johndaligault.adder-remover", "com.github.rmehri01.onenord", "com.github.micmine.logana-jetbrains", "org.openasr.idiolect", "ua.nechay.notation", "com.github.linwancen.plugin.coverage", "io.ddiu.moegi.theme.jetbrains", "ca.alexgirard.HarpoonIJ", "pageqiu.conveniently.input.special.symbols", "xyz.zes.zokes", "com.github.aarcangeli.ideaclangformat", "fr.socolin.rider.plugins.highlightsspecialfiles", "com.antiy.scs.intellij", "com.dynamic.quick.develop", "org.OverEngineer.InlineProblems", "top.goodz.tools", "org.taack.taack-autocomplete", "com.github.linuxchina.jetbrains.prql", "com.mcarlin.gruvbox-theme-reboot", "com.goldenBellCocoon.gyro", "ru.alezhu.idea.plugins.named_argument_stubs", "dev.rollczi.litecommands.intellijplugin", "com.programmersbox.Full-Multiplatform-Compose", "net.morimori0317.inp", "jp.masakura.Semgrep", "io.exam.intellij.plugin", "yyds.auto.devplugin", "com.github.skyloft7.nextsketch2", "slak44.angularinjectintoaction", "ca.justinpark.build.idea-docker-java-debugger", "com.github.iyashpal.intellijgithubthemes", "com.obertdev.harpoon-minimal", "org.yeepay", "me.kdev.dingdong", "org.fzq.pomodoro", "id.andgen.andgen", "br.com.vineivel.Iridio77", "dev.spark61.test", "<PERSON><PERSON><PERSON><PERSON>", "JavaUML-parser", "cn.moresec.sca.ScaScanner", "com.alibaba.mkt.doraemon-pocket-plugin", "com.logigear.allure.step.formatter", "com.kgxl.killer", "com.brodalee.jirlabprojecteventsnotifier.JirlabProjectEventsNotifier", "com.beyond.plugin.mybatis.generator", "me.strajk.intellijpluginmarkdownlint", "com.tech.DoStruct", "com.flutter.blocd1.plugin", "com.dcm", "com.hitechrush.javarush", "com.github.unldenis.YamlEnumerable", "git-comment-for-link-xie<PERSON>o", "com.github.tandrews.gptest", "com.bing.qtools", "com.jackkenney.solarized-chandrian-theme", "com.github.ericodemecha.Modern_Cpp_New_Project-plugin", "com.intellij.settingsSync", "com.glycin.funfinal.FunFinal", "dev.ad<PERSON><PERSON><PERSON>.mirandaintellijplugin", "com.yisiliang.idea.plugins.apusic", "com.pyrange", "io.smallrye.graphql-client-plugin", "com.intellij.laf.macos", "com.intellij.laf.win10", "io.github.askmeagain.endoflinehint", "zzccctv.Pulsaride", "com.aiwan.jorm", "com.hangox.myandroidtools", "com.jetbrains.php.eval", "com.jetbrains.performancePlugin.yourkit", "com.jetbrains.performancePlugin.async", "com.peng.idea.plugin.builder-generator-plus-v2", "com.abstractprogrammer.NullNotion", "debugToCursor", "de.achimonline.ansible_lint", "de.mr-pine.simplecodetester-plugin", "com.m-gd.Neo_MJS", "com.github.nguyenphuc22.androidpackagerenamer", "com.mindflakes.colorizedproject", "com.flop.RestTester", "com.example.idea-plugin-dart-generator", "ice.explosive.painless-go", "com.fobgochod.git.commit.message.format", "me.bechberger.jfrplugin", "com.intellij.dubbo", "pl.semidude.tree-view-by-default", "com.typhur.android", "me.cjcrafter.py-get-set", "rife.idea", "com.wortin.wortin-intellij-plugin-demo", "com.dohyeon5626.auto-gitkeep-plugin", "com.maukaim.budde.assistant.budde-assistant-intellij-plugin", "com.github.tomislaw.pickyourautocompletion", "com.aiwan.request-tool", "com.rabbitpre.unique.plugin.id", "ris58h.androidkeymaps.intellij", "com.huawei.codecheck.plugin", "com.github.almarzn.intelligpt", "com.jxcc.trxx.idea.plugin.code.generator", "org.jetbrains.plugins.astro", "top.threep.plugin.txtic", "com.biron.magolor", "com.ouedyan.gomodhelper", "com.nancheung.plugins.jetbrains.legado-reader", "com.wilinz.compose.intellij.dependencies", "com.jiuli.mes.code.templates", "com.jzyhywxz.mindreader", "me.serce.cfrules", "ch.heiafr.intel<PERSON>", "com.intellij.kmm", "org.sourcelab.intellij.BuildMonitor", "com.otmoc.codereview", "org.wilbert.crashretrace", "com.github.astrapi69.fileformattransformer", "com.smponi.intellij.faultscope", "com.logicui.simpledroid.SimpleDroid", "com.github.vineetver.virtus", "com.github.gabrielmaialva33.jetbrains", "org.metailurini.svoice", "io.github.riej.lsl", "com.eggnstone.jetbrainsplugins.DartFormat", "dev.gabrielchl.intellij-pets", "com.plugin.codeharbor", "com.ruijie.shamee.gitcheck.plugin.id", "com.dalezhuang.dlz-code", "com.klevan.lombok.idea.plugin", "com.xgh", "com.pradeo.pradeo-intellij-plugin", "com.fanruan.p3c.smartfox", "com.julyyu.asplugins", "com.github.binhlecong.androidscanner", "com.wuyulin.ChineseToPinyin", "org.jetbrains.research.testgenie", "com.github.supersld.strategyviewer", "ChatGPT-CN", "chief.at.lambdatest.LambdaAndroid", "JavaUtils", "com.github.irtimir.intellijplatformpluginlinktoremote", "me.klez.senpai", "BarneyProgressBar", "com.github.krios2146.intellij-theme-cathub", "com.yonyou.yip", "icu.stopit", "com.alanvan.LiveDataComplete", "com.ruimin.helper", "com.eric-li.layout-inspector-v2", "com.github.ioannuwu.inline", "com.fuhrer.intellij.jump", "com.xiaobaicai", "ee.carlrobert.chatgpt", "cloud.filibuster.plugin", "com.github.burkclik.asplugin", "dev.nx.console", "tel.panfilov.intellij.plugin.mvntermenv", "xwj1024.ajin-theme", "com.ilsmp.base", "org.dochub.idea.test", "com.github.nghiatm.robotframeworkplugin", "com.huawei.codearts.snap", "com.autoroute.helper", "com.acmesoftware.clean-framework", "pega.intellij.modeler", "com.doktech.dokTechCodeFormatPlugin", "com.fuhrer.intellij.barrier", "color.scheme.PyDarcula", "com.offensive360.plugin", "color.scheme.monochromatic-code-light", "com.the-epic-split", "com.vfun.vfunCodeHelper", "com.github.domonion.kgeorgiymediaplayer", "io.kol.gitlabreview", "com.haeyum.NamingFox", "com.github.linuxchina.jetbrains.wit", "ru.vassuv.plugin.create_from_template", "sampelAPICallsTest", "com.hngngn.solid.snippets", "carvalhedo.inline-url", "com.github.warningimhack3r.npmupdatedependencies", "com.yth.ai.codereview", "com.dmt.ye.rhino-plugin", "zone.pusu.mybatis-code-generator", "com.zcm.cronHelper", "com.toyfivver.tolerance", "dev.koly<PERSON>.jetbrains-cyberpunk-neon-theme", "poimandres", "GsonFormatR", "com.karsta26.recent-projects-with-branch", "in.payu.dependencies.PayU-Dependencies", "de.justin-klein.signed-todos", "global.genesis.igenesis", "com.en_circle.slt.plugin", "TVariable", "dev.davidemar<PERSON>li.FileFinder", "com.find.reference.FindReference", "com.julienphalip.jvmdefaultoptions", "cn.sp.<PERSON>", "picimako.justkitting", "bsoft-commit-helper-idea-plugin", "dev.diversion.plugin.dvidea", "com.compose.palette", "color.scheme.MonokaiByNeo", "com.akulaku.aku_flutter_template", "com.example.SubversionMavenIncrement", "com.hxl.plugin.springboot-scheduled-invoke", "generic-inlay-hints-rider", "com.example.CodeNote", "com.example.FastTalk", "com.zscaler.zpccloud.zpc-intellij-plugin", "io.evil.evil-idea-tools", "com.free2one.idea.php-accessor", "io.jetclient", "org.comroid.kscr.intellij", "dev.semgrep.intellij", "digital.pedda.loremIpsum", "com.github.dyriavin.mimesistheme", "com.tsintergy.ssc.database.generator", "com.api.plugin.postcat", "io.ayfri.jetbrainsplugincopysave", "com.trickbd.CodeGPT", "com.plugin.chancey.google.translate", "com.cqry.java.code.generate.plugin.id", "com.github.peco2282.gitignore", "app.spellbox.plugin", "com.mullan.asmulcreatplugin", "com.branch.files", "com.kuinfu.ide-plugin", "com.gchenxx.plugin-demo", "org.aurora.engine", "com.tcubedstudios.angularstudio", "org.nio.ButterKnifeToViewBind", "codiumai.codiumai", "com.yonyou.yy-tool", "com.zerdicorp.acl", "me.shakhzod.intellij.snippets.blazinglyfast", "com.github.tarcv.testingteam.surveyoridea", "dev.priporov.format-converter", "com.github.gllxl.imagepreview", "com.sonnie.SecurityScan", "com.github.rebel000.cmdlineargs", "com.pannous.jini-plugin", "com.xixi.chatgpt", "net.njcp.ias.plugin.log", "com.v8en.padder", "com.jetbrains.rider.plugins.dependencymonkey", "link.dlup.solidCherryTheme", "cn.yisu.idea.plugin.gpt-quick-dev", "org.mallowigi.focusmode", "com.minivv.pilot", "com.xixi.ernie", "me.rerere.discord-ij", "net.binis.intellij.code-generation-intellij-plugin", "com.github.linwancen.drawgraph", "com.xiaobaicai.tools.JsonTools", "com.york.tool", "com.github.burtbai.calculator", "com.ld.chatGPT-copilot", "com.luomacode.ChatMoss", "com.lilittlecat.intellij-jts-test-builder", "com.jetbrains.rider.plugins.et", "com.aiops.CloudAlert", "org.example.BrightspotDebugPlaygroundPlugin", "com.wanganqing.adb-auto-connect-idea-plugin", "com.unicorn.uu5_intellij_snippets", "com.zhengfei.aicoding", "com.github.liyipeng123.armor", "com.github.panli1988.cocobuilder", "com.plugin.solution-generator", "se.wellfish.Code-file-grabber", "com.github.cherijs.intellijpluginslugify", "cn.mrdear.intellij.intellij-log-highlight", "org.example.mybatis-sql-log", "com.github.howieyoung91.aicodehelper", "Cyberdyne20XX", "com.moseoh.programmers_helper", "io.github.anvell.dune-theme", "com.maple.plugs", "com.pimcenter.dms-code-check", "com.github.cspanda8989.chatgptcodereviewplugin", "com.aiwan.rocket-cat-plugin", "finance.antalpha.fed.antalpha-fed-tools", "com.ld.chatGPT-copilot-helper", "app.aicommit.plugin", "com.github.pmkyl.retropcgreentheme", "com.profiq.codexor", "ru.ztrap.plugin.idea.compose-color-preview", "zzz.prgrmz.pycharm-odoo-helper", "com.github.anuragkanwar.modernreactsnippetsplugin", "com.ld.Copilot.ChatGPT", "com.github.xbbljty.gostructtojson", "com.xiaobaicai.ChatGPTCoding", "com.coteLeKing.QuickOverflowSearch", "dev.jamiecraane.gptmentorplugin", "com.github.daputzy.intellij-sops-plugin", "com.yalhyane.intellij.goAiDocComment.go-ai-doc-comment", "com.upchina.soup", "com.yili.build.mapper", "train", "YapiUploadPro", "com.xiaolan.happyCode", "cn.ysxx.xsan.idea.plugin", "com.zhangbyby.BeanFieldComparator", "ir.amirab.debugboard.idea-plugin", "com.github.lppedd.idea-pomsky", "com.automybatis.autoMybatis", "com.github.link00000000.carbonfoxjetbrains", "com.github.blarc.ai-commits-intellij-plugin", "com.id.FieldComperare", "top.nobodycares.quicksearch", "Duck_GP<PERSON>_Plugins", "com.haulmont.jpahelper", "com.maple.git_config", "com.geppetto", "org.owasp.ide-vulscanner", "com.nekofar.milad.intellij.hexo", "se.illusionlabs.RecentTabOrder", "com.sankuai.idekit.starter", "io.github.ambalashov.structurizr-idea-plugin", "com.ot.plugin", "polybpmn-intellij-visualizer", "new-dark", "SkyCode", "com.nekofar.milad.intellij.blitz", "com.github.ekryd.graphqlformatter", "checklistbuddy-intellij", "tui", "com.nekofar.milad.intellij.brunch", "com.nekofar.milad.intellij.redwood", "org.noear.solon.idea.plugin", "com.linkkou.plugin.intellij.assistant.mybaitslog", "com.nekofar.milad.intellij.qwik", "me.small.db<PERSON><PERSON>er", "com.github.smallteenager.today-poetry", "pascal.taie.intellij", "com.moshuk.aistorm", "json-dart-serializable", "com.yalhyane.intellij.phpaicode.php-ai-code", "com.nekofar.milad.intellij.gatsby", "com.nekofar.milad.intellij.expo", "com.github.yunabraska.githubworkflowplugin", "de.maschmi.idea.chatgpt", "de.ithock.idea.blitz", "io.github.prgrmz07.QuickRequest", "com.xdd.flutter_code_helper", "com.github.loosheng.intellijvitessetheme", "com.baizey.npmupdater", "com.github.chengyuxing.rabbit-sql-plugin", "com.github.hxj0x", "com.wj.flutter.template", "com.xf.ai-assistant", "com.miti99.intelligd", "com.github.warfaj.paxintellijplugin", "com.github.abcaeffchen.smartyblockfolding", "com.snacks.onegai.codegpt", "com.nekofar.milad.intellij.docusaurus", "com.github.marc-r2.commit_gpt-intellij-plugin", "com.xrosstools.xunit.editor", "com.nekofar.milad.intellij.solidjs", "network.adel.alang", "color.scheme.New_Dark", "pers.wjx.plugin.yourProgressBar", "com.github.a3318375.hyxmybatistool", "tq.jmh.plugin", "com.sk.ts", "com.refraction.plugin.jetbrains-refraction", "com.github.moreaframework.pluginintellij", "com.kh.tools.ng", "com.mrzlab630.creatCodeIA.creatCodeIA", "com.huawei.agc.toolkit", "com.my.code.codeTag", "aquatic-dark", "com.himesh.tagman.TagmanPlugin", "com.jade.code.companion", "com.github.beansoft.newui.buddy", "org.sqlx", "com.aydinduygu.Click2Comment", "com.raysun.composex", "com.github.ivaneod.kotlinjsinspections", "ai.codemaker.jetbrains", "github.tdonuk.string-helper", "copApiDocx", "com.ddoong2.gitautolink", "com.github.idea.plugins.autofill.AutoSet", "tech.velocity.jb-cde-plugin", "dev.shaoxing.AarSourcesFixup", "com.methodFinder.MethodFinder", "top.aicore.aicode", "com.hellogroup.plugin.lua", "com.feibi.plugin.transfer", "io.intenics.python.fileNameMismatch", "com.nus.CompoundInterestCalculator", "io.miso.SurroundCode", "cn.fjdmy.uniapp", "com.phongphan.keyhashlugin", "com.zengdw.mybatis.mybatis-generate-idea-plugin", "com.dbvis.yguard-unscrambler", "io.seedwing.policy.seedwing-dogma", "dev.cerus.better-alt-ins-menu", "com.jetbrains.rider.plugins.autocompleteVoiceCoding", "com.mininglamp.tech.myai.check-enum-plugin", "Seanke.CleanDarkTheme", "io.github.zrdzn.plugins.ij-daily-bible", "com.lexun.idea.plugin", "com.vcque.prompto", "io.github.emacsist.idea.pomversion", "eu.oakroot.mcname", "com.vladsch.plugins.consoleFileCaddy", "org.remremegg.mitsuko_plugin", "com.allan.OpenHere", "io.github.hyuga0410.lombok-enums-component", "cc.unitmesh.devti", "com.xrosstools.idea.gef", "com.example.findActivity", "Nox_Theme", "com.bluewhale.YamlLens", "com.xrosstools.xdecision.editor", "com.idcpj.change_export", "com.longer.ec-tool", "wl-spring-assistant", "com.tabnine.TabNine-Enterprise", "org.openasr.idiolect.azure", "com.timindustries.regexpluginv2", "com.linkkids.Annotation-ShortCut", "com.dubreuia.tool", "io.github.pdkst.auto-complete-log", "com.example.SENG4430", "it.craftspire.gpt-review", "cn.ryoii.muyu", "com.vladsch.plugins.SimpleSerialConnectorService", "com.samirdjelal.ferris", "<PERSON><PERSON><PERSON><PERSON>", "HighliterNS_File", "HighliterNS_Network", "easycode.ChatGPT-Copilot", "com.xrosstools.xstate.editor", "ChatGPTGold", "cn.coderpig.CpStringFormat", "com.lightbc.templatej", "com.lalith.AISummariser", "org.armadillo.core", "com.palg", "ai.welltested.fluttergpt", "com.th.smali", "io.github.stdupanda.plugin.intellij.MyPlugin", "com.github.putyourlightson.intellijsprigsupport", "me.zerol.rr", "com.mikefmh.gcc-integration", "com.ksmgl.exceptionai", "com.guohanlin.JsonToRust", "cn.xr21.newbing", "com.guohanlin.jsontocsharp", "com.guohanlin.JsonToPython", "com.guohanlin.JsonToCPlusPlus", "com.jetbrains.rider.plugins.rimworlddev", "me.benmelz.jetbrains.plugins.hamllint", "com.luigivampa92.remoteandroidbuilds.ideplugin", "com.guohanlin.JsonToPhp", "easy.char", "com.guohanlin.JsonToGo", "com.wonddak.fontHelper", "com.guohanlin.JsonToSwift", "com.guohanlin.JsonToKotlin", "lin.wang.plugin.linktest", "com.jetbrains.rider.plugins.dehungarianiser", "com.guohanlin.JsonToDart", "com.guohanlin.JsonToJava", "com.daugaard47.classytoggle", "com.angel.torre.testng-snippets", "com.guohanlin.JsonToObjectiveC", "com.leansoftx.testpluginlsx", "com.epo.guider", "com.chianing.plugin.data-converter", "labs.hardhacker.theme.jetbrains", "com.hamurcuabi.layoutbounder", "com.yglong.plugin.intellij.DocxBrowser", "org.c3lang.c3intellij", "com.tony.nebula-highlight", "cn.org.wangchangjiu.sqltomongo", "com.wrike.sprinter", "com.codeium.enterpriseUpdater", "de.itemis.mps.linenumbers", "com.elicul.azure-devops-commit-message-plugin", "dev.zhaox.bionic-reading", "io.github.heyuch", "com.example.intellij-geometry-plugin", "me.callahan.cyclone", "com.github.theapache64.rebuggerplugin", "liubsyyflymoon", "feature-sliced-design-helper", "com.codactor.codactor-intellij-plugin", "dev.yanisk", "com.example.tcd", "com.nanoya.intellij-typescript-fluent-builder-live-templates", "io.apimap.intellij", "com.gumingnc.mars-support", "com.troy.a-theme", "com.easycode.react", "de.firstcoder.nugetexport", "color.scheme.EclipseDarkNewUi", "com.nekofar.milad.intellij.tauri", "com.nobugboy.AiExplain", "org.jboss.tools.intellij.mtr", "com.github.nikolaikopernik.codecomplexity", "org.kframe", "logwire2-designer", "com.github.linuxchina.jetbrains.markdown_chatgpt", "com.maximde.serverstatus", "com.intellij.bigdatatools.zeppelin", "business.impawtant.cloud-sql-connection-manager", "com.dark.intellij.logconsole", "com.codingdreamtree.DtoToMyBatisResultMap", "com.obroom.plugin.coderadiolite", "com.obroom.plugin.regextester", "com.github.bofa1ex.wizardgpt", "com.tecknobit.javadocky", "com.github.seguri.finalobsession", "com.ouguiyuan.generator", "cn.bob.mh-plugin", "cn.hperfect", "com.github.crazybene.crazycolortheme", "com.intellij.bigdatatools.spark", "com.intellij.bigdatatools.binary.files", "com.intellij.bigdatatools.flink", "com.intellij.bigdatatools.kafka", "com.intellij.bigdatatools.rfs", "ai.sapient.unit.plugin.sapient-unit-plugin", "commit-message-helper-idea-plugin-new", "com.intellij.bigdatatools.metastore.core", "com.intellij.bigdatatools.core", "cn.org.wangchangjiu.table2Entity", "net.cicchiello.intellij.settings.intellij-settings-share", "com.touch.fish.view.TouchFish", "ccom.joetr.modulemaker", "com.lightbc.databasej", "integrated_development.ambient", "fr.devcafeine.Typescript_Class_Tools", "de.keeyzar.gpt-helper.gpt-helper", "com.browserstack", "com.intellij.velocity", "CodeAnt", "com.github.openjkdev.picturezipplugin", "cn.ilovejj.dukpt-tools", "com.lowjungxuan.proz", "com.clean.helper.clean_helper", "io.github.slava0135.doktestidea", "com.numq.protobufblueprint", "com.yahorbarkouski.okdoc", "com.github.erotourtes.harpoon", "com.github.lipan1658.cocomybatislog", "com.isaac.BuildFinishNotifier", "QuickRequest", "me.panxin.plugin.idea.pojocheck", "com.lijian.jb-browser", "top.ezzz.es-client", "com.huayi.intellijplatform.gitstats", "com.intellij.fragment.workspace", "com.github.h598937749.devtools", "cn.shaunwu.ptah", "com.wjcdx.verilog-lang-plugin", "io.github.cdgeass.p3c.smartfox", "com.project.GiphFinder", "ideaopenprotocolhandler", "com.vmware.tanzu.application.platform.intellij", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.UnaToolbox", "me.panxin.plugin.idea.jumpcontroller", "ris58h.webcalm", "com.github.nizienko.properties_switcher", "com.zkz.UniApp", "Workspaces2", "com.hxl.plugin.springboot-invoke", "org.bdware.yjs-formater", "color.scheme.One-Monokai-80s", "com.nosqlnavigator.mongo", "com.ssup.packagegenerator", "com.aexyn.compose.navigation.ComposeNavigationTemplate", "com.github.tiden0614.intellijfuncargcomment", "com.shuaishuai.golangHelper", "com.michaelcozzolino.mockmate", "de.tum.in.QuADTool_Plugin", "com.eitanliu.dart.mappable", "com.joetr.gradlefilesorter", "io.github.newhoo.restkit.ext.solon", "org.synccode", "me.naotiki.chiiugo-ij-plugin", "cn.xr21.chat.gpt", "com.yunpu.gem", "mythe", "com.conversion.pojo2avro", "com.github.johyunchol.intellijindexgeneratorplugin", "today.movatech.plugins.cuefy", "editor.for.fun", "com.zhjw.water_reader", "com.mola.mmp.<PERSON>-MMP-Template", "top.scaleda", "com.strive.gitlabprojects", "top.yinkh.CustomCompletion", "jamesons-dark", "idv.arc.convert", "com.darksheep.sheepnote.20230426", "com.tfc.ulht.Drop-Project-for-Intellij-Idea", "io.naraway.task-buddy-plugin", "com.allfine.software.plugin.pojo2record.pojo2records", "org.atya.opensource", "com.franklin.ideaplugin.easy-testing", "com.github.okayfine996.wasmify", "com.staryeah.gaoqiaoliangjiexingguang", "pt.ist.fenixframework.plugin", "com.plugin.nexchatgpt", "fr.o80.CodingProgressBar", "com.example.plugin", "com.tools.ToolsPackage", "com.github.zhangweizhe.tinypngplugin", "in.imperialb.imperial", "com.codingdreamtree.RestDocCodeGenerator", "com.github.ilovegamecoding.intellijcodexp", "io.github.guoci.PythonDocumentationLinkProvider", "dev.glasberg.marcelosdartplugin", "dev.turingcomplete.intellijdevelopertoolsplugins", "org.zowe.jcl", "com.github.bridgecrewio.prismacloud", "dev.xdark.betterrepeatable", "com.baomidou.plugin.idea.mybatis", "com.ivyapps.composehammer", "chatgpt.generate", "ru.ivanmurzin.FlutterWidgetGenerator", "com.kimtaek.OneDarkMode-theme", "com.syc.plugin", "com.neatlogic.i18nhelper", "com.emreyh.project-version-increment", "io.bitswar.OpenProject", "com.coreyun.pojogenerator", "com.xxf.i18n.plugin", "pagoda-tools", "com.plugin.freechatgpt", "FosseyeScan", "com.manolo_stiller.DocGPT", "com.wmsay.GPT4_lll", "com.shen.plugin.element2023", "color.scheme.early.riser.syntax", "org.vorpal.research.kex-plugin", "color.scheme.MonokaiPastelDimmed", "com.ecology.ecology_codegen", "vn.com.fsoft.docify", "com.github.kuweiguge.cleancodesweep", "com.felixastner.magefx", "AureliaStormCommunity", "com.ljk.JavaHelper", "com.github.shlaikov.intellijbpmn2plugin", "com.didalgo.chatgpt", "cn.messycode.scenicspot.beijingolympicpack", "com.inanc.smartcommit", "software.alicloud.cloudspec.plugin", "vip.testops.qa_design", "org.jetbrains.plugins.docker.gateway", "com.abraun.kcodegpt", "com.cppcxy.Intellij-EmmyLuaCodeStyle", "com.domaindoma.kyu", "com.hoteamsoft.sprite-intellij-plugin", "cn.pfinal.club.pf-generated-string", "com.intellij.ThemeSwitcher", "com.glab.glab", "sandipchitale.springinitializr", "net.seesharpsoft.intellij.plugins.csv.fake", "com.github.csnzoo.ja295u.wayfair-progress-bar", "com.github.akusanas.akusannoyami", "indi.nonoas.bookmarkx", "lauvsong.refactorgpt", "us.detr.Deno<PERSON>etLinter", "com.github.bapos.koogle", "com.github.peppy.ppyjetbrainstheme", "com.github.manu156.jpqltosql", "com.github.issaloubani.glbviewer", "app.baly.BalyOTP", "com.hamurcuabi.devprofiler", "com.github.linyuzai.cloud.plugin.intellij", "com.town.basket", "com.externi.codedoc", "me.thet.adb_debug_menu", "lwm.plugins", "de.firesource.gptassistant", "com.github.tranthach89.awssso", "org.saidake.smp-init", "com.idt.idea.UserSettings", "com.intellij.jsonpath", "io.github.coderlaoliu.encrypt.tools", "org.jdz.translate.translate", "com.davidxxx.easy-tabs", "com.azure.wind.nzook", "com.github.nbreum15.monorepotools", "com.tiobe.plugins.intellij", "cadonuno.pipelinescanautotrigger", "it.codewiththeitalians.weeeeeeeee", "sandipchitale.copywithlinenumbers", "com.wwr.apipost-idea-plugin", "com.android.tools.idea.smali", "com.intellij.css", "JavaScript", "com.intellij.jdbi", "com.huaanhuang.translate", "com.github.siropkin.kursor", "com.nekofar.milad.intellij.hono", "com.aeolid.GeneratorProofing", "com.github.shoaibkakal.photon", "com.liaodq.pgyerUploadPlugin", "net.seesharpsoft.intellij.plugins.chatgpthelper", "com.github.arnold.CopyMethodReference", "com.github.dcsmf.intellij-format-methods-plugin", "co.anbora.labs.jmeter.jmeter-intellij", "com.example.FlutterFlowExtension", "com.videogameaholic.intellij.starcoder", "com.bruce.intellijplugin.dtomarker", "cn.youhaveme.Comma", "com.fxkxb.demo", "com.github.antonerofeev.intellijplugin.remindme", "com.tecknobit.dotDb", "table-helper", "BajUploadToYapi", "org.viqueen.ide.project-links-plugin", "com.yinye.yyedt", "indi.qiaolin.idea.plugin", "com.finanteq.plugins.idea.cucumber-kotlin-idea-plugin", "software.xdev.saveactions", "com.mladich.lastactionnote", "dev.tool.nitfy", "io.terminus.gaia.BBC.plugins.BBC3", "sandipchitale.gistdoit", "com.pixelperfect.npm-package-json", "com.couchbase.couchbase-intellij-plugin", "org.szkug.protocol", "io.github.vacxe.cliactions", "dev.andrybak.intellij.copy_commit_reference", "com.yahorbarkouski.ctructure", "com.github.adraheem.darkino", "com.mfarag.fluorescent", "com.veracode.integrations.intellij", "dev.johanness.nixos-idea-publication-testing", "nano-monokai", "com.qz.json.util", "dev.j-a.swift", "com.vladislav-og.color-palette-generator", "dev.priporov.bc.branch-clipboard", "com.example.CreateConfig", "com.sugood.ijplugin.douyin-miniprogram", "com.whoami.qwerty-learner-idea", "com.mybatisflex.bigtian", "com.janvee.ImportYapi", "ua.romenkost.varreplacer", "jzy.taining.plugins.JSpark", "com.4lex4.precious-themes-intellij-platform", "com.github.maiqingqiang.goormhelper", "com.github.anushkasingh98.codeaid", "com.thoughtworks.hash-defaults", "com.github.bigmouthcn.talkxideaplugin", "RADioMANiac", "org.techtime.easytime-idea", "com.example.provider_temp_generator", "com.example.FlowTrackerPlugin", "com.xiaobaicai.AzureCoding", "com.jw.insertJPO", "com.yangyang5214.sts", "com.github.balbekovad.helloworldplugin", "com.mrs.platform", "com.sherloqrnd.sherloq_data_plugin", "guru.ide.gitlab", "com.steverules.customtitles.IntelliJCustomTitles", "me.rerere.unocss-intellij", "im.aitools.zego.ZegoAITools", "com.github.hib4.foldergenerator", "jrebel-supplement-extension", "com.superside.superfront", "com.example.toSQL", "com.iwhalecloud.oss.wsc", "com.github.frgmt.intellijplatformplugindotpath", "com.github.bepil.kofuse", "com.kozhanov.localstack.plugin", "com.github.chinavolvocars.android.studio.platform.plugin", "com.alistar.kotlinClassMapperGenerator", "com.brewlab.smellyorange", "cn.elasticj.optionalchaining", "com.mt.mybatisOrIbatis-log-plugin-free", "com.intellij-sentry", "com.github.ivanlarios.clean-architecture-plugin", "run.daodao.codehelper", "sutee-ide-plugins-intellij-base1231", "com.dbin.crud", "com.cxy.LiveCodeSnippet", "net.pmmp.library.phpstorm", "com.github.baristageek.watermelonintellij", "com.github.sloppylopez.moneypennyideaplugin", "cc.implicated.intellij.plugins.bunny", "com.notmrs.platform", "com.org.sqlsyntaxchecker", "cn.catarc.modelica", "com.github.sunznx.missingEmacsActions", "com.artificial-person.intellij-unit-test-plugin", "com.just161.tab2textplugin.Tab2Text_Plugin", "org.uicgroup.jiratempo", "ort.yutog.omega", "com.github.tom-power.project-view-select", "com.moorror.cola.strategy.plugin", "com.github.choiminseok.intellijplugin", "com.nekofar.milad.intellij.enhance", "com.tools.bobsrobbin", "co.makerflow.intellijplugin", "com.intellij.ml.llm", "com.wh.GaeaHelper", "dev.eltonsandre.kafka", "com.github.sebastianbarrozo.etendoideaplugin", "com.github.diekautz.ideplugin", "com.ClassOperations.xq", "com.nekofar.milad.intellij.turbo", "com.sursey.plugin", "com.brigido.springrestcodegenerator", "com.github.atlas.ls", "com.huan.JPA-SQL-LOG", "io.ariga.atlas.hcl", "co.anbora.labs.jsoncrack", "com.org.recommendvariable", "plus.lixiang.java-tostring-to-json", "com.toscl.markdownquote", "com.github.izhangzhihao.vscodepack", "org.github.ponking66.ccecdit", "com.cppcxy.Intellij-SumnekoLua", "io.timesavior.oneidea", "com.github.owenrumney.spotiJ", "org.wavescale.sourcesyncpro", "com.gafner.azd", "com.oldschoolcoder.echo-golang-plugin", "com.jameswoo.mvvmtemplate", "com.gitlab.plugin", "com.simple.plugin", "io.embrace.android.intellij.plugin", "com.gk646.codeStats", "terminator-theme", "com.az4mxl.plugin.import-image", "com.nekofar.milad.intellij.gridsome", "uz.rsteam.intellij.compose", "BarbiePink", "chatgpt.codehelper", "com.rrtt2323.quick_log", "com.lingyue.digital.plugin.generate", "androidstudio.tools.missed", "com.github.rslbl.ColorNameGenerator", "com.goploy", "com.xlh.plugin.fast-mvc-frame", "de.kfindeisen.intellijplugins.awesomenumbersearcher", "cc.bitky.jetbrains.plugin.universalgenerate", "com.github.xiaomiwujiecao.uniappmacrosupport", "com.github.olivernybroe.wingidea", "com.rocyuan.intellij.one", "com.github.linwancen.plugin.compare", "net.coding.ide.cloud-studio-toolkit", "io.github.onlyeat3.which-name", "com.github.hardcorequal.intellijgitquickview", "net.prestalife.svirtual", "org.xuxiake.swaggerToOpenapi", "com.andygu.SqliteRemote", "pers.zhf.fastdevtool.fastdevtool", "ru.rzn.gmyasoedov.gmaven", "com.dhiwise.wisegpt", "com.github.cyenite.codescribe", "chao.rims.idea-plugin-for-rims", "app.codepreview.plugin", "com.willian.plugin.idea.ControllerToJspPlugin", "com.tabbyml.intellij-tabby", "io.github.godfather1103.alibaba.p3c", "com.jetbrains.support.OpenKeymapSettings", "DesignPatternGenerator", "com.github.besok.foresterintellijplugin", "im.simo.rubymine.sorbet-lsp", "com.github.michaelpaulhp.daytheme", "com.rust.json.quick.RustJson", "com.ritacsullag.reviewer-plugin", "cn.kduck.plugin", "com.github.intfish123.authbyenv", "com.zelaux.arcplugin", "org.zahramokhtari.FinalPr", "com.flutterflow.FlutterFlowCodeExport", "org.example.chad.template", "com.labourway.mihuo.mihuo_migration_tool", "com.jetbrains.rust", "dev.autometrics.plugin", "de.tschallacka.phpstormxdebugskip", "cn.lbc.customization", "com.caiyun.translate", "cn.lbc.novel", "liuhao86.coco.intellij", "codeemoji-plugin", "com.github.owenrumney.nighthawk", "io.nanfeng.creator.ddd", "com.vinted.packwerkintellij", "br.com.emersonmendes.hover-translation", "notimeforphoton.dpb", "com.trianguloy.mavendependencycollapse", "fr.imacaron.keylogger", "org.bitlap.sbtDependencyAnalyzer", "com.aiwan.RustTool", "com.yingxingguihai.idea.plugins.polaris.tomcat", "me.omico.intellij.settingsHero", "me.waleedyaseen.runescript-plugin", "com.lovecaa.airscript", "com.jsp.xss.idea", "com.h8000572003.intellijplugin", "simple-zhang", "com.e2e-testing.uuv-intellij-plugin", "com.intellij.restClient.postmanConverter", "com.suixin.fieldadd", "com.jetbrains.ide.streamdeck", "twistdll.relative-lines-jump", "com.mozt.plugins.restful", "commit-msg-helper-idea-plugin", "com.crmuk.avahwhitehead.commit-template-idea-plugin", "com.github.lipiridi.spotless-applier", "com.falsepattern.zigbrains", "com.c2ray.idea.plugin.sqllog", "github.zimoyin.Autojsx.WIFI", "com.tinkertoolkits.imageToVector", "ski.chrzanow.shakeoff", "com.qiyun.plugin.MavenHelperPro", "com.mel.android.templates", "com.cross.crosstest", "com.optimus.yapi", "com.datagraph", "de.j<PERSON><PERSON><PERSON><PERSON>.IntelliSeal", "com.github.leondevlifelog.gitea", "com.rickclephas.kmp.nativecoroutines", "adithya-devtools", "org.q2276225819.goerrorfold", "com.codigrate.aurora-borealis.theme", "com.github.clojure-lsp", "com.gavinhome.flutter.flying-redux-template", "com.hermicrab.lombok-hermitcrab", "com.suhli.datagrip-plugin-format-mongojs-prettier", "com.kusej.git-manger", "com.zhoujie.firstPlugin", "com.github.stephanjames.zenify", "de.lostmekka.important-files-idea-plugin", "com.zelaux.hjson", "in.payu.integration.helper.PayU-Integration-Helper", "my.ide.cloud-studio-toolkit", "MyCodeHelper", "org.hzero.copilot", "com.apisearch.ApiSearch", "KraggiTheme", "zju.cst.aces.chatunitest_plugin", "com.lio.mvc_generator", "ModificationNotifier", "com.ipaylinks.code-master", "com.github.flyhero.toolbox", "com.better.CodeGlancePro", "me.klez.jasper-report-support", "skill-theme", "com.zxy.ijplugin.wechat-miniprogram2023", "com.github.anorigami.myplugin", "com.wang.give-me-a-name", "io.github.godfather1103.custom.functions.for.live.templates", "cn.iichen.json2dart", "CodeWarsPlugin", "com.danlee.css2js", "com.example.ijanusz.phpspec-helper", "com.dilu.plugin.image-doc", "com.heger.countdown", "me.omico.intellij.gradm", "worktree.windson.git_worktree_manage", "com.github.vladsoroka.gradledaemonservices", "CodeQualityAnalysisPlugin", "com.zcyppyh.work_log", "com.abdallah.clearing", "squirrelsong-light", "com.pengdafu.IDEWebBrowser", "de.j<PERSON><PERSON><PERSON><PERSON>.KubeSeal", "com.github.osiristeam.deskuintellijplugin", "com.acctrue.commit-template-zentao-plugin", "com.example.updateCheck", "com.zhipin.diamond", "com.kusej.git-manager", "net.postchain.rellide.jetbrains", "me.jaffe2718.devkit", "com.mcjcloud.vscode-splitscreen-jb", "com.tencent.sdk.language", "com.company.updateCheck", "FitLang", "com.super.update.check", "com.odbpo.flutter.plugin", "com.oxygenxml.json.schema.generator", "com.krizej.ShellPipe", "com.github.seayon.jsonrpchelper", "com.github.dinbtechit.ngxs", "com.github.linuxchina.microservices.annotator.ext.plugin", "com.action.update.check", "chup.sql.cleaner.plugin", "com.kusej.idCardGeneration", "ru.sabo.commitcheckstyle", "net.fallingangel.jimmer-dto", "com.semgrep.idea", "ayu-dark", "com.jetbrains.support.KeyCodesTest", "com.abdallah.ProjectStructureGenerator", "com.github.h3rmt.intellijyuck", "com.proboscis.pinjected", "dev.runabout", "org.tony.liu.gradle.tree", "com.rri.LSV-plugin", "net.postchain.rellide.jetbrains.ultimate", "speech-to-text", "com.github.tobiasz.ideastatusbarfilename", "com.github.leecho.mp-ddl-creator", "com.essenceai", "org.rri.ijTextmate", "com.kusej.dbDocExport", "com.StackRecord", "com.yongtay.show-bookmark-description", "com.intuit.intuit-assist", "cn.ssuperrice.plugin.code-snippets", "com.junjiestudio.DatabaseVersionManager", "com.codigrate.everest.theme", "sandipchitale.helmet", "com.ls.akong.mysql_proxy", "de.mischmaschine.PasteIt", "com.alibaba.kos.schema.intellij.plugin", "com.dobodox.AdaptiveCaretScroll", "asm.bytecode_outline.re", "io.github.pursuewind.pursue", "com.elegant.ELEGANTPLUGIN", "pers.fanxing.idea.springboot.gen.plugin", "ca.codebuddy.plugin", "it.czerwinski.ide.plugins.valve", "io.github.folkly.guppi", "cn.you<PERSON>.<PERSON>ro<PERSON>ckCreate", "com.example.BongoCat", "cn.apipost.Apipost-idea-plugin-2.0", "com.hth.pojo2sql", "io.mend.advisecode", "com.dgm", "lk.ijse.javafx-controller-boilerplate-generator", "com.iflytek", "com.baway.bw-plugin", "com.samuraism.plugins.tiger", "com.github.yifei0727.idea-plugin-errno-auto-suggestion", "cn.moltres.component_bus.plugin", "com.jetbrains.edu.remdev", "me.prouge.sealedfluentbuilder", "com.github.hardcorequal.clearremovedrecentprojects", "com.github.bkmbigo.solitaire", "co.com.devlinx9.git-user-stbar", "org.kotsuite", "com.codigrate.autumn.theme", "color.scheme.dark-inspire", "com.bj58.wuba-sec-plugin", "NLSExchange", "ru.avicorp.EMV_Tag_Decode_Plugin", "com.skcc.plugin.corus", "com.github.continuedev.continueintellijextension", "sandipchitale.windows-open-with-dialog", "com.github.anorigami.template", "sandipchitale.select-in-vscode", "com.tiamaes.cloud.p3c", "com.ssh.tool", "dev.note11.dart_model_gen_kit.DartModelGenerateKit", "tech.qiya.ImportAll", "com.ast.vizitest", "de.tobiasdroste.androidactivitybackstackviewer", "com.watermelon.context", "com.jetbrains.hktn23.ij-nav-history", "com.github.xnchung.retrofitassistant", "com.bambi.bambi-kdocer", "solarized-dark-jetbrains", "com.intechcore.scell.ideaplugin", "com.ale.vncs.codfy", "vonpartridge.uni.Flow9-plugin", "de.ac.greenduckprogressbar", "com.minediversion.Javel", "com.envr.manage.env-manager", "eu.tyrano.Cyanogen", "com.zys.http", "e.v.cats-progress-bar", "CodeLineStack", "com.delicacy.SQLFormatter", "color.scheme.white-inspire", "e.v.pepe-progress-bar", "pl.digsa.flutter_arb_action", "io.intenics.python.oopCompanion", "com.supernova.schema-viewer-plugin", "color.scheme.SteffulaUltra", "com.itshixun.qstDevToolKit", "com.graykode.foundry", "com.vhausler.sorter", "in.xiv.rust", "se.isselab.hans", "com.github.biomejs.intellijbiome", "com.github.valich.FleetKeymap", "e.v.crypto-prices-status-bar", "polina4096.voices", "com.jetbrains.outsight", "IDS", "com.alibaba.wdk.nbf", "com.kopyl.commit", "com.github.dinbtechit.jetbrainsnestjs", "cn.xor7.easefieldtypein", "e.v.youtube-player", "com.github.dt.dependency-trans", "e.v.discord-companion", "com.uroozgeek.gitproxytogglerjb", "com.hmdevconsulting.vidoc", "com.github.lemick.hoverflyui-intellij-plugin", "site.futool.tools.futool", "com.github.aliakbarmostafaei.jetbrainsspacetheme", "com.phillippko.emojify", "me.seclerp.msbuild.devkit", "com.luoyangwei.goctl-devkit", "com.jun.o2ox", "com.nd.codevision", "com.sanyavertolet.kotlinjspreview", "dev.alexjs.dark-matter", "com.orbitasolutions.geleia.intellijplugin", "jp.masakura.textlint", "com.intellij.spring.graphql", "com.leit.idea.plugin", "ai.ctrlb.plugin", "com.github.kelinzhou.uikittemplates", "citric", "intellij.jupyter", "e.v.code-n-excalidraw", "com.github.bollu.leantellij", "com.github.alxmag.intellijfakersupport", "com.github.sashamikhailau.aemtesttool", "com.github.lordfirespeed.intellij-typescript-run-configuration", "holby-dark", "com.codigrate.sequoia.theme", "Late_Night_Operations_Technical_Office", "alkh", "io.github.appspirimentlabs.vectorpreview", "org.modelix.model.server.mps", "org.sidi.sdliferay", "com.github.danmou.copilotwordbyword", "co.huggingface.llm-intellij", "e.v.ai-companions", "com.github.fanlun007.helloideaplugin", "network.strong.remotessh", "com.masca.plugin.MaSCA", "io.datawire.telepresence-jetbrains-plugin", "com.gnakic.gruvbox.material", "com.github.izhangzhihao.idefeaturesmanager", "com.hecategames.jbtwitchchat", "com.stephanmeesters.QuickTab", "com.beqodia.GPTDebugger", "DevKit", "com.makeevrserg.mvikotlin.intellij.plugin", "com.github.aooohan.ktormgenerator", "org.jetbrains.plugins.gitlab", "io.zenwave360.platform.zdl", "com.spr.helmlint.helm-lint", "e.v.meme-reactions", "com.yahorbarkouski.recent", "com.wk.paas.visualDDD", "cn.yockliu.pmi", "me.joshh.intellij-code-screenshots", "com.github.wenpiner.flutterassets", "dev.jab125.intellij-preprocessor", "MrRedisTool", "com.codigrate.sakura.theme", "com.sscui.apifox-helper", "com.enation.itbuilder.plugin", "com.zeedas.plugin", "com.example.JumpingLines", "com.fappslab.DiffLinesCounter", "com.enation.softfactory.plugin", "freeriders.mag.<PERSON>-Architecture-Generator", "color.scheme.UntittledDark", "com.github.helloyeew.helloyeewjetbraintheme", "com.implemica.drypush", "com.msxf.mdp", "com.github.notanelephant.codingbuddyplugin", "com.github.julianshalaby.chatwindow", "com.github.toolbox", "com.intellij.bicep", "com.kun.idea.plugin.jarPackage", "org.mdui.plugin", "org.zksky.TabColor", "blue-y", "com.github.meimingle.jetbrains-jpegxl", "com.github.pberdnik.dependencyhighlighter", "org.hurricup.profiler.perf", "com.hiui.component.snippet", "com.github.ttxd19.seatcattab", "com.framework.plugin.yisingle", "com.lasagnerd.odin", "com.github.lokeshponnada.adbxpert", "com.github.crazybunqnq.pluginsexporter", "cn.xmirror.xcheck", "color.scheme.Dark", "com.himura.easyut", "me.dench327.plugins.guidgenerator", "com.oracle.ocidbtest", "co.anbora.labs.nushell.community", "com.noumenadigital.npl.idea", "com.github.fbricon.cuddlyenigma", "com.github.fusuma.androiduithemescreenshot", "com.codigrate.roraima.theme", "SE-laboratory.colog", "me.dench327.plugins.usinglocator", "com.github.dshane001.postbuildexecutorplugin", "com.mena97villalobos.VersionCatalogMigrator", "com.github.bgomar.consolelogger", "com.hibug.idea.plugin", "codeshell", "com.jiang.GenerateSetAndGet", "cn.mercury.xcode", "org.jetbrains.bazel", "DarkTheme", "com.github.guykroizman.fortunecommit", "com.hitechrush.codegym", "com.giovds.maven-property-extractor-plugin", "philipp.seiringer.IntelliJPluginDemo", "org.jetbrains.android", "com.android.tools.design", "com.github.hpich.cognitide", "tech.gujin.ideaplugin.openurl", "org.recompyle.recompyle", "jxml", "io.vanny96.argo-workflow-support", "org.manictime.plugin", "org.bgyfw.plugin.bygfw-ai-chat-plugin", "com.axx.devopts.axx-code-idx-intellij", "com.gwm.harmony", "pama1234.processing-pama1234-theme", "com.shopee.ShopeeGit", "com.dashwave.plugin", "com.github.bannirui.orm-generator", "com.github.wonjongyoo.ngm", "com.casthighlightsca.extension", "com.onepiece.wj.plugin-graphql-java-tools", "AntSupport", "org.extechnica.jira", "com.github.catppuccin.jetbrains_icons", "io.github.mnesimiyilmaz.sql4json-intellij-plugin", "com.xwrite.toolkit.flutter-build-runner", "teamcypher.cypherprogress", "com.mayi.ColorPlugin", "com.alita.plugin", "com.sonic.pycharmplus", "com.github.manu156.pinpoint-integration", "co.tula.MermaidChart", "com.github.kkkiio.gocopyqualifiedname", "jdh_gpt", "org.tom.customcodegen.CustomCodeGen", "com.github.GitIdentityChecker", "com.doodkin.copywithproblems", "com.netreq.fix.plugin", "com.aiwan.compose", "com.oeong.idea-tools", "com.navercorp.idea.plugin.maven.sdkhelper.maven-sdk-helper", "be.sweetmustard.testnurturer", "ca.ulaval.glo4002.codereview", "com.sailorj.itools", "com.anker.pilot", "com.ws.generate.plugin", "dev.kahon.language.cairo", "paper", "MrJsonTool", "dev.slint.ideaplugin", "com.epitech.EpiHeader", "com.github.pernilsalat.newbarrelplugin", "call-graph-2023", "org.label.translate.LabelTranslate", "failgood.idea", "com.fushi.hotswap.01", "com.github.clojure-repl", "JavaFlowDiagram", "org.jetbrains.amper", "com.cycode.plugin", "com.mrkekovich.future_sliced_design", "me.mattco.serenityos-dsl", "dev.voqal", "com.storyhasyou.drd", "com.templ.templ", "core.framework.core-ng-code-generate-plugin", "CodeAnalysis", "com.intellij.editSuggestion", "io.portx.datasonnet", "com.ciandt.flow.at.ide", "com.github.denghuichao.chatcode", "com.kozhun.commit-message-template", "org.utbot.pycharm", "com.neko233.jetbrains-plugin.git-daily-report", "com.ofya.JVroom", "org.utbot.python", "org.jetbrains.support.ClearRecentProjectsList", "SolarizedLight", "alxmag.dcp", "com.vladislavog.light-contrast-headers", "com.spin.CaseGenTool", "org.sequencediagram.ideplugin", "com.google.CoroutineStacks", "so.ray.linker", "JASS", "com.easy-query.plugin", "color.scheme.Darcula", "my.tool.xrefactoring", "com.elias.Import", "com.colacode", "com.github.jinwatanabe.kitsune", "com.scanner.plugin.vulscanner", "de.shyim.idea.devenv", "com.github.curiosity97.slotpagetemplate", "com.nnnmdzz.idea.plugin.codereviewer", "Palantiri", "com.dazimu.dzm-idea-plugin", "com.github.linwancen.plugin.sql", "com.orientalSalad.TroubleShot", "top.verytouch.vkit.mydoc.plugin.idea", "com.aboat365.feign-helper", "com.bizyback.ding", "zzzz.test.a.aaaa", "br.yth.inCorporate", "com.vlaptev.test-order-repairman", "com.tunc.sqldelightcreator", "com.github.secretj12.hackatum2023namerator", "com.github.imyuyu.sqltoy-idea-plugin", "com.osum.plugin.localizeit", "com.github.adrienpessu.sarifviewer", "com.appwiir.android.plugin", "com.easy.deploy", "io.maliboot.www.hyperf", "com.wuhdev.swagger-generator-plugin", "kr.co.wincom.sjc.SimpleJsonCompare", "com.lindum.financequotestools", "com.pino.intellij-jdbi-sql-jump", "com.stackspot.codebuddy", "com.github.sufuk.serialportdatasender", "wzq.codelf.plugin", "com.mm.datealive", "com.tec.zhiyou.easy-latias", "com.nobidev.jetbrains.ide-helper", "com.github.strindberg.emacsj", "com.github.y0ung3r.gitglobalhookslocator", "com.github.kyay10.prettify-kotlin", "com.binmu.arkts.data.classes.from.json", "com.tiamaes.cloud.spring.boot.assistant", "com.zlcao.plugins.ArkCompilerSupport", "com.LanBaiCode.HookCodeGenerator", "kr.craft.java-builder-fill", "com.creorai.ReviewAI", "com.github.hamfer.bracketblock", "com.github.sitdownrightnow2552.unpolyjetbrains", "com.hello-ideplugin", "com.github.sblundy.changelistprotocol", "nautime.io", "com.github.koterra.intellijdoom", "com.github.dillongrimes.pycharmimagepreviewer", "com.github.imthegoose.goosetheme", "com.prismstats.plugin.jetbrains", "com.sensetime.sensecode.jetbrains.raccoon", "com.upenv.linkpuml", "com.github.kechinvv.libslpluginij", "io.codetrail.codetrail-intellij", "com.guozhiy.demo", "me.lokbok.luke", "com.neko233.jetbrains-plugin.my-terminal", "me.tomsavage.vamify.Vamify", "com.bottlerocket.plugin.jsonfilter.JsonFilter", "com.cts.process", "io.github.mani-sh-reddy.mariana-light", "com.javastat.javaStat", "cn.haloop.swi.openapi", "com.github.flagshipio.jetbrain", "org.fulib.fulibFeedback.intellij", "com.tracy.a8translateplus", "kingdee.cosmic.coding.assistant", "com.redhat.devtools.lsp4ij", "ai.devchat.plugin", "com.saigyoujinexas.AutoWSAConnection", "com.github.drjacky.avocado", "com.yzxaz.GenTools", "com.hyway.plugin.atelier", "com.escape.plugin", "com.maiya.ImageCompression", "com.nasller.requestmapper", "com.serranofp.fir", "com.esprito.spring", "com.iflytek.autofly.plugin.vcs", "com.alexandermalcin.browser", "gruber-darker", "com.github.beardyking.bettercommandlineargs", "ca.uwaterloo.swag.pilaipidisoruku", "PalantiriLite", "io.daytona.jetbrains.gateway", "org.aurora.gobatis-goland-plugin", "com.xolving.deepup", "com.github.ikorennoy.remote-file-access", "com.github.sometimesayjez.jetbrainproject1", "me.rafaelldi.aspire", "easy-entity", "com.don.plugin-module", "com.vk.idea.plugin.vkompose", "typed.rocks.witt", "com.github.connorwyatt.sakuraintellijtheme", "com.github.reinaldomoreira.emscriptintellijplugin", "com.colin.toolLibrary", "io.github.linwancen.plugin.line", "org.sui.lang", "AlexPovar.ReSharperHelpers.Rider", "com.ouyang.CodeTemplateGenerator", "uk.ac.manchester.beehive.TornadoInsight", "tnt.evoscada.PluginForMavenSettings", "Cappuccino093.CappuccinoTheme", "com.mystnihon.timestamp", "com.github.lofcz.minfold", "org.nosemaj.astemplates", "dev.agnor.codecbuilder", "com.mm.codegen", "com.zhongan.devPilot", "com.github.peco2282.excel", "com.github.oldmegit.goframehelper", "com.suixin.poihelper", "com.alita.chat.plugin", "com.enciyo.NavigationComponentLint", "com.memority.MemorityIntegrationTool", "com.pyfox.pps", "com.csmc.pilot", "io.github.xiaozhuai.jetbrains-jump-to-file", "com.coderzf1.StringsXMLSorter", "com.dj.tool", "com.nisus.my-classic-eclipse-dark", "org.vicotorcooperlol.gombok", "com.codeminds.jqExpress", "alibabacloud.developer.toolkit", "com.alan.plugins.MyBatisLogFormatter", "org.matyrobbrt.scribe", "gh.umaaz.asm.viewer", "com.kishmakov.mojo", "color.scheme.Atom_One_Dark_Sena", "com.microej.tools.android-studio-plugin", "org.basedsoft.plugins.basedtyping", "com.lixiang.liUI-helper", "io.kusionstack.kcl", "com.mercedes-benz.sechub.sechub-plugin-intellij", "io.github.lobodpav.spock", "org.ton.intellij-ton", "com.cl1fe", "com.tecknobit.mantisplugin", "com.github.dawidzak.languagebuddy", "com.github.codecov", "com.github.dixtdf.archive.manager", "ind.arming.go-struct-to-json", "dev.zenstack.zenstack", "net.sv-studios.tools", "com.intellij.ml.llm.template", "com.wlk.ideaPlugin.QLExpressDebugger", "ai.codeant.ide", "online.generalpashon.jpize-ui-idea-plugin", "com.plugin.tools.JHelper", "cn.yangtuooc.gin", "cn.myxl.gvsd-devkit-idea-plugin", "com.luomacode.CodeMoss", "org.custompush", "io.github.swan-geese.maven-cleaner", "com.tensor.createconfig", "org.jetbrains.plugins.code_assist", "dev.azn9.plugins.discord", "com.ep.EpPatcher", "com.github.disaded.midnighttheme", "com.gyoge.pb2", "com.techhuntstudio.matrix", "com.generate.generate-JsonProperty", "com.lipeng.zookeeper-client", "com.github.cirry.wxreaderjetbrainsplugin", "com.github.palmerovicdev.architecturegenerator", "com.github.leoliudong.pojoeach", "org.michaelangeloio.plugins.dit", "com.liliudong.mybatis-sql", "com.sage.codex.SageCodeX", "ru.xs8.arc-dark-theme-xs8", "cn.xmirror.sast.xmirror_plugin", "org.smartdot.idea.plugins", "me.alvin.dev.metadata-toolkit", "com.work.plugin.work-tools", "com.ltp.swagger.generate", "VisibleForTesting-atlassian", "com.my.antlr.tool", "com.mituuz.fuzzier", "com.huawei.hdn.craft", "com.github.d1sd3s.sort", "com.codeminds.swisskit", "kz.yers.parser", "Cairo_Analyzer_Test", "one.platform.plugin.PlatformNote", "com.github.lauvsong.langcursor", "cn.ccwcy.FileTemplates", "com.TypeInfer", "com.example.pssupporter", "com.cj.uploadPushFile", "com.vesoft.intellij.plugin.nebula", "com.tomtom.androidstudio.kmp.instrumented.tests", "com.github.nvelychenko.drupalextend", "com.baidu.comate", "com.jiang.mybatisCode", "com.github.warningimhack3r.intellijshadcnplugin", "org.wooter.mpxjs", "com.leaniss.jetbrains-ide-extension", "top.codeease", "com.cyb.cybertruck", "intellij.index.storages", "com.amber.tool.TranslateAndroid", "com.bofa1ex.dlvx", "pub.yufu.chat", "fleet.backend.scala", "co.huggingface.llm-intellij-sk", "csdn-<PERSON><PERSON><PERSON><PERSON><PERSON>", "ru.stykalin.FastTempFiles", "com.github.dergonokay.generatefluttermocksplugin", "fakethu.edu.cn", "com.rakts.JsonToArkTsModel", "com.github.nayunjae.wantedandroidstudioplugin", "de.andrena.tools.altn8-th", "com.github.jhonhenkel.brdevtoolsjetbrainside", "sh.suiyun.chooseLicense", "com.mgorkov.explainpostgresql", "com.kuanghc.halcyon-jetbrains", "com.apihug.copilot", "edu.kit.kastel.sdq.intelligrade", "com.jdd.FepFetcher", "18112397095", "com.sipios.documentation.genai-documentation-plugin", "kim.nzxy.spel.kit", "com.intellij.angularjs", "com.yunzhi.orava.plugin.orava-idea-plugin", "com.intellij.LineProfiler", "com.intellij.cvp", "com.ian2018.plugin.idea.AndroidStringLocalize", "com.oxygenxml.json.schema.diagram.editor", "com.hxl.plugin.cool-request", "com.ilscipio.magnolia-integration", "com.aboat365.dm", "com.jgdt.codeReview", "dev.<PERSON><PERSON><PERSON><PERSON>.spring-initializr", "com.mahmoud.CucumberDataGenerator", "com.generator.dirty.dirtyGenerator", "github.nowsoar.HelloWorldAction", "com.ben.ideaplugindemo", "ru.zoommax.BitcoinDocs", "com.amos.pitmutationmate.pitmutationmate", "com.emresakarya.tailwindshortclassname", "com.autoLog.plugin.autoLog", "com.iiyeung.plugin.zentao", "io.kadena.pact", "com.github.jairoguo.gostructtojsonplugin", "dev.hylas.ij.plugin.reorganizer", "io.github.notstirred.dasm-plugin", "com.rbeditor", "ru.ntrubkin.laconic.tests", "com.bee.pro.dev.string.localization", "com.github.dineug.erdeditorintellijplugin", "com.plugin.cssextractor", "com.hgy.plugin.J2O", "com.karsta26.VBScript", "com.drewzillawood.CustomProgressBar", "com.github.hillside6.idea-plugin-stocks", "com.cj.codeCollection", "JajajaGPT", "com.zzz.lombok-helper", "com.bytedance.gec.promotion.tetrisHelper", "org.firebase4idea", "com.protoseo.input-source-auto-converter", "com.aboat365.snake", "com.chtml.coder.rcc.cn", "com.artframework.domain.domain-plugin", "color.scheme.Fleetokai", "dev.nx.console.extensions", "com.github.connectai_e.kimi-commits-interllij", "xjn.nlnl.plugin.demo", "com.github.palmerovicdev.atomonedarkazure", "com.zane.plugin", "co.fitten.fittencode-intellij-beta", "commit-template-plugin", "com.github.deskid.external.view", "com.vmware.tanzu.apps.accelerator", "com.lomovtsev.xit-support", "com.github.bigdecimal-folding2", "com.kevindai.pys.PYA", "com.cquilez.pitesthelper", "com.chalice.yald.weather.plugin.id", "cn.com.mustache.plugins.restful.finder", "cn.com.mustache.plugins.smart.setter", "com.aosinin.plugin.tasks.cas", "io.nimbly.i18n", "cn.com.mustache.multicolor.brackets", "com.github.advanced-java-folding2", "typed.rocks.ts-worksheet", "com.shamilov.prayer", "net.hirohiso.comprohelper4j", "com.razomy.notation.idea.plugin", "Cairo_Analyze", "Operation_Technical_Office", "com.github.lohni.gruvboxnr", "org.linrol.77tool", "MavenBatch", "studio.crows.relativegoto", "fanta-theme", "com.melendez.upcaser", "com.github.vharatian.refexpo", "com.ilscipio.language", "dev.eltonsandre.micronaut-launch", "io.github.seiko.ktorfit.plugin", "com.aperfilyev.version-catalog-helper", "com.insperedia.yiiFramework", "dev.smart<PERSON><PERSON>", "com.intellij.wiremock", "com.bobdotcom.Nasal", "com.wanzi.ChangeCode", "cn.com.mustache.plugins.mybatis.link", "com.target.ConfigSorter", "com.jetbrains.restClient.fallback-engine", "fi", "HighContrastGreen", "HighContrastBlue", "com.ray.plugin.treasure", "com.github.korshunru.rgsgitcommiter", "com.github.stefanosansone.intellijtargetprocessintegration", "com.nesprasit.JsonToDartSerializable", "io.github.pavelannin.keemun.plugin.template", "com.dustingauthier.SmarterSemicolon", "org.atheesh.umd-submitserverplugin", "com.lzy.EntityJson", "com.github.optimisticGeek.spring-doc-helper", "org.jetbrains.jettrain", "de.achimonline.recent_projects_cleaner", "org.jetbrains.research.ictl.bf", "com.tomy.idea-office-assistant", "ru.vost<PERSON><PERSON>ov.grader", "com.auto.espresso.lab", "com.intellij.javaee.reverseEngineering", "com.intellij.flyway", "com.intellij.liquibase", "com.creorai.whattodo", "io.soteri.soteri-security-scanner", "com.github.jpvos.keylogger", "com.thomas.cleanBloc", "com.adgainai.springbooturlfullpath", "gli-gutenberg-snippets", "com.oakraw.MappingCopyPaste", "com.github.michaltrojek.selektivni-generator", "io.snakeOrCamel", "com.ogre.scriptslsp.ogrescriptslsp", "com.vaadin.intellij-plugin", "org.kunlab.scenamatica.plugin.idea.Scenamaticer", "sandipchitale.gradle.taskinfo", "com.josephggd.import-index", "com.bossymr.intellij-rapid", "neat-flutter", "org.standardsolvers.ps-code-snippets", "com.github.beatreichenbach.one_dark_two", "io.hanghae99plus.snippetCaseConverter", "com.github.dannyxcii.deepspace", "io.github.swan-geese.pretty-json", "com.intellij.react", "top.january.hypium_devtools", "dev.mirror-kt.jetbrains.phan", "coderhdemo1", "com.github.michaltrojek.selective-generator", "com.github.stackus.gohtjetbrains", "com.github.shih6.phptemplate2thymeleaf", "me.RomanAndr.googlefonts", "com.github.cracklybody.mojistatus", "com.github.rinchinov.ijdbtplugin", "UniSDKAssistantBeta", "com.indexer.exer", "com.github.openmindculture.intellijorangerainlighttheme", "com.github.openmindculture.intellijorangeraindarktheme", "com.github.boukenijhuis.breakpointlogselection", "net.floatinginspace.androidnotification", "co.lyall.intellij-selenized-theme", "com.github.openmindculture.intellijcommodorenightdarktheme", "com.xtu.plugins.github.trending", "com.github.lynxie.Oxocarbon", "com.xiaodingsiren.BeanUtilsHelper", "org.basedsoft.plugins.mypy", "com.anivia.aTools", "co.anbora.labs.jmeter.plugins.manager", "zielu.gitworktree", "com.intellij.openRewrite", "xyz.danizz.commit-message-wrapper-fixer", "me.kyren223.trident", "vn.com.ntq.nxdev", "com.samuel.zuo.summarize-code-changes", "com.hasura", "com.h.y.f.idea.plugin.fast_developer_center", "com.github.jozott00.wokwiintellij", "com.github.mrdolch.externaljavaformatter", "com.github.wl2027.datapivotplugin", "com.github.azbh111.ideaplugin.environmentvariable", "com.github.weizero.smartcoderintellij", "de.espend.idea.vuejs", "com.qyrus.QyrusTestForge", "com.garcia.ignacio.cleanarchgenerator", "ing.llamaz.woolly", "pp.qdev.solarized", "me.fornever.todosaurus", "com.bahua.flutter_print", "com.murphy", "org.chy.lamia-plugin", "com.griffin.jsontotypescriptclass", "ZL-Code-Review-Plugin", "cn.zxs.hotedit", "com.hhplus.dependencies-version-helper", "co.anbora.labs.jmeter.runner.jmeter-runner-intellij", "just.said.PythonProgressBar", "com.devsense.intelliphp", "com.aboat365.tetris", "yogi-amoled", "com.loganmay.jetbrains.plugins.wordwrapcomments", "com.pkx.plugins.PKXCodeGenerator", "com.tcl.plugin.search", "com.banfftech.edmTools", "com.segp_17.mymark", "ai.smartcoder", "com.sms.Undo-Method", "com.github.tom-power.gradle-plugin-settings-actions", "com.keyboardsamurais.plugin.source-clipboard-export-plugin", "com.varabyte.kobweb", "de.kfindeisen.intellijplugins.awesomecaretlocator", "org.btik.idf-for-clion", "co.za.principle.clai-intellij", "org.kylchik.git.worktree.checkout", "com.calo.open-iterated-files", "com.kiber.CompareMaster", "com.boaglio.ccpb", "com.supermaven.intellij", "software.xdev.openrewriter", "de.cubbossa.tinytranslations", "log.it.plugin", "com.tony.plugins.clog", "com.joymutlu.api-explorer", "com.balsdon.android-ally-plugin", "com.terminaltabtailor.TerminalTabTailor", "com.github.thilankabowala.securecodingguideline", "com.orrsoftware.maven_codeartifact_token_helper", "io.github.divinenickname.kotlin.utgen.idea.plugin", "CodeAssistant", "CodeAssistantFree", "Analog", "NewDark", "com.plugin.samusprogressbar", "com.nextchaptersoftware.unblocked-jetbrains", "ai.codelines.codelines", "com.kuaishou.kwaipilot", "com.github.samge0.KotlinxAndroidSyntheticToViewBinding", "com.zhangyq.code-generate-idea", "com.example.Base64", "com.codacy.intellij.plugin", "com.myplugin.myplugin", "com.nijunyang.idea-git.code.review", "lermitage.ij.extraidetweaks", "tech.contextive.contextive", "MPS<PERSON>ThirdParty", "com.github.gon2gon2.mdtdd", "pro.jothe.pydjinni-intellij-plugin", "com.github.liff.dbexternalcommandauth", "PikuseruDark", "tailwind.fold.tailwind-fold", "ObsidianSunset", "com.example.CodeGPT_2", "com.plugins.zhz", "com.samcarswell.MultipleEntry", "com.github.kordrad.angularsnippets", "com.crazymt.intellij.plugins.CatProgressBar", "XunchangTestPlugin1", "com.vel.buildvariantplugin", "io.xarate.idea", "com.mohave.ClasspathModification", "com.netwisd.inlog", "org.akov.akovformation", "com.xbingo.NacosHelper", "top.rxai.api.plugin", "endernon_midnight_theme", "cn.itcast", "dev.slne.surf.ide.surf-ide-extension", "robert.AndroidLayoutsHelper", "com.liang.novel.lnovel-intellij-plugin", "precisiontestautomation.scriptlesscsv", "jebe.liao.JBLMavenCleaner", "tech.shiker.enc-decrypt", "inc.kaizen.automata", "com.github.siggisigmann.PacManProgressBar.PacManProgressBar", "color.scheme.DarkMonokai", "hassony105.dartbarrelfile", "com.guonl.convert-bean", "com.nio.plugin.GsonToDart", "cz.tobb.stringpaste", "com.zxj.h6codegenerator", "com.Migliaccio.DB_ProgressBar", "dev.imake.ideracingprogressbar", "com.xhl.demo", "cn.gbk.navigableVal", "com.crazymt.AITranslate", "org.moniaga.openapi", "io.nerdythings.askgpt", "dev.el<PERSON><PERSON>re.mongodb", "com.wangyan.javaBean2doc", "wit", "com.ai.fexcopilot.template", "com.cl.CamelCase", "com.starlight.theme.gcd", "com.github.anbuiii.geminiplugin", "com.crazymt.aicodeinterpreter", "net.reqnroll", "com.yisiliang.idea.plugins.converter.file-encoding-converter", "com.xxley.commit-template-simple", "com.NZM.luchangsun.GMMessageWatcher", "wkkr.plugin.package-branch-coloring", "io.github.andrelmv.plugin.inlay", "com.example.typograph-lebedev.typograph-lebedev", "Momo_Code_Sec_Inspectors", "com.github.maximedallons.noteplugin", "com.ld.GPT-copilot", "cn.ilikex<PERSON>.TranslateDemon", "bright_coloured_theme", "io.github.pandier.intellijdiscordrp", "com.mabingbing.autoIncrease", "com.github.trex.raven.iceblue", "com.example.demo3", "it.orlov.cooklang", "ee.ut.lahendus.intellij", "teng.intellij.crypto", "com.jetbrains.writerside.assets.internal", "org.pehrs.vespa-yql-plugin", "tech.carcadex.kotlinbukkitkit.tooling", "com.zving.ztagcompletion", "sandipchitale.replany", "de.keeyzar.ddddirectory", "dev.ankit.CleanArchitectureForAndroid", "com.gitee.threefish.idea.plugins.PluginDevKitMaven", "org.remco.software.ncrunch", "com.songhq.Tools_all_develop", "tech.carcadex.kotlinbukkitkit.tooling.menu", "commit-message-helper-idea-plugin-Korean", "hellobike.cn", "org.qtproject.qt.androidstudiotools", "idea.hellobike.cn", "cz.tobb.imagefeedback", "cat.wavy.catactivity", "com.devfive.vim_switch_ko", "likco.plugins.structuregenerator", "com.contexts.authz-intellij.authz-intellij", "com.augmentcode", "de.uni_passau.fim.se2.intelligame", "com.github.mohamead.allure.report.plugin", "intellij-elm", "com.github.thinkami.railroads", "com.wildwind.windSlayerGitCommit", "com.mt.mpstdidekit.std.ws", "com.nice.meet_tool.PrintSQL", "Annotation<PERSON><PERSON>cker", "gradle_plugin_demo", "org.gamekins.ide", "com.wsl.symlinks", "com.afzaln.changelogcreator", "com.code47.screenshot", "io.edutech.aion.intellijextension", "com.mjr.lifeSaver", "com.cydeer.plugin.mybatis", "com.scireum.sirius-startup-notify", "dev.a<PERSON><PERSON>.<PERSON><PERSON>", "com.intellij.rider.godot.community", "com.codingchapters.main", "trantruong.oceanic-dark-theme", "com.ai.boy.programming", "ru.ellizio.odatacliui", "at.rayman.projectTabs", "in.sudhi.processor.toml", "boo.fox.haskelllsp", "com.erl.pdev", "com.github.mercari.grpcfederation", "com.codax.jetbrains", "com.zeroxera.android.res", "com.zp.pluginDemo", "com.gaodun.luffy.common.build.jar", "com.gaodun.luffy.code.build", "com.reneroboter.linklinker", "com.peter.ollamaPlugin", "loli.ball.imefix", "InertiaRails", "cn.aixcyi.plugin.TinySnake", "com.xl.skzz.android.plugin.auto", "shop.itbug.SalvoRsTool", "com.totrit.modulemold", "com.insyncwithfoo.pyright", "com.insyncwithfoo.pyrightls", "com.bohdanpokusa.WebSiteStatus", "org.jiang.WebServiceHelper", "com.wzp.plugin.code", "com.github.songgyubin.cleanarchitecturecoretemplateplugin", "com.czy.case_conv", "ToggleQuotes", "com.github.coolbeevip.git-assistant-intellij-plugin", "com.nov.flutter.config", "com.qax.CodeGen", "dev.ja.copilot-shortcut", "com.simple.SimpleConvert", "net.bondoc.tools.changelogchecker", "soulx.CodeReadingMarkNotePro", "com.dioxuslabs.dioxus", "com.github.jellyterra.intellij-tip-inlay", "org.julia.copyurl", "com.merkost.drawablepreview", "com.github.continuedev.ysw1206intellijextension", "com.devoxx.genie", "Lincheck", "com.browserstack.applive-android-studio-plugin", "com.majera.codereview.gitee", "com.github.leonschreuder.justsemantics", "com.github.jokerpper.intellij-maven-project-version-plugin", "com.gwm.json.HarmonyJsonToObject", "com.crazymt.chatgpt", "com.github.laihaojie.ideaplugin", "org.plugins.codechat", "io.vlang", "com.github.helloworldsg.tokenforge", "dev.ja.deeptext", "codemine.file-commander", "yij.ie.i18ner", "com.github.buptmiao.ffplugin", "ua.colorPickerPlugin", "com.xotoplug", "xyz.perspect.jetbrains", "com.github.norbert515.widgetpreview", "cn.clscls.plugin.simple-redis-client", "com.google.tools.ij.aiplugin", "com.light.MyBrowser", "com.srdcloud.IDEPlugin", "com.delicacy.SQLDebugger", "dev.<PERSON><PERSON><PERSON><PERSON>.gradle-jumper", "com.xixi.phone.screen", "com.liuujun.class2dml", "com.github.jbou.ideaundelegaterun", "com.showcode.ShowCodePlugin", "org.nette.latte", "com.github.xjw580.quickfxml", "ru.msu.deryugin.diplom-plugin", "ru.bazikalov.jira.JiraList", "com.pswidersk.sdkimportplugin", "com.apidog.helper", "com.netwisd.incoder", "com.redhat.devtools.gateway", "com.wquasar.codeowners.lens", "com.yuyuanweb.codecopy", "top.ctong.plugin.toogleboolean", "strug.intellijidea.shortcuttrainer", "com.intellij.python.django", "poimandres-darker", "aws.toolkit.core", "xyz.dowenliu.resilience4j-annotation-support", "amazon.q", "io.github.turansky.seskar", "com.suhuamo.comment", "com.thunder.haha", "visualboost.plugin", "com.evolvedqube.panic", "dev.ttyuyin.nocalhost-intellij-plugin", "com.github.amon365.ktgpt", "com.vv.fold.flutter_l10n_fold", "icu.hacking.plugins.zenith-laravel-plus", "com.craftsman.plugins.initializenamedarguments", "com.evolvedqube.easy-utils", "com.tcpip147.querybook", "com.github.tautastic.hamcrest2assertj", "org.polyfrost.sorbet.intelliprocessor", "com.coderevolt.super-hotswap", "shop.itbug.dd_kotlin_util", "com.github.biluping.chattool", "cn.k7g.CopyProperties", "com.keyflare.anygen", "com.github.blai30.galewindtheme", "at.brian.actions", "cn.kcnco.FastShow", "StackMobileTheme", "PengUI", "com.git.jumpToBrowse.GitJumpToBrowse", "xyz.dowenliu.alibaba-sentinel-annotation-support", "io.github.sorashi.attachi.Attachi", "portswigger.bchecks", "cn.kcnco.idea.plugin.FastShow", "io.threatrix.ai-certify", "com.github.nikolaymatrosov.cucumbergo", "com.marscode", "ValHighlight", "com.github.jmechamfd.kotlinadvancedfolding", "RecordBuilder", "you.thiago.PhraseDroid", "org.thesis.thesis-plugin", "com.vladigeras.open-git-remote", "me.pandras.color_names", "file.new_file_from_selection", "com.sercheo.file-analyzer", "DarculaMinimalDark", "rgrunin.dart.filenamesync.dart_file_name_sync_intelij_plugin", "com.zetrith.remodder", "com.linden.asteria-theme", "com.github.mwguerra.copyfilecontent", "com.vanjor.FragmentInComposeHelper", "com.mitohato14.shiftmodifier", "com.github.oomeow.jenv", "com.oscngl.java-progress-bar", "dev.fromnowon.fenix-buddy", "inga.intellij-inga", "com.intellij.bigdatatools.databricks", "com.androidbolts.fluttergenerator.FCCG", "com.kaciras.esbench", "com.intellisolve.intellisolve-plugin", "org.support.jetbrains.ShowKeymapSettings", "me.panxin.plugin.idea.PTool", "com.github.huangkl1024.defaultjvmargs", "com.intellij.exposed", "cx.by.backgroundpro", "com.fzero17.GitUser", "com.codegpt.CodeGPT", "com.atalgaba.jbwebosstudio", "com.dubreuia.save.tool", "com.mongodb.jbplugin", "xyz.shblock.justenoughinspections", "com.tencent.cloud.codingcopilot", "com.github.iseri.aicolleague", "cn.zhiyou.ZhiYouToolkit", "com.asus.ocis.AfsCoderIntellij", "com.flutter_code_generator.flutter_code_generator", "com.dahuangf", "com.qiuhuanhen.plugin.swagger.excel.doc", "com.alipay.sofa.koupleless.kouplelessIDE", "com.zhutmost.systemverilog", "cn.bigfire.protosttuff.generate", "com.gustavo.swagger.generate", "dev.openfga.intellijplugin", "LJYXP.EditorAssistForCS", "com.liubs.jaredit", "com.github.MorningZengJ.Toolset", "com.chenyuan.cy-db-log-plugin", "lime.plugins.fusiongrip", "com.github.bty834.tineyutheme", "com.github.shalaga44.missing-annotations-therapist", "com.github.maximedezette.tddhelperforrider", "com.sumanmsoft.quickdev", "com.github.kcdragon.stimuluslspintellij", "com.liuzhihang.code.inspections", "com.github.shiraji.namesticker", "com.fatnotebook.FastNoteBook", "com.github.vitallium.rubylsp", "com.github.feddericokz.gptassistant", "com.github.koooooo7.robin", "io.kusionstack.kcl-lsp", "com.peter.dependencyPlugin", "app.onefit.android.inspection", "cn.mindcode.MindCode", "com.github.testparty.pregamePhp", "sandipchitale.kubemmander", "com.github.xiaohundun.statusbarstocks", "com.aboat365.monitor", "com.intellij.cron", "com.github.aisetudelft.iallms", "com.intellij.jpa.jpb.model", "ai.maru.connector", "com.ssafy.ododocintellij", "com.aboat365.salary", "com.github.kings1990.fastconfig", "io.gitlab.jfronny.s-dom", "net.modulo3.robot", "gpt_android", "com.hfm.saghi", "io.github.Heracles-Support", "com.higherorderco.bend", "com.CommitScheduler.CommitScheduler6", "com.masarat.gen", "com.junkfactory.tokyodark", "AoikFind-IntelliJ", "dev.plugincraft.jam", "com.robotqa.devicefarmplugin", "me.pandras.riverpod_wrapper", "io.github.future0923.DebugPower", "com.fmz.plugin", "org.sovbox.plugins.sso", "jarvis.ee.carlrobert.chatgpt", "com.intellij.classic.ui", "org.bookmark.pro.id", "com.github.tooandy.blankspace", "com.github.dhcoderdev.dhcoder", "io.github.mschieder.openjpa-idea-plugin", "com.codeminds.hovernumberconverter.HoverNumberConverter", "com.fortify.ide.idea.scan", "dev.japo.japo-intellij", "com.huan.wang.save.action.tool", "com.liereal.PbGenerator", "de.jonihoffi.plugins.enhavo", "com.baidu.comate.gitee", "com.ai.dev.tools.languagePack.it", "com.github.virtualgraviton.cppfastcoding", "com.tsybulka.autoRefactoringPlugin", "com.jetbrains.interactiveRebase", "com.xxxlin.jsgf", "io.github.bty834.SummonSetters", "com.github.janmoeller.clioncmakesourcelistsorter", "dev.jeka.ide", "com.github.federicolencina.jetbrainsroterlagotheme", "fi.aalto.cs.inspections", "com.gitlab.coderider", "com.hy.daily_tasks", "com.jetbrains.plugins.bower", "com.github.tasksamurai.jetbrainsplatformplugin", "com.github.nizienko.weather-widget", "com.ray.ideaplugin.mybatis-navigator", "com.github.ultramangaia.gaiasec", "com.nttdata.copilot", "com.yc.zhFish", "one.bitby.retroblock", "vlascik.plugins.tabdir-redux", "work.zfh521.Intellij-Idea-plugin-Native2ascii", "dark-plus-theme", "io.github.wangzhengsi.method.invoker", "irony.ide.plugin.qsseditor", "io.github.hhy.bookmark", "com.github.scyslz.idea-debugger-enhancer", "com.aiwan.flutter-kit", "org.qihoo.codeyoda", "com.ai.dev.tools.languagePack.fr", "com.cmss.acepilot", "com.ai.dev.tools.languagePack.es", "com.ai.dev.tools.languagePack.pt", "com.ai.dev.tools.languagePack.de", "com.ai.dev.tools.languagePack.ru", "com.aic.codevista", "io.github.jeffset.yatagan.yatagan-conditions", "in.specmatic.specmatic-intellij-plugin", "com.phodal.shire", "io.arrow-kt.arrow-intellij", "Kaffu093.KaffuJetBrainsTheme", "com.rohanvashisht.bend.language", "gnome-theme", "com.github.yuezk.tomcat", "org.ax1.LispIdeaPlugin", "com.github.piter75.gruvbox-themes", "lermitage.ij.all.pack", "LJYXP.ColorBrackets", "io.github.takahirom.rob<PERSON><PERSON>", "com.intellij.stimulus", "com.github.natestah.blitzintellij", "com.AutoMain.ColorfulBrackets", "com.leon7.intellij-swagger-model-generator", "picimako<PERSON>lucas", "dev.el<PERSON><PERSON>re.camunda-starter", "com.vinist.faker-data", "com.faangx.KTP-Course-Update-Plugin", "color.scheme.SayanTheme", "com.knopov.plugin", "com.fpt.codevista", "com.github.kings1990.BeanAssistant", "com.igetcool.iCodeTest", "com.makeevrserg.mvikotlin.plugin", "com.joyo.plugin.joyo-plugin", "com.github.antonirokitnicki.codepatternprospector", "com.github.continuedev.ycontinueintellijextension", "com.akif.commitmessager", "BlackBoxFunctions", "com.github.naoyukik.copyprettygitlog", "com.github.jlcool.flutterbuildpublish", "com.github.olavlinddam.everforest-dark", "com.delicacy.DaoGenerator", "f.schnabel.idea.linkfilter", "org.getx_arch_template", "org.jdz.jtranslate", "com.joonsung.kson", "com.github.bty834.highlightstream", "com.github.minhnguyendeveloper.myfirstplugin", "com.guodaxia.mybatis-plus-generator", "com.xiejj.idea.plugin.db-doc", "com.github.sam0delkin.intellijpsa", "com.github.rcunal.testplugintemplate", "de.gebit.plugins.autoconfig", "com.zhen.fastBean", "com.drossan.project-list", "com.github.strangelookingnerd.pedro-progress-bar", "fkh.plugin", "color.scheme.CobaltM", "com.nickzhang.jetbrains.file_description_plugin", "com.biyusheng.maven.plus", "com.github.vikthorvergara.vesperoso", "com.mallowigi.permify", "linindoo.ssh", "com.github.jlcool.shorebird", "com.alfayedoficial.astagfirullah", "com.gitlab.coder-rider", "ee.carlrobert.MVSCoder", "com.github.bty834.intellijikunsound", "de.sirywell.handlehints", "cn.zjamss.plugin.CodeMemo", "cz.lukynka.twitch-channel-points", "cc.allape.caddyfile", "com.roytf.plugin.AutoTool", "com.my.company.unique.plugin.id", "com.xxxlin.json.ext", "com.maple.selected-camel-words", "com.adapgpt.xcodemap-idea", "com.hxb.restart", "com.ts.rubik.studio", "unoplatform", "dev.jezzy.cutiepro", "pers.wjx.plugin.carol-theme", "com.dodecahedron.plugin", "cn.tongdao.ideaplugintest", "com.meteoro.MonorepoManager", "FN", "okfe-code-helper", "com.uniscale.develop", "com.github.ramonvermeulen.dbtToolkit", "org.fromcountry.XHierarchy.HierarchyExtender", "io.github.tandemdude.hikari-lightbulb-support", "com.github.zjh7890.gpttools", "net.tsingyun.plugin.metadata", "pdf-viewer-mustache-enhanced", "com.github.mkartashev.hserr", "com.github.x0berkay.EzanVakti", "com.github.continuedev.continueintellijextension.worklink.copilot", "ru.ozon.ideplugin.kelp", "de.sparkteams.kasama", "com.intellij.wechat.miniprogram", "com.robmux.quickopen", "switchcaseinspection", "com.shinriyo.freezed_bingsoo", "io.github.estivensh4.kotlin-multiplatform-wizard", "io.github.orangain.prettyjsonlog", "com.jtracker.jtracker4intellij", "com.github.iml885203.intellijgitopen", "drak_greek_theme", "io.lambdachecker.LC-Intellij-Extension", "com.aicodemetrics.AICodeMetrics", "com.stoprefactoring.Christmas", "icu.xwj.<PERSON><PERSON><PERSON><PERSON><PERSON>", "ir.ma<PERSON>-ta<PERSON><PERSON><PERSON>.ShelveMe", "se.clau.gleam", "com.github.guilhermearpassos.extractStructFromParams", "com.github.jdha.codedivider", "nl.fwest98.jetbrains.approvaltests", "com.one.mobile.ButterByeBye", "cc.danteware.assets", "org.vyperlang.plugin.vyper-plugin", "com.drossan.cxserver", "com.xtb.lokalise.plugin", "blue-light-theme", "com.emertozd.imagevectorwizard", "com.enestoptas.KotlinAutoTest", "com.kingsrook.qqq-app-developer-intellij-plugin", "com.github.rikaaa0928.plugintest", "com.CodeGraphQLExtended.CodeGraphQLExtended", "me.itishermann.ollamacommitsummarizer", "com.github.jazzytomato.hurl", "com.rankweis", "com.opencsg.codesoulerintellijextension", "cn.memoryzy.json", "blues", "com.birariro.comment.highlight", "com.yantu.CNDroid", "CallerHierarchy", "breakstatementinspection", "com.titansoftApp.code_gen_template", "com.hugui.swagger3.tool", "com.github.onewildgamer.macosmeetsintellij", "com.george.gale.CombineAndCopyFiles", "cn.effine.smart-cryptor", "com.github.fmueller.jarvis", "com.sludge.beanutilsplugin", "io.moku.rubyfunctionrunner", "com.jetbrains.performanceScripts", "com.fyc.dev_tools", "color.scheme.Sober", "io.infracost.plugins.jetbrains-infracost", "com.github.selfancy.plugins.codespy", "com.github.oldfurybird.archery", "org.jetbrains.plugins.workspace", "xyz.dowenliu.alibaba-sentinel-annotation-support-freemium", "neo.plugin.cleanArchitecture.cleanArchitectureHelper", "palantir-gradle-jdks", "com.longshare.fm.plugin.ls-autogen-plugin", "com.github.salab.iccheck", "com.icoding.intellijextension", "com.runtime.pivot.plugin", "ai.zencoder.plugin", "robmart.ck3tigeridea", "com.github.hollis.live.templates.plus.hutool", "com.github.seaxlab.sea-idea-plugin", "io.github.composegears.valkyrie", "me.kapilarny.HackClubArcadeManager", "com.landamessenger.go_mvp.go_mvp_intellij", "com.github.projectaj14.fluttertestsassistant", "awesome.console.x", "com.github.kairaedsch.intellijpyinvoke", "neon-cat", "com.hjwei.Mybatis-log-parser", "com.wl.demo", "com.extractToDataclass", "com.soniyck.taborganizer", "net.abdulahad.branch_deployer", "com.github.hollis.live.templates.plus.apache.commons", "com.yourname.odincommit", "cn", "cn.yjq.plugin.idea", "org.jetbrains.idea.gradle.dsl", "org.zowe.cobol", "com.jetbrains.spaceport.rd-backend-plugin", "demo", "com.aymendev.aymoai", "com.jtracker.vscodeicons", "org.pyxy.pyxycharm", "com.github.donslon1.arcade", "com.lingyue.digital.plugin.joycode", "com.xiaobaicai.plugin", "cn.aixcyi.dunderall", "com.jbritian.read_book.id", "com.github.sydowma.fast.development", "pkg.tool.paster", "color.scheme.arc-dark-pastel", "com.yonyou.iuap.yms", "com.hmydk.CodeNameSuggest", "intellij.jupyter.py", "com.zhanglinwei.zTools", "com.sapient.chatgpt", "com.bf.copy2md", "com.atif.learn-intellij-plugins", "com.optimumcit.plugin", "com.github.salimkham.intellijgloodyplugin", "cloud.graal.gdk", "dev.honkanen.htmxpro", "com.adgainai.ApolloConfigVisualization", "com.wyf.float.tab", "de.achimonline.quickjinja", "com.hmydk.aigit", "ch.kleis.lcaac.plugin", "cn.ztion.sakura", "de.omar.clean_architecture_plugin", "org.pehrs.freemarker-generator-plugin", "com.github.skleprozzz.intellijfluttercleanfeature", "com.github.hspragggodaddy.insightfuldependencies", "com.jin_xc", "com.epoint.ztb.ztb-dependencycheck", "com.hamalawy.CleanArchTemplate", "io.metersphere.idea.plugin", "com.zhouzhengxi.vefaas-plugin", "org.fatpo.JetbrainsShowTimestampPlugin", "io.github.kituin.ModMultiVersion", "io.github.deblockt.cucumber-datatable-to-bean-mapping-intelij-plugin", "happy.mjstudio.HWPHelper", "org.code.generator.CodeGenerator", "com.snsoft.nbserverplugin", "com.intellij.notebooks.core", "DarkSakuraTheme", "MultivariablePostfixCompletion", "com.LK.ToolsBox", "me.theboiboi8.miasma", "com.github.whitel1st.intellijtestplugin", "si.vegamind.ftccompanion", "com.gbti.snapshotsforai", "com.yinhai.TA3AiPlugin", "me.panxin.plugin.idea.FAW.p3c", "com.github.mersadesalati.demopluginuserfeedback", "com.gocodeo.plugin", "dk.vuetest.testing.jumping", "com.RussianBar.RussianBar", "com.hamalawy.Ayah", "com.ideaPlugindemo.rcc.id", "com.github.l34130.mise", "com.gafner.rlm", "cn.aixcyi.shebang", "com.github.xgheaven.intellijkeymapnofn", "com.hxios.lang", "com.qunar.p3c.marketrule", "snap.dev.jetbrains.plugins.template", "nl.zaxiure.jetbrains.lazygit", "com.ukg.trackingids.add-tracking-id", "com.escuela.plugin", "com.github.kings1990.iBlog", "color.scheme.OrbitDarkPro", "org.apiduck.idea-plugin", "com.github.snaphat.jumptosourcediff", "com.yanftch.JsonFormat", "com.sylwek845.mockito.to.mockk", "michel-solarized-theme", "com.liaimei.CodeSuperTool", "com.charlyFixing.HexagonalCreator", "vanstudio.sequence.java", "cc.flawcra.phalconautocomplete.3", "cc.flawcra.phalconautocomplete.5", "com.yumi.CodeReadTracker", "com.techx.fastdroid", "fleet.internal.inspections", "com.bpce.fr.GenIndo", "com.github.aleksandrsl.intellijluau", "co.anbora.labs.codeql", "com.shinriyo.bufferconfig", "cn.nicepkg.gptrunner", "com.kevinmueller.razor-sense-jetbrains", "insyncwithfoo.uv", "cn.codegraph.codegraph", "com.github.iml885203.intellijgitmergeinto", "littleowle.ai.ollama.commit", "com.github.Glider2355.gauge", "vanstudio.sequence.js", "com.github.droiddevgeeks.cfsdk", "com.github.whwuhan.retrotheme", "com.bugever.trash", "com.locale_sphere.plugin", "rife.bld.idea", "com.qi.flutter_mvc", "linqingying.cangjie", "com.techx.xgen", "org.elegance.plugin.code.code-elegance", "org.omni4j", "org.sast.omni4j", "com.github.zz6880817.androidcodetemplate", "dev.aikido.aikido-intellij-plugin", "com.helpers.flyway-helper", "com.qi.flutter-dev-tool", "io.intino.itrules.plugin", "com.leve.aiHelper", "com.github.he1pa.intellijpluginpublishtest", "com.tseyler.livetemplates.sharing", "com.github.andrei5090.onboardingllms", "com.xiaohai.mybatis-log-converter", "com.generiscorp.cara-script-plugin", "org.sast.core.omni4j", "dev.adamko.problemIgnorer", "ChineseSimplified", "com.dev.memo.CodeMemo", "dev.<PERSON><PERSON>.lightsheet", "com.github.artem123456789.postmanplugin", "coco.cheese.ide", "cn.psvmc.<PERSON>p", "com.github.srwi.pixellens", "CursorSmoothCaretTransition", "dev.kikugie.stonecutter", "mats.bengt<PERSON>.askgptmibadd", "com.neuronbit.businessflow.designer", "com.htffund.plugin.replace-annotation", "CodeFlow", "com.intellias.intellicopilot", "me.blep.WebpackIntellijModulePlugin", "com.github.avayne2.uuidgenerator", "org.me.Notes", "com.github.crtree.ailoganalysis", "org.ldemetrios.kvasir", "com.github.kosavpa.pcontroller", "az.mtech.autoapigen", "tech.ryansu.simpleadmin.SimpleAdmin-Extension", "com.github.bancolombia.devsecops-engine-tools", "com.pharmcube.xjy.pl_SqlToDsl", "com.github.yhzion.jetbrains.plugin.deltareview", "com.kk.pluginLearn", "com.cppcxy.Intellij-EmmyLua", "org.dropProject.intellij-student-plugin", "com.github.dekola.intellijplugin", "sql-joining-graph", "com.claudemind", "com.nando.plugin.parameter-object-plugin", "com.github.minxie1209.dubboinvoker", "com.github.nymann.commitrefactoring", "me.sciberras.christian.php-version-switcher", "cx.eri.gutter-coverage-display", "com.nges.faas-paris", "com.echoapi.EchoAPI-Helper", "cn.com.moxi.plugins.api.search", "com.cpiassistant.CPIAssistant", "com.jjzq.qtool", "org.yelog.ideavim.flash", "github_color_theme", "org.juancatalan.edgepaircoverageplugin", "lean4ij", "nl.rabobank.investments.cactus.commons.BbanIbanConverter", "com.cheng.ChgPlugin", "com.yndongyong.androiddevhelper", "org.anupam.plugins.savepoint", "com.github.ctrl-ai", "com.ai.fynix", "com.github.denbedstegamer.pets", "uk.co.envyware.idea.helios", "com.seonhyuk.my_templates", "com.hzhiping.text-beautifier", "keploy", "com.huacai.automation-helper", "sspeiser.gMockGen", "com.github.gamefixxer.php-constant-manager", "com.tory.ide_dart", "fa09224710174f4f897968bcbc2c7c98", "com.github.badfalcon.backlog", "com.mbpolis.swaggerparametercheck", "com.meryl.dtk", "com.platform.auto", "com.jetbrains.intellij.plugin.teamcity", "org.mvnsearch.jetbrains.plugins.argc-assistant", "leigod.jenkins.control", "br.com.inoovexa.allyzio", "com.madbrains.mad_auth", "com.yanftch.json2ets", "ru.faresabubaker.plugin.requirements", "com.seczone.idea.seczone-sca-security", "cn.birdbaby.plugins", "com.razomy._etbrains._ntellij.ide.plugin.project_view.reverse_ordering", "com.github.codepalai.codepalintellij", "com.bruce.hotswapHelper", "ai.swiq", "<PERSON><PERSON>", "fi.aalto.cs.replace", "translate", "AndroidAOPPlugin", "cn.xxstudy.QuickFlutter", "com.ideaPlugindemo.gyl.id", "com.luban.plugin", "com.dynatrace.intellij-plugin", "com.github.maxfallishe.glossarygurupycharmplugin", "com.moonkite.moon-pets", "com.github.bukunmiola.vdetekt", "com.alaje.intellijplugins.flutter_images_gutter_icon", "EasyCoder", "com.jiduauto.millow-helper", "com.yanftch.hdc_tools", "BBCodeHelper", "snakeskin-lang", "com.merge.check.designer", "com.dengzii.json2entity", "com.github.enciyo.ghcheideaplugin", "com.sap.cap.cds", "org.tabooproject.development", "com.TaiwanStock", "com.txl.hosts", "org.fluidattacks.plugins.template", "com.jmpeax.ssltoolbox", "io.github.eskibear.jetbrains-code-runner", "com.funenc.Openapi-JS", "com.github.cnrture.quickprojectwizard", "com.github.firebender.androidstudiocopilot", "com.wangyuanye.plugin", "com.github.liupack.flutterriverpod", "com.github.adityasz.theme", "insyncwithfoo.ryecharm", "react.css.module.all", "com.anshulkanwar.melange", "com.github.seablue1.treeviewobject", "com.github.lld4n.rocketiconsjetbrains", "com.dobest1.boyka", "com.github.JoeKerouac.beancopy", "io.github.linwancen.plugin.fix", "http4k.project.wizard", "org.kvisha.ReportGenerator", "pl.becmer.dev.mdbook", "com.roeniss.plugin.requestMappingSafeDelete", "com.cdn.plugin.log", "de.and<PERSON>.coding-aider", "github.threefish.idea.opens.plugins.id", "org.wl.sdk.action", "com.github.themartdev.intellijgleam", "com.pandali.iedit-intellij", "com.github.yuyuanweb.mianshiyaplugin", "com.delicacy.MybatisDaoGenerator", "com.solmano.datagen", "com.supalle.constant-x", "com.tencent.Java2ArkTs", "com.checkmarx.cxviewer", "com.github.sey2.intlautosort", "com.macoto.structureTree", "dev.spearkkk.deep-oceanic-next-theme", "com.seas.plugin.SeasHelper", "be.bruyere.romain.geometry-viewer", "codes.vg.better-kotlin-java-completion", "io.github.sukaiyi.spring-run-idea-plugin", "com.mapledoum.ccopier", "sh.illumi.labs.jetbrains-crackboard", "com.xiaolvpuzi.tool.smartinputpro", "com.nextcodegen.chatgptintellij", "com.kirikodevv.ContextGrab", "gitlab.lordcrekit.victoria3modsupport", "com.wd.msu", "darcula-even-darker", "com.intellij.javaee.jakarta.data", "com.aifbd.arms", "com.github.olivercastillo.jetbrainsdarkfalltheme", "org.ladybird.dsl", "be.sigmadelta.ballastcompanion", "com.block.goose-intellij", "com.pang.decrypt", "lermitage.openasproject", "com.intellij.qt", "com.intellij.spring.debugger", "com.intellij.lang.qml", "com.intellij.uiDesigner", "lermitage.noai", "com.wjy35.wij", "com.haohtml.translate", "org.jetbrains.debugger.visual", "com.lucas.lambda.env-generator", "com.antiumbo.transfer-time", "com.zeekbtye.tokyonight", "com.talebly.contents-to-clipboard", "solutions.sulfura.projections-dsl-intellij-plugin", "com.xiangzheng.demo", "com.error_search", "com.lmt.mtcoder", "com.oahceh.feval", "org.kratosgado.tabout", "com.itrunfenix.PR-SnapView", "com.github.imvs.cloud-config-helper", "com.mdrsolutions.thymeleaf.thymeleaf-support", "com.uptmr.plugin.chinesetypography", "ac.quant.quickfixspec", "com.intrucept.appsecops.intellijplugin", "com.sanshine.intellij-json-log-plugin", "ru.bmstr.demo", "org.mattshoe.shoebox.Kdux-devtools-plugin", "app.oyal.plugins.gitcommitmessagetool", "io.codeclimbers.jetbrains", "com.wangyuanye.plugin.MSC", "solutions.sulfura.telosys-intellij-plugin", "com.mystnihon.keymap-switcher", "io.codeling.plugins.jetbrains", "com.github.getcurrentthread.recursivemarkdowngenerator", "com.nicolasdrapier.pytorch-documentation", "org.vane.hub", "com.iamnaveenoff.darkCoder", "com.thirteenmodder.dart_string_extractor", "cn.xuhuanzy.plugin.translate", "org.jetbrains.profailer", "com.intellij.modules.json", "com.github.sirnoname2705.vscatalog", "cn.hb.hb", "com.vectioneer.plugin.motorcortex.toolchain", "com.github.zakarea.androidnestingintents", "com.vectioneer.plugin.motorcortex", "io.github.drewlakee.plugins.json.openapi", "vanstudio.sequence.rust", "com.wibowo.fixtools", "com.cppcxy.emmylua2.attach-debugger", "com.chinaclear.sz.component.generator", "org.vane.combo", "gradle-consistent-versions", "com.jinn.DirectoryToolBox", "everest-night", "com.aurimasniekis.idea.typespec", "com.github.dekola.androidMvvmBoilerPlate", "insyncwithfoo.taplo", "com.github.xepozz.yiisoft", "dev.th3dilli.vsdev", "io.olensmar.ImageReplacerPlugin", "org.dongx.plugins.gremlin-client", "cn.olange.rest.link", "akashcats", "com.github.zzcode.zzcodeintellijextension", "de.kherud.plugin.prompt", "com.usooft.intellij-ud-plugin", "com.codescene.vanilla", "com.theodo.apps.kuik", "com.xjj.idea", "org.tsingj.TsingjCodeGenTools", "dev.prots<PERSON>.security-linter", "com.dove.ohpm", "com.remove-comments.appRemoveComments", "org.datastic.docsBuilder", "com.one-pauintxi-theme.marcosramos87.intellijonepauintxitheme", "com.yulate.ReflectorMate", "com.github.heftekharm.vectorizesvgfromclipboard", "com.kuaishou.kspay.member.fasthash", "com.github.sieff.mapairtool", "jetbrains-plugin-fss", "org.datastic.pacman", "com.program.toDoList", "com.acutecoder.kmp.projectview", "com.jetbrains.rider.plugins.trx", "com.app.DrinkWaterReminder", "com.lhstack.tools", "com.kevink.SleecLanguageExtension", "com.dev.gear", "com.luweijie.dev.gear.generate.mapping.constructor", "com.addzero.autoddl", "com.softwaare.tryexceptplugin", "net.codeoasis.sce_jetbrain", "palei.yurii.image-converter", "ExperienceBuilder", "com.luweijie.dev.gear.InteractiveSqlGenerator", "be.sweetmustard.springrestdocsgenerator", "org.dashboard.scriptValidator", "life.ai4us.project-grouping", "com.dev.gear.GenerateDBEntity", "com.dev.gear.NewClassGenerator", "uz.xaldarof.mayda", "org.kanan", "com.meow.demo", "com.github.akashdhotre873.intellijlivetemplatesplugin", "us.appfluent.xwidget-intellij-plugin", "solutions.sulfura.ioc-flow", "com.github.drjacky.docgenie", "ru.zplugin.ZPlugin", "io.github.ozkanpakdil.opentelemetry-debug-log-viewer", "com.me.favourite.directory", "com.rafaelheid.fluidcomponents", "com.github.t-kuni.sisho-idea", "io.tayviscon.idea", "org.TongJiUniversity-SSE.code_versioning_plugin", "ru.tbank.gitlabci.helper", "com.github.karataydev.zedonedark", "champ", "com.github.tkuni.sishoideav2", "com.cln", "com.snart.idea.plugin.json2pojo", "com.myth.earth.earth-restful-helper", "com.xrosstools.xbehavior.editor", "com.openrouterai", "com.bamuel.spllang", "me.hang.elegant-coding-guide", "com.nd.sdp.ci", "io.xxl.TestStudio", "com.a4b.android-helper", "io.gitlab.jfronny.globalmenu", "com.cockepit.intellij-plugin", "com.dropdrage.SimpleComposePreviewGenerator", "com.codesight.idea", "nova.tech.sql-cop", "com.zj.gyl.windyplugin", "com.satya.prakash.nandy.json-2-env", "de.lieb<PERSON>.MyOllamaEnhancer", "contact.e7.npm-studio", "sandipchitale.portmon", "org.dawn.deveco.plugin.ResourceEditor", "com.github.jaksonlin.pitestintellij", "xyz.kbws.K-SpringTemplate", "com.szlanyou.youcode", "com.biyusheng.mybatis.pro", "me.wladimiiir.plugins.aider-plugin", "com.github.raschild6.momentumplugin", "com.zhangwh.g_pilot_idea", "com.github.xiaolyuh.mrtf-git-flow-tools", "com.jjy.plugin.ajindedabaobei", "DataBaseManager", "de.rakhman.vcs.scroll", "com.innovatenorth.matigenix", "color.scheme.Theme", "io.github.nahuel92.pit4u", "com.wei.wreader", "com.linecorp.android.featureflag.ij.plugin", "LangSpring", "com.neoteem.tools.neoteemtools", "com.itrunfenix.PR-FlowTracker", "mp.code.ij", "com.wjy.plugin.mapper2sql", "com.junyeong.generateRandomJsonString", "com.mwnciau.rblade", "gruvbox-material-light", "com.cullen.graham.repodepot", "org.fe3dback.nixlsp", "com.niikelion.ic10_language", "andrzejgil.idea-plsql_spec_and_body_lines", "com.wsl.encry", "com.zabank.ZAMagicAssist", "insyncwithfoo.rustanalyzer", "com.lyflexi.feignx", "com.tavanuka.onemonokai", "com.github.aragonerua.codecompletionfim", "com.pg.plugin.idea.mybatis-sql-easy-get", "dev.codetime.codetime-jetbrains", "com.chat2db", "com.gls.GlsLanguage", "com.BGT.BGT", "com.github.blackkbox.demo", "vanstudio.sequence.go", "com.xws111.SourceHelper", "dev.priporov.custom-icons", "com.github.agawronteam.changedtestsrunner", "instant-invoke", "io.namaek2.plugins", "com.alex.alekseev.CombineAndCopyFiles", "com.github.moredreadd.minijava-intellij-plugin", "com.gdme.plugins.webpulseforecast", "gruvbox-material-dark", "com.yanftch.StopCoding", "com.archistrator.idea.arch", "mc<PERSON><PERSON><PERSON>", "phphleb", "com.5peak2me.plugin.idea.gradle-version-catalogs-plugin", "com.github.tidelift.intellij_integration", "cloudcodecheck.oh.plugin", "com.simple.simple-util", "com.easytools.jsonscheme", "io.openbpm.studio", "com.jjy.plugin.ajinbigbaby", "pl.michalzimka.CodeSandboxPlugin", "pl.mi<PERSON><PERSON><PERSON><PERSON>.NameAssistantPlugin", "net.exoego.digdag", "intellij.javascript.bun", "com.vibrant.prismio", "com.sakshamc.unescaper", "com.eliasyoussef47.php.SuppressedAttributes", "com.github.tmr232.function_graph_overview", "io.github.bric3.rectangle", "com.github.rishabjaiswal.dartassetmanager", "com.github.cichyvx.beginformatter", "dev.timothyw.treegen", "sandipchitale.jb-exchange-point-and-mark", "ai.hounddog_plugin", "com.github.mathism.thecollection", "com.pycalc.plugin", "org.akazukin.editorBackgroundImage", "com.cppcxy.Intellij-EmmyLua-CFX", "com.goctl.plugin.goctl-helper", "com.ttxp.demo", "com.jitera.plugins.jitera", "leon.healthReminder", "org.wenxy.fishcodereview", "io.gitee.nn.actibpm", "com.xp.plugin.git-checkout", "dev.camunda.bpmn-editor", "com.intellij.rml.dfa.devtools", "com.eagle.focus.android-focus-develop-tool", "myplugin.layeredarchitecturegenerator", "com.github.cazayus.cmdsupport", "com.alessandro.composepreview.ComposePreviewGenerator", "com.github.antonerofeev.linesorterintellijplugin", "com.back-to-the-feature", "core-ng-generator-plugin", "color.scheme.Dream_Light", "color.scheme.Dream_Dark", "com.intellij.jvm.dfa.analysis", "com.yawl.vagrant", "woowacourse-java-format", "FluxVersionNumUpdatePre", "com.youssefmahmoud.pubdevSearch", "com.github.nizienko.notes", "com.samuraism.plugins.backlog", "com.lczerniawski.BetterComments", "com.github.takanuva15.intellijreduxdevtools", "caiqichang.mybatisuite", "sandipchitale.jb-pathtools", "com.nerdzlab.mvvm-flutter", "com.tasking.winidea", "com.ssafy.codesync", "com.xiaolvpuzi.tool.smartinputpro.marketplace", "com.tolgagureli.turkish-unicode-converter", "pro.ivan<PERSON><PERSON>.<PERSON><PERSON>", "com.github.softwarearoma.dartutility", "com.eugenashka.AutoASD", "com.ppolivka.javadocer", "color.scheme.dev_dark", "com.xiangyun", "com.project.yamlcrossfilenavigator", "com.takima.shortcut-learner", "pro.respawn.flowmvi.ideplugin", "ai.asserta.plugin", "org.cheek.SecretKeeper", "z.incubator.idea.plugin", "dev.sandip<PERSON>tale.jb-kubernetes-dashboard", "org.cfn", "love.forte.plugin.BadAppleTheme", "com.julienphalip.ideavim.peekaboo", "com.bhavishay.autogenerateplugin", "ovh.fedox.ntr", "FluxVersionNumUpdate2.0", "com.github.com.cakevm.intellij_huff_plugin", "tech.codemajesty.ai-file-format", "com.alan.plugin.projectautocustomconfig", "com.github.hadywalied.zakker", "me.datafox.dfxengine.handles-intellij-plugin", "com.github.orange-guo.fill-kotlin-arguments", "rs.devlabs.maven-project-info", "com.github.kam1k4dze.kaijuPycharmPlugin", "com.github.jdha.brackets", "com.liubs.visualclassbytes", "com.easy.code.stat", "cn.sp.CodeFaster", "com.github.ryoochan.acmt", "TypeSpec", "m.client.ide.morpheus", "com.burton.plugin", "com.github.ptitcoutu.codayinidea", "vanstudio.sequence.cpp", "coocoogame.com.et-tool", "com.ai.dev.tools.languagePack.tw", "com.ai.dev.tools.languagePack.cs", "com.ai.dev.tools.languagePack.pl", "com.ai.dev.tools.languagePack.tr", "com.ai.dev.tools.languagePack.hu", "app.productlaw.plugin", "com.github.cashanevm.aiplugin", "com.github.byshy.flutterassistant", "ir.mohsenafshar.toolkits.jetbrains.KotlinDataMapper", "com.unihaoui.ai.committer", "com.ruijie.feinfrastructure.aicopilot", "com.lyh.avalon-tools", "com.zll.search-all", "com.weirddev.log", "com.github.pray.fff", "cn.bughub.dev-box", "com.creappi.org.bloc_dart_mono_state.bloc_dart_mono_state", "io.github.fdietze.sabuni", "com.artena.llm.ide.koboldij", "com.tsvetilian.intellijproxyswitcher", "baby.mumu.plugin", "com.teamide.maker", "com.go.plugin.kratos-helper", "com.github.ugdot.plugins", "com.interlacekit.jetbrains", "code-light-theme", "com.wahid.GenArch", "jp.s6n.idea.typespec", "com.pengfei.li.gitredminejump", "com.github.jsw6701.devplugin", "com.larseckart.builder", "com.github.mucahitkayadan.envmasker", "com.github.deeepamin.gitlabciaid", "com.yiwei.plugins", "com.supporter.prj.expGitFilePrj", "io.github.wzs.code.gen", "com.dmitrysamoylenko.zeusthunderbolt", "marseille-progressbar", "dev.sandipchitale.jb-multi-clipboard", "org.byaicodereview.ai_cr_plugin", "id.devcert.plugin", "com.creappi.org.dart_class_and_repositories.dart_class_and_repositories", "rs.devlabs.gradle-project-info", "com.github.natank25.epitechutils", "com.oigx.lookmu", "SukhTheme", "com.go.plugin.hertz-helper", "Carbon.AbpInsight", "com.dangersoft.codevisualization", "com.basarozcan.easyconsolelogger", "com.github.mohamead.copy.to.jira.plugin", "dev.honkanen.vagrantfile-support", "works.szabope.mypy", "com.x-generator", "com.tencentmeeting.AutoGenMeetingBackTestCasebyAi", "im.monica.code.jetbrains.extension", "net.xprogrammer.yudao.ide", "com.shiqi.simpledemo", "dev.pnbarxAndTalos0248.idea.treecolor", "com.base64file.idea_plugin", "com.julienphalip.ideavim.functiontextobj", "dev.sandip<PERSON>tale.jb-docker-dive", "com.julienphalip.ideavim.switch", "me.v<PERSON><PERSON><PERSON>ov.ftoai", "com.px.px2vh", "com.padya.step-builder", "com.github.kyrylr.circom", "com.dmcwll.vb.just-demo", "com.plugin.jh.jhLogger", "com.lin.plugin.fish.reader", "org.threeform.idea.plugins.zen_editor", "Gruvbox-theme", "github.camilesing.flinksqlheler", "xyz.mwszksnmdys.demo-plugin", "com.intlimit.grsplugin", "com.bytegatherer.jsonplusyaml", "resharper_MyDy", "com.tfx.jspo", "<PERSON><PERSON><PERSON><PERSON>", "top.forwardxiang.jetbrains", "com.apidog.fastrequest", "com.yonyou.YonbipPlugin", "onedev-tasks", "com.layhuts.gradle.helper", "dev.hagios.BuildTimes", "tools.codereview.plugin.jetbrains", "SequenceDiagramR", "com.aiwan.design", "club.zhanssh.tool", "ai.jqk.plugin", "io.github.guoyixing.nacos-idea-plugin", "com.samdark.intellij-visual-studio-code-dark-plus-fixed", "com.github.efeegbevwie.bitbucketcreatepr", "ai.qwiet.jetbrains", "onedev-tasks-debug", "com.tfx.sudoku", "com.github.simonbuchan.oxlintIntellijPlugin", "com.guzz.ide.FloatingToolbarPlus", "dev.hagios.jetpackcomposepreviewcreator", "org.labs.genesis", "com.namecheap.nameko.rpc", "net.tuchnyak.bronotes", "new-builder-generator", "io.jactl.intellij-jactl-plugin", "com.fluent.builder.fluent-builder", "E<PERSON><PERSON><PERSON>", "com.github.gyeom.dependencyexplorer", "com.plugin.fast-translation", "hu.borkutip.opencvdoc", "com.github.nathanmbrown.uncloakinvoke", "com.github.eric65697.sessionguru", "com.github.kaktushose.jda-commands-inspection", "vanstudio.sequence.python", "JitWatch4i", "com.chylex.coloredbrackets", "com.genesiscorp.auto-save-on-typing", "jasper-report", "dev.sandipchitale.jb-helm", "com.github.iamjuaness.blackholetheme", "com.anyilanxin.plugin.gitcommithelper", "com.anyilanxin.plugin.easycode", "com.nc.pojo2yaml", "com.lupor.spectral-linter-intellij-plugin", "com.volcengine.vefaas-code-deployer", "com.hiosdra.commitmessagecleanerplugin", "com.liuchen.git-commit-genie", "com.github.diydriller.aisqltranslator", "com.FuzzyFileSearch", "schoettker.acejump.reloaded", "com.github.stock.viewer", "com.stec.stec-devops-client", "com.tfx.jumpBlock", "fr.craftmywebsite.extension", "pro.monokai", "de.timheide.ollama-proxy", "com.mitu.bloctemplate.BlocTemplate", "com.github.blarc.sops-intellij-plugin", "com.teamz.coderadar", "color.scheme.Anysphere-theme", "com.ryan.serialuid-generator", "com.kivojenko.plugin.display", "com.github.ahmdsalahme.fireflyer", "falcon-jetbrains-themes", "com.adrianguenter.php_aliases", "ch.nmeylan.plugin.jpa-sql-generator", "ai.latta.tool", "lofcz.tbp", "com.jinshuo.simpledemo", "com.github.seriousjul.sprinter", "com.github.damiano1996.jetbrains.incoder", "com.saba.idlenotifier", "de.timjungk.Keystore-Browser", "slang.plugin", "at.alire<PERSON>oh.idea_whisperer_for_laravel", "com.metepg", "cn.enaium.jimmer.dto.lsp.ij", "com.yii2supportExtended", "com.codezap.plugin", "thriftlabs.thrift-formatter", "com.niiti.hutouch", "ocaiosantos.com.github.CleanArchGenerator", "com.github.ojacquemart.adventofcodesubmitplugin", "com.github.shogawa.intellijenvfileplugin", "com.fs.restfultools.pro", "spring-javaformat", "io.github.mrkekovich.ktor-ddd-generator", "com.gnwebsoft.gnw", "com.spellchecker", "org.vito.classfinder", "com.intellij.mcpServer", "com.github.starfederation.datastarjetbrainsplugin", "dev.sandipchitale.kubernetes-context-namespace", "com.microsoft.azure.agent.plugin.AgentPlugin", "github.camilesing.sparksqlheler", "com.artificial-person.intellij-unit-test-plugin-main", "com.markdtask", "com.github.menwhorust.everforest", "com.github.felixng21.codeassistant", "com.seeyon.chat", "com.github.firusv.smarti18n", "com.gitlab.multisearch", "com.configcat.intellijplugin", "com.github.tobiashorst.jenkins-linter-idea-plugin", "org.zeith.hammerhelper", "com.charles.mybatis-sql", "com.github.flowfan.eventbusplugin", "com.duosl.plugin.rbk", "org.jetbrains.junie", "com.pedrao.serialversionuidgenerator", "com.adityaputra.sholatreminder", "com.lofcz.intellij-visual-studio-code-dark-minus", "com.yqn.codeSynchronize", "com.adhithya.jsonconsolelogs", "com.ultramega.timetracker", "dev.mikify.gqlqueryloader", "dev.sandipchitale.dynakeymap", "kz.s<PERSON><PERSON><PERSON><PERSON>.navigate-exceptions", "com.github.aui.ideplugin", "com.ownicn.action-script-plugin", "com.github.xepozz.metastorm", "moonbit", "com.github.efeegbevwie.jsonsmith", "com.github.formattoday.v2viewer", "org.sdk.popup", "io.kotzilla.koin", "com.microsoft.azure.agent.plugin.fenzhotest", "com.zl", "com.github.farexzy.easytool", "com.github.openmindculture.intellijmochamouselighttheme", "top.forwardxiang.jetbrains.vuepress", "lekanich.plugins.maven", "com.integer.copy-mate", "com.github.MaxAstin.KaiTester", "com.mryexiaoqiu.vcode", "org.jetbrains.fleet.devkit.intellij", "cn.season.plugin.curl2py", "io.atasc.tcptunnelj", "net.odyssi.log4jb", "guohui.me.AIDT", "dev.sand<PERSON><PERSON>tale.PluginExplorer", "org.aspiresys.codespellai", "com.flutterplugin.newfeature", "senozoid.warm-light-theme", "kd.xkqj.unittest.generator", "com.wry.build-project", "com.github.ewwwdp.darker-horizon", "com.krzyshio.idesurfers", "com.c5inco.studiothemes", "com.ssharaev.k8s.env.plugin", "com.devek.dev", "pl.piotrz.project-structure", "metallij", "com.puntogris.telescope", "io.github", "com.github.pddmain.memesplugin", "club.zhanssh.tool.fish", "com.javaLogManager", "org.hugopalma.domaintranslator", "cn.wanda.restful.tool", "color.scheme.MREPUMPKIN_Light_Scheme", "org.kth.plugins.json-matcher", "org.mapper.generator.MapperPlugin", "com.github.lukey78.exportfilestoclipboard", "com.ebenjs.flutter_untranslated", "com.banlake.enum-generator", "org.shaozz.sdk.quickCommitPatch", "cai.<PERSON><PERSON><PERSON><PERSON><PERSON>", "top.forwardxiang.jetbrains.bookmark", "com.jtools.mybatis.log.jtools-mybatis-log", "linqingying.gitcode", "com.ai.dev.tools.languagePack.en", "com.pedrollanca.loveframeworksupport", "com.vscode.jetbrainssync", "com.liuga.disableFileCommit", "whu", "com.github.zero9178.mlirods", "com.gofynd.fynix", "com.github.blingyshs.openincursor", "cc.oofo.easy-translate", "io.github.vkaze.crayon", "chester.language.plugin", "com.github.naoyukik.intellijplugingithubnotifications", "dev.robotcode.robotcode4ij", "com.boot.plugin.Log", "com.github.maniac787.rchspringcodegeneration", "com.github.joshflash.ponylangplugin", "com.github.nhatminhptithcm.json-debugger", "com.lifecosys.file-truck", "guru.ide.codepipeline", "com.github.jeffwise26.gitmetheurl", "com.wuhao.code.style", "com.microgpt.codeoptimizer", "micropython-tools-jetbrains", "com.wdkg.D-MAN", "KakouneBrain", "org.wxy.ideaplugin.ailowcode", "cn.ximcloud.XIMCloudTools", "com.pytestarchitect", "org.getrafty.fragments", "net.tuchnyak.brobrowser", "com.sedam.eui", "kk.kkhouse777.okogeprogressbar", "ChatCode", "cn.lidd.workspace.tool-for-ares", "slanglsp", "PopNLock", "com.youlu.plugin", "com.harmonyos.cases", "cn.lyy.NiuKeInIdea", "com.github.xtestw.cutoolideaplugin", "com.sapient.slingshot", "pangu-pt-language", "com.sohocn.deep.seek", "com.llm.gpt_home", "com.fenghen.notforlearning", "com.softtek.FridaGPT", "com.github.eucyt.rashimban", "github.com.coderyw.goland-swag-apifox-plugin", "com.fina.wheelclass", "com.ternaryop.mos", "taxi.tap30.compose.preview.ComposePreviewGenerator", "com.multixlab.conditional.search", "dev.sandipchitale.jb-codeflow", "com.wjcdx.vhdl-lang-plugin", "de.nrunodos.plugins.custom-comparison", "com.akefirad.groom", "co.anbora.labs.jenkins.jenkinsFile", "com.github.continuedev.autosailintellijextension", "com.adrianguenter.php_declaration_hints", "dev.sweep.assistant", "com.gitlab.jethril.poweredCursor", "org.logicboost.chat", "org.cc.cstc", "org.openspg.schema-highlighter", "com.github.continuedev.autosaildevintellijextension", "com.zoey.EasyCommit", "io.harness.ai.codeassistant", "ai.lbai.plugin.LBAICopilot", "com.contrastsecurity.ide", "com.letmedevelop.copyplugin", "aiquant.plugins", "com.nahco314.foro", "com.github.thkhxm.tgfplugin", "com.github.amondeshir.rustroverronremix", "io.github.chriso345.BatchUI", "com.github.qczone.switch2cursor", "io.github.gerardorodriguezdev.chamaleon", "com.hotovo.plugins.aider-desk-connector", "com.keepdev.PyCharmBrowserExtension", "co.anbora.labs.intellij-export-to-zip", "org.sayandev.sayanplugin", "com.github.schmosbyy.mobtimer", "top.fwjok.TinyCopy", "com.lancewu602.git-commit-message-tool", "com.cjoop.mybatis.codegen.tool", "Ruyi-progress-bar", "com.bin.plugin.timestamp", "com.ai.techsignific.TechSignific", "dev.meanmail.plugin.plugin-dev-assistant", "de.drick.compose.hotpreview.plugin", "net.opengrabeso.intellijprivatepluginauth", "com.jetbrains.mindstorm", "com.coderknock.codegen.CodeGenTool", "com.eino.jet-brains-extension", "com.github.nfzsh.intellijrancherplugin", "com.github.xepozz.phplsp", "com.lexun.lexun-plugin-version2", "com.github.codebase2prompt", "eva-theme", "com.github.52523.copyclassline", "com.kuantuum.seeker", "com.more.fw.mcstudioidea", "com.forlan.gitcommitplus", "com.github.xepozz.buggregator", "com.github.uc4w6c.bedrockassistant", "com.val-t-develop.wand-lang-plugin", "com.kelp.code-toolkit", "com.a7dev.clean-architecture", "org.javamaster.HttpRequest", "com.bytetube.BytetubePlugin", "com.alexkuz.composetemplates", "navicat-decrypt", "io.neo.plugin.mf.BetterX_Tools", "com.github.skrcode.springxmlbeantoannotation", "works.szabope.pylint", "<PERSON><PERSON><PERSON><PERSON>", "com.weakviord.filetagger", "com.work.plugin.jump-server", "io.revenate.actionate", "com.github.tanshion.mybatis-condition", "com.nothing.plugin.TranslateResMerger", "net.bluehill.commentRemover", "com.loklok.confuse.confuse", "com.intellij.smartUpdate", "com.github.antonirokitnicki.gitassumeunchanged", "it.casaricci.hass.plugin", "com.plugin.custompromptexporter", "com.henry.plugin.AdbCommandSender", "io.mamad.astgrepjetbrains", "fr.bare<PERSON>.<PERSON>", "com.github.yamlandprops", "com.locobuilder.helper.lb-helper-ws", "com.locobuilder.helper.idea.lb-helper", "com.wenjunhuang.codeepiphany", "com.jetbrains.mvvmhelperrider", "net.bluehill.commentRemover.csvbcpp", "com.hsjry.lingxi.apidocgen", "com.github.eatmoreapple.juice", "lermitage.extratci.lifetime", "co.anbora.labs.jenkinsfile.linter", "in.vikasrathod.markdownview", "com.giabrend.copycode", "JavaxPlugin", "com.github.xepozz.crontab", "com.blackbox.intellijextension", "org.ifinalframework.plugins.aio", "HexagonalGenerator", "com.modulith.handler-navigator", "com.intellij.monorepo.devkit", "intelliboom", "pl.atingo.bts", "com.intellij.invalidid", "com.github.bukowa.twuiplug", "com.quickmemo.plugin", "net.fedustria.reactcomponentcreator", "rs.soph.kotwiser", "dev.honkanen.htmxpro-lifetime", "com.utility.json", "com.myaos.xmind-to-excel", "com.github.zarkob.code2prompt", "cn.hylstudio.skykoma.plugin.idea", "gg.ninetyfive", "com.github.peterhoburg.filepopup", "com.banshee.BetterCommit", "com.github.pcha.bloblang", "com.github.sisimomo.codegraph", "com.murlodin.fca-plugin", "com.xhf.leetcode-runner", "com.anritsu.mediation.plugin.dtl", "net.optionfactory.jetbrains.ansivault", "com.kamel.rafiq_al_iman", "com.github.seanwang2008.json2tablep", "com.linecorp.cse.scavenger-viewer", "com.uublue.yimi.codegen", "sh.arif.llm<PERSON>", "com.github.yorik56.gitmoji-commit-column", "com.tzengshinfu.advanced-java-folding-plus", "com.pawsql.jetbrain", "com.fina.valuetojson", "com.github.xepozz.robots_txt", "com.hoho.cargo-helper", "com.jetbrains.gerryThemesPro.lifetime", "com.github.xepozz.phplrt", "com.zhang.demo3", "com.tortoise.dsl-plugin", "com.github.xepozz.gitattributes", "com.anan.annote", "com.gendtest.ai", "com.github.mr3zee.kotlinPlugins", "xyz.block.kotlin-formatter", "com.clipcraft", "com.guanpj.me.codereview", "com.acooly.plugin", "docs.gen.helper", "org.faktorips", "com.github.xepozz.gitcodeowners", "io.duhanmo.quicknote", "com.graceful.convert.GracefulConvert", "com.github.wkkya.fusionpdf", "org.localization4idea", "com.aminnez.plugin.clean.CleanArchitecture", "cai.ConvenientTools", "com.stackfilesync.plugin", "me.shawaf.visualcomment", "cc.xiaonuo.smart-flow-plugin", "com.github.roscrl.inlineaichat", "com.shinriyo.SlangMate", "org.phyreapps.PhyreStorm", "jp.s6n.idea.rustowl", "additional-editor-hotkeys", "poimandres-vs-code", "com.github.mousechannel.jetbrains_icons", "com.ccopy.CCopy", "com.github.zepocas.zenith", "org.jetbrains.plugins.remote-run", "cc.wenmin92.jsonpathnavigator", "com.wjcdx.systemverilog-lang-plugin", "org.hai.work.deepseek-ai-test", "de.franzbender.copyforai", "de.monochromata.coged", "AbsoluteDark", "com.kz.pojo_lazy_tool", "com.frybits.android.startup.sync", "de.r<PERSON><PERSON>.removeallbutthisbreakpoint", "cn.yuyao.cef-jump", "marcel2012.database.missingIndexPlugin", "com.juc.plugin.idea.mavenversionuploadx", "LetsCollabThemes", "com.jetbrains.helpsearch", "com.github.bsafwen.sbtcodeartifactcreds", "com.fina.cxkprogressbar", "net.ishchenko.idea.nginx", "com.github.xepozz.sitemap", "com.tools.deep.seek", "me.sparky983.komponent.ide", "com.github.mpecan.runconfigenvinjector", "com.github.alexandrelam.slamp", "com.github.irismessage.sessionterminatedcodeass", "org.tiktok.plugin", "neokai", "com.randlly.TextToolWindow", "MavenHelperPro", "cn.org.expect.idea.plugin.maven", "org.yazeez.LayeredPackage", "dev.michaelomichael.methodnumbers", "org.codewithyou365.easyjava", "alibaba-cloud.lincode", "com.diarcastro.drupalgenerator", "com.sky.consolelog", "org.localization4idea.es", "com.xxxx.laozhang-idea-plugin-maptoclass", "co.anbora.labs.groovy.jenkinsFile", "com.jeremymorren.opentelemetryriderdebugviewer", "com.aigit.tools", "com.deepseekgit.tools", "com.mohil_bansal.repo_quest.RepoQuest", "com.sherloqrnd.sherloq_data_plugin_enterprise_pl", "com.github.isnotokay.geojsonfiletypeplugin", "io.harness.jetbrains.gateway", "io.harness.oss.jetbrains.gateway", "Conflict", "NightWatchTheme", "com.javanight.theme", "com.gonudayo.judgeit", "com.diagram.Diagram_AI", "color.theme.ritchie", "com.wd.GoGenerator", "com.technology.ncode", "com.github.guchengod.projectassistant", "com.autohome.ide.plugin.p2c-plugin", "com.github.quytm.likec4", "com.ayPlugins.generatingFunction", "cn.enaium.jimmer.buddy", "org.pastalab.fray.idea", "org.altzet.foldercreator", "com.github.origamidevpete.comboopen", "com.giabrend.instacode", "com.github.continuedev.continue_ci", "com.morefun.morefun-plugins", "com.dw.ar.TempApiSupport", "ru.andreynaz4renko.junit-jupiter-annotations-helper", "com.github.ajaymamtora.ijsiblingfileselector", "ua.roslav.clipps", "csp-drogon-plugin", "intellij.caches.shared", "de.kaemmelot.datafilesorter", "com.mpxfactor.DartDoc", "com.github.sashi0034.angelintellij", "tangzeqi.com.chatplugin", "intellij.git.commit.modal", "z.zoomzb.idea.plugin", "net.orivis.orivis-plugin", "com.amlzq.csle.inspection.dart", "svelte-themes", "com.Brostoffed.ContextBuilder", "cn.idea.memoryzy.json", "com.chancetop.naixt", "com.github.mengxiaofei007.studyideaplugin", "cn.tool.memoryzy.json", "com.rubius.plugins.auto-api", "com.github.baroncyrus.aicodehelper", "com.json_to_dart_converter", "intellij.grid.loader.parquet", "intellij.grid.loader.shp", "com.ibrahimShe7ab.remember.android", "org.pilov.skripton", "com.github.ajaymamtora.ijutilities", "com.d10ng.aicodereview", "com.lawlielt.gvm", "com.shlok.SampleTestsDownloader", "com.lv.tool.private-reader", "top.forwardxiang.jetbrains.input", "org.tera201.vcs-analysis-toolkit", "com.intellij.analysis.pwa.java", "com.r0adkll.danger", "com.developer.deep.seek", "org.domaframework.doma", "gordeev.dev.aicodereview", "com.github.skywalkboy.comment2json", "com.github.tamj0rd2.anuraplugin", "com.sra.vectr.jetbrains.vectr-ide-plugin", "com.pawrequest.redscript", "dev.mockgen.plugin", "com.cyjet", "com.github.kristensala.pinnedtabs", "com.leibuyun.xidl", "com.livteam.jsoninja", "org.csv-color-pro", "org.pyango.plugins.gitlab.variables", "com.idea.tools.aicodereview", "com.aruisi.bean-collapse", "cai.DevelopTools", "com.felbus.contexter", "org.localization4idea.pt", "com.humbledroid.Kommander", "eu.dynomake.wupify", "com.jolt.plugin", "com.report.tools.aicodereview", "com.xavier.idea.plugin.XavierPojoGenerator", "com.zhen.fastCommit", "palantir-addon-updater", "org.localization4idea.fr", "com.devkit.ut.generate", "org.consolelogpro", "com.hysea.CryptoTool", "top.forwardxiang.jetbrains.obfuscator", "com.hope.ToolsPlugin", "com.miksuki.HighlightCursor", "vision.osk.taskme.plugin", "SanityTheme", "com.github.dxtan.theme.spacegray-dark", "xyz.block.gradle-monorepo", "dummy", "iHomeCoder2", "com.github.zijing66.dependencymanager", "org.mmmhaha85.code-assistant", "com.github.kodexdev.continueintellijextension", "cn.yunrong.easycontinuedev.continueintellijextension", "com.opencursor", "com.wd.yaml-helper", "io.github.timetrackerx", "com.github.woniu9524.codeaskjb", "com.immotors.mydialogplugin", "com.github.mikeamputer.arraycommentindexing", "com.markbakos.todo", "com.feisuanyz.feisuan-javaai", "RW.<PERSON>", "com.cn21.edrive.taghopper", "com.github.amirshafri.todohighlighter", "org.zhangwenqing.idea.ooxml-tools", "com.cerry.baroncyrus.saicode", "com.kld.plugin.demo.platform", "unhurian.json2array", "com.kld.md.modelDesigner", "co.anbora.labs.ireport6", "com.immotors.aicoder", "com.optimus.json2ts", "com.liuyangjun.pluginDemo", "com.sohocn.serialVersionUid", "org.localization4idea.de", "com.rios.codeawaretypo", "www.cy27.cn.mybatis-plus-code-generation", "io.tiklab.postin.ideaplugin", "com.redaction.sensitive-data-redaction", "com.thinksure.SqlToJson", "com.my.deep.over", "com.thinksure.smart_ddl", "com.cerry.body.saicode", "com.amlzq.csle.inspection.jvm", "com.pig4cloud.Method-refactoring", "com.ghozimahdi.jsontodart", "com.zz.code.sql-param-setter", "com.ghozimahdi.gmbloc.gmbloc", "org.aryak.demo", "com.prodot.plugins.recommendedextension", "com.kite.code.cartools", "org.tiktok.plugin.arthas.enhance.mybatis", "com.raj.plugin", "com.devkit.ut.test", "com.ideaPlugindemo.hnfy258.id", "com.s0xzwasd.go.version.plugin", "dev.thoq.Sorbet<PERSON>", "com.github.wanniwa.EditorJumper", "com.lxd.webviewplugin", "com.dmall.plugin.dev-support", "com.bawnorton.msp.MixinSquared", "insyncwithfoo.pyrefly", "com.devkelvin.ascii-replacer", "com.wangxt.ai-auto-commit", "com.laravel.plugin", "com.oysterqaq.dragonfly", "com.amlzq.csle.inspection", "com.tsingin.kt2ts", "com.muskan.TODOHelper", "dev.go<PERSON><PERSON>.smoothcaret", "com.fangyinwei.swagger2doc", "one.tain.jbp.code-screenshoter", "com.github.davidseptimus.armada", "net.justonedev.codestyle", "io.github.vudsen.arthas-ui", "com.davidmszrs.horizonjetbrains", "io.joswlv.jirabranch", "generator.TemplateGenerator", "com.github.ivanmisyats.copycontentplugin", "com.muskan.TODOManager", "org.li.ma.spring-navi", "kz.kolesa.branch-adder", "ro.raicabogdan.translationi18n", "com.alettsy.alettsyJetbrainsTheme", "com.alefranc.compose-preview-screenshot-testing-plugin", "dev.sweep.assistant.cloud", "com.horizon.plugin", "com.chat.deep.ai", "com.github.sunnywalden.crypto-price-plugin", "com.elitea.chat.plugin", "com.github.vsuhanov.fastpeek", "com.SKIE.MVC-Shift", "com.neuro.jb", "com.github.skoch13.pnpmcataloglens", "com.mockfei.SQLMonitor", "org.lect.lect-jb", "com.github.jlifeng.classtojson", "com.c5inco.studiothemes.newdarcula", "com.c5inco.studiothemes.cloudyblue", "com.c5inco.studiothemes.inbedby7pm", "com.cosmicfreyjalab.nordic-electric-ai-theme", "com.serranofp.kotlin-mismatch-hints", "com.paweld.projectscout", "yeamy.restlite.i18n.go", "io.github.nyub.selfieintellijplugin", "dev.stillya.vpet", "com.github.fineke.cinjector", "com.github.paulknisely.intellijpromptcopyplugin", "com.ai.engine.cty", "com.github.xepozz.php.xpath", "com.vpen.sync-field", "com.rhw.web-url-copy", "BetterDarkTheme", "cn.cangnova.cangjie", "com.lulu.object-format", "com.valantic.ide.plugin.pit", "com.fazlizekiqi.enVar", "org.stark.SetterSmith", "com.zhiav2", "net.nightvision.plugin", "com.blackbox.IntellijRobocoder", "com.zhangcan.simpleJump", "com.bril.plugin.plugintest", "cn.codeforfun.ngxs", "org.jetbrains.aidebugger", "ai.poolside.assistant", "yeamy.restlite.i18n.ets", "com.openfhir", "com.github.eric0117.jetbrainh3wrapper", "com.the0day.tinify", "com.daichongweb.gitplus", "rsarv.netrwplugin", "com.github.puhua.codeflux", "org.cobalyte.commit-prefixer", "de.rlang.alloy", "cn.ash<PERSON>.Shortcut-Key-Conflicts", "de.jaimerojas.clickup", "ir.farsroidx.m31", "top.fyam.GradioLauncher", "com.litvin.dependency.converter", "com.github.atm1020.tuilaunch", "dev.bloomaai.devassistance", "color.scheme.IrohTheCatRustyMidnight", "com.codeclocker", "com.github.thermoweb.adrmanager", "run.mone.plugin", "org.arcot.APIWiz", "com.tancy.plug.shouce-annotation-doc-helper", "com.aykoo.copyforllm", "dev.el<PERSON><PERSON><PERSON>.vaden-starter", "thrift-assistant", "com.github.turingassist.continueintellijextension", "org.ltz.Project-ReadOnly-Mode", "com.trustt.testgenerator", "com.cn.json.simple", "de.dontknow.gitlab-pipelines", "com.alimoor.flutterl10nhelper", "com.github.volodymyrlashko.monkeydarktheme", "com.ymb.dockercat", "com.app.app-test-license", "top.wuhunyu.plugin.copy-class", "com.tc.HamiltonIDE", "cc.unitmesh.sketch", "org.autojs.autojs.devplugin", "com.miksuki.TabSplitter", "com.nicorp.vibecopy", "com.github.michaelomichael.scopeswap", "com.jetbrains.git.explicit.rename", "sunghuni.plugin.jasypt", "github.actions.helper", "sagevspace.rainglow", "AureliaStormRE", "io.github.lidian.ideaplugin", "org.summer.kit.plugin", "com.free.my.deep.seek", "com.coding.set.code", "xyz.danizz.extended-prop-searcher", "net.codeoasis.sce_exp", "it.reply.cm.junit-generator", "ltd.clearsolutions.ai-commit-by-mavka", "com.wjf.switchbranch", "org.marry.SqlParse", "com.free.dp.git.gt", "com.thesis.CodeComparer", "page1", "com.github.iptton.kbuilder", "one-dark-sitects", "flutter-full-structure-generator", "com.ds.commit.seek", "com.honsin.aiword", "com.smarttranslation.plugin", "com.simple.my.js", "dev.meanmail.plugin.django-power-tools", "com.code.gpt.fast", "io.github.ayfri.kore.kore-assistant", "co.uk.androidalliance.plugin.dpad", "com.softsense.dartdatadas", "com.git.deepseek.cat", "com.ai.git.tools", "xyz.codeexplain.plugin", "com.ai.my.aicode", "io.greycat.greycat-plugin", "com.cx.code-review-plugin", "org.micoli.dxcompanion", "com.coffebara.stretch-timer", "com.free.zhang.code", "com.chat.gpt.tools", "com.github.kibettheophilus.buildlogicgenerator", "org.jin.plugins.boj", "com.ai.coding.my", "com.mon.free.coder", "com.gameamea.plugins.SaveAs", "ir.amv.os.CodeMarks", "com.shinriyo.riverpod-jump-to-provider-for-as", "com.nobunagastudios.utilitibelt", "de.theflames.theme", "com.audi.portmanager", "com.sarhan.sort-annotation-plugin", "syncfiles", "org.cursing_less", "com.github.jimmymtl.logshot", "ru.decahthuk.TransactionHelperPlugin", "com.chpl.plugin", "com.github.oxc.project.oxcintellijplugin", "com.weimuu.demo", "com.ai.assistant.bot", "com.ihorkozar.fvmfluttertoolkit", "com.github.tomdesch.prjumper", "com.leafagent.LeafPlugin", "faros.ai.plugin", "dev.oszoou.aura-theme", "com.github.marvellinusvincent.arcticdepth", "com.tencent.kuikly-open.KuiklyIdeaPlugin", "com.yl.logutil", "Cyberpunked", "com.tencent.TCA", "com.thingsboard.toolbox", "stringfastselect", "com.husttwj.TinyPngCompressor", "pink-functional", "dev.bulbul.flutter_bloc_generator", "com.github.maciekwiso.pyfunrun", "ai-dl.enlighter", "com.dalsegnosolutions.ij.swrf", "com.felixastner.nestkit.NestKit", "com.github.niklaswortmann.arktypeideaplugin", "com.gnakic.kanagawa.dragon", "com.thingsboard.widgets", "com.github.korykim.ruoyivueplusmodulefastgenerator", "com.dhh.flutter_freezed_live_template", "org.hz.cauchy.parcelablegenerator", "io.github.avaxerrr.filetreegenerator.dirdoc", "com.ai.code.my.review", "bvin.dev.plugin.commitgraph", "com.ai.boy.translate", "com.github.antonokolelov.openapiguieditor", "com.github.xenforo.query", "org.plugin.mvcUtil", "io.github.abpubli.dot-support", "com.free.deep.mycode", "com.sybernatus.syb-mindmap", "io.github.idanshperling.prettyjsonlog", "svg.downloader", "com.github.rabiloo.etheryintellijextension", "com.lwz.doc", "com.github.onuro.onuro", "com.github.denysrichter4.libraryversioncompare", "com.elenintech.capicopy", "com.plugin.commitwatcher", "dev.meanmail.plugin.devshiftsdk", "com.jiyuren.adb", "com.felixastner.aitools.ai-tools", "com.intuit.ixp.flagfinder", "org.work.deepseek-unit.tools", "com.husttwj.drawablepreview", "com.mf.bigtian", "monokai-pro-legacy", "io.github.avaxerrr.qsstoolkit", "pro.dkart.bumbu", "com.squareup.tools.mdx-daemon-plugin", "org.clean.architecture", "com.tiagocareta.ptbr", "org.mm.tool.junit", "org.jetbrains.idea.maven", "com.cfin.novel.cfin-mybatis-log", "com.simple.translate.go", "key.actions", "com.github.ginex25.RiverpodX", "com.commonroom.showprojecttreetooltips", "com.my.translate.set", "cn.helper.my.json", "org.javamaster.elementui", "qsseditor", "org.ai.test.tool", "org.stm.translate.smart", "org.assertive.plugin.id", "com.github.feelixs.sshplugin", "cn.harmonyoslearn.NavRouter", "com.ds.rev.code", "intellij-olive-theme", "io.github.vinccool96.lingua.idea-lingua", "com.github.hichemtabtech.jettreemark", "color.scheme.true-dark-candy", "com.github.takemikami.intellij-pyproject-tools-linter-connector", "com.adapgpt.vibedebug", "com.jellynugget.codeportal", "com.zj.jsonsql.JsonSql", "com.ugarosa.idea.edgemotion", "com.xhsoft.plugins.utils", "com.envilopeio.envilope", "com.miladnalbandi.composer.command.helper", "com.alaje.intellijplugins.pokepop", "com.liuhao.kwargs-spellchecker", "com.github.tomatofrieslan.accessibleai", "chain.report.pinescript", "com.ai.code.report", "com.github.jaksonlin.testcraftpro", "kz.pandev.jira_auto_worklog", "io.simplelocalize.extension", "ua.com.pimenov.latte", "com.kgJr.postKid", "callgraph.callgraph", "TestDataGender", "com.advanced.bookmarks.AdvancedBookmarks", "com.mm.yaml-runner", "chester", "com.github.senocak.propertiestoyaml", "com.github.hms58.statusbarstocksplus", "dev.abd3lraouf.openintools", "commit-message-support-idea-plugin", "faros.ai", "com.expose_run_debug_plugin", "org.ai.unit.my.test", "me.watermelon.a-touch-of-watermelon", "<PERSON><PERSON><PERSON>", "com.ltu.progress.bar.LTUProgressBar", "com.mxc.kratos-generator-plugin", "io.github.javafactoryplugindev.plugin", "com.majera.conventionalcomments", "host.anzo.commons-idea", "org.hamalawey.JsonMapper", "com.github.hootegor.copilot", "com.github.aignatev.intellijxdtsyntax", "com.vaf", "online.yudream.intl", "com.antlr4plus.plugin", "com.github.skrcode.javarefactoringtoolkit", "pe.msbaek.approved-files", "com.hsy.img2webp", "com.kitakkun.backintime.tooling.idea", "cloud.yelynn.env-manager", "org.chebur.LocalizeMyApp", "com.plugin.gitmultimerge", "com.acrool.react.utils", "org.tool.test.dp", "me.jkdhn.idea.dblookup", "com.mygittool.gitcheck", "com.rannett.fix-viewer", "com.github.dilika.tailwindsmartplugin", "org.nx.dbtablescounter", "tech.beskar.baid", "com.gitcontribution", "com.mixel.stacktrace", "com.loterio.aidg", "com.github.amriteshgupta.pojotoproto", "com.sercheo.KarateGen", "org.aixsoft.stream-navigator", "argparse.helper", "cn.uo.EditorTools", "com.simiacryptus.cognotik", "org.tonstudio.tact", "com.github.fcamara.fcopilotintellijextension", "com.frees.idea-plugin-external-libraries-folding", "com.mituuz.cobolforge", "org.ton.mylocalton-intellij", "com.zhen.goArrowFunctions", "me.deft.typograf", "ncap.sflogexplorer.SF-LogExplorer", "cn.ilikexff.codepins", "com.clover.dubboMocker", "org.jetbrains.mcpdebugger", "YanPak.CrocodileForGitLab", "com.skyland.jumptonql", "com.github.zjcrender.i18next", "com.bigphil.parquetviewer", "com.batyan.QuickLLMCopy", "org.contextmapper.intellij-plugin", "com.anthropic.code.plugin", "com.dalsegnosolutions.kotlinrefhighlighting", "com.github.jdha.selectpaircontent", "com.zhangheng.arktspage", "com.github.timan1802.fakedatainsert", "com.github.rohit091193.codereviewhelper", "com.nobl9.nobl9", "com.liuhao.doubleClickToCloseTab", "org.bropy.FileHightLight", "org.vito.mycodetour", "com.omnedia.functionfolding", "de.emn4tor.CommentGlow", "com.amtgard.buildertraitscompletions", "org.morkalork.tstesttoolbox", "cn.medmi.global.JavaMethodSignatureCopy", "org.icon.kit.tool.mapper", "cn.vitucrexd.flowmermaid", "com.github.magneto3572.spotyy", "org.stark.CoverageQuickLink", "io.github.nchaugen.tabletest", "com.github.rollingdad.mybatislog", "com.codemaestro.plugins.codemaestrotoolwindow", "com.stellar.plugin", "com.jetbrains.jessiealastor.plugins.alphabetizeit", "com.unspoken.corgell-highlighter", "com.0r0loo.quicklog", "cz.cuni.mff.umlift.plugin", "moscript", "com.PiCode.androidiosemulator", "com.hyperether.compose-multiplatform-res-locator", "com.danielholgate.agilecomponentloader", "com.ai.dev.tools.languagePack.community.ru", "com.ai.dev.tools.languagePack.community.fr", "com.ai.dev.tools.languagePack.community.es", "com.ai.dev.tools.languagePack.community.it", "com.ai.dev.tools.languagePack.community.pt", "com.ai.dev.tools.languagePack.community.de", "com.github.convisoappsec.securecodeintellijplugin", "com.github.avrilfanomar.picatplugin", "com.github.harleygilpin.gromlsupportplugin", "io.github.lklbjn.cryptor", "winter-is-coming", "com.github.xepozz.caddy", "com.nutys.simplelog.SimpleLog", "com.tensor.dccleaner", "org.jetbrains.plugins.gradle", "com.intellij.java", "com.intellij.freemarker", "intellij.aiplayground", "intellij.compilation.charts", "JUnit", "com.intellij.java-i18n", "Coverage", "com.intellij.configurationScript", "org.jetbrains.idea.reposearch", "com.intellij.dev", "com.github.stijndcl.marco", "com.cmion.idea", "xyz.block.ftl.ideaplugin", "com.ruiyu.cw", "com.go.atlas", "com.smartdrive.code-monitor", "com.liuhao.customImport", "cn.tinyue.console", "net.pandadev.ziit-jetbrains", "cssvarsassistant", "com.sohocn.kebabCase", "com.moutaouakkil.projectcolor", "dev.xeonkryptos.xeonrobotframeworkplugin", "com.huq.idea.flow", "solop.cc.batchrenamerplugin", "csense.idea.java-not-highlighter", "com.modestswan64.CopyCodebaseDetails", "com.xxyxxdmc.hoshisuki", "com.maidbridge", "com.hmncube.FlutterPreview", "com.lukastomoszek.idea.codemetricsvisualization", "de.hd.highlight", "com.github.mihalypal.biroplugin", "com.mine.HookRequest", "Fly_Monokai_Sublime_Text", "de.knudev.ai-export", "com.netease.gl.componnet", "com.yohannzhang.aigit", "com.ciandt.flow.coderintellijextension", "org.maravilla.SpringBootBannerPlugin", "com.github.bmccar.pitestidea", "com.leqee.codeGen", "color.scheme.Colorful", "org.almasons.SummonLog", "com.intellij.java30", "com.github.brendio.smartfold", "org.my.tool.icon", "su.rus.language-pack", "com.lxd.brick-breaker-game", "com.fueledbycaffeine.spotlight", "new-angular-component", "com.boundaryml.jetbrains_ext", "org.bigsy.integrantnavigator", "com.yognevoy.bx-controller-navigator", "com.uublue.yimi.codegen-ce", "com.automocker.plugin", "com.github.sebnoirot.copilotpromptplugin", "com.navigator.codeflownavigator", "com.github.khmelov.actionfx", "tech.shiker.orange-tech", "com.tbusk.vala_plugin", "com.wiggin.dubbo-tool", "com.zhen.fastBuild", "com.kdiachenko.aem-vlt-tool", "org.tool.extra.icons", "com.gradle.develocity.ide", "org.millipixel.portulan", "nl.jolanrensen.kodex", "io.github.aabmets.proof-frog-lang", "dev.forkyo<PERSON>.<PERSON>", "org.sididev.smart-commit-generator", "com.jefferyxhy.plugins.maven-project-peeker", "com.sentra.ut.generator.action", "com.ant.translation", "com.varnish.vtc", "com.codepulse.timetracker", "dev.rohs.jetbrains-whatthecommit", "io.github.linfers.autoinput", "com.jetbrains.opentelemetry", "com.jtool-echarts-view.rcc.id", "com.cx.TranslateAndCamelCase", "de.achimonline.easychmod", "com.github.mistralai.mistralcodeintellijextension", "com.jetbrains.remoteDevServer", "com.github.tke.compositefixplugin", "com.github.raystatic.commitgenie", "com.github.ivw.ezmode", "com.namingPlugin.naming", "be.ajuvercr.swls", "org.kiwi.lang", "com.kuns.moblyrunner", "com.y19th.DextensionTemplates", "com.github.ivw.expand-commit-tool-window", "com.eastnine.json_writer", "mybatisSql.wy-mybatis-plugin", "com.julianbruyers.gologo", "com.innoo.WordCountBar", "com.innoo.ChineseSymbolPair", "com.wd.YamlScriptHelper", "com.ollama.code.tool", "de.craftsblock.craftsnet-dev", "uz.softex.classgeneratorplugin", "hackerStyleTheme", "com.charlesbases.moonlit-theme", "com.liuhao.TID", "com.mska3i.mainmenucraft", "kilua.project.wizard", "spring.core.demoPlugin", "com.shuyixiao.yixiaoPlugins", "com.kalvan.tool", "com.java.gendoc", "com.yourcompany.tabsperproject", "com.github.marcopla99.cleancoderearranger", "org-ryr.reader", "com.lq.codemind.intellijextension", "com.koxudaxi.tlinter", "org.ideas2it.composecraft", "com.gustavoliu.copyopenfiles", "io.github.robertomahl.visual-java-profiler", "com.ai.ass.help", "com.nick.paste-with-commas-plugin", "software.rabbitgarden.intellij-oebb-progress-bar", "com.github.hsz.ctrlscroll", "com.intfloatbool.CodeComplex", "com.josecjuniors.tsidplugin", "no.spk.fiskeoye.plugin", "com.mrkhokh.Win1251ToUtf8", "com.intfloatbool.CodeComplexPro", "com.MOBmaster.Production.CodeSnapAndroidt3", "com.tables.get", "yanchi", "com.github.sebnoirot.copier", "io.testowl.testowl-plugin", "org.moqui.idea.plugin", "com.github.zxyphp.bornuntil", "com.zll.ymlhelper", "com.github.lucernae.intellijplugins.cucumbergodog", "com.exmay.gitcoffee.codebot.gitcoffee-codebot", "com.aicodeeye.plugin", "com.clb.timetools", "com.github.hazzajenko.jetbrainshighlightoncopy", "io.github.dimkich.integration.testing.idea-plugin", "com.sebnoirot.cortexscaffolder", "fnmap", "com.github.aleksi-kangas.VisualVM-Integration", "com.cybedefend.jetbrains", "com.clb.ToolsBox", "dev.MikhailShad.nuXmv-plugin", "net.pitan76.MPLTemplateIDEPlugin", "in.payu.payupayments", "com.devthoughtdispatch.jsonviewer", "org.moyi.cjs-to-esm-converter", "com.github.onbassnaga.wiremockjsonplugin", "com.alex.rubyscope.RubyScope", "io.github.andyssder.ffind", "com.jiang.ai.plugin", "dev.meanmail.plugin.smart-change-list", "io.github.abpubli.dot-compat", "com.pond.pondaction", "com.pond.pondstorm", "com.baoxin.CleanArchHelper", "com.github.mrdolch.plantarchintellijplugin", "com.lydia.lydia-plugin", "com.stefan_zemljic.reorder", "fish.crafting.FIMPlugin", "com.arampetrosyann.eplangSyntax", "com.itrunfenix.PR-FreeTracker", "com.naming.code-Plugin", "com.yonyou.projectmanager", "com.github.pop1213.typingpracticeplugin", "com.code.review.ai", "com.github.kkolyan.fyroxlite", "com.umizhang.plugin", "lhd.mybatis_simple_log", "org.jetbrains.kotlin.compiler.devkit", "com.wrh.dev.cr_tool", "kz.kolesa.env-diff-notifier", "com.chenying.inputvariation", "com.dfsek.terra.codetool", "swaix.dev.plugin.cleanarchitecturegenerator", "com.chen.PrettySQL", "io.github.haradakunihiko.intellij-plugin-php-serialized-object-viewer", "com.juanmaperez.pomodoro", "HbuilderXSoftGreenTheme", "com.minetoblend.plugins.osuframework", "com.github.jokerpper.intellij-maven-project-version-plugin.maven-search", "com.github.jokerpper.intellij-maven-project-version-plugin.maven-update", "com.maker.dto-maker", "com.NaiadAI.NaiadLens-jetbrains", "com.cybrosys.odoo.code", "com.github.alanxtl.wslpathconverter", "com.tipsngoding.ginplugin", "com.jtools-Gomoku.rcc.id", "cn.yesand.intellijplugin.lwe-intellij-plugin", "com.github.tolgacanunal.intellijgltfviewerplugin", "com.github.GDcheerios.theme", "owl.home.ChangeDescriptor", "me.kolterdyx.mcbasiclanguage", "com.solidity.jetbrains", "com.istyphoon.CleanCodeTrainer", "com.raia.idea", "com.github.jokerpper.intellij-maven-project-version-plugin.maven-with-me", "benetis.go-constructor-indicator", "com.github.egorbaranov.cod3", "com.github.stefanzemljic.bytejump", "com.hublhero", "com.suimove.lang", "com.pgyer.upload.plugin", "com.giabrend.CopyPasteReplace", "dev.meanmail.plugin.k8s", "com.lessing", "org.utcook.i18nHelper", "com.lili.ZhNameGen", "YapiResultGenerate", "com.naviy.uz.clean_gen", "moon1it.com.confetti", "com.umzhang.calendarclock", "com.github.yanglikun.run_with_jvm_param", "com.jdc.tools.pre-commit-review", "com.akashsoni.api.plugin", "cn.it58.goodname", "com.ai.code.checker", "com.oraclia.ai.intellij-plugin", "com.faridbabayev.apiatoviewhelper", "dev.sbs.simplified-annotations", "com.mastercard.hassan.plugins.tobase64", "com.claudeProjectFiles.claude.integration", "club.doki7.ffm-plus", "ThriftRequest", "com.toolkit.ai.code", "com.secretsmasker", "org.bookmark2", "com.roman.bem.plugin", "stepangrigoryan.tabs-switch", "spacegray_custom", "com.github.garetht.typstsupport"]