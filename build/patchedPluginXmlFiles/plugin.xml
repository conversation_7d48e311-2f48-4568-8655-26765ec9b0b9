<idea-plugin>
  <version>1.0.4</version>
  <change-notes><![CDATA[## 1.0.4 - 2025-06-20

### Added

- Modern intention action for better IDE integration
- Enhanced settings UI with improved layout and visual design
- Support for latest OpenAI models (GPT-4o, GPT-4o-mini)
- Comprehensive performance monitoring and metrics
- Advanced security utilities for input validation
- Modern startup activity for IntelliJ 2025.1+

### Changed

- Updated to support IntelliJ Platform 2025.1 (build 251)
- Migrated to Java 17 for platform compatibility
- Improved error handling with user-friendly messages
- Enhanced notification system with actionable buttons
- Better memory management and resource cleanup

### Fixed

- Resolved build issues with IntelliJ 2025.1
- Fixed plugin verification warnings
- Disabled problematic buildSearchableOptions task
- Corrected Java version compatibility issues
- Removed conflicting coroutines dependencies

### Security

- Enhanced input sanitization and validation
- Improved API key security handling
- Added protection against malicious input patterns]]></change-notes>
  <id>com.wontlost.zigzag</id>
  <name>Zigzag</name>
  <vendor email="<EMAIL>" url="https://wontlost.com">WontLost Ltd</vendor>
  <description><![CDATA[<h1>Zigzag - AI-Powered Coding Assistant</h1>
        <p>Zigzag integrates OpenAI's powerful language models directly into your IntelliJ IDEA environment, 
        providing intelligent code completion, generation, and refactoring capabilities.</p>
        
        <h2>Features:</h2>
        <ul>
            <li><strong>Smart Code Completion:</strong> Get context-aware code suggestions as you type</li>
            <li><strong>Code Generation:</strong> Generate code from natural language comments</li>
            <li><strong>Code Refactoring:</strong> Improve and optimize your existing code</li>
            <li><strong>Interactive Chat:</strong> Ask questions and get coding help in real-time</li>
        </ul>
        
        <h2>Getting Started:</h2>
        <p>After installation, configure your OpenAI API key in <strong>Settings | Tools | Zigzag</strong> 
        to start using the AI-powered features.</p>]]></description>
  <idea-version since-build="251" until-build="253.*" />
  <depends>com.intellij.modules.platform</depends>
  <depends>com.intellij.modules.java</depends>
  <depends>com.intellij.modules.lang</depends>
  <extensions defaultExtensionNs="com.intellij">
    <notificationGroup id="Zigzag Notifications" displayType="BALLOON" />
    <completion.contributor language="ANY" implementationClass="com.wontlost.zigzag.CodexCompletionContributor" />
    <toolWindow id="Zigzag Assistant" anchor="right" factoryClass="com.wontlost.zigzag.CodexAssistantToolWindowFactory" />
    <applicationConfigurable id="com.wontlost.zigzag.settings" displayName="Zigzag" instance="com.wontlost.zigzag.OpenAISettingsConfigurable" />
    <projectActivity implementation="com.wontlost.zigzag.ZigzagStartupActivity" />
    <intentionAction>
      <className>com.wontlost.zigzag.ZigzagIntentionAction</className>
      <category>Zigzag AI</category>
    </intentionAction>
  </extensions>
  <actions>
    <action id="com.wontlost.zigzag.ZigzagAction" class="com.wontlost.zigzag.ZigzagAction" text="Test Zigzag" description="Test Zigzag connection">
      <add-to-group group-id="ToolsMenu" anchor="last" />
    </action>
    <action id="com.wontlost.zigzag.CodexContextAction" class="com.wontlost.zigzag.CodexContextAction" text="AI Assist" description="Generate or refactor with AI">
      <add-to-group group-id="EditorPopupMenu" anchor="last" />
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt A" />
    </action>
  </actions>
</idea-plugin>
