# Contributing to Zigzag

Thank you for your interest in contributing to Zigzag! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Java 21 (required for IntelliJ 2025.1 compatibility)
- IntelliJ IDEA 2025.1 or higher
- Git
- OpenAI API key (for testing)

### Development Setup
1. Fork the repository
2. Clone your fork:
   ```bash
   git clone https://github.com/yourusername/zigzag.git
   cd zigzag
   ```
3. Open the project in IntelliJ IDEA
4. Build the project:
   ```bash
   ./gradlew build
   ```

## 🏗️ Project Structure

```
src/
├── main/
│   ├── java/com/wontlost/zigzag/
│   │   └── AIClient.java          # OpenAI API client
│   ├── kotlin/com/wontlost/zigzag/
│   │   ├── ZigzagAction.kt           # Test action
│   │   ├── AIContextAction.kt     # Context menu action
│   │   ├── AICompletionContributor.kt # Code completion
│   │   ├── AIAssistantPanel.kt    # Chat UI
│   │   ├── OpenAISettingsComponent.kt # Settings storage
│   │   ├── OpenAISettingsConfigurable.kt # Settings UI
│   │   ├── ZigzagNotifier.kt         # Notifications
│   │   ├── ZigzagLogger.kt           # Logging utility
│   │   ├── ErrorHandler.kt           # Error handling
│   │   ├── PerformanceMonitor.kt     # Performance tracking
│   │   ├── ConfigurationValidator.kt # Settings validation
│   │   └── SecurityUtils.kt          # Security utilities
│   └── resources/
│       └── META-INF/plugin.xml       # Plugin configuration
└── test/
    └── kotlin/com/wontlost/zigzag/   # Unit tests
```

## 🧪 Testing

### Running Tests
```bash
./gradlew test
```

### Writing Tests
- Place test files in `src/test/kotlin/com/wontlost/zigzag/`
- Use JUnit 5 and Kotlin test assertions
- Mock external dependencies
- Test both success and failure scenarios

### Test Categories
1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions
3. **UI Tests**: Test user interface components

## 📝 Code Style

### Kotlin Guidelines
- Follow [Kotlin coding conventions](https://kotlinlang.org/docs/coding-conventions.html)
- Use meaningful variable and function names
- Add KDoc comments for public APIs
- Prefer immutable data structures
- Use null safety features

### Java Guidelines
- Follow standard Java conventions
- Use JavaDoc for public methods
- Prefer composition over inheritance
- Handle exceptions appropriately

### General Guidelines
- Keep functions small and focused
- Use descriptive commit messages
- Add comments for complex logic
- Follow existing code patterns

## 🔧 Development Workflow

### Branch Naming
- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `refactor/description` - Code refactoring
- `docs/description` - Documentation updates

### Commit Messages
Follow conventional commits format:
```
type(scope): description

[optional body]

[optional footer]
```

Examples:
- `feat(completion): add support for multiple languages`
- `fix(api): handle rate limit errors gracefully`
- `docs(readme): update installation instructions`

### Pull Request Process
1. Create a feature branch from `main`
2. Make your changes with tests
3. Ensure all tests pass
4. Update documentation if needed
5. Submit a pull request with:
   - Clear description of changes
   - Link to related issues
   - Screenshots for UI changes

## 🐛 Bug Reports

When reporting bugs, please include:
- IntelliJ IDEA version
- Plugin version
- Operating system
- Steps to reproduce
- Expected vs actual behavior
- Error logs (if any)

## 💡 Feature Requests

For new features, please:
- Check existing issues first
- Describe the use case
- Explain the expected behavior
- Consider implementation complexity
- Discuss with maintainers before starting

## 🔒 Security

### Reporting Security Issues
- **DO NOT** create public issues for security vulnerabilities
- Email <EMAIL> with details
- Include steps to reproduce
- Allow time for investigation and fix

### Security Guidelines
- Never commit API keys or secrets
- Sanitize user input
- Use secure communication protocols
- Follow principle of least privilege
- Validate all external data

## 📚 Documentation

### Code Documentation
- Add KDoc/JavaDoc for public APIs
- Include usage examples
- Document complex algorithms
- Explain design decisions

### User Documentation
- Update README.md for user-facing changes
- Add screenshots for UI changes
- Update troubleshooting guide
- Keep installation instructions current

## 🚀 Release Process

### Version Numbering
We follow [Semantic Versioning](https://semver.org/):
- `MAJOR.MINOR.PATCH`
- Major: Breaking changes
- Minor: New features (backward compatible)
- Patch: Bug fixes (backward compatible)

### Release Checklist
1. Update version in `gradle.properties`
2. Update `CHANGELOG.md`
3. Run full test suite
4. Create release tag
5. Build and test plugin
6. Publish to marketplace

## 🤝 Community

### Communication
- GitHub Issues for bugs and features
- GitHub Discussions for questions
- Email for security issues

### Code of Conduct
- Be respectful and inclusive
- Focus on constructive feedback
- Help others learn and grow
- Follow GitHub's community guidelines

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Plugin about dialog

Thank you for contributing to Zigzag! 🎉
