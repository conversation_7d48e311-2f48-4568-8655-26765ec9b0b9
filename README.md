# Zigzag IntelliJ Plugin

This repository contains a minimal IntelliJ IDEA plugin built with Gradle.
It uses the JetBrains IntelliJ Platform SDK via the `org.jetbrains.intellij` Gradle plugin.
Development and compilation require **Java 21**.

## Building the Plugin

Use the Gradle wrapper to build the plugin distribution zip:

```bash
./gradlew buildPlugin
```

If `gradle/wrapper/gradle-wrapper.jar` is missing, generate it with:

```bash
gradle wrapper
```

The plugin can then be found under `build/distributions`.

## Configuring the Plugin

After installing, open **Preferences | Tools | Zigzag** to configure the OpenAI
API key and Codex parameters. You can also enable or disable inline completions
and the chat panel from this settings page.
