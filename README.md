# Zigzag IntelliJ Plugin

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/wontlost/zigzag)
[![Version](https://img.shields.io/badge/version-1.0.3-blue)](https://github.com/wontlost/zigzag/releases)
[![License](https://img.shields.io/badge/license-MIT-green)](LICENSE)

Zigzag is a powerful IntelliJ IDEA plugin that integrates OpenAI's advanced language models directly into your development environment. It provides intelligent code completion, generation, and refactoring capabilities to boost your productivity.

## 🚀 Features

### 🤖 AI-Powered Code Completion
- **Smart Inline Completions**: Get context-aware code suggestions as you type
- **Multi-language Support**: Works with Java, Kotlin, Python, JavaScript, and more
- **Intelligent Context Analysis**: Understands your codebase context for better suggestions

### 💬 Interactive AI Assistant
- **Chat Panel**: Ask questions and get coding help in real-time
- **Code Explanation**: Get detailed explanations of complex code snippets
- **Best Practices**: Receive suggestions for code improvements and optimizations

### 🔧 Code Generation & Refactoring
- **Comment-to-Code**: Generate code from natural language comments
- **Smart Refactoring**: Improve and optimize existing code with AI suggestions
- **Quick Actions**: Context-aware actions available in the editor

### ⚙️ Advanced Configuration
- **Multiple AI Models**: Support for GPT-3.5-turbo, GPT-4, and GPT-4-turbo
- **Customizable Parameters**: Adjust temperature, token limits, and timeouts
- **Rate Limiting**: Built-in protection against API rate limits
- **Secure Storage**: Encrypted API key storage

## 📦 Installation

### From Plugin Marketplace (Recommended)
1. Open IntelliJ IDEA
2. Go to **File | Settings | Plugins**
3. Search for "Zigzag" in the marketplace
4. Click **Install** and restart the IDE

### Manual Installation
1. Download the latest release from [GitHub Releases](https://github.com/wontlost/zigzag/releases)
2. In IntelliJ IDEA, go to **File | Settings | Plugins**
3. Click the gear icon and select **Install Plugin from Disk**
4. Select the downloaded ZIP file and restart the IDE

### Build from Source
```bash
git clone https://github.com/wontlost/zigzag.git
cd zigzag
./gradlew buildPlugin
```
The plugin ZIP will be available in `build/distributions/`.

## 🔑 Setup

1. **Get an OpenAI API Key**:
   - Visit [OpenAI Platform](https://platform.openai.com/api-keys)
   - Create a new API key
   - Copy the key for configuration

2. **Configure the Plugin**:
   - Open **File | Settings | Tools | Zigzag**
   - Enter your OpenAI API key
   - Adjust settings as needed (see [Configuration](#configuration) below)

3. **Start Coding**:
   - The plugin is now ready to assist you!
   - Try typing a comment and see AI suggestions
   - Use **Ctrl+Alt+A** for context actions

## ⚙️ Configuration

Access plugin settings via **File | Settings | Tools | Zigzag**:

### API Configuration
- **OpenAI API Key**: Your OpenAI API key (required)
- **Model**: Choose between GPT-3.5-turbo, GPT-4, or GPT-4-turbo
- **Request Timeout**: Maximum time to wait for API responses (5-120 seconds)

### Generation Parameters
- **Temperature**: Controls randomness (0.0 = deterministic, 2.0 = very creative)
- **Max Tokens**: Maximum tokens to generate (affects cost and response length)

### Features
- **Enable Inline Completions**: Show AI suggestions while typing
- **Enable Chat Panel**: Show the AI assistant tool window
- **Enable Debug Logging**: Log API requests for troubleshooting

## 🎯 Usage

### Inline Completions
- Simply start typing in any supported file
- AI suggestions will appear automatically
- Press **Tab** to accept suggestions

### Context Actions
- Select code or write a comment
- Right-click and choose **AI Assist** or press **Ctrl+Alt+A**
- Choose to generate code from comments or refactor existing code

### Chat Assistant
- Open the **Zigzag Assistant** tool window (usually on the right side)
- Type your questions or requests
- Get instant AI-powered responses

## 🛠️ Development

### Prerequisites
- Java 17 or higher (Java 17 required for IntelliJ 2025.1 compatibility)
- IntelliJ IDEA 2025.1 or higher
- Gradle 8.0 or higher

### Building
```bash
./gradlew clean build
```

### Testing
```bash
./gradlew test
```

### Running in Development
```bash
./gradlew runIde
```

## 🔒 Security & Privacy

- **API Key Security**: Keys are stored securely using IntelliJ's credential storage
- **Data Privacy**: Only selected code/comments are sent to OpenAI
- **Rate Limiting**: Built-in protection against excessive API usage
- **Local Processing**: Plugin logic runs entirely locally

## 🐛 Troubleshooting

### Common Issues

**No suggestions appear**
- Verify your API key is correct in settings
- Check your internet connection
- Ensure you have sufficient OpenAI API credits

**Build fails with Java errors**
- Ensure you're using Java 21 or higher
- Update your JAVA_HOME environment variable

**Plugin not loading**
- Check IDE logs: **Help | Show Log in Explorer/Finder**
- Verify plugin compatibility with your IntelliJ version

**API rate limit errors**
- The plugin includes built-in rate limiting
- Consider upgrading your OpenAI plan for higher limits
- Reduce frequency of requests in settings

### Getting Help
- Check the [Issues](https://github.com/wontlost/zigzag/issues) page
- Create a new issue with detailed information
- Include IDE version, plugin version, and error logs

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please read our [Contributing Guidelines](CONTRIBUTING.md) for details on how to submit pull requests, report issues, and contribute to the project.

## 📞 Support

- **Documentation**: [GitHub Wiki](https://github.com/wontlost/zigzag/wiki)
- **Issues**: [GitHub Issues](https://github.com/wontlost/zigzag/issues)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- OpenAI for providing the powerful language models
- JetBrains for the excellent IntelliJ Platform SDK
- The open-source community for inspiration and feedback

