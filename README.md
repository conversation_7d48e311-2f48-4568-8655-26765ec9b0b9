# Zigzag IntelliJ Plugin

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/wontlost/zigzag)
[![Version](https://img.shields.io/badge/version-1.0.3-blue)](https://github.com/wontlost/zigzag/releases)
[![License](https://img.shields.io/badge/license-MIT-green)](LICENSE)

Zigzag is a powerful IntelliJ IDEA plugin that integrates OpenAI's advanced language models directly into your development environment. It provides intelligent code completion, generation, and refactoring capabilities to boost your productivity.

## 🚀 Features

### 🤖 AI-Powered Code Completion
- **Smart Inline Completions**: Get context-aware code suggestions as you type
- **Multi-language Support**: Works with Java, Kotlin, Python, JavaScript, and more
- **Intelligent Context Analysis**: Understands your codebase context for better suggestions

### 💬 Interactive AI Assistant Panel ✨ **NEW**
- **Modern Chat Interface**: Beautiful, card-based design with message bubbles
- **Editor Integration**: One-click copy and insert code directly into your editor
- **Syntax Highlighting**: AI responses displayed with proper code highlighting
- **Real-time Feedback**: Loading indicators and status updates
 - **Smart Actions**: Copy, insert, and redo responses with one click
- **Conversation Management**: Clear chat history and auto-scroll features

### 🔧 Code Generation & Refactoring
- **Comment-to-Code**: Generate code from natural language comments
- **Smart Refactoring**: Improve and optimize existing code with AI suggestions
- **Intention Actions**: Modern IDE-integrated code suggestions
- **Quick Actions**: Context-aware actions available in the editor

### ⚙️ Beautiful Settings Interface ✨ **NEW**
- **Card-Based Layout**: Organized, modern settings with visual hierarchy
- **Real-time Cost Estimation**: See API costs as you adjust settings
- **Connection Testing**: Built-in API key validation
- **Multiple AI Models**: Support for GPT-3.5-turbo, GPT-4, GPT-4o, and GPT-4o-mini
- **Smart Validation**: Immediate feedback on configuration errors
- **Help Integration**: Direct links to OpenAI documentation

### 🔒 Enterprise-Grade Security
- **Secure API Key Storage**: Encrypted storage using IntelliJ's credential system
- **Input Validation**: Protection against malicious input patterns
- **Rate Limiting**: Built-in protection against API abuse (60 requests/minute)
- **Request Timeouts**: Configurable timeouts with retry logic

## 📦 Installation

### From Plugin Marketplace (Recommended)
1. Open IntelliJ IDEA
2. Go to **File | Settings | Plugins**
3. Search for "Zigzag" in the marketplace
4. Click **Install** and restart the IDE

### Manual Installation
1. Download the latest release from [GitHub Releases](https://github.com/wontlost/zigzag/releases)
2. In IntelliJ IDEA, go to **File | Settings | Plugins**
3. Click the gear icon and select **Install Plugin from Disk**
4. Select the downloaded ZIP file and restart the IDE

### Build from Source
```bash
git clone https://github.com/wontlost/zigzag.git
cd zigzag
./gradlew buildPlugin
```
The plugin ZIP will be available in `build/distributions/`.

## 🔑 Setup

1. **Get an OpenAI API Key**:
   - Visit [OpenAI Platform](https://platform.openai.com/api-keys)
   - Create a new API key
   - Copy the key for configuration

2. **Configure the Plugin**:
   - Open **File | Settings | Tools | Zigzag**
   - Enter your OpenAI API key
   - Adjust settings as needed (see [Configuration](#configuration) below)

3. **Start Coding**:
   - The plugin is now ready to assist you!
   - Try typing a comment and see AI suggestions
   - Use **Ctrl+Alt+A** for context actions

## ⚙️ Configuration

Access the beautiful new settings interface via **File | Settings | Tools | Zigzag**:

### 🔑 API Configuration
- **OpenAI API Key**: Secure password field for your API key
- **Test Connection**: Built-in button to verify your API key works
- **Model Selection**: Choose from GPT-3.5-turbo, GPT-4, GPT-4o, and GPT-4o-mini
- **Quick Links**: Direct access to OpenAI platform for getting API keys

### ⚙️ Model & Performance Settings
- **Temperature**: Controls creativity (0.0 = deterministic, 2.0 = very creative)
- **Max Tokens**: Maximum response length (affects cost and quality)
- **Request Timeout**: Maximum wait time for API responses (5-120 seconds)

### 🚀 Features
- **Enable Inline Completions**: Show AI suggestions while typing
- **Enable Chat Panel**: Show the enhanced AI assistant tool window
- **Enable Debug Logging**: Detailed logging for troubleshooting

### 💰 Cost Estimation ✨ **NEW**
- **Real-time Cost Calculation**: See estimated costs as you change settings
- **Model Comparison**: Understand cost differences between models
- **Warning Indicators**: Alerts for expensive configurations

## 🎯 Usage

### Inline Completions
- Simply start typing in any supported file
- AI suggestions will appear automatically
- Press **Tab** to accept suggestions

### Context Actions & Intention Actions
- Select code or write a comment
- Right-click and choose **AI Assist** or press **Ctrl+Alt+A**
- Use **Alt+Enter** for intention actions on comments
- Choose to generate code from comments or refactor existing code

### Enhanced Chat Assistant ✨ **NEW**
- Open the **Zigzag Assistant** tool window (usually on the right side)
- Type your questions or requests in the modern chat interface
- Get instant AI-powered responses with syntax highlighting
- **Copy responses** to clipboard with one click
- **Insert code directly** into your editor
- **Redo requests** to refine results (per message or entire chat)
- **Clear conversation** to start fresh
- **Auto-scroll** to follow the conversation

## 🛠️ Development

### Prerequisites
- Java 17 or higher (Java 17 required for IntelliJ 2025.1 compatibility)
- IntelliJ IDEA 2025.1 or higher
- Gradle 8.0 or higher

### Building
```bash
./gradlew clean build
```

### Testing
```bash
./gradlew test
```

### Running in Development
```bash
./gradlew runIde
```

## 🔒 Security & Privacy

- **API Key Security**: Keys are stored securely using IntelliJ's credential storage
- **Data Privacy**: Only selected code/comments are sent to OpenAI
- **Rate Limiting**: Built-in protection against excessive API usage
- **Local Processing**: Plugin logic runs entirely locally

## 🐛 Troubleshooting

### Common Issues

**No suggestions appear**
- Verify your API key is correct in settings
- Check your internet connection
- Ensure you have sufficient OpenAI API credits

**Build fails with Java errors**
- Ensure you're using Java 17 (required for IntelliJ 2025.1)
- Update your JAVA_HOME environment variable

**Plugin not loading**
- Check IDE logs: **Help | Show Log in Explorer/Finder**
- Verify plugin compatibility with your IntelliJ version

**API rate limit errors**
- The plugin includes built-in rate limiting
- Consider upgrading your OpenAI plan for higher limits
- Reduce frequency of requests in settings

### Getting Help
- Check the [Issues](https://github.com/wontlost/zigzag/issues) page
- Create a new issue with detailed information
- Include IDE version, plugin version, and error logs

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please read our [Contributing Guidelines](CONTRIBUTING.md) for details on how to submit pull requests, report issues, and contribute to the project.

## 📞 Support

- **Documentation**: [GitHub Wiki](https://github.com/wontlost/zigzag/wiki)
- **Issues**: [GitHub Issues](https://github.com/wontlost/zigzag/issues)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- OpenAI for providing the powerful language models
- JetBrains for the excellent IntelliJ Platform SDK
- The open-source community for inspiration and feedback

