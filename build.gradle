plugins {
    id 'java'
    id 'org.jetbrains.intellij' version '1.17.3'
    id 'org.jetbrains.changelog' version '2.2.0'
}

group = 'com.wontlost.zigzag'
version = file('gradle.properties').withReader { props ->
    def properties = new Properties()
    properties.load(props)
    return properties['pluginVersion'] ?: '1.0.0'
}

repositories {
    mavenCentral()
}

intellij {
    version = '2024.1'
    type = 'IC'
}

changelog {
    path = 'CHANGELOG.md'
    version = project.version
}

patchPluginXml {
    sinceBuild = '241'
    version = project.version
    changeNotes = provider { changelog.renderItem(project.version) }
}

tasks.register('incrementVersion') {
    doLast {
        def propsFile = file('gradle.properties')
        def props = new Properties()
        propsFile.withInputStream { props.load(it) }
        def ver = (props['pluginVersion'] ?: '0.0.0').tokenize('.')*.toInteger()
        ver[-1] = ver[-1] + 1
        props['pluginVersion'] = ver.join('.')
        propsFile.withOutputStream { props.store(it, null) }
        println "Version bumped to ${props['pluginVersion']}"
    }
}

buildPlugin {
    dependsOn incrementVersion, patchChangelog
}

publishPlugin {
    dependsOn buildPlugin
    token = System.getenv('PUBLISH_TOKEN')
}
