package com.wontlost.zigzag

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class MarkdownRendererTest {
    @Test
    fun `extractPlainText removes formatting`() {
        val md = "## Title\nSome **bold** text\n\n```kotlin\nval x = 1\n```"
        val plain = MarkdownRenderer.extractPlainText(md)
        assertEquals("Title\nSome bold text", plain)
    }

    @Test
    fun `hasCodeBlocks detects fenced blocks`() {
        assertTrue(MarkdownRenderer.hasCodeBlocks("Code:\n```println()```"))
        assertFalse(MarkdownRenderer.hasCodeBlocks("No code here"))
    }
}
