package com.wontlost.zigzag

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

/**
 * Simple unit tests that don't require external dependencies
 */
class UnifiedAIClientTest {

    @Test
    fun `test basic functionality`() {
        // Test basic assertions work
        assertTrue(true)
        assertFalse(false)
        assertEquals("test", "test")
    }

    @Test
    fun `test provider enum exists`() {
        // Just test that the enum exists and has values
        val providers = UnifiedAIClient.Provider.values()
        assertTrue(providers.isNotEmpty())
        assertTrue(providers.contains(UnifiedAIClient.Provider.OPENAI))
    }
    
}
