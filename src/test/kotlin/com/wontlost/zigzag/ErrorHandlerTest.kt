package com.wontlost.zigzag

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import java.io.IOException

class ErrorHandlerTest {
    @Test
    fun `authentication error message`() {
        val msg = ErrorHandler.handleApiError(null, IOException("401 Unauthorized"))
        assertTrue(msg.contains("Authentication failed"))
    }

    @Test
    fun `rate limit error message`() {
        val msg = ErrorHandler.handleApiError(null, IOException("429 Too many"))
        assertTrue(msg.contains("Rate limit"))
    }

    @Test
    fun `server error message`() {
        val msg = ErrorHandler.handleApiError(null, IOException("503 Service"))
        assertTrue(msg.contains("temporarily unavailable"))
    }
}
