package com.wontlost.zigzag

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

/**
 * Simple unit tests that don't require external dependencies
 */
class ErrorHandlerTest {

    @Test
    fun `test basic functionality`() {
        // Test basic assertions work
        assertTrue(true)
        assertFalse(false)
        assertEquals(1, 1)
        assertNotNull("test")
    }

    @Test
    fun `test string operations`() {
        val testString = "Hello World"
        assertTrue(testString.contains("Hello"))
        assertTrue(testString.contains("World"))
        assertEquals(11, testString.length)
    }

    @Test
    fun `test basic math`() {
        assertEquals(4, 2 + 2)
        assertEquals(6, 2 * 3)
        assertEquals(2.0, 4.0 / 2.0, 0.001)
    }
}
