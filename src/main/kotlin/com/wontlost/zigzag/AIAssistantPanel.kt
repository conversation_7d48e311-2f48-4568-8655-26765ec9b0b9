package com.wontlost.zigzag

import com.google.gson.Gson
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.ide.CopyPasteManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.text.StringUtil
import com.intellij.ui.jcef.JBCefBrowser
import com.intellij.ui.jcef.JBCefBrowserBase
import com.intellij.ui.jcef.JBCefJSQuery
import java.awt.BorderLayout
import javax.swing.JPanel
import javax.swing.SwingUtilities
import java.awt.datatransfer.StringSelection

/**
 * Enhanced AI Assistant Panel using JCEF for rich browser functionality
 * Supports CSS styling, JavaScript interactions, and markdown rendering
 */
class AIAssistantPanel(private val project: Project) : JPanel(BorderLayout()) {
    private val browser = JBCefBrowser()
    private val gson = Gson()
    private val conversation = mutableListOf<ChatMessage>()

    // Modern JCEF JS Query creation (non-deprecated)
    private val inputQuery = JBCefJSQuery.create(browser as JBCefBrowserBase)
    private val copyQuery = JBCefJSQuery.create(browser as JBCefBrowserBase)
    private val insertQuery = JBCefJSQuery.create(browser as JBCefBrowserBase)
    private val redoQuery = JBCefJSQuery.create(browser as JBCefBrowserBase)
    private val redoAllQuery = JBCefJSQuery.create(browser as JBCefBrowserBase)
    private val voteQuery = JBCefJSQuery.create(browser as JBCefBrowserBase)

    data class ChatMessage(val role: String, val content: String, val timestamp: Long = System.currentTimeMillis())

    init {
        setupBrowser()
        setupJSHandlers()
        loadInitialPage()
        addWelcomeMessage()
    }

    private fun setupBrowser() {
        add(browser.component, BorderLayout.CENTER)
    }

    private fun setupJSHandlers() {
        inputQuery.addHandler { text ->
            handleUserInput(text)
            null
        }

        copyQuery.addHandler { idx ->
            handleCopy(idx.toInt())
            null
        }

        insertQuery.addHandler { idx ->
            handleInsert(idx.toInt())
            null
        }

        redoQuery.addHandler { idx ->
            handleRedo(idx.toInt())
            null
        }

        redoAllQuery.addHandler { _ ->
            handleRedoAll()
            null
        }

        voteQuery.addHandler { data ->
            handleVote(data)
            null
        }
    }

    private fun loadInitialPage() {
        browser.loadHTML(createEnhancedPage())
    }

    private fun createEnhancedPage(): String = """
        <html>
          <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Zigzag AI Assistant</title>
            <style>
              :root {
                --primary-color: #007acc;
                --secondary-color: #f3f3f3;
                --text-color: #333;
                --border-color: #ddd;
                --user-bg: #e3f2fd;
                --assistant-bg: #ffffff;
                --code-bg: #f5f5f5;
                --shadow: 0 2px 4px rgba(0,0,0,0.1);
              }

              * { box-sizing: border-box; }

              body, html, #container {
                height: 100%;
                margin: 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              }

              body {
                background: var(--secondary-color);
                display: flex;
                flex-direction: column;
                color: var(--text-color);
              }

              #container {
                display: flex;
                flex-direction: column;
                height: 100vh;
              }

              #messages {
                padding: 16px;
                overflow-y: auto;
                flex: 1;
                scroll-behavior: smooth;
              }

              .message {
                margin-bottom: 16px;
                padding: 12px 16px;
                border-radius: 12px;
                max-width: 85%;
                box-shadow: var(--shadow);
                animation: fadeIn 0.3s ease-in;
              }

              @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
              }

              .user {
                background: var(--user-bg);
                align-self: flex-end;
                margin-left: auto;
                border: 1px solid #bbdefb;
              }

              .assistant {
                background: var(--assistant-bg);
                border: 1px solid var(--border-color);
                align-self: flex-start;
              }

              .message-header {
                font-weight: 600;
                font-size: 14px;
                margin-bottom: 8px;
                color: var(--primary-color);
              }

              .message-content {
                line-height: 1.6;
              }

              pre {
                background: var(--code-bg);
                border: 1px solid var(--border-color);
                border-radius: 6px;
                padding: 12px;
                overflow-x: auto;
                font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
                font-size: 13px;
                margin: 8px 0;
              }

              code {
                background: var(--code-bg);
                padding: 2px 6px;
                border-radius: 4px;
                font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
                font-size: 13px;
              }

              .actions {
                margin-top: 12px;
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
              }

              .action-btn {
                background: var(--primary-color);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
                font-weight: 500;
              }

              .action-btn:hover {
                background: #005a9e;
                transform: translateY(-1px);
              }

              .vote-btn {
                background: transparent;
                color: var(--text-color);
                border: 1px solid var(--border-color);
              }

              .vote-btn:hover {
                background: var(--secondary-color);
                transform: translateY(-1px);
              }

              .vote-btn.voted {
                background: var(--primary-color);
                color: white;
              }

              #chat-bar {
                display: flex;
                align-items: flex-end;
                padding: 16px;
                border-top: 1px solid var(--border-color);
                background: white;
                gap: 12px;
                box-shadow: 0 -2px 4px rgba(0,0,0,0.05);
              }

              #avatar {
                font-size: 24px;
                margin-bottom: 8px;
              }

              #chat-input {
                flex: 1;
                padding: 12px 16px;
                border-radius: 24px;
                border: 1px solid var(--border-color);
                resize: none;
                min-height: 20px;
                max-height: 120px;
                outline: none;
                font-family: inherit;
                font-size: 14px;
                line-height: 1.4;
                transition: border-color 0.2s ease;
              }

              #chat-input:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
              }

              .icon-btn {
                background: var(--primary-color);
                color: white;
                border: none;
                border-radius: 50%;
                width: 44px;
                height: 44px;
                cursor: pointer;
                font-size: 18px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
                margin-bottom: 2px;
              }

              .icon-btn:hover {
                background: #005a9e;
                transform: scale(1.05);
              }

              .icon-btn:active {
                transform: scale(0.95);
              }

              .loading {
                opacity: 0.6;
                pointer-events: none;
              }

              .typing-indicator {
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 12px 16px;
                color: #666;
                font-style: italic;
              }

              .typing-dots {
                display: flex;
                gap: 2px;
              }

              .typing-dots span {
                width: 6px;
                height: 6px;
                background: #666;
                border-radius: 50%;
                animation: typing 1.4s infinite ease-in-out;
              }

              .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
              .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

              @keyframes typing {
                0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
                40% { transform: scale(1); opacity: 1; }
              }
            </style>
          </head>
          <body>
            <div id='container'>
              <div id='messages'></div>
              <div id='chat-bar'>
                <div id='avatar'>🤖</div>
                <textarea id='chat-input' placeholder='Ask Zigzag AI anything...' rows='1'></textarea>
                <button id='send-btn' class='icon-btn' title='Send message'>➤</button>
                <button id='clear-btn' class='icon-btn' title='Clear conversation'>🗑️</button>
              </div>
            </div>
            <script>
              // Enhanced JavaScript for rich interactions
              let isLoading = false;

              // Inject functions from Kotlin
              function sendToKotlin(text) { ${inputQuery.inject("text")}; }
              function copyMessage(idx) { ${copyQuery.inject("idx")}; }
              function insertMessage(idx) { ${insertQuery.inject("idx")}; }
              function redoMessage(idx) { ${redoQuery.inject("idx")}; }
              function redoAll() { ${redoAllQuery.inject("")}; }
              function voteMessage(data) { ${voteQuery.inject("data")}; }

              function send() {
                const input = document.getElementById('chat-input');
                const text = input.value.trim();
                if (!text || isLoading) return;

                input.value = '';
                autoResize(input);
                setLoading(true);
                sendToKotlin(text);
              }

              function setLoading(loading) {
                isLoading = loading;
                const sendBtn = document.getElementById('send-btn');
                const input = document.getElementById('chat-input');

                if (loading) {
                  sendBtn.classList.add('loading');
                  input.disabled = true;
                  showTypingIndicator();
                } else {
                  sendBtn.classList.remove('loading');
                  input.disabled = false;
                  hideTypingIndicator();
                }
              }

              function showTypingIndicator() {
                const indicator = document.createElement('div');
                indicator.className = 'typing-indicator';
                indicator.id = 'typing-indicator';
                indicator.innerHTML = 'Zigzag AI is thinking<div class="typing-dots"><span></span><span></span><span></span></div>';
                document.getElementById('messages').appendChild(indicator);
                scrollToBottom();
              }

              function hideTypingIndicator() {
                const indicator = document.getElementById('typing-indicator');
                if (indicator) indicator.remove();
              }

              function autoResize(textarea) {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
              }

              function scrollToBottom() {
                const messages = document.getElementById('messages');
                messages.scrollTop = messages.scrollHeight;
              }

              function clearConversation() {
                document.getElementById('messages').innerHTML = '';
                ${redoAllQuery.inject("'clear'")}
              }

              // Event listeners
              document.getElementById('send-btn').addEventListener('click', send);
              document.getElementById('clear-btn').addEventListener('click', clearConversation);

              const chatInput = document.getElementById('chat-input');
              chatInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  send();
                } else if (e.key === 'Enter' && e.shiftKey) {
                  // Allow new line
                  setTimeout(() => autoResize(this), 0);
                }
              });

              chatInput.addEventListener('input', function() {
                autoResize(this);
              });

              // Enhanced message adding function
              window.addMessage = function(role, html, idx, timestamp) {
                hideTypingIndicator();

                const messageDiv = document.createElement('div');
                messageDiv.className = 'message ' + role;

                if (role === 'user') {
                  messageDiv.innerHTML = '<div class="message-header">You</div><div class="message-content">' + html + '</div>';
                } else {
                  const header = '<div class="message-header">🤖 Zigzag AI</div>';
                  const content = '<div class="message-content">' + html + '</div>';
                  let actions = '';

                  if (idx >= 0) {
                    actions = '<div class="actions">' +
                      '<button class="action-btn" onclick="copyMessage(' + idx + ')">📋 Copy</button>' +
                      '<button class="action-btn" onclick="insertMessage(' + idx + ')">⬇️ Insert</button>' +
                      '<button class="action-btn" onclick="redoMessage(' + idx + ')">🔄 Redo</button>' +
                      '<button class="vote-btn" onclick="voteMessage(\'' + idx + ':up\')">👍</button>' +
                      '<button class="vote-btn" onclick="voteMessage(\'' + idx + ':down\')">👎</button>' +
                      '</div>';
                  }

                  messageDiv.innerHTML = header + content + actions;
                }

                document.getElementById('messages').appendChild(messageDiv);
                scrollToBottom();
                setLoading(false);
              };

              // Utility functions
              window.setLoadingState = function(loading) {
                setLoading(loading);
              };

              window.showError = function(message) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'message assistant';
                errorDiv.innerHTML = '<div class="message-header">⚠️ Error</div><div class="message-content" style="color: #d32f2f;">' + message + '</div>';
                document.getElementById('messages').appendChild(errorDiv);
                scrollToBottom();
                setLoading(false);
              };
            </script>
          </body>
        </html>
    """.trimIndent()

    private fun addWelcomeMessage() {
        val welcome = """
            <h3>👋 Welcome to Zigzag AI Assistant!</h3>
            <p>I can help you with:</p>
            <ul>
                <li><strong>Code generation</strong> and completion</li>
                <li><strong>Code explanation</strong> and documentation</li>
                <li><strong>Bug fixing</strong> and optimization</li>
                <li><strong>Best practices</strong> and suggestions</li>
                <li><strong>Multi-provider support</strong> (OpenAI, Meta, Google, Local models)</li>
            </ul>
            <p>Just type your question below and I'll assist you! 🚀</p>
        """.trimIndent()
        addAssistantEntry(welcome, rawHtml = true, record = false)
    }

    private fun handleUserInput(text: String) {
        val prompt = text.trim()
        if (prompt.isEmpty()) return

        // Handle special commands
        if (prompt.startsWith("/")) {
            handleCommand(prompt)
            return
        }

        addUserEntry(StringUtil.escapeXmlEntities(prompt))

        ApplicationManager.getApplication().executeOnPooledThread {
            val responseText = try {
                val settings = OpenAISettingsComponent.getInstance()
                val fullPrompt = "You are a helpful coding assistant. Please provide a concise and helpful response with proper markdown formatting to: $prompt"
                val request = ProviderUtils.createAIRequest(fullPrompt, settings)
                val client = UnifiedAIClient()
                val response = client.createCompletion(request)
                response.text.trim()
            } catch (e: Exception) {
                ZigzagLogger.error("AI request failed", e)
                ErrorHandler.handleApiError(project, e)
                "Sorry, I encountered an error. Please check your settings and try again."
            }

            SwingUtilities.invokeLater {
                addAssistantEntry(responseText)
            }
        }
    }

    private fun handleCommand(command: String) {
        when {
            command == "/clear" -> {
                clearConversation()
                return
            }
            command == "/help" -> {
                val helpText = """
                    <h4>Available Commands:</h4>
                    <ul>
                        <li><code>/clear</code> - Clear conversation</li>
                        <li><code>/help</code> - Show this help</li>
                        <li><code>/settings</code> - Open settings</li>
                        <li><code>/providers</code> - List available providers</li>
                    </ul>
                """.trimIndent()
                addAssistantEntry(helpText, rawHtml = true, record = false)
                return
            }
            command == "/settings" -> {
                ZigzagNotifier.notifyInfo(project, "Opening settings... Go to File > Settings > Tools > Zigzag")
                return
            }
            command == "/providers" -> {
                val settings = OpenAISettingsComponent.getInstance()
                val providersText = """
                    <h4>Available Providers:</h4>
                    <ul>
                        <li><strong>OpenAI</strong> - GPT models (current: ${if (settings.provider == "OpenAI") "✅" else "❌"})</li>
                        <li><strong>Meta Llama</strong> - Llama models via Together AI</li>
                        <li><strong>Google Gemini</strong> - Gemini Pro models</li>
                        <li><strong>Local Ollama</strong> - Local models via Ollama</li>
                        <li><strong>Local Llama.cpp</strong> - Local models via llama.cpp</li>
                    </ul>
                    <p>Current provider: <strong>${settings.provider}</strong></p>
                    <p>Current model: <strong>${settings.model}</strong></p>
                """.trimIndent()
                addAssistantEntry(providersText, rawHtml = true, record = false)
                return
            }
        }

        // Unknown command
        addAssistantEntry("Unknown command: $command. Type /help for available commands.", rawHtml = false, record = false)
    }

    private fun addUserEntry(text: String) {
        conversation.add(ChatMessage("user", text))
        val index = conversation.lastIndex
        val js = "window.addMessage('user', " + gson.toJson(text.replace("\n", "<br>")) + ", $index);"
        browser.cefBrowser.executeJavaScript(js, browser.cefBrowser.url, 0)
    }

    private fun addAssistantEntry(text: String, rawHtml: Boolean = false, record: Boolean = true) {
        val html = if (rawHtml) text else renderMarkdown(text)
        if (record) {
            conversation.add(ChatMessage("assistant", text))
        }
        val index = if (record) conversation.lastIndex else -1
        val js = "window.addMessage('assistant', " + gson.toJson(html) + ", " + index + ");"
        browser.cefBrowser.executeJavaScript(js, browser.cefBrowser.url, 0)
    }

    /**
     * Simple markdown renderer for basic formatting
     */
    private fun renderMarkdown(text: String): String {
        var html = StringUtil.escapeXmlEntities(text)

        // Code blocks
        html = html.replace(Regex("```([\\s\\S]*?)```")) { match ->
            "<pre><code>${match.groupValues[1].trim()}</code></pre>"
        }

        // Inline code
        html = html.replace(Regex("`([^`]+)`")) { match ->
            "<code>${match.groupValues[1]}</code>"
        }

        // Bold
        html = html.replace(Regex("\\*\\*([^*]+)\\*\\*")) { match ->
            "<strong>${match.groupValues[1]}</strong>"
        }

        // Italic
        html = html.replace(Regex("\\*([^*]+)\\*")) { match ->
            "<em>${match.groupValues[1]}</em>"
        }

        // Headers
        html = html.replace(Regex("^### (.+)$", RegexOption.MULTILINE)) { match ->
            "<h3>${match.groupValues[1]}</h3>"
        }
        html = html.replace(Regex("^## (.+)$", RegexOption.MULTILINE)) { match ->
            "<h2>${match.groupValues[1]}</h2>"
        }
        html = html.replace(Regex("^# (.+)$", RegexOption.MULTILINE)) { match ->
            "<h1>${match.groupValues[1]}</h1>"
        }

        // Lists
        html = html.replace(Regex("^- (.+)$", RegexOption.MULTILINE)) { match ->
            "<li>${match.groupValues[1]}</li>"
        }
        html = html.replace(Regex("(<li>.*</li>)", RegexOption.DOT_MATCHES_ALL)) { match ->
            "<ul>${match.groupValues[1]}</ul>"
        }

        // Line breaks
        html = html.replace("\n", "<br>")

        return html
    }

    private fun clearConversation() {
        browser.cefBrowser.executeJavaScript("document.getElementById('messages').innerHTML=''", browser.cefBrowser.url, 0)
        conversation.clear()
        addWelcomeMessage()
    }

    private fun handleCopy(index: Int) {
        if (index < 0 || index >= conversation.size) return
        val text = conversation[index].content
        CopyPasteManager.getInstance().setContents(StringSelection(text))
        ZigzagNotifier.notifyInfo(project, "Message copied to clipboard!")
    }

    private fun handleInsert(index: Int) {
        if (index < 0 || index >= conversation.size) return
        val text = conversation[index].content
        val editor = FileEditorManager.getInstance(project).selectedTextEditor
        if (editor != null) {
            WriteCommandAction.runWriteCommandAction(project) {
                editor.document.insertString(editor.caretModel.offset, text)
            }
            ZigzagNotifier.notifyInfo(project, "Message inserted into editor!")
        } else {
            ZigzagNotifier.notifyWarning(project, "No editor is currently open.")
        }
    }

    private fun handleRedo(index: Int) {
        if (index <= 0 || index > conversation.lastIndex) return
        var userIdx = index - 1
        while (userIdx >= 0 && conversation[userIdx].role != "user") {
            userIdx--
        }
        if (userIdx >= 0) {
            val prompt = conversation[userIdx].content
            handleUserInput(prompt)
        }
    }

    private fun handleRedoAll() {
        if (conversation.isEmpty()) return
        val lastUserMessage = conversation.findLast { it.role == "user" }
        if (lastUserMessage != null) {
            handleUserInput(lastUserMessage.content)
        }
    }

    private fun handleVote(data: String) {
        val parts = data.split(":")
        if (parts.size == 2) {
            val index = parts[0].toIntOrNull()
            val vote = parts[1]

            if (index != null && index >= 0 && index < conversation.size) {
                val isPositive = vote == "up"
                ZigzagLogger.info("User voted ${if (isPositive) "positive" else "negative"} for message at index $index")

                val message = if (isPositive) {
                    "Thanks for the positive feedback! 👍"
                } else {
                    "Thanks for the feedback. We'll work to improve! 👎"
                }
                ZigzagNotifier.notifyInfo(project, message)

                // Update UI to show vote was registered
                browser.cefBrowser.executeJavaScript(
                    "document.querySelector('[onclick=\"voteMessage(\\\'$data\\\')\"]').classList.add('voted');",
                    browser.cefBrowser.url, 0
                )
            }
        }
    }

    /**
     * Simple markdown renderer for basic formatting
     */
    private fun renderMarkdown(text: String): String {
        var html = StringUtil.escapeXmlEntities(text)

        // Code blocks
        html = html.replace(Regex("```([\\s\\S]*?)```")) { match ->
            "<pre><code>${match.groupValues[1].trim()}</code></pre>"
        }

        // Inline code
        html = html.replace(Regex("`([^`]+)`")) { match ->
            "<code>${match.groupValues[1]}</code>"
        }

        // Bold
        html = html.replace(Regex("\\*\\*([^*]+)\\*\\*")) { match ->
            "<strong>${match.groupValues[1]}</strong>"
        }

        // Italic
        html = html.replace(Regex("\\*([^*]+)\\*")) { match ->
            "<em>${match.groupValues[1]}</em>"
        }

        // Headers
        html = html.replace(Regex("^### (.+)$", RegexOption.MULTILINE)) { match ->
            "<h3>${match.groupValues[1]}</h3>"
        }
        html = html.replace(Regex("^## (.+)$", RegexOption.MULTILINE)) { match ->
            "<h2>${match.groupValues[1]}</h2>"
        }
        html = html.replace(Regex("^# (.+)$", RegexOption.MULTILINE)) { match ->
            "<h1>${match.groupValues[1]}</h1>"
        }

        // Lists
        html = html.replace(Regex("^- (.+)$", RegexOption.MULTILINE)) { match ->
            "<li>${match.groupValues[1]}</li>"
        }
        html = html.replace(Regex("(<li>.*</li>)", RegexOption.DOT_MATCHES_ALL)) { match ->
            "<ul>${match.groupValues[1]}</ul>"
        }

        // Line breaks
        html = html.replace("\n", "<br>")

        return html
    }
}
