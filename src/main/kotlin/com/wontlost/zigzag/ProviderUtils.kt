package com.wontlost.zigzag

/**
 * Utility class for provider-related operations to reduce code duplication
 */
object ProviderUtils {
    
    /**
     * Get API key for the specified provider from settings
     */
    fun getApiKeyForProvider(provider: UnifiedAIClient.Provider, settings: OpenAISettingsComponent): String? {
        return when (provider) {
            UnifiedAIClient.Provider.OPENAI -> settings.apiKey
            UnifiedAIClient.Provider.META -> settings.metaApiKey
            UnifiedAIClient.Provider.GOOGLE -> settings.googleApiKey
            UnifiedAIClient.Provider.OLLAMA, UnifiedAIClient.Provider.LLAMACPP -> null
        }
    }
    
    /**
     * Get base URL for the specified provider from settings
     */
    fun getBaseUrlForProvider(provider: UnifiedAIClient.Provider, settings: OpenAISettingsComponent): String? {
        return when (provider) {
            UnifiedAIClient.Provider.OLLAMA -> settings.ollamaUrl
            UnifiedAIClient.Provider.LLAMACPP -> settings.llamaCppUrl
            else -> settings.customBaseUrl
        }
    }
    
    /**
     * Create an AI request from current settings
     */
    fun createAIRequest(
        prompt: String,
        settings: OpenAISettingsComponent,
        maxTokens: Int? = null
    ): UnifiedAIClient.AIRequest {
        val provider = try {
            UnifiedAIClient.Provider.valueOf(settings.provider.uppercase().replace(" ", "_"))
        } catch (e: IllegalArgumentException) {
            UnifiedAIClient.Provider.OPENAI // Default fallback
        }
        
        return UnifiedAIClient.AIRequest(
            prompt = prompt,
            provider = provider,
            model = settings.model,
            apiKey = getApiKeyForProvider(provider, settings),
            baseUrl = getBaseUrlForProvider(provider, settings),
            maxTokens = maxTokens ?: settings.maxTokens,
            temperature = settings.temperature
        )
    }
    
    /**
     * Validate provider configuration
     */
    fun validateProviderConfig(provider: UnifiedAIClient.Provider, settings: OpenAISettingsComponent): String? {
        return when (provider) {
            UnifiedAIClient.Provider.OPENAI -> {
                if (settings.apiKey.isNullOrBlank()) "OpenAI API key is required" else null
            }
            UnifiedAIClient.Provider.META -> {
                if (settings.metaApiKey.isNullOrBlank()) "Meta API key is required" else null
            }
            UnifiedAIClient.Provider.GOOGLE -> {
                if (settings.googleApiKey.isNullOrBlank()) "Google API key is required" else null
            }
            UnifiedAIClient.Provider.OLLAMA -> {
                if (settings.ollamaUrl.isBlank()) "Ollama URL is required" else null
            }
            UnifiedAIClient.Provider.LLAMACPP -> {
                if (settings.llamaCppUrl.isBlank()) "Llama.cpp URL is required" else null
            }
        }
    }
    
    /**
     * Get cost estimate for a provider (returns 0.0 for local providers)
     */
    fun estimateCost(provider: UnifiedAIClient.Provider, model: String, tokens: Int): Double {
        return when (provider) {
            UnifiedAIClient.Provider.OPENAI -> {
                when (model) {
                    "gpt-3.5-turbo" -> tokens * 0.000002
                    "gpt-4" -> tokens * 0.00003
                    "gpt-4-turbo" -> tokens * 0.00001
                    "gpt-4o" -> tokens * 0.000005
                    "gpt-4o-mini" -> tokens * 0.00000015
                    else -> tokens * 0.000002
                }
            }
            UnifiedAIClient.Provider.META -> tokens * 0.0000008 // Together AI pricing
            UnifiedAIClient.Provider.GOOGLE -> tokens * 0.000001 // Gemini pricing
            UnifiedAIClient.Provider.OLLAMA, UnifiedAIClient.Provider.LLAMACPP -> 0.0 // Local is free
        }
    }
    
    /**
     * Get display name for provider
     */
    fun getProviderDisplayName(provider: UnifiedAIClient.Provider): String {
        return provider.displayName
    }
    
    /**
     * Get available models for provider
     */
    fun getModelsForProvider(provider: UnifiedAIClient.Provider): List<String> {
        return when (provider) {
            UnifiedAIClient.Provider.OPENAI -> listOf("gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini")
            UnifiedAIClient.Provider.META -> listOf("meta-llama/Llama-2-70b-chat-hf", "meta-llama/Llama-2-13b-chat-hf", "meta-llama/Llama-2-7b-chat-hf")
            UnifiedAIClient.Provider.GOOGLE -> listOf("gemini-pro", "gemini-pro-vision", "gemini-1.5-pro")
            UnifiedAIClient.Provider.OLLAMA -> listOf("llama2", "codellama", "mistral", "neural-chat", "starcode")
            UnifiedAIClient.Provider.LLAMACPP -> listOf("local-model")
        }
    }
}
