package com.wontlost.zigzag

import com.intellij.icons.AllIcons
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.Messages
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTextArea
import com.intellij.util.ui.JBUI
import java.awt.*
import java.awt.datatransfer.StringSelection
import java.awt.event.ActionEvent
import java.awt.event.KeyAdapter
import java.awt.event.KeyEvent
import javax.swing.*
import javax.swing.border.EmptyBorder

/**
 * Simplified AI assistant panel using native Swing components for better performance and maintainability
 */
class SimplifiedAssistantPanel(private val project: Project) : JPanel(BorderLayout()) {
    
    private val conversationArea = JBTextArea()
    private val inputArea = JBTextArea()
    private val sendButton = JButton("Send")
    private val clearButton = JButton("Clear")
    private val conversation = mutableListOf<Pair<String, String>>() // role to content
    
    init {
        setupUI()
        addWelcomeMessage()
    }
    
    private fun setupUI() {
        background = JBUI.CurrentTheme.DefaultTabs.background()
        
        // Setup conversation area
        conversationArea.apply {
            isEditable = false
            wrapStyleWord = true
            lineWrap = true
            font = Font(Font.SANS_SERIF, Font.PLAIN, 13)
            background = Color.WHITE
            border = EmptyBorder(10, 10, 10, 10)
        }
        
        val conversationScrollPane = JBScrollPane(conversationArea).apply {
            verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED
            horizontalScrollBarPolicy = JScrollPane.HORIZONTAL_SCROLLBAR_NEVER
            border = JBUI.Borders.customLine(Color.GRAY)
        }
        
        // Setup input area
        inputArea.apply {
            wrapStyleWord = true
            lineWrap = true
            font = Font(Font.SANS_SERIF, Font.PLAIN, 13)
            rows = 3
            border = EmptyBorder(8, 8, 8, 8)
            
            addKeyListener(object : KeyAdapter() {
                override fun keyPressed(e: KeyEvent) {
                    if (e.keyCode == KeyEvent.VK_ENTER && !e.isShiftDown) {
                        e.consume()
                        sendMessage()
                    }
                }
            })
        }
        
        val inputScrollPane = JBScrollPane(inputArea).apply {
            verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED
            horizontalScrollBarPolicy = JScrollPane.HORIZONTAL_SCROLLBAR_NEVER
            border = JBUI.Borders.customLine(Color.GRAY)
            preferredSize = Dimension(0, 80)
        }
        
        // Setup buttons
        sendButton.apply {
            icon = AllIcons.Actions.Execute
            addActionListener { sendMessage() }
            preferredSize = Dimension(80, 30)
        }
        
        clearButton.apply {
            icon = AllIcons.Actions.GC
            addActionListener { clearConversation() }
            preferredSize = Dimension(80, 30)
        }
        
        // Layout
        val inputPanel = JPanel(BorderLayout()).apply {
            border = EmptyBorder(5, 5, 5, 5)
            add(JLabel("💬 Ask Zigzag AI:"), BorderLayout.NORTH)
            add(inputScrollPane, BorderLayout.CENTER)
            
            val buttonPanel = JPanel(FlowLayout(FlowLayout.RIGHT)).apply {
                add(clearButton)
                add(sendButton)
            }
            add(buttonPanel, BorderLayout.SOUTH)
        }
        
        add(conversationScrollPane, BorderLayout.CENTER)
        add(inputPanel, BorderLayout.SOUTH)
    }
    
    private fun addWelcomeMessage() {
        val welcome = """
            👋 Welcome to Zigzag AI Assistant!
            
            I can help you with:
            • Code generation and completion
            • Code explanation and documentation
            • Bug fixing and optimization
            • Best practices and suggestions
            
            Just type your question below and I'll assist you!
            
            ═══════════════════════════════════════════════════════════════
            
        """.trimIndent()
        
        conversationArea.text = welcome
        conversation.add("assistant" to welcome)
    }
    
    private fun sendMessage() {
        val prompt = inputArea.text.trim()
        if (prompt.isEmpty()) return
        
        inputArea.text = ""
        sendButton.isEnabled = false
        sendButton.text = "Sending..."
        
        // Add user message
        addMessage("user", prompt)
        
        // Send to AI in background
        ApplicationManager.getApplication().executeOnPooledThread {
            val response = try {
                val settings = OpenAISettingsComponent.getInstance()
                val provider = UnifiedAIClient.Provider.valueOf(settings.provider.uppercase().replace(" ", "_"))
                
                val request = UnifiedAIClient.AIRequest(
                    prompt = "You are a helpful coding assistant. Please provide a concise and helpful response to: $prompt",
                    provider = provider,
                    model = settings.model,
                    apiKey = getApiKeyForProvider(provider, settings),
                    baseUrl = getBaseUrlForProvider(provider, settings),
                    maxTokens = settings.maxTokens,
                    temperature = settings.temperature
                )
                
                val client = UnifiedAIClient()
                val aiResponse = client.createCompletion(request)
                aiResponse.text
            } catch (e: Exception) {
                ErrorHandler.handleApiError(project, e)
                "Sorry, I encountered an error. Please try again."
            }
            
            SwingUtilities.invokeLater {
                addMessage("assistant", response)
                sendButton.isEnabled = true
                sendButton.text = "Send"
            }
        }
    }
    
    private fun addMessage(role: String, content: String) {
        conversation.add(role to content)
        
        val roleIcon = if (role == "user") "👤" else "🤖"
        val roleName = if (role == "user") "You" else "Zigzag AI"
        
        val messageText = "\n$roleIcon $roleName:\n$content\n"
        
        if (role == "assistant") {
            // Add action buttons for AI responses
            val actionsText = "\n[Copy] [Insert] [👍] [👎]\n"
            conversationArea.append(messageText + actionsText)
        } else {
            conversationArea.append(messageText)
        }
        
        conversationArea.append("═══════════════════════════════════════════════════════════════\n")
        
        // Auto-scroll to bottom
        SwingUtilities.invokeLater {
            conversationArea.caretPosition = conversationArea.document.length
        }
    }
    
    private fun clearConversation() {
        conversationArea.text = ""
        conversation.clear()
        addWelcomeMessage()
    }
    
    private fun getApiKeyForProvider(provider: UnifiedAIClient.Provider, settings: OpenAISettingsComponent): String? {
        return when (provider) {
            UnifiedAIClient.Provider.OPENAI -> settings.apiKey
            UnifiedAIClient.Provider.META -> settings.metaApiKey
            UnifiedAIClient.Provider.GOOGLE -> settings.googleApiKey
            UnifiedAIClient.Provider.OLLAMA, UnifiedAIClient.Provider.LLAMACPP -> null
        }
    }
    
    private fun getBaseUrlForProvider(provider: UnifiedAIClient.Provider, settings: OpenAISettingsComponent): String? {
        return when (provider) {
            UnifiedAIClient.Provider.OLLAMA -> settings.ollamaUrl
            UnifiedAIClient.Provider.LLAMACPP -> settings.llamaCppUrl
            else -> settings.customBaseUrl
        }
    }
    
    /**
     * Copy the last AI response to clipboard
     */
    fun copyLastResponse() {
        val lastAssistantMessage = conversation.findLast { it.first == "assistant" }?.second
        if (lastAssistantMessage != null) {
            val clipboard = Toolkit.getDefaultToolkit().systemClipboard
            clipboard.setContents(StringSelection(lastAssistantMessage), null)
            ZigzagNotifier.notifyInfo(project, "Response copied to clipboard!")
        }
    }
    
    /**
     * Insert the last AI response into the active editor
     */
    fun insertLastResponse() {
        val lastAssistantMessage = conversation.findLast { it.first == "assistant" }?.second
        if (lastAssistantMessage != null) {
            val editor = FileEditorManager.getInstance(project).selectedTextEditor
            if (editor != null) {
                WriteCommandAction.runWriteCommandAction(project) {
                    val document = editor.document
                    val caretModel = editor.caretModel
                    document.insertString(caretModel.offset, lastAssistantMessage)
                }
                ZigzagNotifier.notifyInfo(project, "Response inserted into editor!")
            } else {
                Messages.showWarningDialog(project, "No editor is currently open.", "Insert Response")
            }
        }
    }
    
    /**
     * Vote on the last AI response
     */
    fun voteOnLastResponse(isPositive: Boolean) {
        val voteType = if (isPositive) "positive" else "negative"
        ZigzagLogger.info("User voted $voteType on last response")
        
        val message = if (isPositive) {
            "Thanks for the positive feedback! 👍"
        } else {
            "Thanks for the feedback. We'll work to improve! 👎"
        }
        ZigzagNotifier.notifyInfo(project, message)
    }
}
