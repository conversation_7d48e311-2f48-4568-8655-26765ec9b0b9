package com.wontlost.zigzag

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.patterns.PlatformPatterns
import com.intellij.psi.PsiFile
import com.intellij.util.ProcessingContext
import com.intellij.openapi.util.TextRange
import com.wontlost.zigzag.ZigzagNotifier

/**
 * Completion contributor that queries OpenAI Codex for inline code suggestions.
 */
class CodexCompletionContributor : CompletionContributor() {
    init {
        extend(
            CompletionType.BASIC,
            PlatformPatterns.psiElement(),
            CodexCompletionProvider()
        )
    }

    private class CodexCompletionProvider : CompletionProvider<CompletionParameters>() {
        override fun addCompletions(
            parameters: CompletionParameters,
            context: ProcessingContext,
            result: CompletionResultSet
        ) {
            val settings = OpenAISettingsComponent.getInstance()
            if (!settings.inlineCompletionsEnabled) {
                return
            }
            val file: PsiFile = parameters.originalFile
            val editor = parameters.editor
            val offset = editor.caretModel.offset
            val document = editor.document

            // Capture surrounding code for context
            val start = (offset - 200).coerceAtLeast(0)
            val end = (offset + 200).coerceAtMost(document.textLength)
            val snippet = document.getText(TextRange(start, end))

            val language = file.language.id
            val prompt = "// language: $language\n$snippet"

            try {
                val client = CodexClient()
                val response = client.createCompletion(prompt)
                response.choices?.forEach { choice ->
                    val suggestion = choice.text?.trim()
                    if (!suggestion.isNullOrEmpty()) {
                        result.addElement(LookupElementBuilder.create(suggestion))
                    }
                }
            } catch (e: Exception) {
                ZigzagNotifier.notifyError(parameters.editor.project, e.message ?: "Unknown error")
            }
        }
    }
}
