package com.wontlost.zigzag

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage

@Service(Service.Level.APP)
@State(name = "OpenAISettings", storages = [Storage("zigzag_settings.xml")])
class OpenAISettingsComponent : PersistentStateComponent<OpenAISettingsComponent.State> {
    class State {
        var apiKey: String? = null
        var temperature: Double = 0.5
        var maxTokens: Int = 100
        var inlineCompletionsEnabled: Boolean = true
        var chatPanelEnabled: Boolean = true
    }

    private var state = State()

    override fun getState(): State = state

    override fun loadState(state: State) {
        this.state = state
    }

    var apiKey: String?
        get() = state.apiKey
        set(value) {
            state.apiKey = value
        }

    var temperature: Double
        get() = state.temperature
        set(value) {
            state.temperature = value
        }

    var maxTokens: Int
        get() = state.maxTokens
        set(value) {
            state.maxTokens = value
        }

    var inlineCompletionsEnabled: Boolean
        get() = state.inlineCompletionsEnabled
        set(value) {
            state.inlineCompletionsEnabled = value
        }

    var chatPanelEnabled: Boolean
        get() = state.chatPanelEnabled
        set(value) {
            state.chatPanelEnabled = value
        }

    companion object {
        @JvmStatic
        fun getInstance(): OpenAISettingsComponent =
            ApplicationManager.getApplication().getService(OpenAISettingsComponent::class.java)
    }
}
