package com.wontlost.zigzag

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage

@Service(Service.Level.APP)
@State(name = "OpenAISettings", storages = [Storage("zigzag_settings.xml")])
class OpenAISettingsComponent : PersistentStateComponent<OpenAISettingsComponent.State> {
    class State {
        var apiKey: String? = null
        var temperature: Double = 0.7
        var maxTokens: Int = 150
        var inlineCompletionsEnabled: Boolean = true
        var chatPanelEnabled: Boolean = true
        var model: String = "gpt-3.5-turbo"
        var enableLogging: Boolean = false
        var requestTimeout: Int = 30

        // Multi-provider settings
        var provider: String = "OpenAI"
        var customBaseUrl: String? = null
        var metaApiKey: String? = null
        var googleApiKey: String? = null
        var ollamaUrl: String = "http://localhost:11434"
        var llamaCppUrl: String = "http://localhost:8080"
    }

    private var state = State()

    override fun getState(): State = state

    override fun loadState(state: State) {
        this.state = state
    }

    var apiKey: String?
        get() = state.apiKey
        set(value) {
            state.apiKey = value
        }

    var temperature: Double
        get() = state.temperature
        set(value) {
            state.temperature = value
        }

    var maxTokens: Int
        get() = state.maxTokens
        set(value) {
            state.maxTokens = value
        }

    var inlineCompletionsEnabled: Boolean
        get() = state.inlineCompletionsEnabled
        set(value) {
            state.inlineCompletionsEnabled = value
        }

    var chatPanelEnabled: Boolean
        get() = state.chatPanelEnabled
        set(value) {
            state.chatPanelEnabled = value
        }

    var model: String
        get() = state.model
        set(value) {
            state.model = value
        }

    var enableLogging: Boolean
        get() = state.enableLogging
        set(value) {
            state.enableLogging = value
        }

    var requestTimeout: Int
        get() = state.requestTimeout
        set(value) {
            state.requestTimeout = value
        }

    // Multi-provider properties
    var provider: String
        get() = state.provider
        set(value) {
            state.provider = value
        }

    var customBaseUrl: String?
        get() = state.customBaseUrl
        set(value) {
            state.customBaseUrl = value
        }

    var metaApiKey: String?
        get() = state.metaApiKey
        set(value) {
            state.metaApiKey = value
        }

    var googleApiKey: String?
        get() = state.googleApiKey
        set(value) {
            state.googleApiKey = value
        }

    var ollamaUrl: String
        get() = state.ollamaUrl
        set(value) {
            state.ollamaUrl = value
        }

    var llamaCppUrl: String
        get() = state.llamaCppUrl
        set(value) {
            state.llamaCppUrl = value
        }

    companion object {
        @JvmStatic
        fun getInstance(): OpenAISettingsComponent =
            ApplicationManager.getApplication().getService(OpenAISettingsComponent::class.java)
    }
}
