package com.wontlost.zigzag

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.text.StringUtil
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.fileTypes.FileTypeManager
import com.wontlost.zigzag.ZigzagNotifier
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.EditorTextField
import java.awt.BorderLayout
import javax.swing.JButton
import javax.swing.JLabel
import javax.swing.JPanel
import javax.swing.JTextField
import javax.swing.BoxLayout
import javax.swing.SwingUtilities

class CodexAssistantPanel(private val project: Project) : JPanel(BorderLayout()) {
    private val conversationPanel = JPanel()
    private val inputField = JTextField()
    private val sendButton = JButton("Send")

    init {
        conversationPanel.layout = BoxLayout(conversationPanel, BoxLayout.Y_AXIS)
        val scrollPane = JBScrollPane(conversationPanel)
        add(scrollPane, BorderLayout.CENTER)

        val bottom = JPanel(BorderLayout())
        bottom.add(inputField, BorderLayout.CENTER)
        bottom.add(sendButton, BorderLayout.EAST)
        add(bottom, BorderLayout.SOUTH)

        sendButton.addActionListener { sendPrompt() }
        inputField.addActionListener { sendPrompt() }
    }

    private fun sendPrompt() {
        val prompt = inputField.text.trim()
        if (prompt.isEmpty()) return
        inputField.text = ""
        addUserEntry(prompt)
        ApplicationManager.getApplication().executeOnPooledThread {
            val responseText = try {
                val client = CodexClient()
                val response = client.createCompletion(prompt)
                response.choices?.firstOrNull()?.text?.trim() ?: ""
            } catch (e: Exception) {
                ZigzagNotifier.notifyError(project, e.message ?: "Unknown error")
                "Error: ${'$'}{e.message}"
            }
            SwingUtilities.invokeLater { addAssistantEntry(responseText) }
        }
    }

    private fun addUserEntry(text: String) {
        val label = JLabel("<html><b>You:</b> " + StringUtil.escapeXmlEntities(text) + "</html>")
        conversationPanel.add(label)
        conversationPanel.revalidate()
    }

    private fun addAssistantEntry(text: String) {
        val fileType = FileTypeManager.getInstance().getFileTypeByExtension("kt")
        val document = EditorFactory.getInstance().createDocument(text)
        val field = EditorTextField(document, project, fileType, true, false)
        field.setOneLineMode(false)
        conversationPanel.add(field)
        conversationPanel.revalidate()
    }
}
