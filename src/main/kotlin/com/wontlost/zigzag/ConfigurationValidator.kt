package com.wontlost.zigzag

/**
 * Validates plugin configuration settings to ensure they are within acceptable ranges
 * and provide helpful feedback to users.
 */
object ConfigurationValidator {
    
    data class ValidationResult(
        val isValid: <PERSON><PERSON>an,
        val errors: List<String> = emptyList(),
        val warnings: List<String> = emptyList()
    )
    
    /**
     * Validate all configuration settings
     */
    fun validateSettings(settings: OpenAISettingsComponent): ValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()
        
        // Validate API key
        validateApiKey(settings.apiKey)?.let { errors.add(it) }
        
        // Validate temperature
        validateTemperature(settings.temperature)?.let { errors.add(it) }
        
        // Validate max tokens
        validateMaxTokens(settings.maxTokens)?.let { error ->
            if (error.startsWith("Warning:")) {
                warnings.add(error.removePrefix("Warning: "))
            } else {
                errors.add(error)
            }
        }
        
        // Validate model
        validateModel(settings.model)?.let { warnings.add(it) }
        
        // Validate timeout
        validateTimeout(settings.requestTimeout)?.let { errors.add(it) }
        
        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }
    
    /**
     * Validate API key format
     */
    private fun validateApiKey(apiKey: String?): String? {
        return when {
            apiKey.isNullOrBlank() -> "API key is required"
            !apiKey.startsWith("sk-") -> "API key should start with 'sk-'"
            apiKey.length < 20 -> "API key appears to be too short"
            else -> null
        }
    }
    
    /**
     * Validate temperature parameter
     */
    private fun validateTemperature(temperature: Double): String? {
        return when {
            temperature < 0.0 -> "Temperature cannot be negative"
            temperature > 2.0 -> "Temperature cannot exceed 2.0"
            else -> null
        }
    }
    
    /**
     * Validate max tokens parameter
     */
    private fun validateMaxTokens(maxTokens: Int): String? {
        return when {
            maxTokens < 1 -> "Max tokens must be at least 1"
            maxTokens > 4000 -> "Max tokens cannot exceed 4000"
            maxTokens > 2000 -> "Warning: High token count may result in expensive API calls"
            maxTokens < 10 -> "Warning: Very low token count may result in incomplete responses"
            else -> null
        }
    }
    
    /**
     * Validate model selection
     */
    private fun validateModel(model: String): String? {
        val supportedModels = listOf("gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini")
        return when {
            model !in supportedModels -> "Unsupported model: $model"
            model.startsWith("gpt-4") && !model.contains("mini") -> "GPT-4 models are more expensive than GPT-3.5-turbo"
            model == "gpt-4o" -> "GPT-4o is the latest and most capable model but also the most expensive"
            else -> null
        }
    }
    
    /**
     * Validate request timeout
     */
    private fun validateTimeout(timeout: Int): String? {
        return when {
            timeout < 5 -> "Timeout must be at least 5 seconds"
            timeout > 120 -> "Timeout cannot exceed 120 seconds"
            else -> null
        }
    }
    
    /**
     * Get recommended settings for different use cases
     */
    fun getRecommendedSettings(useCase: UseCase): Map<String, Any> {
        return when (useCase) {
            UseCase.FAST_COMPLETIONS -> mapOf(
                "model" to "gpt-3.5-turbo",
                "temperature" to 0.3,
                "maxTokens" to 50,
                "requestTimeout" to 15
            )
            
            UseCase.BALANCED -> mapOf(
                "model" to "gpt-3.5-turbo",
                "temperature" to 0.7,
                "maxTokens" to 150,
                "requestTimeout" to 30
            )
            
            UseCase.HIGH_QUALITY -> mapOf(
                "model" to "gpt-4",
                "temperature" to 0.5,
                "maxTokens" to 300,
                "requestTimeout" to 60
            )
            
            UseCase.CREATIVE -> mapOf(
                "model" to "gpt-4",
                "temperature" to 1.2,
                "maxTokens" to 500,
                "requestTimeout" to 60
            )
        }
    }
    
    enum class UseCase {
        FAST_COMPLETIONS,
        BALANCED,
        HIGH_QUALITY,
        CREATIVE
    }
    
    /**
     * Estimate API cost per request based on current settings
     */
    fun estimateCostPerRequest(settings: OpenAISettingsComponent): Double {
        // Updated cost estimates (as of 2025, subject to change)
        val costPer1kTokens = when (settings.model) {
            "gpt-3.5-turbo" -> 0.002 // $0.002 per 1K tokens
            "gpt-4" -> 0.03 // $0.03 per 1K tokens
            "gpt-4-turbo" -> 0.01 // $0.01 per 1K tokens
            "gpt-4o" -> 0.015 // $0.015 per 1K tokens
            "gpt-4o-mini" -> 0.0015 // $0.0015 per 1K tokens
            else -> 0.002
        }

        // Estimate total tokens (prompt + completion)
        val estimatedPromptTokens = 100 // Average prompt size
        val estimatedTotalTokens = estimatedPromptTokens + settings.maxTokens

        return (estimatedTotalTokens / 1000.0) * costPer1kTokens
    }
}
