package com.wontlost.zigzag

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.fileTypes.FileTypeManager
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.openapi.application.ApplicationManager
import com.intellij.ui.EditorTextField
import javax.swing.JComponent
import javax.swing.SwingUtilities

/**
 * Context-aware action that generates code from comments or refactors selected code using AI.
 */
class AIContextAction : AnAction() {
    override fun actionPerformed(event: AnActionEvent) {
        val project = event.project ?: return
        val editor = event.getData(CommonDataKeys.EDITOR) ?: return
        val selection = editor.selectionModel
        val selectedText = selection.selectedText ?: return
        val trimmed = selectedText.trimStart()
        val isComment = trimmed.startsWith("//") || trimmed.startsWith("/*") || trimmed.startsWith("*")

        val prompt = if (isComment) {
            "Generate the code for the following description:\n$selectedText"
        } else {
            "Refactor the following code and only return the improved code:\n$selectedText"
        }

        ApplicationManager.getApplication().executeOnPooledThread {
            val result = try {
                val client = AIClient()
                val response = client.createCompletion(prompt)
                response.choices?.firstOrNull()?.text?.trim() ?: "No response generated"
            } catch (e: Exception) {
                ErrorHandler.handleApiError(project, e)
            }

            SwingUtilities.invokeLater {
                val dialog = object : DialogWrapper(project) {
                    init {
                        init()
                        title = "AI Suggestion"
                    }

                    override fun createCenterPanel(): JComponent {
                        val doc = EditorFactory.getInstance().createDocument(result)
                        val fileType = FileTypeManager.getInstance().getFileTypeByExtension("kt")
                        return EditorTextField(doc, project, fileType, true, false)
                    }
                }

                if (dialog.showAndGet()) {
                    WriteCommandAction.runWriteCommandAction(project) {
                        if (isComment) {
                            editor.document.insertString(selection.selectionEnd, "\n$result")
                        } else {
                            editor.document.replaceString(selection.selectionStart, selection.selectionEnd, result)
                        }
                    }
                }
            }
        }
    }
}
