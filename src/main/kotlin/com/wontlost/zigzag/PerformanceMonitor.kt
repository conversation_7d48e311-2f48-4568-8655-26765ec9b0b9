package com.wontlost.zigzag

import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import kotlin.system.measureTimeMillis

/**
 * Performance monitoring utility for tracking plugin performance metrics.
 */
object PerformanceMonitor {
    @PublishedApi
    internal val operationCounts = ConcurrentHashMap<String, AtomicLong>()
    @PublishedApi
    internal val operationTimes = ConcurrentHashMap<String, AtomicLong>()
    @PublishedApi
    internal val operationErrors = ConcurrentHashMap<String, AtomicLong>()
    
    /**
     * Execute an operation and measure its performance
     */
    inline fun <T> measureOperation(operationName: String, operation: () -> T): T {
        val count = operationCounts.computeIfAbsent(operationName) { AtomicLong(0) }
        val totalTime = operationTimes.computeIfAbsent(operationName) { AtomicLong(0) }
        val errors = operationErrors.computeIfAbsent(operationName) { AtomicLong(0) }
        
        count.incrementAndGet()
        
        return try {
            val result: T
            val duration = measureTimeMillis {
                result = operation()
            }
            
            totalTime.addAndGet(duration)
            ZigzagLogger.logPerformance(operationName, duration)
            
            result
        } catch (e: Exception) {
            errors.incrementAndGet()
            ZigzagLogger.error("Operation '$operationName' failed", e)
            throw e
        }
    }
    
    /**
     * Get performance statistics for an operation
     */
    fun getStats(operationName: String): OperationStats? {
        val count = operationCounts[operationName]?.get() ?: return null
        val totalTime = operationTimes[operationName]?.get() ?: 0
        val errors = operationErrors[operationName]?.get() ?: 0
        
        return OperationStats(
            operationName = operationName,
            totalCalls = count,
            totalTimeMs = totalTime,
            averageTimeMs = if (count > 0) totalTime / count else 0,
            errorCount = errors,
            successRate = if (count > 0) ((count - errors).toDouble() / count * 100) else 0.0
        )
    }
    
    /**
     * Get all performance statistics
     */
    fun getAllStats(): List<OperationStats> {
        return operationCounts.keys.mapNotNull { getStats(it) }
            .sortedByDescending { it.totalCalls }
    }
    
    /**
     * Reset all performance statistics
     */
    fun reset() {
        operationCounts.clear()
        operationTimes.clear()
        operationErrors.clear()
    }
    
    /**
     * Log performance summary
     */
    fun logSummary() {
        val stats = getAllStats()
        if (stats.isEmpty()) {
            ZigzagLogger.info("No performance data available")
            return
        }
        
        ZigzagLogger.info("=== Performance Summary ===")
        stats.forEach { stat ->
            ZigzagLogger.info(
                "${stat.operationName}: ${stat.totalCalls} calls, " +
                "avg ${stat.averageTimeMs}ms, ${stat.successRate.format(1)}% success"
            )
        }
    }
    
    private fun Double.format(digits: Int) = "%.${digits}f".format(this)
    
    data class OperationStats(
        val operationName: String,
        val totalCalls: Long,
        val totalTimeMs: Long,
        val averageTimeMs: Long,
        val errorCount: Long,
        val successRate: Double
    )
}

/**
 * Extension function to easily measure performance of any block
 */
inline fun <T> measurePerformance(operationName: String, block: () -> T): T {
    return PerformanceMonitor.measureOperation(operationName, block)
}
