package com.wontlost.zigzag

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.openapi.application.ApplicationManager
import com.intellij.patterns.PlatformPatterns
import com.intellij.psi.PsiFile
import com.intellij.util.ProcessingContext
import com.intellij.openapi.util.TextRange

/**
 * Completion contributor that queries AI for inline code suggestions.
 */
class AICompletionContributor : CompletionContributor() {
    init {
        extend(
            CompletionType.BASIC,
            PlatformPatterns.psiElement(),
            AICompletionProvider()
        )
    }

    private class AICompletionProvider : CompletionProvider<CompletionParameters>() {
        override fun addCompletions(
            parameters: CompletionParameters,
            context: ProcessingContext,
            result: CompletionResultSet
        ) {
            val settings = OpenAISettingsComponent.getInstance()
            if (!settings.inlineCompletionsEnabled) {
                return
            }
            val file: PsiFile = parameters.originalFile
            val editor = parameters.editor
            val offset = editor.caretModel.offset
            val document = editor.document

            // Capture surrounding code for context
            val start = (offset - 200).coerceAtLeast(0)
            val end = (offset + 200).coerceAtMost(document.textLength)
            val snippet = document.getText(TextRange(start, end))

            val language = file.language.id
            val prompt = "// language: $language\n$snippet"

            // Use background thread for non-blocking completion
            ApplicationManager.getApplication().executeOnPooledThread {
                try {
                    val client = AIClient()
                    val response = client.createCompletion(prompt)

                    // Switch back to EDT for UI updates
                    ApplicationManager.getApplication().invokeLater {
                        response.choices?.forEach { choice ->
                            val suggestion = choice.text?.trim()
                            if (!suggestion.isNullOrEmpty() && suggestion.length > 2) {
                                // Create a more informative lookup element
                                val lookupElement = LookupElementBuilder.create(suggestion)
                                    .withTypeText("AI Suggestion")
                                    .withIcon(com.intellij.icons.AllIcons.Actions.IntentionBulb)
                                    .withTailText(" (AI)", true)
                                result.addElement(lookupElement)
                            }
                        }
                    }
                } catch (e: Exception) {
                    // Use error handler but don't show notifications for completions
                    ErrorHandler.handleCompletionError(e)
                }
            }
        }
    }
}
