package com.wontlost.zigzag

import com.intellij.openapi.options.Configurable
import javax.swing.*

class OpenAISettingsConfigurable : Configurable {
    private var panel: JPanel? = null
    private var apiKeyField: JPasswordField? = null
    private var temperatureField: JSpinner? = null
    private var maxTokensField: JSpinner? = null
    private var inlineCompletionBox: JCheckBox? = null
    private var chatPanelBox: JCheckBox? = null

    override fun createComponent(): JComponent {
        val settings = OpenAISettingsComponent.getInstance()
        apiKeyField = JPasswordField(settings.apiKey ?: "")
        temperatureField = JSpinner(SpinnerNumberModel(settings.temperature, 0.0, 1.0, 0.1))
        maxTokensField = JSpinner(SpinnerNumberModel(settings.maxTokens, 1, 4000, 1))
        inlineCompletionBox = JCheckBox("Enable inline completions", settings.inlineCompletionsEnabled)
        chatPanelBox = JCheckBox("Enable chat panel", settings.chatPanelEnabled)

        panel = JPanel()
        panel!!.layout = BoxLayout(panel, BoxLayout.Y_AXIS)
        panel!!.add(JLabel("OpenAI API Key:"))
        panel!!.add(apiKeyField)
        panel!!.add(JLabel("Temperature:"))
        panel!!.add(temperatureField)
        panel!!.add(JLabel("Max Tokens:"))
        panel!!.add(maxTokensField)
        panel!!.add(inlineCompletionBox)
        panel!!.add(chatPanelBox)
        return panel as JPanel
    }

    override fun isModified(): Boolean {
        val settings = OpenAISettingsComponent.getInstance()
        return settings.apiKey != String(apiKeyField?.password ?: CharArray(0)) ||
               settings.temperature != (temperatureField?.value as Double) ||
               settings.maxTokens != (maxTokensField?.value as Int) ||
               settings.inlineCompletionsEnabled != inlineCompletionBox?.isSelected ||
               settings.chatPanelEnabled != chatPanelBox?.isSelected
    }

    override fun apply() {
        val settings = OpenAISettingsComponent.getInstance()
        settings.apiKey = String(apiKeyField?.password ?: CharArray(0))
        settings.temperature = temperatureField?.value as Double
        settings.maxTokens = maxTokensField?.value as Int
        settings.inlineCompletionsEnabled = inlineCompletionBox?.isSelected ?: true
        settings.chatPanelEnabled = chatPanelBox?.isSelected ?: true
    }

    override fun reset() {
        val settings = OpenAISettingsComponent.getInstance()
        apiKeyField?.text = settings.apiKey ?: ""
        temperatureField?.value = settings.temperature
        maxTokensField?.value = settings.maxTokens
        inlineCompletionBox?.isSelected = settings.inlineCompletionsEnabled
        chatPanelBox?.isSelected = settings.chatPanelEnabled
    }

    override fun getDisplayName(): String = "Zigzag"

    override fun disposeUIResources() {
        panel = null
        apiKeyField = null
        temperatureField = null
        maxTokensField = null
        inlineCompletionBox = null
        chatPanelBox = null
    }
}
