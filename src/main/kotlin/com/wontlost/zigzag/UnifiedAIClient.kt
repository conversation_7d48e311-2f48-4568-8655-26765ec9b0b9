package com.wontlost.zigzag

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.util.concurrent.TimeUnit

/**
 * Unified AI client supporting multiple providers with simplified architecture
 */
class UnifiedAIClient {
    
    companion object {
        private val gson = Gson()
        private val httpClient = OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }
    
    enum class Provider(val displayName: String) {
        OPENAI("OpenAI"),
        META("Meta Llama"),
        GOOGLE("Google Gemini"),
        OLLAMA("Local Ollama"),
        LLAMACPP("Local Llama.cpp")
    }
    
    data class AIRequest(
        val prompt: String,
        val provider: Provider = Provider.OPENAI,
        val model: String = "gpt-3.5-turbo",
        val apiKey: String? = null,
        val baseUrl: String? = null,
        val maxTokens: Int = 1000,
        val temperature: Double = 0.7
    )
    
    data class AIResponse(
        val text: String,
        val usage: Usage? = null,
        val provider: Provider
    ) {
        data class Usage(
            val totalTokens: Int = 0,
            val promptTokens: Int = 0,
            val completionTokens: Int = 0
        )
    }
    
    @Throws(IOException::class)
    fun createCompletion(request: AIRequest): AIResponse {
        return when (request.provider) {
            Provider.OPENAI -> createOpenAICompletion(request)
            Provider.META -> createMetaCompletion(request)
            Provider.GOOGLE -> createGoogleCompletion(request)
            Provider.OLLAMA -> createOllamaCompletion(request)
            Provider.LLAMACPP -> createLlamaCppCompletion(request)
        }
    }
    
    private fun createOpenAICompletion(request: AIRequest): AIResponse {
        val url = request.baseUrl ?: "https://api.openai.com/v1/chat/completions"
        
        val payload = mapOf(
            "model" to request.model,
            "messages" to listOf(mapOf("role" to "user", "content" to request.prompt)),
            "max_tokens" to request.maxTokens,
            "temperature" to request.temperature
        )
        
        val httpRequest = Request.Builder()
            .url(url)
            .header("Authorization", "Bearer ${request.apiKey}")
            .header("Content-Type", "application/json")
            .post(gson.toJson(payload).toRequestBody("application/json".toMediaType()))
            .build()
        
        val response = executeRequest(httpRequest)
        val responseMap = parseJsonResponse(response)
        
        val choices = responseMap["choices"] as? List<*>
        val firstChoice = choices?.firstOrNull() as? Map<*, *>
        val message = firstChoice?.get("message") as? Map<*, *>
        val text = message?.get("content") as? String ?: ""
        
        val usage = responseMap["usage"] as? Map<*, *>
        val aiUsage = usage?.let {
            AIResponse.Usage(
                totalTokens = (it["total_tokens"] as? Number)?.toInt() ?: 0,
                promptTokens = (it["prompt_tokens"] as? Number)?.toInt() ?: 0,
                completionTokens = (it["completion_tokens"] as? Number)?.toInt() ?: 0
            )
        }
        
        return AIResponse(text, aiUsage, Provider.OPENAI)
    }
    
    private fun createMetaCompletion(request: AIRequest): AIResponse {
        val url = request.baseUrl ?: "https://api.together.xyz/v1/chat/completions"
        
        val payload = mapOf(
            "model" to request.model,
            "messages" to listOf(mapOf("role" to "user", "content" to request.prompt)),
            "max_tokens" to request.maxTokens,
            "temperature" to request.temperature
        )
        
        val httpRequest = Request.Builder()
            .url(url)
            .header("Authorization", "Bearer ${request.apiKey}")
            .header("Content-Type", "application/json")
            .post(gson.toJson(payload).toRequestBody("application/json".toMediaType()))
            .build()
        
        val response = executeRequest(httpRequest)
        val responseMap = parseJsonResponse(response)
        
        val choices = responseMap["choices"] as? List<*>
        val firstChoice = choices?.firstOrNull() as? Map<*, *>
        val message = firstChoice?.get("message") as? Map<*, *>
        val text = message?.get("content") as? String ?: ""
        
        return AIResponse(text, null, Provider.META)
    }
    
    private fun createGoogleCompletion(request: AIRequest): AIResponse {
        val url = request.baseUrl ?: "https://generativelanguage.googleapis.com/v1beta/models/${request.model}:generateContent"
        
        val payload = mapOf(
            "contents" to listOf(
                mapOf("parts" to listOf(mapOf("text" to request.prompt)))
            ),
            "generationConfig" to mapOf(
                "maxOutputTokens" to request.maxTokens,
                "temperature" to request.temperature
            )
        )
        
        val httpRequest = Request.Builder()
            .url("$url?key=${request.apiKey}")
            .header("Content-Type", "application/json")
            .post(gson.toJson(payload).toRequestBody("application/json".toMediaType()))
            .build()
        
        val response = executeRequest(httpRequest)
        val responseMap = parseJsonResponse(response)
        
        val candidates = responseMap["candidates"] as? List<*>
        val firstCandidate = candidates?.firstOrNull() as? Map<*, *>
        val content = firstCandidate?.get("content") as? Map<*, *>
        val parts = content?.get("parts") as? List<*>
        val firstPart = parts?.firstOrNull() as? Map<*, *>
        val text = firstPart?.get("text") as? String ?: ""
        
        return AIResponse(text, null, Provider.GOOGLE)
    }
    
    private fun createOllamaCompletion(request: AIRequest): AIResponse {
        val url = request.baseUrl ?: "http://localhost:11434/api/generate"
        
        val payload = mapOf(
            "model" to request.model,
            "prompt" to request.prompt,
            "stream" to false,
            "options" to mapOf(
                "num_predict" to request.maxTokens,
                "temperature" to request.temperature
            )
        )
        
        val httpRequest = Request.Builder()
            .url(url)
            .header("Content-Type", "application/json")
            .post(gson.toJson(payload).toRequestBody("application/json".toMediaType()))
            .build()
        
        val response = executeRequest(httpRequest)
        val responseMap = parseJsonResponse(response)
        
        val text = responseMap["response"] as? String ?: ""
        return AIResponse(text, null, Provider.OLLAMA)
    }
    
    private fun createLlamaCppCompletion(request: AIRequest): AIResponse {
        val url = request.baseUrl ?: "http://localhost:8080/completion"
        
        val payload = mapOf(
            "prompt" to request.prompt,
            "n_predict" to request.maxTokens,
            "temperature" to request.temperature,
            "stop" to listOf("</s>", "Human:", "Assistant:")
        )
        
        val httpRequest = Request.Builder()
            .url(url)
            .header("Content-Type", "application/json")
            .post(gson.toJson(payload).toRequestBody("application/json".toMediaType()))
            .build()
        
        val response = executeRequest(httpRequest)
        val responseMap = parseJsonResponse(response)
        
        val text = responseMap["content"] as? String ?: ""
        return AIResponse(text, null, Provider.LLAMACPP)
    }
    
    private fun executeRequest(request: Request): String {
        httpClient.newCall(request).execute().use { response ->
            if (!response.isSuccessful) {
                val errorBody = response.body?.string() ?: "Unknown error"
                throw IOException("API request failed with status ${response.code}: $errorBody")
            }
            return response.body?.string() ?: throw IOException("Empty response body")
        }
    }
    
    private fun parseJsonResponse(responseBody: String): Map<String, Any> {
        return try {
            @Suppress("UNCHECKED_CAST")
            gson.fromJson(responseBody, Map::class.java) as Map<String, Any>
        } catch (e: JsonSyntaxException) {
            throw IOException("Invalid JSON response: ${e.message}")
        }
    }
    
    /**
     * Get available models for a provider
     */
    fun getModelsForProvider(provider: Provider): List<String> {
        return when (provider) {
            Provider.OPENAI -> listOf("gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini")
            Provider.META -> listOf("meta-llama/Llama-2-70b-chat-hf", "meta-llama/Llama-2-13b-chat-hf", "meta-llama/Llama-2-7b-chat-hf")
            Provider.GOOGLE -> listOf("gemini-pro", "gemini-pro-vision", "gemini-1.5-pro")
            Provider.OLLAMA -> listOf("llama2", "codellama", "mistral", "neural-chat", "starcode")
            Provider.LLAMACPP -> listOf("local-model")
        }
    }
    
    /**
     * Test connection to a provider
     */
    fun testConnection(request: AIRequest): Boolean {
        return try {
            val testRequest = request.copy(prompt = "Hello", maxTokens = 10)
            createCompletion(testRequest)
            true
        } catch (e: Exception) {
            ZigzagLogger.warn("Connection test failed for ${request.provider}: ${e.message}")
            false
        }
    }
}
