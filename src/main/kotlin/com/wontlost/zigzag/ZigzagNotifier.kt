package com.wontlost.zigzag

import com.intellij.notification.NotificationGroupManager
import com.intellij.notification.NotificationType
import com.intellij.openapi.project.Project

object ZigzagNotifier {
    private val group = NotificationGroupManager.getInstance().getNotificationGroup("Zigzag Notifications")

    fun notifyError(project: Project?, message: String) {
        group.createNotification(message, NotificationType.ERROR).notify(project)
    }
}
