package com.wontlost.zigzag

import com.intellij.notification.*
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.options.ShowSettingsUtil
import com.intellij.openapi.project.Project

/**
 * Modern notification utility with enhanced features for IntelliJ 2025.1+
 */
object ZigzagNotifier {

    private val notificationGroup = NotificationGroupManager.getInstance()
        .getNotificationGroup("Zigzag Notifications")

    fun notifyError(project: Project?, message: String, throwable: Throwable? = null) {
        val notification = notificationGroup
            .createNotification("Zigzag Error", message, NotificationType.ERROR)
            .addAction(createSettingsAction())

        if (throwable != null) {
            notification.addAction(object : AnAction("Show Details") {
                override fun actionPerformed(e: AnActionEvent) {
                    ZigzagLogger.error("Error details", throwable)
                    // Could show a detailed error dialog here
                }
            })
        }

        notification.notify(project)
    }

    fun notifyInfo(project: Project?, message: String) {
        notificationGroup
            .createNotification("Zigzag", message, NotificationType.INFORMATION)
            .notify(project)
    }

    fun notifyWarning(project: Project?, message: String) {
        notificationGroup
            .createNotification("Zigzag Warning", message, NotificationType.WARNING)
            .addAction(createSettingsAction())
            .notify(project)
    }

    fun notifySuccess(project: Project?, message: String) {
        notificationGroup
            .createNotification("Zigzag", message, NotificationType.INFORMATION)
            .setIcon(com.intellij.icons.AllIcons.General.InspectionsOK)
            .notify(project)
    }

    fun notifyConfigurationRequired(project: Project?) {
        notificationGroup
            .createNotification(
                "Zigzag Configuration Required",
                "Please configure your OpenAI API key to use Zigzag features.",
                NotificationType.WARNING
            )
            .addAction(createSettingsAction())
            .setImportant(true)
            .notify(project)
    }

    private fun createSettingsAction(): AnAction {
        return object : AnAction("Open Settings") {
            override fun actionPerformed(e: AnActionEvent) {
                val project = e.project
                ShowSettingsUtil.getInstance().showSettingsDialog(
                    project,
                    OpenAISettingsConfigurable::class.java
                )
            }
        }
    }
}
