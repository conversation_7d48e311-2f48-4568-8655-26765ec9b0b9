package com.wontlost.zigzag

import com.intellij.openapi.project.Project
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * Centralized error handling for the Zigzag plugin.
 * Provides user-friendly error messages and appropriate recovery suggestions.
 */
object ErrorHandler {
    
    /**
     * Handle API-related errors and provide user-friendly messages
     */
    fun handleApiError(project: Project?, error: Throwable): String {
        ZigzagLogger.error("API error occurred", error)
        
        val userMessage = when (error) {
            is IllegalStateException -> {
                if (error.message?.contains("API key") == true) {
                    "🔑 API key not configured. Please set your OpenAI API key in Settings > Tools > Zigzag"
                } else {
                    "⚠️ Configuration error: ${error.message}"
                }
            }
            
            is ConnectException, is UnknownHostException -> {
                "🌐 Network connection failed. Please check your internet connection and try again."
            }
            
            is SocketTimeoutException -> {
                "⏱️ Request timed out. The OpenAI service might be slow. Try increasing the timeout in settings or try again later."
            }
            
            is IOException -> {
                when {
                    error.message?.contains("401") == true -> 
                        "🔐 Authentication failed. Please check your API key in Settings > Tools > Zigzag"
                    
                    error.message?.contains("429") == true -> 
                        "🚦 Rate limit exceeded. Please wait a moment before making more requests."
                    
                    error.message?.contains("400") == true -> 
                        "📝 Invalid request. Please check your input and try again."
                    
                    error.message?.contains("500") == true || 
                    error.message?.contains("502") == true || 
                    error.message?.contains("503") == true -> 
                        "🔧 OpenAI service is temporarily unavailable. Please try again in a few minutes."
                    
                    else -> "🌐 Network error: ${error.message}"
                }
            }
            
            else -> "❌ Unexpected error: ${error.message ?: "Unknown error"}"
        }
        
        // Show notification for critical errors
        if (shouldShowNotification(error)) {
            ZigzagNotifier.notifyError(project, userMessage)
        }
        
        return userMessage
    }
    
    /**
     * Handle completion-specific errors (less intrusive)
     */
    fun handleCompletionError(error: Throwable): String {
        ZigzagLogger.warn("Completion error occurred", error)
        
        return when (error) {
            is IllegalStateException -> {
                if (error.message?.contains("API key") == true) {
                    "API key not configured"
                } else {
                    "Configuration error"
                }
            }
            
            is IOException -> {
                when {
                    error.message?.contains("401") == true -> "Authentication failed"
                    error.message?.contains("429") == true -> "Rate limit exceeded"
                    error.message?.contains("timeout") == true -> "Request timed out"
                    else -> "Network error"
                }
            }
            
            else -> "Error: ${error.message ?: "Unknown"}"
        }
    }
    
    /**
     * Determine if an error should trigger a notification
     */
    private fun shouldShowNotification(error: Throwable): Boolean {
        return when (error) {
            is IllegalStateException -> true // Configuration issues
            is ConnectException, is UnknownHostException -> true // Network issues
            else -> false // Don't spam with notifications for rate limits, etc.
        }
    }
    
    /**
     * Create a recovery suggestion based on the error type
     */
    fun getRecoverySuggestion(error: Throwable): String? {
        return when (error) {
            is IllegalStateException -> {
                if (error.message?.contains("API key") == true) {
                    "Configure your OpenAI API key in Settings > Tools > Zigzag"
                } else null
            }
            
            is ConnectException, is UnknownHostException -> 
                "Check your internet connection and firewall settings"
            
            is SocketTimeoutException -> 
                "Increase the request timeout in plugin settings or try again later"
            
            is IOException -> {
                when {
                    error.message?.contains("401") == true -> 
                        "Verify your API key is correct and has sufficient permissions"
                    
                    error.message?.contains("429") == true -> 
                        "Wait a few minutes before making more requests, or upgrade your OpenAI plan"
                    
                    error.message?.contains("400") == true -> 
                        "Check your input for invalid characters or excessive length"
                    
                    else -> null
                }
            }
            
            else -> null
        }
    }
    
    /**
     * Log error with context information
     */
    fun logErrorWithContext(operation: String, error: Throwable, context: Map<String, Any> = emptyMap()) {
        val contextStr = context.entries.joinToString(", ") { "${it.key}=${it.value}" }
        ZigzagLogger.error("Error in $operation${if (contextStr.isNotEmpty()) " [$contextStr]" else ""}", error)
    }
}
