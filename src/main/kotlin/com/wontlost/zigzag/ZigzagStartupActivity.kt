package com.wontlost.zigzag

import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity

/**
 * Startup activity for initializing Zigzag plugin features when a project opens.
 * This replaces the deprecated StartupActivity interface with the modern ProjectActivity.
 */
class ZigzagStartupActivity : ProjectActivity {
    
    override suspend fun execute(project: Project) {
        // Initialize plugin components
        initializePlugin(project)
        
        // Check for configuration issues
        validateConfiguration()
        
        // Log startup
        ZigzagLogger.info("Zigzag plugin initialized for project: ${project.name}")
    }
    
    private fun initializePlugin(project: Project) {
        // Ensure settings component is initialized
        OpenAISettingsComponent.getInstance()
        
        // Initialize performance monitoring
        PerformanceMonitor.reset()
        
        // Clean up any stale cache entries
        cleanupResources()
    }
    
    private fun validateConfiguration() {
        try {
            val settings = OpenAISettingsComponent.getInstance()
            val validation = ConfigurationValidator.validateSettings(settings)
            
            if (!validation.isValid) {
                ZigzagLogger.warn("Configuration issues detected: ${validation.errors.joinToString(", ")}")
            }
            
            if (validation.warnings.isNotEmpty()) {
                ZigzagLogger.info("Configuration warnings: ${validation.warnings.joinToString(", ")}")
            }
        } catch (e: Exception) {
            ZigzagLogger.error("Failed to validate configuration", e)
        }
    }
    
    private fun cleanupResources() {
        // This could be expanded to clean up any resources from previous sessions
        ZigzagLogger.debug("Cleaned up plugin resources")
    }
}
