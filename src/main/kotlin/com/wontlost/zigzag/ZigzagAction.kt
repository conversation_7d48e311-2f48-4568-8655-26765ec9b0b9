package com.wontlost.zigzag

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.ui.Messages
import com.wontlost.zigzag.ZigzagNotifier

class ZigzagAction: AnAction() {
    override fun actionPerformed(event: AnActionEvent) {
        val client = AIClient()
        val result = try {
            val response = client.createCompletion("Say hello and confirm the Zigzag plugin is working correctly.")
            val content = response.choices?.firstOrNull()?.text?.trim() ?: "No response"
            "✅ Zigzag is working!\n\nAI Response: $content"
        } catch (e: Exception) {
            val errorMsg = ErrorHandler.handleApiError(event.project, e)
            "❌ Connection test failed\n\n$errorMsg"
        }
        Messages.showMessageDialog(event.project, result, "Zigzag Connection Test", Messages.getInformationIcon())
    }
}
