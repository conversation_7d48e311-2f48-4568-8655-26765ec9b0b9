package com.wontlost.zigzag

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.ui.Messages
import com.wontlost.zigzag.ZigzagNotifier

class ZigzagAction: AnAction() {
    override fun actionPerformed(event: AnActionEvent) {
        val client = CodexClient()
        val result = try {
            val response = client.createCompletion("// Hello World")
            response.choices?.firstOrNull()?.text?.trim() ?: "No response"
        } catch (e: Exception) {
            ZigzagNotifier.notifyError(event.project, e.message ?: "Unknown error")
            "Error: ${'$'}{e.message}"
        }
        Messages.showMessageDialog(event.project, result, "Zigzag", Messages.getInformationIcon())
    }
}
