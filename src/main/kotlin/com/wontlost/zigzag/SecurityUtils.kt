package com.wontlost.zigzag

import java.security.MessageDigest
import java.util.regex.Pattern

/**
 * Security utilities for handling sensitive data and preventing data leaks.
 */
object SecurityUtils {
    
    private val API_KEY_PATTERN = Pattern.compile("sk-[a-zA-Z0-9]{48}")
    private val SENSITIVE_PATTERNS = listOf(
        Pattern.compile("password", Pattern.CASE_INSENSITIVE),
        Pattern.compile("secret", Pattern.CASE_INSENSITIVE),
        Pattern.compile("token", Pattern.CASE_INSENSITIVE),
        Pattern.compile("key", Pattern.CASE_INSENSITIVE)
    )
    
    /**
     * Mask sensitive information in strings for logging
     */
    fun maskSensitiveData(input: String): String {
        var masked = input
        
        // Mask API keys
        masked = API_KEY_PATTERN.matcher(masked).replaceAll("sk-****")
        
        // Mask other sensitive patterns
        SENSITIVE_PATTERNS.forEach { pattern ->
            masked = pattern.matcher(masked).replaceAll { matchResult ->
                val match = matchResult.group()
                if (match.length <= 4) {
                    "*".repeat(match.length)
                } else {
                    match.substring(0, 2) + "*".repeat(match.length - 4) + match.substring(match.length - 2)
                }
            }
        }
        
        return masked
    }
    
    /**
     * Sanitize user input to prevent injection attacks
     */
    fun sanitizeInput(input: String): String {
        return input
            .replace(Regex("[<>\"'&]"), "") // Remove potentially dangerous characters
            .trim()
            .take(10000) // Limit length to prevent DoS
    }
    
    /**
     * Validate API key format without exposing the actual key
     */
    fun validateApiKeyFormat(apiKey: String?): Boolean {
        return apiKey != null && API_KEY_PATTERN.matcher(apiKey).matches()
    }
    
    /**
     * Create a hash of sensitive data for comparison without storing the actual value
     */
    fun hashSensitiveData(data: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(data.toByteArray())
        return hashBytes.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * Mask API key for display purposes
     */
    fun maskApiKey(apiKey: String?): String {
        return when {
            apiKey.isNullOrBlank() -> "Not configured"
            apiKey.length < 8 -> "*".repeat(apiKey.length)
            else -> "${apiKey.take(3)}${"*".repeat(apiKey.length - 6)}${apiKey.takeLast(3)}"
        }
    }
    
    /**
     * Check if a string contains potentially sensitive information
     */
    fun containsSensitiveData(input: String): Boolean {
        return API_KEY_PATTERN.matcher(input).find() || 
               SENSITIVE_PATTERNS.any { it.matcher(input).find() }
    }
    
    /**
     * Sanitize prompt for API calls to remove potentially sensitive information
     */
    fun sanitizePrompt(prompt: String): String {
        var sanitized = prompt
        
        // Remove potential API keys
        sanitized = API_KEY_PATTERN.matcher(sanitized).replaceAll("[API_KEY_REMOVED]")
        
        // Remove potential passwords or secrets
        sanitized = sanitized.replace(Regex("password\\s*[:=]\\s*\\S+", RegexOption.IGNORE_CASE), "password: [REMOVED]")
        sanitized = sanitized.replace(Regex("secret\\s*[:=]\\s*\\S+", RegexOption.IGNORE_CASE), "secret: [REMOVED]")
        sanitized = sanitized.replace(Regex("token\\s*[:=]\\s*\\S+", RegexOption.IGNORE_CASE), "token: [REMOVED]")
        
        return sanitized
    }
    
    /**
     * Generate a secure random string for testing purposes
     */
    fun generateTestApiKey(): String {
        val chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return "sk-" + (1..48).map { chars.random() }.joinToString("")
    }
    
    /**
     * Validate that user input doesn't contain malicious content
     */
    fun validateUserInput(input: String): ValidationResult {
        val issues = mutableListOf<String>()
        
        // Check for script injection attempts
        if (input.contains("<script", ignoreCase = true)) {
            issues.add("Script tags are not allowed")
        }
        
        // Check for SQL injection patterns
        val sqlPatterns = listOf("DROP TABLE", "DELETE FROM", "INSERT INTO", "UPDATE SET")
        if (sqlPatterns.any { input.contains(it, ignoreCase = true) }) {
            issues.add("SQL commands are not allowed")
        }
        
        // Check for excessive length
        if (input.length > 50000) {
            issues.add("Input is too long")
        }
        
        // Check for potential command injection
        val commandPatterns = listOf("rm -rf", "del /", "format c:", "sudo")
        if (commandPatterns.any { input.contains(it, ignoreCase = true) }) {
            issues.add("System commands are not allowed")
        }
        
        return ValidationResult(
            isValid = issues.isEmpty(),
            errors = issues
        )
    }
    
    data class ValidationResult(
        val isValid: Boolean,
        val errors: List<String>
    )
    
    /**
     * Secure string comparison to prevent timing attacks
     */
    fun secureEquals(a: String?, b: String?): Boolean {
        if (a == null || b == null) return a == b
        if (a.length != b.length) return false
        
        var result = 0
        for (i in a.indices) {
            result = result or (a[i].code xor b[i].code)
        }
        return result == 0
    }
}
