package com.wontlost.zigzag

import com.google.gson.Gson
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException

/**
 * Multi-provider LLM client supporting OpenAI, Meta, Google, and local models
 */
class MultiProviderClient {
    
    companion object {
        private val gson = Gson()
        private val httpClient = OkHttpClient.Builder()
            .connectTimeout(java.time.Duration.ofSeconds(30))
            .readTimeout(java.time.Duration.ofSeconds(60))
            .writeTimeout(java.time.Duration.ofSeconds(30))
            .build()
    }
    
    enum class Provider {
        OPENAI,
        META_LLAMA,
        GOOGLE_GEMINI,
        LOCAL_OLLAMA,
        LOCAL_LLAMACPP
    }
    
    data class ModelConfig(
        val provider: Provider,
        val modelName: String,
        val apiKey: String? = null,
        val baseUrl: String? = null,
        val maxTokens: Int = 1000,
        val temperature: Double = 0.7
    )
    
    data class ChatMessage(
        val role: String,
        val content: String
    )
    
    data class ChatResponse(
        val choices: List<Choice>?,
        val usage: Usage? = null
    ) {
        data class Choice(
            val message: ChatMessage?,
            val text: String? = null
        )
        
        data class Usage(
            val totalTokens: Int? = null,
            val promptTokens: Int? = null,
            val completionTokens: Int? = null
        )
    }
    
    suspend fun createCompletion(prompt: String, config: ModelConfig): ChatResponse {
        return when (config.provider) {
            Provider.OPENAI -> createOpenAICompletion(prompt, config)
            Provider.META_LLAMA -> createMetaCompletion(prompt, config)
            Provider.GOOGLE_GEMINI -> createGoogleCompletion(prompt, config)
            Provider.LOCAL_OLLAMA -> createOllamaCompletion(prompt, config)
            Provider.LOCAL_LLAMACPP -> createLlamaCppCompletion(prompt, config)
        }
    }
    
    private fun createOpenAICompletion(prompt: String, config: ModelConfig): ChatResponse {
        val url = config.baseUrl ?: "https://api.openai.com/v1/chat/completions"
        
        val payload = mapOf(
            "model" to config.modelName,
            "messages" to listOf(
                mapOf("role" to "user", "content" to prompt)
            ),
            "max_tokens" to config.maxTokens,
            "temperature" to config.temperature
        )
        
        val request = Request.Builder()
            .url(url)
            .header("Authorization", "Bearer ${config.apiKey}")
            .header("Content-Type", "application/json")
            .post(gson.toJson(payload).toRequestBody("application/json".toMediaType()))
            .build()
        
        return executeRequest(request)
    }
    
    private fun createMetaCompletion(prompt: String, config: ModelConfig): ChatResponse {
        // Meta Llama via Replicate or Together AI
        val url = config.baseUrl ?: "https://api.together.xyz/v1/chat/completions"
        
        val payload = mapOf(
            "model" to config.modelName, // e.g., "meta-llama/Llama-2-70b-chat-hf"
            "messages" to listOf(
                mapOf("role" to "user", "content" to prompt)
            ),
            "max_tokens" to config.maxTokens,
            "temperature" to config.temperature
        )
        
        val request = Request.Builder()
            .url(url)
            .header("Authorization", "Bearer ${config.apiKey}")
            .header("Content-Type", "application/json")
            .post(gson.toJson(payload).toRequestBody("application/json".toMediaType()))
            .build()
        
        return executeRequest(request)
    }
    
    private fun createGoogleCompletion(prompt: String, config: ModelConfig): ChatResponse {
        // Google Gemini API
        val url = config.baseUrl ?: "https://generativelanguage.googleapis.com/v1beta/models/${config.modelName}:generateContent"
        
        val payload = mapOf(
            "contents" to listOf(
                mapOf(
                    "parts" to listOf(
                        mapOf("text" to prompt)
                    )
                )
            ),
            "generationConfig" to mapOf(
                "maxOutputTokens" to config.maxTokens,
                "temperature" to config.temperature
            )
        )
        
        val request = Request.Builder()
            .url("$url?key=${config.apiKey}")
            .header("Content-Type", "application/json")
            .post(gson.toJson(payload).toRequestBody("application/json".toMediaType()))
            .build()
        
        return executeGoogleRequest(request)
    }
    
    private fun createOllamaCompletion(prompt: String, config: ModelConfig): ChatResponse {
        // Local Ollama instance
        val url = config.baseUrl ?: "http://localhost:11434/api/generate"
        
        val payload = mapOf(
            "model" to config.modelName,
            "prompt" to prompt,
            "stream" to false,
            "options" to mapOf(
                "num_predict" to config.maxTokens,
                "temperature" to config.temperature
            )
        )
        
        val request = Request.Builder()
            .url(url)
            .header("Content-Type", "application/json")
            .post(gson.toJson(payload).toRequestBody("application/json".toMediaType()))
            .build()
        
        return executeOllamaRequest(request)
    }
    
    private fun createLlamaCppCompletion(prompt: String, config: ModelConfig): ChatResponse {
        // Local llama.cpp server
        val url = config.baseUrl ?: "http://localhost:8080/completion"
        
        val payload = mapOf(
            "prompt" to prompt,
            "n_predict" to config.maxTokens,
            "temperature" to config.temperature,
            "stop" to listOf("</s>", "Human:", "Assistant:")
        )
        
        val request = Request.Builder()
            .url(url)
            .header("Content-Type", "application/json")
            .post(gson.toJson(payload).toRequestBody("application/json".toMediaType()))
            .build()
        
        return executeLlamaCppRequest(request)
    }
    
    private fun executeRequest(request: Request): ChatResponse {
        val response = httpClient.newCall(request).execute()
        
        if (!response.isSuccessful) {
            throw IOException("API request failed with status ${response.code}: ${response.body?.string()}")
        }
        
        val responseBody = response.body?.string() ?: throw IOException("Empty response body")
        return gson.fromJson(responseBody, ChatResponse::class.java)
    }

    @Suppress("UNCHECKED_CAST")
    private fun executeGoogleRequest(request: Request): ChatResponse {
        val response = httpClient.newCall(request).execute()
        
        if (!response.isSuccessful) {
            throw IOException("Google API request failed with status ${response.code}: ${response.body?.string()}")
        }
        
        val responseBody = response.body?.string() ?: throw IOException("Empty response body")
        val googleResponse = gson.fromJson(responseBody, Map::class.java)
        
        // Convert Google response format to our standard format
        val candidates = googleResponse["candidates"] as? List<Map<String, Any>>
        val content = candidates?.firstOrNull()?.get("content") as? Map<String, Any>
        val parts = content?.get("parts") as? List<Map<String, Any>>
        val text = parts?.firstOrNull()?.get("text") as? String ?: ""
        
        return ChatResponse(
            choices = listOf(ChatResponse.Choice(
                message = ChatMessage("assistant", text),
                text = text
            ))
        )
    }
    
    private fun executeOllamaRequest(request: Request): ChatResponse {
        val response = httpClient.newCall(request).execute()
        
        if (!response.isSuccessful) {
            throw IOException("Ollama request failed with status ${response.code}: ${response.body?.string()}")
        }
        
        val responseBody = response.body?.string() ?: throw IOException("Empty response body")
        val ollamaResponse = gson.fromJson(responseBody, Map::class.java)
        
        val text = ollamaResponse["response"] as? String ?: ""
        
        return ChatResponse(
            choices = listOf(ChatResponse.Choice(
                message = ChatMessage("assistant", text),
                text = text
            ))
        )
    }
    
    private fun executeLlamaCppRequest(request: Request): ChatResponse {
        val response = httpClient.newCall(request).execute()
        
        if (!response.isSuccessful) {
            throw IOException("Llama.cpp request failed with status ${response.code}: ${response.body?.string()}")
        }
        
        val responseBody = response.body?.string() ?: throw IOException("Empty response body")
        val llamaResponse = gson.fromJson(responseBody, Map::class.java)
        
        val text = llamaResponse["content"] as? String ?: ""
        
        return ChatResponse(
            choices = listOf(ChatResponse.Choice(
                message = ChatMessage("assistant", text),
                text = text
            ))
        )
    }
}
