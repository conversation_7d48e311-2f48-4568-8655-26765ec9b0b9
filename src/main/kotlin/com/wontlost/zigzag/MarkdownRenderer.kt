package com.wontlost.zigzag

import org.commonmark.Extension
import org.commonmark.ext.gfm.tables.TablesExtension
import org.commonmark.ext.heading.anchor.HeadingAnchorExtension
import org.commonmark.parser.Parser
import org.commonmark.renderer.html.HtmlRenderer
import java.awt.Color
import java.awt.Font
import javax.swing.JEditorPane
import javax.swing.text.html.HTMLEditorKit
import javax.swing.text.html.StyleSheet

/**
 * Enhanced markdown renderer for AI responses with syntax highlighting and proper styling
 */
object MarkdownRenderer {
    
    private val parser: Parser
    private val renderer: HtmlRenderer
    
    init {
        val extensions: List<Extension> = listOf(
            TablesExtension.create(),
            HeadingAnchorExtension.create()
        )
        
        parser = Parser.builder()
            .extensions(extensions)
            .build()
            
        renderer = HtmlRenderer.builder()
            .extensions(extensions)
            .build()
    }
    
    /**
     * Create a JEditorPane with rendered markdown content
     */
    fun createMarkdownPane(markdownText: String): JEditorPane {
        val document = parser.parse(markdownText)
        val html = renderer.render(document)
        
        val editorPane = JEditorPane()
        editorPane.contentType = "text/html"
        editorPane.isEditable = false
        editorPane.isOpaque = false
        
        // Set up HTML editor kit with custom styles
        val kit = HTMLEditorKit()
        val styleSheet = createStyleSheet()
        kit.styleSheet = styleSheet
        editorPane.editorKit = kit
        
        // Wrap in proper HTML structure with styling
        val styledHtml = """
            <html>
            <head>
                <style>
                    body { font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace; font-size: 13px; color: #333; margin: 8px; }
                    pre { background-color: #f5f5f5; border: 1px solid #ddd; padding: 12px; margin: 8px 0; font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace; font-size: 12px; }
                    code { background-color: #f0f0f0; padding: 2px 4px; font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace; font-size: 12px; }
                    h1, h2, h3, h4, h5, h6 { color: #2c3e50; margin-top: 16px; margin-bottom: 8px; }
                    h1 { font-size: 18px; font-weight: bold; }
                    h2 { font-size: 16px; font-weight: bold; }
                    h3 { font-size: 14px; font-weight: bold; }
                    blockquote { border-left: 4px solid #ddd; margin: 8px 0; padding-left: 12px; color: #666; }
                    ul, ol { margin: 8px 0; padding-left: 20px; }
                    li { margin: 2px 0; }
                    table { border-collapse: collapse; width: 100%; margin: 8px 0; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f5f5f5; font-weight: bold; }
                    a { color: #3498db; text-decoration: none; }
                    a:hover { text-decoration: underline; }
                    p { margin: 8px 0; }
                </style>
            </head>
            <body>
                $html
            </body>
            </html>
        """.trimIndent()
        
        editorPane.text = styledHtml
        
        return editorPane
    }

    /**
     * Render markdown to raw HTML for JCEF based views
     */
    fun renderMarkdownToHtml(markdownText: String): String {
        val document = parser.parse(markdownText)
        return renderer.render(document)
    }

    private fun createStyleSheet(): StyleSheet {
        val styleSheet = StyleSheet()
        styleSheet.addRule("body { font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace; font-size: 13px; color: #333; margin: 8px; }")
        styleSheet.addRule("pre { background-color: #f5f5f5; border: 1px solid #ddd; padding: 12px; margin: 8px 0; font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace; font-size: 12px; }")
        styleSheet.addRule("code { background-color: #f0f0f0; padding: 2px 4px; font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace; font-size: 12px; }")
        styleSheet.addRule("h1, h2, h3, h4, h5, h6 { color: #2c3e50; margin-top: 16px; margin-bottom: 8px; }")
        styleSheet.addRule("h1 { font-size: 18px; font-weight: bold; }")
        styleSheet.addRule("h2 { font-size: 16px; font-weight: bold; }")
        styleSheet.addRule("h3 { font-size: 14px; font-weight: bold; }")
        styleSheet.addRule("blockquote { border-left: 4px solid #ddd; margin: 8px 0; padding-left: 12px; color: #666; }")
        styleSheet.addRule("ul, ol { margin: 8px 0; padding-left: 20px; }")
        styleSheet.addRule("li { margin: 2px 0; }")
        styleSheet.addRule("table { border-collapse: collapse; width: 100%; margin: 8px 0; }")
        styleSheet.addRule("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }")
        styleSheet.addRule("th { background-color: #f5f5f5; font-weight: bold; }")
        styleSheet.addRule("a { color: #3498db; text-decoration: none; }")
        styleSheet.addRule("a:hover { text-decoration: underline; }")
        styleSheet.addRule("p { margin: 8px 0; }")
        return styleSheet
    }
    
    /**
     * Extract plain text from markdown for copying
     */
    fun extractPlainText(markdownText: String): String {
        // Simple extraction - remove markdown formatting
        return markdownText
            .replace(Regex("```[\\s\\S]*?```"), "") // Remove code blocks
            .replace(Regex("`([^`]+)`"), "$1") // Remove inline code formatting
            .replace(Regex("\\*\\*([^*]+)\\*\\*"), "$1") // Remove bold
            .replace(Regex("\\*([^*]+)\\*"), "$1") // Remove italic
            .replace(Regex("#{1,6}\\s+"), "") // Remove headers
            .replace(Regex("\\[([^]]+)\\]\\([^)]+\\)"), "$1") // Remove links, keep text
            .trim()
    }
    
    /**
     * Extract code blocks from markdown
     */
    fun extractCodeBlocks(markdownText: String): List<String> {
        val codeBlocks = mutableListOf<String>()
        val codeBlockRegex = Regex("```(?:[a-zA-Z]*\\n)?([\\s\\S]*?)```")
        
        codeBlockRegex.findAll(markdownText).forEach { match ->
            val code = match.groupValues[1].trim()
            if (code.isNotEmpty()) {
                codeBlocks.add(code)
            }
        }
        
        return codeBlocks
    }
    
    /**
     * Check if text contains code blocks
     */
    fun hasCodeBlocks(text: String): Boolean {
        return text.contains("```")
    }
    
    /**
     * Format text for better display
     */
    fun formatText(text: String): String {
        return text
            .replace("\n\n", "\n") // Reduce excessive line breaks
            .replace(Regex("\\n{3,}"), "\n\n") // Limit consecutive line breaks
            .trim()
    }
}
