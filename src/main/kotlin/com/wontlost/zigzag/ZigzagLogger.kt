package com.wontlost.zigzag

import com.intellij.openapi.diagnostic.Logger

/**
 * Centralized logging utility for the Zigzag plugin.
 * Provides consistent logging across all components with configurable levels.
 */
object ZigzagLogger {
    private val logger = Logger.getInstance("Zigzag")
    
    fun debug(message: String, throwable: Throwable? = null) {
        if (isDebugEnabled()) {
            if (throwable != null) {
                logger.debug(message, throwable)
            } else {
                logger.debug(message)
            }
        }
    }
    
    fun info(message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            logger.info(message, throwable)
        } else {
            logger.info(message)
        }
    }
    
    fun warn(message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            logger.warn(message, throwable)
        } else {
            logger.warn(message)
        }
    }
    
    fun error(message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            logger.error(message, throwable)
        } else {
            logger.error(message)
        }
    }
    
    private fun isDebugEnabled(): Boolean {
        return try {
            OpenAISettingsComponent.getInstance().enableLogging
        } catch (e: Exception) {
            // Fallback to false if settings are not available
            false
        }
    }
    
    /**
     * Log API request details (only if debug logging is enabled)
     */
    fun logApiRequest(endpoint: String, prompt: String) {
        if (isDebugEnabled()) {
            debug("API Request to $endpoint with prompt length: ${prompt.length}")
            debug("Prompt preview: ${prompt.take(100)}${if (prompt.length > 100) "..." else ""}")
        }
    }
    
    /**
     * Log API response details (only if debug logging is enabled)
     */
    fun logApiResponse(statusCode: Int, responseLength: Int, tokensUsed: Int? = null) {
        if (isDebugEnabled()) {
            debug("API Response: status=$statusCode, length=$responseLength${tokensUsed?.let { ", tokens=$it" } ?: ""}")
        }
    }
    
    /**
     * Log performance metrics
     */
    fun logPerformance(operation: String, durationMs: Long) {
        if (isDebugEnabled()) {
            debug("Performance: $operation took ${durationMs}ms")
        }
    }
}
