package com.wontlost.zigzag

import com.intellij.codeInsight.intention.IntentionAction
import com.intellij.codeInsight.intention.PriorityAction
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiFile
import com.intellij.util.IncorrectOperationException

/**
 * Modern intention action for IntelliJ 2025.1+ that provides AI-powered code suggestions.
 * This replaces context menu actions with more integrated IDE experience.
 */
class ZigzagIntentionAction : IntentionAction, PriorityAction {
    
    override fun getText(): String = "Generate code with AI"
    
    override fun getFamilyName(): String = "Zigzag AI Assistant"
    
    override fun isAvailable(project: Project, editor: Editor?, file: PsiFile?): Boolean {
        if (editor == null || file == null) return false
        
        // Check if API key is configured
        val settings = OpenAISettingsComponent.getInstance()
        if (settings.apiKey.isNullOrBlank()) return false
        
        // Check if there's selected text or we're in a comment
        val selectionModel = editor.selectionModel
        if (selectionModel.hasSelection()) return true
        
        // Check if current line contains a comment
        val document = editor.document
        val caretModel = editor.caretModel
        val lineNumber = document.getLineNumber(caretModel.offset)
        val lineStartOffset = document.getLineStartOffset(lineNumber)
        val lineEndOffset = document.getLineEndOffset(lineNumber)
        val lineText = document.getText(com.intellij.openapi.util.TextRange(lineStartOffset, lineEndOffset))
        
        return lineText.trim().startsWith("//") || lineText.trim().startsWith("/*")
    }
    
    override fun invoke(project: Project, editor: Editor?, file: PsiFile?) {
        if (editor == null || file == null) return
        
        val selectionModel = editor.selectionModel
        val document = editor.document
        
        val prompt = if (selectionModel.hasSelection()) {
            "Improve this code:\n${selectionModel.selectedText}"
        } else {
            // Get current line (assuming it's a comment)
            val caretModel = editor.caretModel
            val lineNumber = document.getLineNumber(caretModel.offset)
            val lineStartOffset = document.getLineStartOffset(lineNumber)
            val lineEndOffset = document.getLineEndOffset(lineNumber)
            val lineText = document.getText(com.intellij.openapi.util.TextRange(lineStartOffset, lineEndOffset))
            "Generate code for: ${lineText.trim().removePrefix("//").removePrefix("/*").removeSuffix("*/").trim()}"
        }
        
        ApplicationManager.getApplication().executeOnPooledThread {
            val result = try {
                val client = AIClient()
                val response = client.createCompletion(prompt)
                response.choices?.firstOrNull()?.text?.trim() ?: "No response generated"
            } catch (e: Exception) {
                ErrorHandler.handleApiError(project, e)
                return@executeOnPooledThread
            }
            
            ApplicationManager.getApplication().invokeLater {
                WriteCommandAction.runWriteCommandAction(project) {
                    if (selectionModel.hasSelection()) {
                        // Replace selected text
                        document.replaceString(
                            selectionModel.selectionStart,
                            selectionModel.selectionEnd,
                            result
                        )
                    } else {
                        // Insert after current line
                        val caretModel = editor.caretModel
                        val lineNumber = document.getLineNumber(caretModel.offset)
                        val lineEndOffset = document.getLineEndOffset(lineNumber)
                        document.insertString(lineEndOffset, "\n$result")
                    }
                }
            }
        }
    }
    
    override fun startInWriteAction(): Boolean = false

    override fun getPriority(): PriorityAction.Priority = PriorityAction.Priority.HIGH
}
