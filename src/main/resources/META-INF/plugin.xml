<idea-plugin>
    <id>com.wontlost.zigzag</id>
    <name>Zigzag</name>
    <version>0.0.1</version>
    <vendor email="<EMAIL>" url="https://wontlost.com">WontLost Ltd</vendor>

    <description><![CDATA[
        AI-powered coding assistant powered by OpenAI Codex.
    ]]></description>

    <idea-version since-build="241" />
    <depends>com.intellij.modules.platform</depends>

    <extensions defaultExtensionNs="com.intellij">
        <notificationGroup id="Zigzag Notifications" displayType="BALLOON" />
        <completion.contributor language="ANY" implementationClass="com.wontlost.zigzag.CodexCompletionContributor"/>
        <toolWindow id="Codex Assistant"
                    anchor="right"
                    factoryClass="com.wontlost.zigzag.CodexAssistantToolWindowFactory"/>
        <applicationConfigurable id="com.wontlost.zigzag.settings"
                                 displayName="Zigzag"
                                 instance="com.wontlost.zigzag.OpenAISettingsConfigurable"/>
    </extensions>

    <actions>
        <action id="com.wontlost.zigzag.ZigzagAction" class="com.wontlost.zigzag.ZigzagAction" text="Say Hello" description="Displays a greeting">
            <add-to-group group-id="ToolsMenu" anchor="last"/>
        </action>
        <action id="com.wontlost.zigzag.CodexContextAction" class="com.wontlost.zigzag.CodexContextAction" text="Codex Assist" description="Generate or refactor with Codex">
            <add-to-group group-id="EditorPopupMenu" anchor="last"/>
        </action>
    </actions>
</idea-plugin>
