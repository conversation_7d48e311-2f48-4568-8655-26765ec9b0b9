package com.wontlost.zigzag;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.intellij.openapi.diagnostic.Logger;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * HTTP client for interacting with the OpenAI Chat Completions API.
 * Includes rate limiting, caching, and comprehensive error handling.
 */
public class AIClient {
    private static final Logger LOG = Logger.getInstance(AIClient.class);
    private static final URI CHAT_COMPLETIONS_URI = URI.create("https://api.openai.com/v1/chat/completions");
    private static final int MAX_CACHE_SIZE = 1000;
    private static final Duration CACHE_TTL = Duration.ofMinutes(30);
    private static final Duration REQUEST_TIMEOUT = Duration.ofSeconds(30);

    // Rate limiting: 60 requests per minute for most OpenAI plans
    private static final int MAX_REQUESTS_PER_MINUTE = 60;
    private static final AtomicLong requestCount = new AtomicLong(0);
    private static volatile Instant windowStart = Instant.now();

    private final HttpClient httpClient;
    private final Gson gson = new Gson();
    private static final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();

    public AIClient() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
    }

    private static class CacheEntry {
        final ChatCompletionResponse response;
        final Instant timestamp;

        CacheEntry(ChatCompletionResponse response) {
            this.response = response;
            this.timestamp = Instant.now();
        }

        boolean isExpired() {
            return Instant.now().isAfter(timestamp.plus(CACHE_TTL));
        }
    }

    private String resolveApiKey() {
        // First check environment variable
        String env = System.getenv("OPENAI_API_KEY");
        if (env != null && !env.isBlank()) {
            return env;
        }

        // Then check settings
        OpenAISettingsComponent settings = OpenAISettingsComponent.getInstance();
        if (settings != null && settings.getApiKey() != null && !settings.getApiKey().isBlank()) {
            return settings.getApiKey();
        }

        throw new IllegalStateException("OpenAI API key not configured. Please set it in Settings > Tools > Zigzag or as OPENAI_API_KEY environment variable.");
    }

    private void checkRateLimit() throws IOException {
        Instant now = Instant.now();

        // Reset window if a minute has passed
        if (now.isAfter(windowStart.plus(Duration.ofMinutes(1)))) {
            windowStart = now;
            requestCount.set(0);
        }

        if (requestCount.incrementAndGet() > MAX_REQUESTS_PER_MINUTE) {
            throw new IOException("Rate limit exceeded. Please wait before making more requests.");
        }
    }

    private String sanitizePrompt(String prompt) {
        if (prompt == null) {
            throw new IllegalArgumentException("Prompt cannot be null");
        }

        // Trim and validate input
        String sanitized = prompt.trim();
        if (sanitized.isEmpty()) {
            throw new IllegalArgumentException("Prompt cannot be empty");
        }

        // Security validation
        SecurityUtils.ValidationResult validation = SecurityUtils.INSTANCE.validateUserInput(sanitized);
        if (!validation.isValid()) {
            throw new IllegalArgumentException("Invalid input: " + String.join(", ", validation.getErrors()));
        }

        // Sanitize for security
        sanitized = SecurityUtils.INSTANCE.sanitizePrompt(sanitized);
        sanitized = SecurityUtils.INSTANCE.sanitizeInput(sanitized);

        // Limit prompt length (OpenAI has token limits)
        if (sanitized.length() > 8000) {
            sanitized = sanitized.substring(0, 8000) + "...";
            LOG.warn("Prompt truncated to 8000 characters");
        }

        return sanitized;
    }

    /**
     * Send a completion request to the OpenAI Chat Completions API.
     * Uses the modern chat completions endpoint with GPT models.
     */
    public ChatCompletionResponse createCompletion(String prompt) throws IOException, InterruptedException {
        String sanitizedPrompt = sanitizePrompt(prompt);

        // Check cache first
        CacheEntry cached = cache.get(sanitizedPrompt);
        if (cached != null && !cached.isExpired()) {
            LOG.debug("Returning cached response for prompt");
            return cached.response;
        }

        // Clean up expired cache entries
        cleanupCache();

        // Check rate limit
        checkRateLimit();

        OpenAISettingsComponent settings = OpenAISettingsComponent.getInstance();
        ChatCompletionRequest payload = new ChatCompletionRequest(sanitizedPrompt, settings.getTemperature(), settings.getMaxTokens());
        String body = gson.toJson(payload);

        ZigzagLogger.INSTANCE.logApiRequest(CHAT_COMPLETIONS_URI.toString(), sanitizedPrompt);

        HttpRequest request = HttpRequest.newBuilder()
                .uri(CHAT_COMPLETIONS_URI)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + resolveApiKey())
                .header("User-Agent", "Zigzag-IntelliJ-Plugin/1.0")
                .timeout(REQUEST_TIMEOUT)
                .POST(HttpRequest.BodyPublishers.ofString(body))
                .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() != 200) {
            String errorMsg = extractError(response.body());
            LOG.warn("API request failed with status " + response.statusCode() + ": " + errorMsg);

            switch (response.statusCode()) {
                case 401:
                    throw new IOException("Authentication failed. Please check your API key: " + errorMsg);
                case 429:
                    throw new IOException("Rate limit exceeded. Please wait before making more requests: " + errorMsg);
                case 400:
                    throw new IOException("Bad request. Please check your input: " + errorMsg);
                case 500:
                case 502:
                case 503:
                    throw new IOException("OpenAI service temporarily unavailable. Please try again later: " + errorMsg);
                default:
                    throw new IOException("API error " + response.statusCode() + ": " + errorMsg);
            }
        }

        ChatCompletionResponse parsed = gson.fromJson(response.body(), ChatCompletionResponse.class);

        // Log response details
        int tokensUsed = parsed.usage != null ? parsed.usage.totalTokens : 0;
        ZigzagLogger.INSTANCE.logApiResponse(response.statusCode(), response.body().length(), tokensUsed);

        // Cache the response
        if (cache.size() < MAX_CACHE_SIZE) {
            cache.put(sanitizedPrompt, new CacheEntry(parsed));
        }

        return parsed;
    }

    private void cleanupCache() {
        cache.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }

    private String extractError(String body) {
        try {
            ErrorResponse err = gson.fromJson(body, ErrorResponse.class);
            if (err != null && err.error != null && err.error.message != null) {
                return err.error.message;
            }
        } catch (Exception e) {
            LOG.warn("Failed to parse error response", e);
        }
        return body != null && !body.isEmpty() ? body : "Unknown error";
    }

    private static class ErrorResponse {
        ErrorDetail error;

        static class ErrorDetail {
            String message;
            String type;
            String code;
        }
    }

    /** JSON payload for the chat completions request. */
    private static class ChatCompletionRequest {
        String model = "gpt-3.5-turbo";
        List<Message> messages;
        int max_tokens = 150;
        double temperature = 0.7;
        boolean stream = false;

        ChatCompletionRequest(String prompt, double temperature, int maxTokens) {
            this.messages = List.of(
                new Message("system", "You are a helpful coding assistant. Provide concise, accurate code suggestions and explanations."),
                new Message("user", prompt)
            );
            this.temperature = Math.max(0.0, Math.min(1.0, temperature)); // Clamp between 0 and 1
            this.max_tokens = Math.max(1, Math.min(4000, maxTokens)); // Reasonable limits
        }

        static class Message {
            String role;
            String content;

            Message(String role, String content) {
                this.role = role;
                this.content = content;
            }
        }
    }

    /** Response from the chat completions API. */
    public static class ChatCompletionResponse {
        @SerializedName("id")
        public String id;
        @SerializedName("object")
        public String object;
        @SerializedName("created")
        public long created;
        @SerializedName("model")
        public String model;
        @SerializedName("choices")
        public Choice[] choices;
        @SerializedName("usage")
        public Usage usage;

        public static class Choice {
            @SerializedName("index")
            public int index;
            @SerializedName("message")
            public Message message;
            @SerializedName("finish_reason")
            public String finishReason;

            public static class Message {
                @SerializedName("role")
                public String role;
                @SerializedName("content")
                public String content;
            }

            // For backward compatibility with old AIResponse
            public String getText() {
                return message != null ? message.content : null;
            }
        }

        public static class Usage {
            @SerializedName("prompt_tokens")
            public int promptTokens;
            @SerializedName("completion_tokens")
            public int completionTokens;
            @SerializedName("total_tokens")
            public int totalTokens;
        }
    }

    // Backward compatibility alias
    public static class AIResponse extends ChatCompletionResponse {
        // This allows existing code to continue working
    }
}
