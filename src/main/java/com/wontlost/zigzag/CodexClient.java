package com.wontlost.zigzag;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 * Simple HTTP client for interacting with the OpenAI Codex API.
 */
public class CodexClient {
    private static final URI CODEX_URI = URI.create("https://api.openai.com/v1/completions");
    private final HttpClient httpClient = HttpClient.newHttpClient();
    private final Gson gson = new Gson();
    private static final Map<String, CodexResponse> cache = new ConcurrentHashMap<>();

    private String resolveApiKey() {
        String env = System.getenv("OPENAI_API_KEY");
        if (env != null && !env.isBlank()) {
            return env;
        }
        OpenAISettingsComponent settings = OpenAISettingsComponent.getInstance();
        if (settings != null && settings.getApiKey() != null && !settings.getApiKey().isBlank()) {
            return settings.getApiKey();
        }
        throw new IllegalStateException("OpenAI API key not configured");
    }

    /**
     * Send a completion request to the Codex API.
     */
    public CodexResponse createCompletion(String prompt) throws IOException, InterruptedException {
        if (cache.containsKey(prompt)) {
            return cache.get(prompt);
        }

        OpenAISettingsComponent settings = OpenAISettingsComponent.getInstance();
        CodexRequest payload = new CodexRequest(prompt, settings.getTemperature(), settings.getMaxTokens());
        String body = gson.toJson(payload);

        HttpRequest request = HttpRequest.newBuilder()
                .uri(CODEX_URI)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + resolveApiKey())
                .POST(HttpRequest.BodyPublishers.ofString(body))
                .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() != 200) {
            String msg = extractError(response.body());
            if (response.statusCode() == 401) {
                throw new IOException("Authentication failed: " + msg);
            } else if (response.statusCode() == 429) {
                throw new IOException("Rate limit exceeded: " + msg);
            } else {
                throw new IOException("API error " + response.statusCode() + ": " + msg);
            }
        }

        CodexResponse parsed = gson.fromJson(response.body(), CodexResponse.class);
        cache.put(prompt, parsed);
        return parsed;
    }

    private String extractError(String body) {
        try {
            ErrorResponse err = gson.fromJson(body, ErrorResponse.class);
            if (err != null && err.error != null && err.error.message != null) {
                return err.error.message;
            }
        } catch (Exception ignored) {}
        return body;
    }

    private static class ErrorResponse {
        ErrorDetail error;

        static class ErrorDetail {
            String message;
        }
    }

    /** JSON payload for the request. */
    private static class CodexRequest {
        String model = "code-davinci-002";
        String prompt;
        int max_tokens = 100;
        double temperature = 0.5;

        CodexRequest(String prompt, double temperature, int maxTokens) {
            this.prompt = prompt;
            this.temperature = temperature;
            this.max_tokens = maxTokens;
        }
    }

    /** Basic representation of the API response. */
    public static class CodexResponse {
        @SerializedName("id")
        public String id;
        @SerializedName("choices")
        public Choice[] choices;

        public static class Choice {
            @SerializedName("text")
            public String text;
        }
    }
}
