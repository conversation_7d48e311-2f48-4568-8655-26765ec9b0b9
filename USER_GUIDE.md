# Zigzag User Guide

## 🚀 Getting Started with Enhanced Features

Welcome to <PERSON><PERSON><PERSON><PERSON>'s enhanced AI assistant! This guide will help you make the most of the new interactive features and beautiful interface.

---

## 💬 Using the Enhanced AI Assistant Panel

### Opening the Assistant
1. **Tool Window**: Look for "Zigzag Assistant" in your tool windows (usually on the right side)
2. **Menu Access**: Go to **View | Tool Windows | Zigzag Assistant**
3. **Quick Access**: Use **Alt+9** (default shortcut) to toggle the panel

### Chat Interface Features

#### **Starting a Conversation**
- Type your question in the input field at the bottom
- Press **Enter** or click the **Send** button
- Watch for the loading indicator while AI processes your request

#### **Message Types**
- **Your Messages**: Displayed in blue bubbles on the right
- **AI Responses**: Displayed in white cards with syntax highlighting
- **Welcome Message**: Helpful introduction when you first open the panel

#### **Interactive Actions**
Each AI response includes action buttons:

**📋 Copy Button**
- Copies the entire response to your clipboard
- Perfect for sharing code or saving snippets
- Shows confirmation notification

**⬇️ Insert Button**
- Inserts code directly into your active editor
- Automatically positions at your cursor location
- Supports undo/redo operations
- Works with all file types

**🔄 Redo Button**
- Re-runs the last request to refine the answer
- Use the arrow to redo the whole conversation instead
- Toggle modes to control whether only the current message or all messages are resent

#### **Managing Conversations**
- **Clear Chat**: Click the 🗑️ button in the toolbar to reset
- **Auto-scroll**: Panel automatically scrolls to show latest messages
- **Resize Panel**: Drag panel borders to adjust size

### Best Practices for Chat

#### **Effective Prompting**
```
Good: "Create a Java method that validates email addresses using regex"
Better: "Create a Java method that validates email addresses using regex, include error handling and unit tests"
```

#### **Context Sharing**
- Mention the programming language you're using
- Include relevant framework or library information
- Provide sample input/output when helpful

#### **Follow-up Questions**
- Ask for explanations: "Can you explain how this regex works?"
- Request modifications: "Make this method more efficient"
- Seek alternatives: "Show me a different approach"

---

## ⚙️ Using the Beautiful Settings Interface

### Accessing Settings
1. Go to **File | Settings** (or **IntelliJ IDEA | Preferences** on macOS)
2. Navigate to **Tools | Zigzag**
3. Enjoy the new card-based layout!

### Settings Sections

#### **🔑 API Configuration**
**API Key Field**
- Secure password field (characters are hidden)
- Paste your OpenAI API key here
- Get your key from [OpenAI Platform](https://platform.openai.com/api-keys)

**Test Connection Button**
- Click to verify your API key works
- Shows success/failure dialog with details
- Helps troubleshoot connection issues

**Quick Links**
- Direct link to OpenAI platform
- Easy access to documentation

#### **⚙️ Model & Performance Settings**
**Model Selection**
- **gpt-3.5-turbo**: Fast and cost-effective for most tasks
- **gpt-4**: More capable but more expensive
- **gpt-4o**: Latest model with best performance
- **gpt-4o-mini**: Balanced option between cost and capability

**Temperature Control**
- **0.0-0.3**: Focused, deterministic responses
- **0.4-0.7**: Balanced creativity and consistency
- **0.8-2.0**: More creative and varied responses

**Max Tokens**
- Controls response length
- Higher values = longer responses = higher cost
- Recommended: 150-300 for most use cases

**Request Timeout**
- How long to wait for API responses
- Recommended: 30-60 seconds
- Increase if you experience timeouts

#### **🚀 Features**
**Inline Completions**
- Shows AI suggestions while you type
- Toggle on/off based on preference
- Works in all supported file types

**Chat Panel**
- Enables/disables the assistant tool window
- Useful if you only want inline completions

**Debug Logging**
- Enables detailed logging for troubleshooting
- Only enable when investigating issues
- May contain sensitive data

#### **💰 Cost Estimation**
**Real-time Calculation**
- Updates as you change settings
- Shows estimated cost per request
- Helps you optimize for budget

**Warning Indicators**
- 🟡 Yellow warning for moderate costs
- 🔴 Red warning for high costs
- 💡 Tips for reducing costs

### Optimizing Settings

#### **For Cost Efficiency**
```
Model: gpt-3.5-turbo
Temperature: 0.3
Max Tokens: 150
Estimated cost: ~$0.0003 per request
```

#### **For Best Quality**
```
Model: gpt-4o
Temperature: 0.7
Max Tokens: 500
Estimated cost: ~$0.0075 per request
```

#### **For Creative Tasks**
```
Model: gpt-4
Temperature: 1.2
Max Tokens: 800
Estimated cost: ~$0.024 per request
```

---

## 🎯 Workflow Examples

### Example 1: Code Generation
1. **Open Chat Panel**: Click on Zigzag Assistant
2. **Ask for Code**: "Create a REST API endpoint in Spring Boot for user management"
3. **Review Response**: AI provides complete code with explanations
4. **Insert Code**: Click the ⬇️ button to insert into your editor
5. **Refine**: Ask follow-up questions to improve the code

### Example 2: Code Explanation
1. **Select Code**: Highlight complex code in your editor
2. **Use Context Action**: Right-click and choose "AI Assist"
3. **Ask for Explanation**: "Explain how this algorithm works"
4. **Learn**: Read the detailed explanation in the chat panel
5. **Copy Notes**: Use 📋 to copy explanation for documentation

### Example 3: Debugging Help
1. **Copy Error**: Copy error message from console
2. **Ask in Chat**: "Help me fix this error: [paste error]"
3. **Provide Context**: Share relevant code snippet
4. **Apply Solution**: Use the suggested fix
5. **Test**: Verify the solution works

### Example 4: Code Review
1. **Select Code**: Highlight code you want reviewed
2. **Ask for Review**: "Review this code for best practices and potential issues"
3. **Get Feedback**: Receive detailed analysis
4. **Apply Suggestions**: Implement recommended improvements
5. **Learn**: Understand the reasoning behind suggestions

---

## 🔧 Troubleshooting

### Common Issues

**Chat Panel Not Responding**
- Check your internet connection
- Verify API key in settings
- Use "Test Connection" button
- Check if you have API credits

**Code Not Inserting**
- Ensure you have an active editor open
- Position cursor where you want code inserted
- Check file is not read-only
- Try copying and pasting manually

**Settings Not Saving**
- Click "Apply" button after making changes
- Check for validation errors
- Restart IntelliJ if issues persist

**High API Costs**
- Use gpt-3.5-turbo for routine tasks
- Reduce max tokens setting
- Lower temperature for focused responses
- Monitor usage in OpenAI dashboard

### Getting Help
- Enable debug logging in settings
- Check IntelliJ logs: **Help | Show Log**
- Report issues on GitHub with logs
- Join community discussions

---

## 💡 Pro Tips

### Maximizing Productivity
1. **Use Keyboard Shortcuts**: Learn shortcuts for quick access
2. **Customize Panel Size**: Resize to fit your workflow
3. **Save Common Prompts**: Keep a note of effective prompts
4. **Combine Features**: Use chat for planning, inline for implementation

### Cost Management
1. **Start Small**: Begin with gpt-3.5-turbo and upgrade as needed
2. **Monitor Usage**: Check OpenAI dashboard regularly
3. **Optimize Prompts**: Clear, specific prompts get better results faster
4. **Use Caching**: Avoid repeating identical requests

### Quality Results
1. **Be Specific**: Include language, framework, and requirements
2. **Provide Context**: Share relevant code or error messages
3. **Iterate**: Refine requests based on initial responses
4. **Verify**: Always review and test generated code

---

## 🎨 Customization

### Panel Layout
- **Resize**: Drag panel borders to preferred size
- **Position**: Move panel to different sides of IDE
- **Hide/Show**: Toggle visibility as needed

### Keyboard Shortcuts
- **Send Message**: Enter key in chat input
- **Clear Chat**: Configurable shortcut
- **Toggle Panel**: Alt+9 (default)

### Theme Integration
- Automatically matches your IntelliJ theme
- Supports light and dark modes
- Consistent with IDE color scheme

---

This guide covers the enhanced features of Zigzag. For more detailed information, see [FEATURES.md](FEATURES.md) or visit our documentation.
