# Changelog

## [Unreleased]

## [1.0.3] - 2025-06-20
### Added
- Updated build configuration with proper dependency management
- Added comprehensive error handling and logging
- Implemented secure API key storage
- Added rate limiting and caching improvements

### Changed
- Migrated from deprecated Codex API to modern OpenAI API
- Improved UI/UX with better error messaging and loading indicators
- Enhanced plugin metadata and descriptions

### Fixed
- Fixed build configuration issues
- Resolved plugin.xml syntax errors
- Fixed memory leaks in UI components
- Added proper null safety checks

### Security
- Implemented secure API key storage
- Added input validation and sanitization
- Added request timeout and retry logic

## [1.0.2] - 2025-06-20
### Added
- Initial plugin setup
- Basic OpenAI integration
- Code completion and generation features
