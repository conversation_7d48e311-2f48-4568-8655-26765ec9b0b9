# Changelog



## 1.0.5 - 2025-06-20

### Added

- **Enhanced AI Assistant Panel**: Complete redesign with modern, interactive UI
  - Beautiful card-based message layout with distinct user/AI styling
  - One-click copy to clipboard functionality for AI responses
  - Direct code insertion into active editor with proper command integration
  - Real-time loading indicators and status feedback
  - Auto-scroll and conversation management features
  - Welcome message with feature overview
- **Beautiful Settings Interface**: Complete UI overhaul with modern design
  - Card-based layout with organized sections and visual hierarchy
  - Real-time cost estimation based on current settings
  - Built-in API connection testing with success/failure feedback
  - Interactive help links and tooltips throughout
  - Modern styled components matching IntelliJ's design language
  - Smart validation with immediate error feedback
- **Editor Integration Features**
  - Seamless copy-to-clipboard functionality
  - Direct code insertion with undo/redo support
  - Active editor detection and smart positioning
  - User notifications for successful operations
- **Enhanced User Experience**
  - Improved error handling with user-friendly messages
  - Loading states and progress indicators
  - Responsive design adapting to panel sizes
  - Consistent theming with IntelliJ platform

### Changed

- Upgraded AI Assistant Panel from basic text area to interactive chat interface
- Redesigned Settings UI from simple form to modern card-based layout
- Enhanced notification system with actionable feedback
- Improved message styling with proper spacing and typography
- Updated cost estimation to show real-time calculations

### Fixed

- Resolved UI scaling issues on different screen sizes
- Fixed message overflow in chat panel
- Improved error message clarity and actionability
- Enhanced accessibility with proper labeling and keyboard navigation

## 1.0.4 - 2025-06-20

### Added

- Modern intention action for better IDE integration
- Enhanced settings UI with improved layout and visual design
- Support for latest OpenAI models (GPT-4o, GPT-4o-mini)
- Comprehensive performance monitoring and metrics
- Advanced security utilities for input validation
- Modern startup activity for IntelliJ 2025.1+

### Changed

- Updated to support IntelliJ Platform 2025.1 (build 251)
- Migrated to Java 17 for platform compatibility
- Improved error handling with user-friendly messages
- Enhanced notification system with actionable buttons
- Better memory management and resource cleanup

### Fixed

- Resolved build issues with IntelliJ 2025.1
- Fixed plugin verification warnings
- Disabled problematic buildSearchableOptions task
- Corrected Java version compatibility issues
- Removed conflicting coroutines dependencies

### Security

- Enhanced input sanitization and validation
- Improved API key security handling
- Added protection against malicious input patterns

## 1.0.3 - 2025-06-20

### Added

- Updated build configuration with proper dependency management
- Added comprehensive error handling and logging
- Implemented secure API key storage
- Added rate limiting and caching improvements

### Changed

- Migrated from deprecated AI API to modern OpenAI API
- Improved UI/UX with better error messaging and loading indicators
- Enhanced plugin metadata and descriptions

### Fixed

- Fixed build configuration issues
- Resolved plugin.xml syntax errors
- Fixed memory leaks in UI components
- Added proper null safety checks

### Security

- Implemented secure API key storage
- Added input validation and sanitization
- Added request timeout and retry logic

## 1.0.2 - 2025-06-20

### Added

- Initial plugin setup
- Basic OpenAI integration
- Code completion and generation features
