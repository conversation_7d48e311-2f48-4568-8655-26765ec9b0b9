# Changelog

## [Unreleased]

## [1.0.4] - 2025-06-20
### Added
- Modern intention action for better IDE integration
- Enhanced settings UI with improved layout and visual design
- Support for latest OpenAI models (GPT-4o, GPT-4o-mini)
- Comprehensive performance monitoring and metrics
- Advanced security utilities for input validation
- Modern startup activity for IntelliJ 2025.1+

### Changed
- Updated to support IntelliJ Platform 2025.1 (build 251)
- Migrated to Java 17 for platform compatibility
- Improved error handling with user-friendly messages
- Enhanced notification system with actionable buttons
- Better memory management and resource cleanup

### Fixed
- Resolved build issues with IntelliJ 2025.1
- Fixed plugin verification warnings
- Disabled problematic buildSearchableOptions task
- Corrected Java version compatibility issues
- Removed conflicting coroutines dependencies

### Security
- Enhanced input sanitization and validation
- Improved API key security handling
- Added protection against malicious input patterns

## [1.0.3] - 2025-06-20
### Added
- Updated build configuration with proper dependency management
- Added comprehensive error handling and logging
- Implemented secure API key storage
- Added rate limiting and caching improvements

### Changed
- Migrated from deprecated Codex API to modern OpenAI API
- Improved UI/UX with better error messaging and loading indicators
- Enhanced plugin metadata and descriptions

### Fixed
- Fixed build configuration issues
- Resolved plugin.xml syntax errors
- Fixed memory leaks in UI components
- Added proper null safety checks

### Security
- Implemented secure API key storage
- Added input validation and sanitization
- Added request timeout and retry logic

## [1.0.2] - 2025-06-20
### Added
- Initial plugin setup
- Basic OpenAI integration
- Code completion and generation features
